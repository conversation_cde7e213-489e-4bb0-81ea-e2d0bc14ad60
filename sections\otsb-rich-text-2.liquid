{% render 'otsb-rich-text-base' %}
{% schema %}
{
  "name": "OT: Rich text #2",
  "tag": "section",
  "class": "section section-rich-text x-section otsb__root otsb-v2",
  "disabled_on": {
    "groups": [
      "header",
      "footer",
      "aside"
    ]
  },
  "settings": [
    {
      "type": "color",
      "id": "heading",
      "label": "Heading color",
      "default": "#242424"
    },
    {
      "type": "color",
      "id": "text",
      "label": "Text color",
      "default": "#242424"
    },
    {
      "type": "color",
      "id": "text_link",
      "label": "Text link",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "color_heading_highlight",
      "label": "Heading highlight color",
      "default": "#ff5900"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#fff"
    },
    {
      "type": "checkbox",
      "id": "change_heading_font",
      "default": false,
      "label": "Change heading font"
    },
    {
      "type": "font_picker",
      "id": "type_header_font",
      "default": "assistant_n4",
      "label": "Heading font"
    },
    {
      "type": "header",
      "content": "Desktop layout"
    },
    {
      "type": "select",
      "id": "content_alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center",
      "label": "Content alignment"
    },
    {
      "type": "checkbox",
      "id": "show_section_divider",
      "default": false,
      "label": "Show section divider"
    },
    {
      "type": "checkbox",
      "id": "make_full_page_width",
      "default": true,
      "label": "Make section full page width"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "unit": "px",
      "step": 4,
      "default": 28,
      "label": "Top padding"
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "unit": "px",
      "step": 4,
      "default": 28,
      "label": "Bottom padding"
    },
    {
      "type": "header",
      "content": "Mobile layout"
    },
    {
      "type": "select",
      "id": "content_alignment_mobile",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "center",
      "label": "Content alignment"
    },
    {
      "id": "show_section_divider_mobile",
      "type": "checkbox",
      "default": false,
      "label": "Show section divider"
    },
    {
      "type": "range",
      "id": "padding_top_mobile",
      "min": 0,
      "max": 100,
      "unit": "px",
      "step": 4,
      "default": 28,
      "label": "Top padding"
    },
    {
      "type": "range",
      "id": "padding_bottom_mobile",
      "min": 0,
      "max": 100,
      "unit": "px",
      "step": 4,
      "default": 28,
      "label": "Bottom padding"
    }
  ],
  "blocks": [
    {
      "type": "heading",
      "name": "Heading",
      "limit": 3,
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "default": "Story [About Us]", 
          "label": "Heading",
          "info": "Wrap your text between [] to add heading highlights. E.g: Adding [marker] will underline [highlight] text."
        },
        {
          "type": "select",
          "id": "highlight_type",
          "default": "underline",
          "label": "Marker",
          "options": [
            {
              "value": "underline",
              "label": "Underline"
            },
            {
              "value": "font_highlight",
              "label": "Font highlight"
            }
          ]
        },
        {
          "type": "range",
          "id": "heading_size",
          "min": 50,
          "max": 200,
          "unit": "%",
          "step": 10,
          "default": 100,
          "label": "Heading size"
        },
        {
          "type": "select",
          "id": "heading_tag",
          "default": "h2",
          "label": "Heading tag", 
          "options": [
            {
              "value": "h1",
              "label": "H1"
            },
            {
              "value": "h2",
              "label": "H2"
            },
            {
              "value": "h3",
              "label": "H3"
            },
            {
              "value": "h4",
              "label": "H4"
            },
            {
              "value": "h5",
              "label": "H5"
            },
            {
              "value": "h6",
              "label": "H6"
            },
            {
              "value": "p",
              "label": "p"
            }
          ]
        },
        {
          "type": "select",
          "id": "heading_text_transform",
          "default": "uppercase",
          "label": "Text transform", 
          "options": [
            {
              "value": "default",
              "label": "Default"
            },
            {
              "value": "capitalize",
              "label": "Capitalize"
            },
            {
              "value": "uppercase",
              "label": "Uppercase"
            },
            {
              "value": "lowercase",
              "label": "Lowercase"
            }
          ]
        }
      ]
    },
    {
      "type": "caption",
      "name": "Caption",
      "limit": 3,
      "settings": [
        {
          "type": "text",
          "id": "caption",
          "default": "Add a tagline",
          "label": "Text"
        },
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "subtitle",
              "label": "Subtitle"
            },
            {
              "value": "uppercase",
              "label": "Uppercase"
            }
          ],
          "default": "uppercase",
          "label": "Text style"
        },
        {
          "type": "range",
          "id": "text_size",
          "min": 50,
          "max": 200,
          "unit": "%",
          "step": 10,
          "default": 100,
          "label": "Text size"
        }
      ]
    },
    {
      "type": "text",
      "name": "Text",
      "limit": 3,
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "default": "<p>Pretium ligula Vestibulum consequat convallis fringilla Vestibulu nulla. cumsan morbi tristique auctor. At risus pretium urna tortor metus fringilla interdum mauris tempor congue. Aenean Lorem vitae id. [<i>Odio ut pretium</i>] ligula quam Vestibulum consequat convallis fringilla Vestibulum nulla. Accumsan morbi tristique auctor. Aenean nulla lacinia Nullam elit vel vel. [<b>At risus pretium</b>] urna tortor met</p>",
          "label": "Description",
          "info": "Wrap your text between [] to add text highlights. E.g: Adding [marker] will highlight text."
        },
        {
          "type": "number",
          "id": "number_of_lines_shown",
          "label": "Number of lines shown by default",
          "info": "Number of lines shown before showing a \"Read more\" link. Leave blank or 0 to show full content."
        },
        {
          "type": "text",
          "id": "read_more_label",
          "default": "Read more",
          "label": "\"Read more\" label"
        },
        {
          "type": "text",
          "id": "see_less_label",
          "default": "See less",
          "label": "\"See less\" label"
        }
      ]
    },
    {
      "type": "button",
      "name": "Buttons",
      "limit": 2,
      "settings": [
        {
          "type": "text", 
          "id": "button_label",
          "default": "Explore Now",
          "label": "First button label",
          "info": "Leave the label blank to hide the button."
        },
        {
          "type": "url",
          "id": "button_link",
          "label": "First button link"
        },
        {
          "type": "checkbox",
          "id": "open_new_window_button",
          "default": false,
          "label": "Open this link in a new window"
        },
        {
          "type": "select",
          "id": "show_button_style_1",
          "options": [
            {
              "value": "primary",
              "label": "Primary"
            },
            {
              "value": "secondary",
              "label": "Secondary"
            },
            {
              "value": "text-link",
              "label": "Text link"
            }
          ],
          "label": "Button style",
          "default": "primary"
        },
        {
          "type": "text",
          "id": "button_label_2",
          "label": "Second button label",
          "info": "Leave the label blank to hide the button."
        },
        {
          "type": "checkbox",
          "id": "open_new_window_button_2",
          "default": false,
          "label": "Open this link in a new window"
        },
        {
          "type": "url",
          "id": "button_link_2",
          "label": "Second button link"
        },
        {
          "type": "select",
          "id": "show_button_style_2",
          "options": [
            {
              "value": "primary",
              "label": "Primary"
            },
            {
              "value": "secondary",
              "label": "Secondary"
            },
            {
              "value": "text-link",
              "label": "Text link"
            }
          ],
          "label": "Button style",
          "default": "primary"
        },
        {
          "type": "header",
          "content": "Button Design"
        },
        {
          "type": "select",
          "id": "button_type",
          "options": [
            {
              "value": "square",
              "label": "Square"
            },
            {
              "value": "rounded",
              "label": "Rounded"
            },
            {
              "value": "rounded_corners",
              "label": "Rounded Corners"
            },
            {
              "value": "mixed",
              "label": "Mixed"
            }
          ],
          "label": "Button style",
          "default": "rounded"
        },
        {
          "type": "select",
          "id": "button_color_mobile",
          "options": [
            {
              "value": "hover",
              "label": "Use button hover color"
            },
            {
              "value": "color",
              "label": "Use button color"
            }
          ],
          "label": "Mobile primary button style",
          "default": "color"
        },
        {
          "type": "select",
          "id": "button_animation",
          "options": [
            {
              "value": "slide",
              "label": "Slide"
            },
            {
              "value": "fill_up",
              "label": "Fill up"
            },
            {
              "value": "sliced",
              "label": "Sliced with icon"
            },
            {
              "value": "underline",
              "label": "Underline"
            }
          ],
          "label": "Primary button hover animation",
          "default": "fill_up"
        },
        {
          "type": "html",
          "id": "custom_icon_button",
          "label": "Custom icon (SVG code)",
          "info": "Applies to 'Sliced with icon' animation type. For assistance with custom SVG code or fixing issues that arise from custom SVG code, please [contact our support](https://support.bsscommerce.com/support/tickets/new)."
        },
        {
          "type": "header",
          "content": "Colors"
        },
        {
          "type": "color",
          "id": "color_button",
          "label": "Button color",
          "default": "#FF5900",

        },
        {
          "type": "color",
          "id": "color_button_hover",
          "label": "Button hover color",
          "default": "#FF5900"
        },
        {
          "type": "color",
          "id": "color_text_button",
          "label": "Button text color",
          "default": "#fff"
        },
        {
          "type": "color",
          "id": "color_text_button_hover",
          "label": "Button text hover color",
          "default": "#fff"
        },
        {
          "type": "color",
          "id": "color_button_secondary",
          "label": "Secondary button color",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "secondary_button_text",
          "default": "#FF5900",
          "label": "Secondary button text color"
        },
        {
          "type": "color",
          "id": "colors_text_link",
          "default": "rgba(0,0,0,0)",
          "label": "Color text link"
        }
      ]
    },
    {
      "type": "media",
      "name": "Media",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "video",
          "id": "video",
          "label": "Video"
        },
        {
          "type": "checkbox",
          "id": "show_sound_control",
          "label": "Show sound control",
          "info": "Applies to auto play videos only.",
          "default": false
        },
        {
          "type": "header",
          "content": "Or embed video from url"
        },
        {
          "type": "paragraph",
          "content": "Shows when no Shopify-hosted video is selected."
        },
        {
          "type": "video_url",
          "id": "video_url",
          "accept": [
            "youtube",
            "vimeo"
          ],
          "label": "URL",
          "info": "Supports YouTube and Vimeo."
        },
        {
          "type": "checkbox",
          "id": "enable_video_autoplay",
          "default": true,
          "label": "Enable video autoplay",
          "info": "Video will be muted when autoplay is on."
        },
        {
          "type": "text",
          "id": "video_alt_text",
          "label": "Video alt text"
        },
        {
          "type": "range",
          "id": "media_size",
          "min": 1,
          "max": 100,
          "unit": "%",
          "step": 1,
          "default": 50,
          "label": "Media size"
        },
        {
          "type": "color",
          "id": "video_icon_color",
          "default": "#fff",
          "label": "Video icon"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "OT: Rich text #2",
      "blocks": [
        {
          "type": "heading"
        },
        {
          "type": "text"
        },
        {
          "type": "button"
        }
      ]
    }
  ]
}
{% endschema %}