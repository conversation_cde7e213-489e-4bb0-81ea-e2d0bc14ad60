[{"name": "theme_info", "theme_name": "DTFW", "theme_version": "1.0", "theme_author": "Wedesigntech", "theme_documentation_url": "https://help.shopify.com/manual/online-store/themes", "theme_support_url": "https://support.shopify.com/"}, {"name": "t:settings_schema.colors.name", "settings": [{"type": "header", "content": "t:settings_schema.colors.settings.header__1.content"}, {"type": "color", "id": "colors_solid_button_labels", "default": "#FFFFFF", "label": "t:settings_schema.colors.settings.colors_solid_button_labels.label", "info": "t:settings_schema.colors.settings.colors_solid_button_labels.info"}, {"id": "gradient_button_1", "type": "color_background", "label": "t:settings_schema.colors.settings.gradient_button_1.label"}, {"id": "gradient_button_hover", "type": "color_background", "label": "t:settings_schema.colors.settings.gradient_button_hover.label"}, {"type": "color", "id": "colors_accent_1", "default": "#121212", "label": "t:settings_schema.colors.settings.colors_accent_1.label", "info": "t:settings_schema.colors.settings.colors_accent_1.info"}, {"id": "gradient_accent_1", "type": "color_background", "label": "t:settings_schema.colors.settings.gradient_accent_1.label"}, {"type": "color", "id": "colors_accent_2", "default": "#334FB4", "label": "t:settings_schema.colors.settings.colors_accent_2.label"}, {"id": "gradient_accent_2", "type": "color_background", "label": "t:settings_schema.colors.settings.gradient_accent_2.label"}, {"type": "color", "id": "colors_accent_3", "default": "#334FB4", "label": "t:settings_schema.colors.settings.colors_accent_3.label"}, {"id": "gradient_accent_3", "type": "color_background", "label": "t:settings_schema.colors.settings.gradient_accent_3.label"}, {"type": "header", "content": "t:settings_schema.colors.settings.header__2.content"}, {"type": "color", "id": "colors_text", "default": "#121212", "label": "t:settings_schema.colors.settings.colors_text.label", "info": "t:settings_schema.colors.settings.colors_text.info"}, {"type": "color", "id": "colors_border", "default": "#F2F2F7", "label": "t:settings_schema.colors.settings.colors_border.label"}, {"type": "color", "id": "colors_outline_button_labels", "default": "#121212", "label": "t:settings_schema.colors.settings.colors_outline_button_labels.label", "info": "t:settings_schema.colors.settings.colors_outline_button_labels.info"}, {"type": "color", "id": "colors_background_1", "default": "#FFFFFF", "label": "t:settings_schema.colors.settings.colors_background_1.label"}, {"id": "gradient_background_1", "type": "color_background", "label": "t:settings_schema.colors.settings.gradient_background_1.label"}, {"type": "color", "id": "colors_background_2", "default": "#F3F3F3", "label": "t:settings_schema.colors.settings.colors_background_2.label"}, {"id": "gradient_background_2", "type": "color_background", "label": "t:settings_schema.colors.settings.gradient_background_2.label"}, {"type": "color", "id": "colors_background_3", "default": "#F3F3F3", "label": "t:settings_schema.colors.settings.colors_background_3.label"}, {"id": "gradient_background_3", "type": "color_background", "label": "t:settings_schema.colors.settings.gradient_background_3.label"}, {"type": "header", "content": "t:settings_schema.colors.settings.header__3.content"}, {"type": "color", "id": "overlay_color", "default": "#F3F3F3", "label": "t:settings_schema.colors.settings.overlay_color.label"}]}, {"name": "t:settings_schema.preloader.name", "settings": [{"type": "checkbox", "id": "preloader_enable", "label": "t:settings_schema.preloader.settings.preloader_enable.label", "default": true}, {"type": "image_picker", "id": "preloader", "label": "t:settings_schema.preloader.settings.preloader.label"}]}, {"name": "t:settings_schema.typography.name", "settings": [{"type": "header", "content": "t:settings_schema.typography.settings.header__1.content"}, {"type": "font_picker", "id": "type_header_font", "default": "assistant_n4", "label": "t:settings_schema.typography.settings.type_header_font.label", "info": "t:settings_schema.typography.settings.type_header_font.info"}, {"type": "range", "id": "heading_scale", "min": 100, "max": 150, "step": 5, "unit": "%", "label": "t:settings_schema.typography.settings.heading_scale.label", "default": 100}, {"type": "textarea", "id": "custom_font_script_1", "label": "t:settings_schema.typography.settings.custom_font_script_1.label"}, {"type": "textarea", "id": "custom_font_family_1", "label": "t:settings_schema.typography.settings.custom_font_family_1.label", "info": "t:settings_schema.typography.settings.custom_font_family_1.info"}, {"type": "header", "content": "t:settings_schema.typography.settings.header__2.content"}, {"type": "font_picker", "id": "type_body_font", "default": "assistant_n4", "label": "t:settings_schema.typography.settings.type_body_font.label", "info": "t:settings_schema.typography.settings.type_body_font.info"}, {"type": "range", "id": "body_scale", "min": 100, "max": 130, "step": 5, "unit": "%", "label": "t:settings_schema.typography.settings.body_scale.label", "default": 100}, {"type": "textarea", "id": "custom_font_script_2", "label": "t:settings_schema.typography.settings.custom_font_script_2.label"}, {"type": "textarea", "id": "custom_font_family_2", "label": "t:settings_schema.typography.settings.custom_font_family_2.label", "info": "t:settings_schema.typography.settings.custom_font_family_2.info"}, {"type": "header", "content": "t:settings_schema.typography.settings.header__3.content"}, {"type": "font_picker", "id": "type_additional_font", "default": "assistant_n4", "label": "t:settings_schema.typography.settings.type_additional_font.label", "info": "t:settings_schema.typography.settings.type_additional_font.info"}, {"type": "textarea", "id": "custom_font_script_3", "label": "t:settings_schema.typography.settings.custom_font_script_3.label"}, {"type": "textarea", "id": "custom_font_family_3", "label": "t:settings_schema.typography.settings.custom_font_family_3.label", "info": "t:settings_schema.typography.settings.custom_font_family_3.info"}]}, {"name": "t:settings_schema.general_settings.name", "settings": [{"type": "checkbox", "id": "display_color_variant", "default": false, "label": "t:settings_schema.general_settings.settings.display_color_variant.label"}, {"type": "checkbox", "id": "display_item_size", "default": false, "label": "t:settings_schema.general_settings.settings.display_item_size.label"}, {"type": "checkbox", "id": "enable_timer", "default": false, "label": "t:settings_schema.general_settings.settings.enable_timer.label"}, {"type": "text", "id": "timer_heading", "label": "t:settings_schema.general_settings.settings.timer_heading.label", "default": "Deal end soon"}, {"type": "checkbox", "id": "enable_wishlist", "default": true, "label": "t:settings_schema.general_settings.settings.enable_wishlist.label"}, {"type": "checkbox", "id": "enable_compare", "default": true, "label": "t:settings_schema.general_settings.settings.enable_compare.label"}, {"type": "checkbox", "id": "enable_quickview", "default": true, "label": "t:settings_schema.general_settings.settings.enable_quickview.label"}, {"type": "checkbox", "id": "enable_quickadd", "default": true, "label": "t:settings_schema.general_settings.settings.enable_quickadd.label"}, {"type": "checkbox", "id": "enable_scroll_to_top", "default": false, "label": "t:settings_schema.general_settings.settings.enable_scroll_to_top.label"}, {"type": "checkbox", "id": "offer_price_enable", "default": true, "label": "t:settings_schema.general_settings.settings.offer_price_enable.label"}]}, {"name": "t:settings_schema.layout.name", "settings": [{"type": "range", "id": "page_width", "min": 1000, "max": 1700, "step": 20, "default": 1200, "unit": "px", "label": "t:settings_schema.layout.settings.page_width.label"}, {"type": "range", "id": "page_width_laptop", "min": 1000, "max": 1200, "step": 20, "default": 1100, "unit": "px", "label": "t:settings_schema.layout.settings.page_width_laptop.label"}, {"type": "range", "id": "page_width_tab", "min": 700, "max": 1000, "step": 10, "default": 960, "unit": "px", "label": "t:settings_schema.layout.settings.page_width_tab.label"}, {"type": "range", "id": "page_full_width_spacing", "min": 0, "max": 20, "step": 2, "unit": "%", "label": "t:settings_schema.layout.settings.page_full_width_spacing.label", "default": 0}, {"type": "range", "id": "spacing_sections", "min": 0, "max": 100, "step": 4, "unit": "px", "label": "t:settings_schema.layout.settings.spacing_sections.label", "default": 0}, {"type": "header", "content": "t:settings_schema.layout.settings.header__grid.content"}, {"type": "paragraph", "content": "t:settings_schema.layout.settings.paragraph__grid.content"}, {"type": "range", "id": "spacing_grid_horizontal", "min": 4, "max": 40, "step": 2, "default": 8, "unit": "px", "label": "t:settings_schema.layout.settings.spacing_grid_horizontal.label"}, {"type": "range", "id": "spacing_grid_vertical", "min": 4, "max": 40, "step": 2, "default": 8, "unit": "px", "label": "t:settings_schema.layout.settings.spacing_grid_vertical.label"}, {"type": "header", "content": "t:settings_schema.layout.settings.header__sidebar.content"}, {"type": "range", "id": "sidebar_width", "min": 0, "max": 400, "step": 5, "label": "t:settings_schema.layout.settings.sidebar_width.label", "default": 380}]}, {"name": "t:settings_schema.buttons.name", "settings": [{"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "buttons_border_thickness", "min": 0, "max": 12, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 1}, {"type": "range", "id": "buttons_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 100}, {"type": "range", "id": "buttons_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 0}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "buttons_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "buttons_shadow_horizontal_offset", "min": -12, "max": 12, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "buttons_shadow_vertical_offset", "min": -12, "max": 12, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "buttons_shadow_blur", "min": 0, "max": 20, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}]}, {"name": "t:settings_schema.variant_pills.name", "settings": [{"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "variant_pills_border_thickness", "min": 0, "max": 12, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 1}, {"type": "range", "id": "variant_pills_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 55}, {"type": "range", "id": "variant_pills_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 40}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "variant_pills_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "variant_pills_shadow_horizontal_offset", "min": -12, "max": 12, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "variant_pills_shadow_vertical_offset", "min": -12, "max": 12, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "variant_pills_shadow_blur", "min": 0, "max": 20, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}]}, {"name": "t:settings_schema.inputs.name", "settings": [{"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "inputs_border_thickness", "min": 0, "max": 12, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 1}, {"type": "range", "id": "inputs_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 55}, {"type": "range", "id": "inputs_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 0}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "inputs_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "inputs_shadow_horizontal_offset", "min": -12, "max": 12, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "inputs_shadow_vertical_offset", "min": -12, "max": 12, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "inputs_shadow_blur", "min": 0, "max": 20, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}]}, {"name": "t:settings_schema.card_style.name", "settings": [{"type": "select", "id": "card_style", "options": [{"value": "button_width_icons", "label": "t:settings_schema.card_style.settings.style.options__1.label"}, {"value": "card_with_icons", "label": "t:settings_schema.card_style.settings.style.options__2.label"}, {"value": "card_with_buttons", "label": "t:settings_schema.card_style.settings.style.options__3.label"}, {"value": "card_with_overlay", "label": "t:settings_schema.card_style.settings.style.options__4.label"}, {"value": "standard", "label": "t:settings_schema.card_style.settings.style.options__5.label"}], "default": "standard", "label": "t:settings_schema.card_style.settings.style.label"}, {"type": "range", "id": "card_image_padding", "min": 0, "max": 20, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.image_padding.label", "default": 0}, {"type": "select", "id": "card_text_alignment", "options": [{"value": "left", "label": "t:settings_schema.global.settings.text_alignment.options__1.label"}, {"value": "center", "label": "t:settings_schema.global.settings.text_alignment.options__2.label"}, {"value": "right", "label": "t:settings_schema.global.settings.text_alignment.options__3.label"}], "default": "left", "label": "t:settings_schema.global.settings.text_alignment.label"}, {"type": "select", "id": "card_color_scheme", "options": [{"value": "accent-1", "label": "t:sections.all.colors.accent_1.label"}, {"value": "accent-2", "label": "t:sections.all.colors.accent_2.label"}, {"value": "background-1", "label": "t:sections.all.colors.background_1.label"}, {"value": "background-2", "label": "t:sections.all.colors.background_2.label"}, {"value": "inverse", "label": "t:sections.all.colors.inverse.label"}], "default": "background-2", "label": "t:sections.all.colors.label"}, {"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "card_border_thickness", "min": 0, "max": 24, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 0}, {"type": "range", "id": "card_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "card_corner_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 0}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "card_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 10}, {"type": "range", "id": "card_shadow_horizontal_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "card_shadow_vertical_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "card_shadow_blur", "min": 0, "max": 40, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}]}, {"name": "t:settings_schema.content_containers.name", "settings": [{"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "text_boxes_border_thickness", "min": 0, "max": 24, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 0}, {"type": "range", "id": "text_boxes_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "text_boxes_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 0}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "text_boxes_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "text_boxes_shadow_horizontal_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "text_boxes_shadow_vertical_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "text_boxes_shadow_blur", "min": 0, "max": 40, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}]}, {"name": "t:settings_schema.media.name", "settings": [{"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "media_border_thickness", "min": 0, "max": 24, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 1}, {"type": "range", "id": "media_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 5}, {"type": "range", "id": "media_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 0}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "media_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "media_shadow_horizontal_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "media_shadow_vertical_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "media_shadow_blur", "min": 0, "max": 40, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}]}, {"name": "t:settings_schema.popups.name", "settings": [{"type": "paragraph", "content": "t:settings_schema.popups.paragraph"}, {"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "popup_border_thickness", "min": 0, "max": 24, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 1}, {"type": "range", "id": "popup_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 10}, {"type": "range", "id": "popup_corner_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 0}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "popup_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "popup_shadow_horizontal_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "popup_shadow_vertical_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "popup_shadow_blur", "min": 0, "max": 40, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}]}, {"name": "t:settings_schema.drawers.name", "settings": [{"type": "header", "content": "t:settings_schema.global.settings.header__border.content"}, {"type": "range", "id": "drawer_border_thickness", "min": 0, "max": 24, "step": 1, "unit": "px", "label": "t:settings_schema.global.settings.thickness.label", "default": 1}, {"type": "range", "id": "drawer_border_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 10}, {"type": "header", "content": "t:settings_schema.global.settings.header__shadow.content"}, {"type": "range", "id": "drawer_shadow_opacity", "min": 0, "max": 100, "step": 5, "unit": "%", "label": "t:settings_schema.global.settings.opacity.label", "default": 0}, {"type": "range", "id": "drawer_shadow_horizontal_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.horizontal_offset.label", "default": 0}, {"type": "range", "id": "drawer_shadow_vertical_offset", "min": -40, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.vertical_offset.label", "default": 0}, {"type": "range", "id": "drawer_shadow_blur", "min": 0, "max": 40, "step": 5, "unit": "px", "label": "t:settings_schema.global.settings.blur.label", "default": 0}]}, {"name": "t:settings_schema.badges.name", "settings": [{"type": "select", "id": "badge_position", "options": [{"value": "bottom-left", "label": "t:settings_schema.badges.settings.position.options__1.label"}, {"value": "bottom-right", "label": "t:settings_schema.badges.settings.position.options__2.label"}, {"value": "top-left", "label": "t:settings_schema.badges.settings.position.options__3.label"}, {"value": "top-right", "label": "t:settings_schema.badges.settings.position.options__4.label"}], "default": "bottom-left", "label": "t:settings_schema.badges.settings.position.label"}, {"type": "range", "id": "badge_corner_radius", "min": 0, "max": 40, "step": 2, "unit": "px", "label": "t:settings_schema.global.settings.corner_radius.label", "default": 40}, {"type": "select", "id": "sale_badge_color_scheme", "options": [{"value": "accent-1", "label": "t:sections.all.colors.accent_1.label"}, {"value": "accent-2", "label": "t:sections.all.colors.accent_2.label"}, {"value": "background-2", "label": "t:sections.all.colors.background_2.label"}], "default": "accent-2", "label": "t:settings_schema.badges.settings.sale_badge_color_scheme.label"}, {"type": "select", "id": "sold_out_badge_color_scheme", "options": [{"value": "background-1", "label": "t:sections.all.colors.background_1.label"}, {"value": "inverse", "label": "t:sections.all.colors.inverse.label"}], "default": "inverse", "label": "t:settings_schema.badges.settings.sold_out_badge_color_scheme.label"}]}, {"name": "t:settings_schema.styles.name", "settings": [{"type": "select", "id": "accent_icons", "options": [{"value": "accent-1", "label": "t:sections.all.colors.accent_1.label"}, {"value": "accent-2", "label": "t:sections.all.colors.accent_2.label"}, {"value": "outline-button", "label": "t:settings_schema.styles.settings.accent_icons.options__3.label"}, {"value": "text", "label": "t:settings_schema.styles.settings.accent_icons.options__4.label"}], "default": "text", "label": "t:settings_schema.styles.settings.accent_icons.label"}]}, {"name": "Breadcrumb", "settings": [{"type": "checkbox", "id": "use_breadcrumb", "label": "Use Breadcrumb?", "default": true}, {"type": "checkbox", "id": "use_breadcrumb_title", "label": "Enable Breadcrumb Title?", "default": true}, {"type": "image_picker", "id": "breadcrumb_image"}, {"type": "select", "id": "breadcrumb_style", "label": "Style", "default": "center", "options": [{"value": "start", "label": "Left"}, {"value": "center", "label": "Center"}, {"value": "end", "label": "Right"}]}, {"type": "range", "id": "image_overlay_opacity", "label": "Opacity", "min": 0, "max": 99, "step": 1, "unit": "%", "default": 40}, {"type": "range", "id": "breadcrumb_padding_top", "label": "Padding Top", "min": 15, "max": 150, "step": 5, "unit": "px", "default": 40}, {"type": "range", "id": "breadcrumb_padding_bottom", "label": "Padding Bottom", "min": 15, "max": 150, "step": 5, "unit": "px", "default": 40}, {"type": "range", "id": "breadcrumb_margin_bottom", "label": "<PERSON>gin <PERSON>", "min": 0, "max": 100, "step": 1, "unit": "px", "default": 40}]}, {"name": "t:settings_schema.social-media.name", "settings": [{"type": "header", "content": "t:settings_schema.social-media.settings.header.content"}, {"type": "text", "id": "social_twitter_link", "label": "t:settings_schema.social-media.settings.social_twitter_link.label", "info": "t:settings_schema.social-media.settings.social_twitter_link.info"}, {"type": "text", "id": "social_facebook_link", "label": "t:settings_schema.social-media.settings.social_facebook_link.label", "info": "t:settings_schema.social-media.settings.social_facebook_link.info"}, {"type": "text", "id": "social_pinterest_link", "label": "t:settings_schema.social-media.settings.social_pinterest_link.label", "info": "t:settings_schema.social-media.settings.social_pinterest_link.info"}, {"type": "text", "id": "social_instagram_link", "label": "t:settings_schema.social-media.settings.social_instagram_link.label", "info": "t:settings_schema.social-media.settings.social_instagram_link.info"}, {"type": "text", "id": "social_tiktok_link", "label": "t:settings_schema.social-media.settings.social_tiktok_link.label", "info": "t:settings_schema.social-media.settings.social_tiktok_link.info"}, {"type": "text", "id": "social_tumblr_link", "label": "t:settings_schema.social-media.settings.social_tumblr_link.label", "info": "t:settings_schema.social-media.settings.social_tumblr_link.info"}, {"type": "text", "id": "social_snapchat_link", "label": "t:settings_schema.social-media.settings.social_snapchat_link.label", "info": "t:settings_schema.social-media.settings.social_snapchat_link.info"}, {"type": "text", "id": "social_youtube_link", "label": "t:settings_schema.social-media.settings.social_youtube_link.label", "info": "t:settings_schema.social-media.settings.social_youtube_link.info"}, {"type": "text", "id": "social_vimeo_link", "label": "t:settings_schema.social-media.settings.social_vimeo_link.label", "info": "t:settings_schema.social-media.settings.social_vimeo_link.info"}]}, {"name": "t:settings_schema.search_input.name", "settings": [{"type": "header", "content": "t:settings_schema.search_input.settings.header.content"}, {"type": "checkbox", "id": "predictive_search_enabled", "default": true, "label": "t:settings_schema.search_input.settings.predictive_search_enabled.label"}, {"type": "checkbox", "id": "predictive_search_show_vendor", "default": false, "label": "t:settings_schema.search_input.settings.predictive_search_show_vendor.label", "info": "t:settings_schema.search_input.settings.predictive_search_show_vendor.info"}, {"type": "checkbox", "id": "predictive_search_show_price", "default": false, "label": "t:settings_schema.search_input.settings.predictive_search_show_price.label", "info": "t:settings_schema.search_input.settings.predictive_search_show_price.info"}, {"type": "textarea", "id": "search_tags", "label": "t:settings_schema.search_input.settings.search_tags.label", "info": "t:settings_schema.search_input.settings.search_tags.info"}, {"type": "collection", "id": "search_collection", "label": "t:settings_schema.search_input.settings.search_collection.label", "info": "t:settings_schema.search_input.settings.search_collection.info"}, {"type": "range", "id": "products_to_show", "min": 2, "max": 12, "step": 1, "default": 4, "label": "t:settings_schema.search_input.settings.products_to_show.label"}, {"type": "range", "id": "columns_desktop", "min": 1, "max": 5, "step": 1, "default": 4, "label": "t:settings_schema.search_input.settings.columns_desktop.label"}, {"type": "header", "content": "t:sections.all.swiper.swiper_slider_title"}, {"type": "checkbox", "id": "swiper_enable", "default": false, "label": "t:sections.all.swiper.swiper_slider_enable"}, {"type": "range", "id": "desktop_column", "min": 1, "max": 10, "step": 1, "label": "t:sections.all.swiper.desktop_column", "default": 4}, {"type": "range", "id": "laptop_column", "min": 1, "max": 10, "step": 1, "label": "t:sections.all.swiper.laptop_column", "default": 4}, {"type": "range", "id": "tablet_column", "min": 1, "max": 5, "step": 1, "label": "t:sections.all.swiper.tablet_column", "default": 3}, {"type": "range", "id": "mobile_column", "min": 1, "max": 3, "step": 1, "label": "t:sections.all.swiper.mobile_column", "default": 1}, {"type": "checkbox", "id": "centered_slide", "default": false, "label": "t:sections.all.swiper.centered_slide"}, {"type": "checkbox", "id": "swiper_pagination", "default": false, "label": "t:sections.all.swiper.swiper_pagination"}, {"type": "checkbox", "id": "swiper_navigation", "default": false, "label": "t:sections.all.swiper.swiper_navigation"}, {"type": "range", "id": "auto_play", "min": 0, "max": 5, "step": 1, "label": "t:sections.all.swiper.auto_play", "default": 0}, {"type": "header", "content": "t:settings_schema.search_input.settings.header.content"}, {"type": "select", "id": "image_ratio", "options": [{"value": "adapt", "label": "t:settings_schema.search_input.settings.image_ratio.options__1.label"}, {"value": "portrait", "label": "t:settings_schema.search_input.settings.image_ratio.options__2.label"}, {"value": "square", "label": "t:settings_schema.search_input.settings.image_ratio.options__3.label"}], "default": "adapt", "label": "t:settings_schema.search_input.settings.image_ratio.label"}, {"type": "checkbox", "id": "show_secondary_image", "default": false, "label": "t:settings_schema.search_input.settings.show_secondary_image.label"}, {"type": "checkbox", "id": "show_new_tag", "default": false, "label": "t:settings_schema.search_input.settings.show_new_tag.label"}, {"type": "checkbox", "id": "show_rating", "default": false, "label": "t:settings_schema.search_input.settings.show_rating.label", "info": "t:settings_schema.search_input.settings.show_rating.info"}]}, {"name": "t:settings_schema.favicon.name", "settings": [{"type": "image_picker", "id": "favicon", "label": "t:settings_schema.favicon.settings.favicon.label", "info": "t:settings_schema.favicon.settings.favicon.info"}, {"type": "text", "id": "flash-text1", "default": "Welcome to OS!", "label": "t:settings_schema.favicon.settings.flash-text1.label"}, {"type": "text", "id": "flash-text2", "default": "Comeback!", "label": "t:settings_schema.favicon.settings.flash-text2.label"}]}, {"name": "t:settings_schema.currency_format.name", "settings": [{"type": "header", "content": "t:settings_schema.currency_format.settings.content"}, {"type": "paragraph", "content": "t:settings_schema.currency_format.settings.paragraph"}, {"type": "checkbox", "id": "currency_code_enabled", "label": "t:settings_schema.currency_format.settings.currency_code_enabled.label", "default": true}]}, {"name": "t:settings_schema.cart.name", "settings": [{"type": "select", "id": "cart_type", "options": [{"value": "drawer", "label": "t:settings_schema.cart.settings.cart_type.drawer.label"}, {"value": "page", "label": "t:settings_schema.cart.settings.cart_type.page.label"}, {"value": "notification", "label": "t:settings_schema.cart.settings.cart_type.notification.label"}], "default": "notification", "label": "t:settings_schema.cart.settings.cart_type.label"}, {"type": "checkbox", "id": "show_vendor", "label": "t:settings_schema.cart.settings.show_vendor.label", "default": false}, {"type": "checkbox", "id": "show_cart_note", "label": "t:settings_schema.cart.settings.show_cart_note.label", "default": false}, {"type": "checkbox", "id": "show_gift_wrap", "label": "t:settings_schema.cart.settings.show_gift_wrap.label", "default": false}, {"type": "checkbox", "id": "show_shipping_info", "label": "t:settings_schema.cart.settings.show_shipping_info.label", "default": false}, {"type": "checkbox", "id": "show_discount_option", "label": "t:settings_schema.cart.settings.show_discount_option.label", "default": false}, {"type": "header", "content": "t:settings_schema.cart.settings.goal.header"}, {"type": "number", "id": "goal", "label": "t:settings_schema.cart.settings.goal.label", "default": 500}, {"type": "header", "content": "t:settings_schema.cart.settings.cart_drawer.header"}, {"type": "collection", "id": "cart_drawer_collection", "label": "t:settings_schema.cart.settings.cart_drawer.collection.label", "info": "t:settings_schema.cart.settings.cart_drawer.collection.info"}]}, {"name": "t:settings_schema.footer.name", "settings": [{"type": "select", "id": "footer_type", "options": [{"value": "style1", "label": "t:settings_schema.footer.settings.footer_type.style1.label"}, {"value": "style2", "label": "t:settings_schema.footer.settings.footer_type.style2.label"}, {"value": "style3", "label": "t:settings_schema.footer.settings.footer_type.style3.label"}, {"value": "style4", "label": "t:settings_schema.footer.settings.footer_type.style4.label"}, {"value": "style5", "label": "t:settings_schema.footer.settings.footer_type.style5.label"}], "default": "style1", "label": "t:settings_schema.footer.settings.footer_type.label"}]}, {"name": "Shipping Rates Calculator", "settings": [{"type": "select", "id": "shipping_calculator", "label": "Show the shipping calculator?", "options": [{"value": "Disabled", "label": "No"}, {"value": "Enabled", "label": "Yes"}], "default": "Enabled"}, {"type": "text", "id": "shipping_calculator_heading", "label": "Heading text", "default": "Get shipping estimates"}, {"type": "text", "id": "shipping_calculator_default_country", "label": "Default country selection", "default": "United States"}, {"type": "paragraph", "content": "If your customer is logged-in, the country in his default shipping address will be selected. If you are not sure about the  spelling to use here, refer to the first checkout page."}, {"type": "text", "id": "shipping_calculator_submit_button_label", "label": "Submit button label", "default": "Calculate shipping"}, {"type": "text", "id": "shipping_calculator_submit_button_label_disabled", "label": "Submit button label when calculating", "default": "Calculating..."}]}]