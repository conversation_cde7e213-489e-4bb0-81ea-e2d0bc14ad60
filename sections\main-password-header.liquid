<div class="color-{{ section.settings.color_scheme }} gradient">
  <div class="password-header">
     <a href="{{ routes.root_url }}" class="logo_link">
    {%- if section.settings.logo -%}
      <img
        src="{{ section.settings.logo | image_url: width: 500, height: 500 }}"
        class="password-logo"
        alt="{{ section.settings.logo.alt | default: shop.name | escape }}"
        style="max-width: {{ section.settings.logo_max_width }}px"
        width="{{ section.settings.logo_max_width }}"
        height="{{ section.settings.logo_max_width | divided_by: section.settings.logo.aspect_ratio }}"
        loading="lazy"
      >
    {%- else -%}
      <h1 class="h2">{{ shop.name }}</h1>
    {%- endif -%}
  </a>
    {%- if shop.password_message != blank -%}
      <div class="password-content">
        {{ shop.password_message }}
      </div>
    {%- endif -%}

    <password-modal>
      <details class="password-modal modal">
        <summary class="modal__toggle" aria-haspopup="dialog">
          <div class="modal__toggle-open password-link link underlined-link">
            {% render 'icon-padlock' %}
            {{ 'general.password_page.login_password_button' | t }}
          </div>
          <div class="modal__toggle-close" aria-hidden="true">
            {% render 'icon-close' %}
          </div>
        </summary>
        <div
          class="modal__content"
          role="dialog"
          aria-labelledby="DialogHeading"
          aria-modal="true"
        >
          <div class="password-modal__content" tabindex="-1">
            <button type="button" class="modal__close-button link" aria-label="{{ 'accessibility.close' | t }}">
              {% render 'icon-close' %}
            </button>
            <h2 class="password-modal__content-heading" id="DialogHeading">
              {{ 'general.password_page.login_form_heading' | t }}
            </h2>
            {%- form 'storefront_password', class: 'password-form' -%}
              <div class="password-field field{% if form.errors %} password-field--error{% endif %}">
                <input
                  type="password"
                  name="password"
                  id="Password"
                  class="field__input"
                  autocomplete="current-password"
                  {% if form.errors %}
                    aria-invalid="true"
                    aria-describedby="PasswordLoginForm-password-error"
                  {%- endif -%}
                  placeholder="{{ 'general.password_page.login_form_password_placeholder' | t }}"
                >
                <label class="field__label" for="{{ 'general.password_page.login_form_password_label' | t }}">{{ 'general.password_page.login_form_password_placeholder' | t }}</label>
                {%- if form.errors -%}
                  <div id="PasswordLoginForm-password-error" role="status">
                    <span class="visually-hidden">{{ 'accessibility.error' | t }}</span>
                    <span class="form__message">{% render 'icon-error' %} {{ 'general.password_page.login_form_error' | t }}</span>
                  </div>
                {%- endif -%}
              </div>
              <button name="commit" class="password-button button button--outline">
                {{ 'general.password_page.login_form_submit' | t }}
              </button>
            {%- endform -%}
            <small class="password__footer-text">{{ 'general.password_page.admin_link_html' | t }}</small>
          </div>
        </div>
      </details>
    </password-modal>
  </div>
</div>
{%- if section.settings.color_scheme == 'background-1' -%}<hr>{%- endif -%}

{% schema %}
{
  "name": "t:sections.main-password-header.name",
  "settings": [
    {
      "type": "image_picker",
      "id": "logo",
      "label": "t:sections.main-password-header.settings.logo.label"
    },
    {
      "type": "range",
      "id": "logo_max_width",
      "min": 50,
      "max": 250,
      "step": 10,
      "default": 100,
      "unit": "t:sections.main-password-header.settings.logo_max_width.unit",
      "label": "t:sections.main-password-header.settings.logo_max_width.label"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "t:sections.all.colors.accent_1.label"
        },
        {
          "value": "accent-2",
          "label": "t:sections.all.colors.accent_2.label"
        },
        {
          "value": "background-1",
          "label": "t:sections.all.colors.background_1.label"
        },
        {
          "value": "background-2",
          "label": "t:sections.all.colors.background_2.label"
        },
        {
          "value": "inverse",
          "label": "t:sections.all.colors.inverse.label"
        }
      ],
      "default": "background-1",
      "label": "t:sections.all.colors.label"
    }
  ]
}
{% endschema %}
