.cart-items td,
.cart-items th {
  padding: 0;
  border: none;
}

.cart-items th {
  text-align: left;
  padding-bottom: 1.8rem;
  font-size: 1.8rem;
  font-weight: 400;
}

.cart-item__quantity-wrapper {
  display: flex;
}

.cart-item__totals {
  position: relative;
}

.cart-items *.right {
  text-align: right;
}

.cart-item__image-container {
  display: inline-flex;
  align-items: flex-start;
}

.cart-item__image-container:after {
  content: none;
}

.cart-item__image {
  height: 100%;
  max-width: 100%;
}

@media screen and (min-width: 861px) {
  .cart-item__image {
    max-width: 100%;
  }
}

.cart-item__details {
  font-size: 1.6rem;
  line-height: calc(1 + 0.4 / var(--font-body-scale));
}

.cart-item__details > * {
  margin: 0;
  max-width: 30rem;
}

.cart-item__details > * + * {
  margin-top: 0.6rem;
}

.cart-item__media {
  position: relative;
}

.cart-item__link {
  display: block;
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  width: 100%;
  height: 100%;
}

.cart-item__name {
  text-decoration: none;
  display: flex;
  transition: all 0.3s linear;
  font-size: 1.6rem;
  font-weight: 500;
  text-align: left;
}
.cart__items .cart-item__name {
  display: inline-block;
}
.cart-item__name:hover {
  /*   text-decoration: underline;
  text-underline-offset: 0.3rem;
  text-decoration-thickness: 0.2rem; */
  color: rgb(var(--color-base-outline-button-labels));
}

.cart-item__price-wrapper > * {
  display: block;
  margin: 0;
  padding: 0;
}

.cart-item__discounted-prices dd {
  margin: 0;
}

.cart-item__discounted-prices .cart-item__old-price {
  font-size: 1.4rem;
}

.cart-item__old-price {
  opacity: 0.7;
}

.cart-item__final-price {
  font-weight: 400;
}

.product-option {
  font-size: 1.6rem;
  word-break: break-all;
  line-height: calc(1 + 0.5 / var(--font-body-scale));
  font-weight: 400;
}

.cart-item cart-remove-button {
  display: flex;
  margin-left: 15px;
  align-items: center;
}


.cart-item__price-wrapper .price{font-size:1.6rem;}
cart-remove-button .button {
  width: 1.6em;
  min-width: 1.6rem;
  min-height:1.6rem;
  height: 1.6rem;
  padding: 0;
  margin:  0;
  border-radius: 50%;
}

cart-remove-button .button:before,
cart-remove-button .button:after {
  content: none;
}

cart-remove-button .button:not([disabled]):hover {
  color: rgb(var(--color-foreground));
}

@media screen and (min-width: 860px) {
  cart-remove-button .button {
    min-width: 1.6rem;
    min-height: 1.6rem;
  }
}

cart-remove-button .icon-remove {
  height: 1.6rem;
  width: 1.6rem;
}

.cart-item .loading-overlay {
  top: 50%;
  left: auto;
  right: auto;
  bottom: 0;
  padding: 0;
  /* transform: translateY(-50%); */
}
.cart-item .loading-overlay .loading-icon{animation: loading-animation 1.5s infinite linear}

@keyframes loading-animation{
0% {
    transform: rotate(0);
}
100% {
    transform: rotate(359deg);
}
}
@media screen and (min-width: 861px) {
  .cart-item .loading-overlay {
    right: 0;
    padding-top: 4rem;
    bottom: 0;
  }
}

.cart-item .loading-overlay:not(.hidden) ~ * {
  visibility: hidden;
}

.cart-item__error {
  display: flex;
  align-items: flex-start;
  margin-top: 1rem;
}

.cart-item__error-text {
  font-size: 1.2rem;
  order: 1;
}

.cart-item__error-text + svg {
  flex-shrink: 0;
  width: 1.2rem;
  margin-right: 0.7rem;
}

.cart-item__error-text:empty + svg {
  display: none;
}

.product-option + .product-option {
/*   margin-top: 0.4rem; */
  align-items: flex-start;
  display: flex;
  gap:2px;
}

.product-option * {
  display: inline;
  margin: 0;
}
.cart-items thead th {
    text-transform: capitalize;
    letter-spacing: normal;
    font-weight: 500;
    font-size: 2.5rem;
}

@media screen and (max-width: 860px) {
  .cart-items,
  .cart-items thead,
  .cart-items tbody {
    display: block;
    width: 100%;
  }

  .cart-items thead tr {
    display: flex;
    justify-content: space-between;
    border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.2);
    margin-bottom: 4rem;
  }

  .cart-item {
    display: grid;
    grid-template: repeat(2, auto) / repeat(4, 1fr);
    gap: 1.5rem;
    /* margin-bottom: 3.5rem; */
  }

  .cart-item:last-child {
    margin-bottom: 0;
  }

  .cart-item__media {
    grid-row: 1 / 3;
  }

  .cart-item__details {
    grid-column: 2 / 4;
  }

  .cart-item__quantity {
    grid-column: 2 / 5;
  }

  .cart-item__quantity-wrapper {
    flex-wrap: wrap;
  }

  .cart-item__totals {
    display: flex;
    align-items: flex-start;
    justify-content: flex-end;
  }
}

.cart-item__error-text + svg {
  margin-top: 0.4rem;
}

.cart-drawer .cart-items thead {
  display: none;
}
@media screen and (min-width: 861px) {
  .cart-items {
    border-spacing: 0;
    border-collapse: separate;
    box-shadow: none;
    width: 100%;
    display: table;
  }

  .cart-items th {
    border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.08);
  }

  .cart-items thead th:first-child {
    width: 50%;
  }


  .cart-items td {
    vertical-align: top;
/*     padding-top: 4rem; */
  }

  .cart-item {
    display: table-row;
  }

  .cart-item__details {
    width: 35rem;
  }

  .cart-item__media {
    width: 10rem;
  }
}

@media screen and (min-width: 990px) {
/*   .cart-item .cart-item__quantity,
  .cart-items .cart-items__heading--wide {
    padding-left: 6rem;
  } */

  .cart-item__details {
    width: 50rem;
  }

  .cart-items thead th:first-child {
    width: 60%;
  }
}
/*custom*/
td.cart-item__details { padding-left: 20px;}
.cart-items td{    padding-top: 40px; padding-bottom:0px;}
.cart-items td{vertical-align: inherit;}
cart-items td.cart-item__details dl .product-option {
    position: relative;
    padding-right: 15px;
}

td.cart-item__details dl{display:flex; flex-wrap:wrap;row-gap: 4px;}
.cart__footer{    background: var(--gradient-base-background-2); padding: 3rem;}
.cart-items .discounts__discount{font-size:14px;}

@media screen and (max-width: 860px) {
  td.cart-item__quantity{padding-top:0; padding-bottom:0; padding-left:0px;}
  .cart-items td{padding-bottom:0;}
  td.cart-item__totals.right.small-hide,
  th.cart-items__heading--wide.small-hide,
  th.small-hide.right.caption-with-letter-spacing{display:none;}
  td.cart-item__details{padding-left:0;}
}
@media screen and (max-width: 700px) {
   td.cart-item__details{padding-left:20px;}
    td.cart-item__quantity{padding-left:20px;}
}

@media screen and (max-width: 576px) and (min-width: 320px) {
  cart-items .cart-item {
    display: flex;
    flex-direction: column;
  }
   cart-items .product-option + .product-option {
  margin-top: 0rem;
}
  cart-items img.cart-item__image {
    width: 100%;
    max-width: 100%;
  }
  cart-items tr.cart-item {
    padding: 30px 30px 0px;
  }
  cart-items .cart-item__quantity-wrapper {
    justify-content: center;
    display: flex;
    flex-direction: column;
    row-gap: 1rem;
    align-items: center;
  }
  cart-items .cart-item__details > * {
    max-width: 100%;
    margin-bottom: 8px;
  } 
  .cart__items .cart-item__name {
    display: inline-block;
    margin: 0;
}
  cart-items td.cart-item__details {
    text-align: center;
  }
  cart-items .cart-item__totals {
    justify-content: center;
  }
  cart-items th.medium-hide.large-up-hide.right.caption-with-letter-spacing {
    display: none;
  }
  cart-items td.cart-item__media {
    display: flex;
    justify-content: center;
  }
  cart-items td.cart-item__details .product-option + .product-option {
    justify-content: center;
  }
  cart-items  .cart-item__name {
    text-align: center;
  }
  cart-items .cart-item__details dl{margin-bottom:0; justify-content:center;}
  td.cart-item__details, td.cart-item__quantity{padding-left:0;}
  .cart-items thead tr{margin-bottom:0; justify-content:center;}
  cart-items .cart__items{padding-bottom:30px;}
  .cart-items td{padding-top:0;}
}
@media screen and (max-width: 576px){
.cart-item cart-remove-button{    width: 16px;height: 16px;margin-top: 10px;}
.cart-item  .discounts__discount{justify-content:center;}
}