{{ 'section-flex-banner.css' | asset_url | stylesheet_tag }}
{%- liquid
  case section.settings.grid-column
  when '1'
  when '2'
  assign column = 'two-column'
  when '3'
  assign column = 'three-column'
  when '4'
  assign column = 'four-column'
  when '5'
  assign column = 'five-column'
  when '6'
  assign column = 'six-column'
  else
  assign column = 'three-column'
  endcase
  if section.settings.swiper_enable
  assign enable_slider = true  
  endif
-%}
{%- style -%}
    .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.50 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.50 | round: 0 }}px;
  }
   @media screen and (min-width: 576px) and (max-width: 749px){
   .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }
   }
  @media screen and (min-width: 750px) {
  .section-{{ section.id }}-padding {
  padding-top: {{ section.settings.padding_top }}px;
  padding-bottom: {{ section.settings.padding_bottom }}px;
  }
  }
  .flex-banner .flex-banner-link {
    width: 100%;
    height: 100%;    
    text-decoration: none;
    position: relative;
    z-index: 0;
 }
  {% for block in section.blocks %} 
  {% if block.settings.image != blank %}
  .flex-banner .image-bar__item.{{block.type}}-{{block.id}}{transition: all var(--duration-default) linear;background: url({{block.settings.image | image_url: width: 1920}});background-repeat:no-repeat;background-size: cover;padding: 0;overflow:hidden;width:100%; }
  {% else %}
  .flex-banner .image-bar__item.{{block.type}}-{{block.id}}{transition: all var(--duration-default) linear;background-image: url("data:image/svg+xml,%3Csvg class='placeholder-svg placeholder' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 525.5 525.5'%3E%3Cpath d='M324.5 212.7H203c-1.6 0-2.8 1.3-2.8 2.8V308c0 1.6 1.3 2.8 2.8 2.8h121.6c1.6 0 2.8-1.3 2.8-2.8v-92.5c0-1.6-1.3-2.8-2.9-2.8zm1.1 95.3c0 .6-.5 1.1-1.1 1.1H203c-.6 0-1.1-.5-1.1-1.1v-92.5c0-.6.5-1.1 1.1-1.1h121.6c.6 0 1.1.5 1.1 1.1V308z'/%3E%3Cpath d='M210.4 299.5H240v.1s.1 0 .2-.1h75.2v-76.2h-105v76.2zm1.8-7.2l20-20c1.6-1.6 3.8-2.5 6.1-2.5s4.5.9 6.1 2.5l1.5 1.5 16.8 16.8c-12.9 3.3-20.7 6.3-22.8 7.2h-27.7v-5.5zm101.5-10.1c-20.1 1.7-36.7 4.8-49.1 7.9l-16.9-16.9 26.3-26.3c1.6-1.6 3.8-2.5 6.1-2.5s4.5.9 6.1 2.5l27.5 27.5v7.8zm-68.9 15.5c9.7-3.5 33.9-10.9 68.9-13.8v13.8h-68.9zm68.9-72.7v46.8l-26.2-26.2c-1.9-1.9-4.5-3-7.3-3s-5.4 1.1-7.3 3l-26.3 26.3-.9-.9c-1.9-1.9-4.5-3-7.3-3s-5.4 1.1-7.3 3l-18.8 18.8V225h101.4z'/%3E%3Cpath d='M232.8 254c4.6 0 8.3-3.7 8.3-8.3s-3.7-8.3-8.3-8.3-8.3 3.7-8.3 8.3 3.7 8.3 8.3 8.3zm0-14.9c3.6 0 6.6 2.9 6.6 6.6s-2.9 6.6-6.6 6.6-6.6-2.9-6.6-6.6 3-6.6 6.6-6.6z'/%3E%3C/svg%3E ");background-repeat:no-repeat;background-size: cover;padding: 0;overflow:hidden;width:100%; }
  {% endif %}
  .flex-banner .image-bar__section .image-bar__section-inner.item-2  .image-bar__item.{{block.type}}-{{block.id}}{flex-basis: 50%;}
  .flex-banner .image-bar__section .image-bar__section-inner.item-3  .image-bar__item.{{block.type}}-{{block.id}}{flex-basis: 33.33%;}
  .flex-banner .image-bar__section .image-bar__section-inner.item-4  .image-bar__item.{{block.type}}-{{block.id}}{flex-basis: 25%;}
  .flex-banner .image-bar__section .image-bar__section-inner.item-5  .image-bar__item.{{block.type}}-{{block.id}}{flex-basis: 20%;}
   .flex-banner .image-bar__section-inner.item-5  .image-bar__item.{{block.type}}-{{block.id}}:hover{flex-basis:22%;} 
  .flex-banner .image-bar__section-inner.item-3  .image-bar__item.{{block.type}}-{{block.id}}:hover{flex-basis:50%;} 
  .flex-banner .image-bar__section-inner.item-4  .image-bar__item.{{block.type}}-{{block.id}}:hover{flex-basis:50%;}
  .flex-banner .image-bar__section-inner.item-2  .image-bar__item.{{block.type}}-{{block.id}}:hover{flex-basis:60%;}
  /*.flex-banner .image-bar__item.{{block.type}}-{{block.id}} .image-gallery-overlay .image-overlay-count{font-size:var(--DTFontSize_H1);font-weight:700;font-family:var(--DTFontTypo_Heading);line-height:normal;transition: all var(--duration-default) linear;color: {{ section.settings.image_link_text_color }};margin:0;}*/
  .flex-banner .image-bar__item.{{block.type}}-{{block.id}} .image-gallery-overlay .image-gallery-title-count {display: grid;grid-template-columns: auto auto;align-self: center;grid-gap: 10px;}
  /* .flex-banner .image-bar__section-inner .image-bar__item.{{block.type}}-{{block.id}}:hover .image-gallery-overlay .image-overlay-count{font-size:calc( var(--DTFontSize_H1) + 40px );}*/
  .flex-banner .image-bar__section-inner.item-5  .image-bar__item.{{block.type}}-{{block.id}}:hover .block-description{color: var(--gradient-background);}
 .flex-banner .image-bar__section-inner.item-5  .image-bar__item.{{block.type}}-{{block.id}} .block-description{transition:all 0.3s linear;}
  
  @media only screen and (max-width: 1199px) {
/*   .flex-banner .image-bar__item.{{block.type}}-{{block.id}}{padding:20px;}
  .flex-banner .image-bar__section .image-bar__section-inner{flex-wrap: wrap;} 
  .flex-banner .image-bar__section .image-bar__section-inner.item-3  .image-bar__item.{{block.type}}-{{block.id}}{flex-basis: 33.33%;}
  .flex-banner .image-bar__section .image-bar__section-inner.item-4  .image-bar__item.{{block.type}}-{{block.id}}{flex-basis: 33.3%;}
  .flex-banner .image-bar__section .image-bar__section-inner.item-5  .image-bar__item.{{block.type}}-{{block.id}}{flex-basis: 33.3%;}
  .flex-banner .image-bar__section .image-bar__section-inner.item-2  .image-bar__item.{{block.type}}-{{block.id}}{flex-basis: 50%;} */
  /*     .flex-banner .image-bar__section .image-bar__section-inner.item-5  .image-bar__item.{{block.type}}-{{block.id}}{flex-basis: 50%;} */
  /*     .flex-banner .image-bar__section-inner.item-5  .image-bar__item.{{block.type}}-{{block.id}}:hover,
  .flex-banner .image-bar__section-inner.item-3  .image-bar__item.{{block.type}}-{{block.id}}:hover,
  .flex-banner .image-bar__section-inner.item-4  .image-bar__item.{{block.type}}-{{block.id}}:hover{flex-basis:40%;} */ 
  }  
  @media (max-width:989px) {
  .flex-banner .image-bar__section .image-bar__section-inner.item-3  .image-bar__item.{{block.type}}-{{block.id}},
  .flex-banner .image-bar__section .image-bar__section-inner.item-4  .image-bar__item.{{block.type}}-{{block.id}},
  .flex-banner .image-bar__section .image-bar__section-inner.item-5  .image-bar__item.{{block.type}}-{{block.id}}{flex-basis: 100%;}
  .flex-banner .image-bar__section .image-bar__section-inner.item-3  .image-bar__item.{{block.type}}-{{block.id}}:last-child,
  .flex-banner .image-bar__section .image-bar__section-inner.item-5  .image-bar__item.{{block.type}}-{{block.id}}:last-child{width: 100%;flex-basis: 100%;}
    .flex-banner .image-bar__section-inner.item-5  .image-bar__item.{{block.type}}-{{block.id}}:hover,
  .flex-banner .image-bar__section-inner.item-3  .image-bar__item.{{block.type}}-{{block.id}}:hover,
  .flex-banner .image-bar__section-inner.item-4  .image-bar__item.{{block.type}}-{{block.id}}:hover{flex-basis:unset;}
  }
  @media (max-width:767px) {
  .flex-banner .image-bar__section .image-bar__section-inner.item-3  .image-bar__item.{{block.type}}-{{block.id}},
  .flex-banner .image-bar__section .image-bar__section-inner.item-4  .image-bar__item.{{block.type}}-{{block.id}},
  .flex-banner .image-bar__section .image-bar__section-inner.item-5  .image-bar__item.{{block.type}}-{{block.id}},
  .flex-banner .swiper .image-bar__section .image-bar__section-inner.item-2  .image-bar__item.{{block.type}}-{{block.id}}{flex-basis: 100%;}
   
    
  } 
  @media (max-width:989px) {
 .flex-banner .image-bar__section .image-bar__section-inner.item-5  .image-bar__item.{{block.type}}-{{block.id}}{flex-basis: 20%;}
 .flex-banner .image-bar__section-inner.item-5  .image-bar__item.{{block.type}}-{{block.id}}:hover{flex-basis:unset;} 
  .flex-banner .image-bar__item.{{block.type}}-{{block.id}}{padding:0;}  
  }
  {% endfor %}  
  @media (min-width:1541px) {
  .flex-banner .image-bar__item .image-gallery-overlay .image-overlay-title{max-width:400px;}
  .flex-banner .image-bar__item {height: {{section.settings.item_height_desktop}}px;}   
  }
  @media (max-width:1540px) {
  .flex-banner .image-bar__item { height: {{section.settings.item_height_laptop}}px; }
  }
  @media only screen and (max-width: 1199px) {
  .flex-banner .image-bar__item { height: {{section.settings.item_height_tab}}px; }
  }
  @media (max-width:767px) {
  .flex-banner .image-bar__section .dt-sc-image-list-btn { position: static; margin-top: 30px; }
  .flex-banner .image-bar__item { height: {{section.settings.item_height_mobile}}px; }
  }  
  @media only screen and (min-width: 577px) and (max-width: 767px) {
  .flex-banner .image-bar__section .image-bar__section-inner { grid-template-columns: repeat(2,1fr); }
  }
{%- endstyle -%}
<div class="flex-banner color-{{ section.settings.color_scheme }} gradient{% if section.settings.title == blank %} no-heading{% endif %} {{ section.settings.custom_class_name }} ">
   <div class="{% if section.settings.page_full_width %} page-full-width {% else %} page-width {% endif %} {% if section.settings.page_full_width_spacing %} page-full-width_spacing {% endif %}section-{{ section.id }}-padding isolate">
      <div class="row">
         <div class="flex-banner-wrapper">
            {%- unless section.settings.title == blank -%}
            <div class="title-wrapper-with-link title-wrapper--self-padded-mobile title-wrapper--no-top-margin content-align--{{ section.settings.column_alignment }}">
               {%- if section.settings.sub_heading != blank -%}  
               <h6 class="sub-heading">{{ section.settings.sub_heading | escape }}</h6>
               {%- endif -%} 
               {%- if section.settings.title != blank -%}  
               <h2 class="title {{ section.settings.heading_size }}">
                  {{ section.settings.title | escape }}
               </h2>
               {%- endif -%} 
               {%- if section.settings.description != blank -%}  
               <p class="description">{{ section.settings.description }}</p>
               {%- endif -%}   
               {%- if section.settings.button_label != blank -%}
               <a {% if section.settings.button_link == blank %} role="link" aria-disabled="true"{% else %} href="{{ section.settings.button_link }}"{% endif %} class="button{% if section.settings.button_style_secondary %} button--secondary{% else %} button--primary{% endif %}">{{ section.settings.button_label | escape }}</a>
               {%- endif -%}
            </div>
            {%- endunless -%}
              <flex-slider class="large-up-hide">
      <div data-slider-options='{"loop": "1","desktop": "{{ section.settings.desktop_column }}", "tablet": "{{ section.settings.tablet_column }}","mobile": "{{ section.settings.mobile_column }}","auto_play": "{{ section.settings.auto_play }}"}'>
      <div class="swiper" data-flex-slider>     
               <div class="{% if enable_slider %}swiper-wrapper{% endif %} image-bar__section-inner  item-{{section.blocks.size}}">
                  {%- for block in section.blocks -%}
                  <li class="{%- if enable_slider %}swiper-slide{% endif %} image-bar__item  {{block.type}}-{{block.id}} {{section.settings.content_position}} {{ section.settings.block_text_align}}" {{ block.shopify_attributes }} >
                  <a class="link flex-banner-link" {% if block.settings.block_button_link == blank %}role="link" aria-disabled="true"{% else %}href="{{ block.settings.block_button_link | escape }}"{% endif %}>
                
                    <div class="image-gallery-overlay">
                     {% if block.settings.block_sub_title != blank %}
                     <h6 class="block-sub-title">{{block.settings.block_sub_title}}</h6>
                     {% endif %}
                     {% if block.settings.block_title != blank %}
                     <h3 class="block-main-title">{{block.settings.block_title}}</h3>
                     {% endif %}
                     
                     {% if block.settings.block_button_text != blank %}
                     <a href="{{block.settings.block_button_link}}" class="button{% if block.settings.button_style_secondary %} button--secondary{% else %} button--primary{% endif %}"">{{block.settings.block_button_text}}</a>
                     {% endif %}
                  </div>
                   {% if block.settings.block_description != blank %}
                     <p class="block-description"><a href="{{block.settings.block_button_link}}">{{block.settings.block_description}}</a></p>
                     {% endif %}
                  </li> 
                     </a>
                  {%- endfor -%}
               </div>
              {% if section.settings.swiper_navigation != blank %}
              <div class="swiper-button-next"><span></span></div>
              <div class="swiper-button-prev"><span></span></div>
              {% endif %}    
              {% if section.settings.swiper_pagination != blank %}
              <div class="swiper-pagination"></div>
              {% endif %}
      </div>
      </div>
      </flex-slider>
            <div class="image-bar__section wow small-hide medium-hide">
               <ul class="image-bar__section-inner  item-{{section.blocks.size}}">
                  {%- for block in section.blocks -%}
                  
                  <li class="image-bar__item  wow  {{block.type}}-{{block.id}} {{section.settings.content_position}} {{ section.settings.block_text_align}}" {{ block.shopify_attributes }} >
                     <a class="link flex-banner-link" {% if block.settings.block_button_link == blank %}role="link" aria-disabled="true"{% else %}href="{{ block.settings.block_button_link | escape }}"{% endif %}>
                    <div class="image-gallery-overlay ">
                     {% if block.settings.block_sub_title != blank %}
                     <h6 class="block-sub-title">{{block.settings.block_sub_title}}</h6>
                     {% endif %} 
                     {% if block.settings.block_title != blank %}
                     <h4 class="block-main-title">{{block.settings.block_title}}</h4>
                     {% endif %}
                     {% if block.settings.block_button_text != blank %}
                     <button href="{{block.settings.block_button_link}}" class="button {% if block.settings.button_style_secondary %} button--secondary {% else %} button--primary{% endif %}">{{block.settings.block_button_text}}</button>
                     {% endif %}
                  </div>
                     {% if block.settings.block_description != blank %}
                     <p class="block-description">{{block.settings.block_description}}</p>
                     {% endif %}   
                  </a>   
                  </li>
                  {%- endfor -%}
               </ul>
              
            </div>
         </div>
      </div>
   </div>
</div>
{% schema %}
{
"name": "t:sections.flex-banner.name",
"class": "section",
"max_blocks": 5,
"settings": [
{
"type":"checkbox",
"id":"page_full_width",
"default": false,
"label": "t:sections.all.page_full_width.label"
},
{
"type":"checkbox",
"id":"page_full_width_spacing",
"default": false,
"label": "t:sections.all.page_full_width_spacing.label"
},
{
"type": "text",
"id": "title",
"default": "Flex banner",
"label": "t:sections.all.title.label"
},
{
"type": "select",
"id": "heading_size",
"options": [
{
"value": "h2",
"label": "t:sections.all.heading_size.options__1.label"
},
{
"value": "h1",
"label": "t:sections.all.heading_size.options__2.label"
},
{
"value": "h0",
"label": "t:sections.all.heading_size.options__3.label"
}
],
"default": "h1",
"label": "t:sections.all.heading_size.label"
},
{
"type": "text",
"id": "sub_heading",
"default": "Sub Heading",   
"label": "t:sections.all.sub_heading.label"
},
{
"type": "text",
"id": "description",
"default": "Use This Text To Share The Information Which You Like!.",   
"label": "t:sections.all.description.label"
},
{
"type": "text",
"id": "button_label",
"default": "Button label",
"label": "t:sections.all.button_label.label"
},
{
"type": "url",
"id": "button_link",
"label": "t:sections.all.button_link.label"
},
{
"type": "checkbox",
"id": "button_style_secondary",
"default": false,
"label": "t:sections.all.button_style_secondary.label"
},
{
"type": "select",
"id": "column_alignment",
"options": [
{
"value": "left",
"label": "t:sections.flex-banner.settings.column_alignment.options__1.label"
},
{
"value": "center",
"label": "t:sections.flex-banner.settings.column_alignment.options__2.label"
}
],
"default": "center",
"label": "t:sections.flex-banner.settings.column_alignment.label"
},
{
"type": "select",
"id": "color_scheme",
"options": [
{
"value": "accent-1",
"label": "t:sections.all.colors.accent_1.label"
},
{
"value": "accent-2",
"label": "t:sections.all.colors.accent_2.label"
},
{
"value": "background-1",
"label": "t:sections.all.colors.background_1.label"
},
{
"value": "background-2",
"label": "t:sections.all.colors.background_2.label"
},
{
"value": "inverse",
"label": "t:sections.all.colors.inverse.label"
}
],
"default": "background-1",
"label": "t:sections.all.colors.label"
},
{
"type": "header",
"content": "t:sections.flex-banner.settings.item_height_settings.content"
}, 
{	
"type": "range",	
"id": "item_height_desktop",	
"label": "t:sections.flex-banner.settings.item_height_desktop.label",
"min": 50,	
"max": 1000,	
"step": 10,	
"default": 450,	
"unit": "px"	
},
{	
"type": "range",	
"id": "item_height_laptop",	
"label": "t:sections.flex-banner.settings.item_height_laptop.label",
"min": 50,	
"max": 1000,	
"step": 10,	
"default": 400,	
"unit": "px"	
},
{	
"type": "range",	
"id": "item_height_tab",	
"label": "t:sections.flex-banner.settings.item_height_tab.label",
"min": 50,	
"max": 1000,	
"step": 10,	
"default": 350,	
"unit": "px"	
},
{	
"type": "range",	
"id": "item_height_mobile",	
"label": "t:sections.flex-banner.settings.item_height_mobile.label",
"min": 50,	
"max": 1000,	
"step": 10,	
"default": 300,	
"unit": "px"	
},
{
"type": "header",
"content": "t:sections.flex-banner.settings.block_settings.content"
},
{
"type": "select",
"id": "content_position",
"label": "t:sections.flex-banner.settings.content_position.label",
"options": [
{
"value": "content-center",
"label": "t:sections.flex-banner.settings.content_position.content-center.label"
},
{
"value": "content-start",
"label": "t:sections.flex-banner.settings.content_position.content-start.label"
},
{
"value": "content-end",
"label": "t:sections.flex-banner.settings.content_position.content-end.label"
}
]
}, 
{
"type": "select",
"id": "block_text_align",
"label": "t:sections.flex-banner.settings.block_text_align.label",
"options": [
{
"value": "text-center",
"label": "t:sections.flex-banner.settings.block_text_align.text-center.label"
},
{
"value": "text-start",
"label": "t:sections.flex-banner.settings.block_text_align.text-start.label"
},
{
"value": "text-end",
"label": "t:sections.flex-banner.settings.block_text_align.text-end.label"
}
]
},
{
"type": "header",
"content": "t:sections.all.padding.section_padding_heading"
},
{
"type": "range",
"id": "padding_top",
"min": 0,
"max": 100,
"step": 4,
"unit": "px",
"label": "t:sections.all.padding.padding_top",
"default": 36
},
{
"type": "range",
"id": "padding_bottom",
"min": 0,
"max": 100,
"step": 4,
"unit": "px",
"label": "t:sections.all.padding.padding_bottom",
"default": 36
},
{
"type": "text",
"id": "custom_class_name",
"label": "t:sections.flex-banner.settings.custom_class_name.label"
},
  {
      "type": "header",
      "content": "t:sections.all.swiper.swiper_slider_title"
    },
    {
      "type": "checkbox",
      "id": "swiper_enable",
      "default": false,
      "label": "t:sections.all.swiper.swiper_slider_enable"
    },
    {
      "type": "range",
      "id": "desktop_column",
      "min": 1,
      "max": 10,
      "step": 1,
      "label": "t:sections.all.swiper.desktop_column",
      "default": 4
    },
    {
      "type": "range",
      "id": "tablet_column",
      "min": 1,
      "max": 5,
      "step": 1,
      "label": "t:sections.all.swiper.tablet_column",
      "default": 3
    },
    {
      "type": "range",
      "id": "mobile_column",
      "min": 1,
      "max": 3,
      "step": 1,
      "label": "t:sections.all.swiper.mobile_column",
      "default": 1
    },
    {
      "type": "checkbox",
      "id": "centered_slide",
      "default": false,
      "label": "t:sections.all.swiper.centered_slide"
    },
    {
      "type": "checkbox",
      "id": "swiper_pagination",
      "default": false,
      "label": "t:sections.all.swiper.swiper_pagination"
    },
    {
      "type": "checkbox",
      "id": "swiper_navigation",
      "default": false,
      "label": "t:sections.all.swiper.swiper_navigation"
    },
    {
      "type": "range",
      "id": "auto_play",
      "min": 0,
      "max": 5,
      "step": 1,
      "label": "t:sections.all.swiper.auto_play",
      "default": 0
    }
],
"blocks": [
{
"type": "image_image",
"name": "t:sections.flex-banner.blocks.image_image.name",
"settings": [
{
"type": "image_picker",
"id": "image",
"label": "t:sections.flex-banner.blocks.image_image.settings.image.label"
},
{
"type": "text",
"id": "block_title",
"label": "t:sections.flex-banner.blocks.image_image.settings.block_title.label",
"default": "Title"
},
{
"type": "text",
"id": "block_sub_title",
"label": "t:sections.flex-banner.blocks.image_image.settings.block_sub_title.label",
"default": "Sub title"
},  
{
"type": "text",
"id": "block_description",
"label": "t:sections.flex-banner.blocks.image_image.settings.block_description.label",
"default": "Short description"
},  
{
"type": "text",
"id": "block_button_text",
"label": "t:sections.flex-banner.blocks.image_image.settings.block_button_text.label"
},
{
"type": "url",
"id": "block_button_link",
"label": "t:sections.flex-banner.blocks.image_image.settings.block_button_link.label"
},
{
"type": "checkbox",
"id": "button_style_secondary",
"default": false,
"label": "t:sections.all.button_style_secondary.label"
}
]
}
],
"presets": [
{
"name": "Flex Banner",
"category": "Image",
"blocks": [
{
"type": "image_image"
},
{
"type": "image_image"
},
{
"type": "image_image"
}
]
}
]
}
{% endschema %}