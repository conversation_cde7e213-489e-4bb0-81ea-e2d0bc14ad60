.loading-overlay {
  position: absolute;
  z-index: 1;
  width: 1.8rem;
}

@media screen and (max-width: 749px) {
  .loading-overlay {
    top: 0;
    right: 0;
  }
}

@media screen and (min-width: 750px) {
  .loading-overlay {
    left: 0;
  }
}

.loading-overlay__spinner {
  background: var(--color-icon);
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
/*   display: inline-block; */
  position:absolute;
  top:0;
  bottom:0;
  right:0;
  left:0;
  transition: all 0.3s linear;
}
.loading-overlay__spinner:hover {
    background:var(--gradient-background);
}
.card__inner .product-icons button:hover .loading-overlay__spinner:hover{
  color: var(--color-icon);background: var(--gradient-base-background-2);
}
.card__inner .product-icons button:hover .loading-overlay__spinner{
  color:var(--gradient-base-background-2);
}
.loading-overlay__spinner svg {
    width: 15px;
    height: 15px;
    color: currentColor;
}
/* .spinner {
  animation: rotator 1.4s linear infinite;
}

@keyframes rotator {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(270deg);
  }
} */

.path {
  stroke-dasharray: 280;
  stroke-dashoffset: 0;
  transform-origin: center;
  stroke: currentcolor;
  animation: dash 1.4s ease-in-out infinite;
  border:none;
  fill: none;
  stroke-width:5px;
}

@media screen and (forced-colors: active) {
  .path{
    stroke: CanvasText;
  }
}

@keyframes dash {
  0% {
    stroke-dashoffset: 280;
  }
  50% {
    stroke-dashoffset: 75;
    transform: rotate(135deg);
  }
  100% {
    stroke-dashoffset: 280;
    transform: rotate(450deg);
  }
}

.loading-overlay:not(.hidden) + .cart-item__price-wrapper,
.loading-overlay:not(.hidden) ~ cart-remove-button {
  opacity: 50%;
}

.loading-overlay:not(.hidden) ~ cart-remove-button {
  pointer-events: none;
  cursor: default;
}
.cart-items .loading-overlay__spinner {
    background: transparent;
}