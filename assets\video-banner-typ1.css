.video-banner-typ1 .watch-more {display:inline-block; color:var( --color-foreground); font-size:14px; text-decoration:none; display:flex; justify-content:center; align-items:center; width:80px; height:80px; background:var( --gradient-background); border-radius:50%; transition:all 0.3s linear; -webkit-animation:ripple 1s linear infinite; animation:ripple 1s linear infinite;}

@keyframes ripple {
0% {box-shadow:0 0 0 0 rgba(255, 255, 255 , 0.05), 0 0 0 10px rgba(255, 255, 255 , 0.05), 0 0 0 30px rgba(255, 255, 255 , 0.05), 0 0 0 50px rgba(255, 255, 255 , 0.05);}
100% {box-shadow:0 0 0 10px rgba(255, 255, 255 , 0.05), 0 0 0 30px rgba(255, 255, 255 , 0.05), 0 0 0 50px rgba(255, 255, 255 , 0.05), 0 0 0 80px rgba(255, 255, 255 , 0);}
}

.video-banner-typ1 .video-play-icon {display:flex; justify-content:center; align-items:center; margin-top:3rem;}
.video-banner-typ1 .watch-more:hover, .watch-more:focus, .watch-more:active {color:var(--color-icon); background:var(--gradient-base-background-1);}
.video-banner-typ1 svg.icon.icon-play {height:20px; width:20px;}
body.gradient.overlay-active {overflow-y:hidden;}
.video-banner-typ1 .modal {display:none; position:fixed; z-index:1; left:0; top:0; width:100%; height:100%; overflow:auto; background-color:rgba(0, 0, 0, 0.4);}
.video-banner-typ1 .video-popup {display:none; z-index:3; position:absolute; top:50%; left:50%; transform:translate(-50%,-50%); border:1px solid #ccc; padding:10px 20px; background-color:#fff; border-radius:0;}
.video-banner-typ1 .video-popup.visible {display:block;}
.video-banner-typ1 .video-popup .close {position:absolute; right:15px; top:15px; font-weight:900; font-size:28px; color:black; padding:5px 10px; border-bottom:none; cursor:pointer;}
.video-banner-typ1 .video-popup .close .fa {color:var(--gradient-base-accent-3); font-size:1.5rem; font-weight:400;}
.video-banner-typ1 .video-popup .close:hover {background:var(--gradient-base-accent-4);}
.video-banner-typ1 .video-wrapper {width:700px; margin:0; top:50%; position:absolute; left:50%; transform:translate(-50%, -50%); justify-content:center;}
body.overlay-active .video-banner-typ1 .video-popup {display:flex!important; align-items:center; justify-content:center; opacity:1; visibility:visible; background-color:#000000e6; cursor:pointer; height:100%; top:0; left:0; position:fixed; width:100%; z-index:98; -webkit-backface-visibility:hidden; backface-visibility:hidden; -webkit-transition:opacity .2s, visibility .2s; transition:opacity .2s, visibility .2s}
@media only screen and (max-width: 560px) {}
.video-banner-typ1 .video-wrapper .video-container {position:relative; padding-bottom:55.25%; height:0; overflow:hidden;}
.video-banner-typ1 .video-wrapper .video-container iframe {position:absolute; top:0; left:0; width:100%; height:100%;}
.video-banner-typ1 .video-section {backdrop-filter:brightness(1); height:100%; display:flex; flex-direction:column; justify-content:space-around;}
.video-banner-typ1 .title-wrapper-with-link.content-align--center > *{margin-top:1rem;}
.video-banner-typ1 .video-banner .video-section__content p {padding:0;}
@media screen and (min-width: 991px) {}
.video-banner-typ1 .video-section__content {text-align:center;}
.video-banner-typ1 .video-banner {position:relative;}
.video-banner-typ1 .video-banner:after {content:''; position:absolute; width:100%; height:100%; left:0; top:0; z-index:0; background:linear-gradient(to right,#d66d73,#d47b78,#e3938a);opacity:0.7;}
.video-banner-typ1 .video-banner .title-wrapper-with-link .title:after {background:currentcolor;}

@media screen and (min-width:750px) and (max-width:1200px) {
.video-banner-typ1 .video-section__content {width:80%; margin:0 auto;}
}

@media screen and (min-width:1200px) {
.video-banner-typ1 .video-section__content {width:60%; margin:0 auto;}
}