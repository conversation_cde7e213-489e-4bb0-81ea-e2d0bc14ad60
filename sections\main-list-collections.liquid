{{ 'component-card.css' | asset_url | stylesheet_tag }}
{{ 'section-collection-list.css' | asset_url | stylesheet_tag }}
  <script src="{{ 'ajaxinate.min.js' | asset_url }}"></script>  
{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
  .collection .load_more_btn {
    display: flex;
    justify-content: center;
    margin-top: 5rem ;
}
{%- endstyle -%}

<div class="{% if section.settings.page_full_width %} page-full-width {% else %} page-width {% endif %} {% if section.settings.page_full_width_spacing %} page-full-width_spacing {% endif %}section-{{ section.id }}-padding isolate">
  <div class="row collection">
    <h1 class="title title--primary">{{ section.settings.title | escape }}</h1>
    {%- liquid
      case section.settings.sort
        when 'products_high', 'products_low'
          assign collections = collections | sort: 'all_products_count'
        when 'date', 'date_reversed'
          assign collections = collections | sort: 'published_at'
      endcase

      if section.settings.sort == 'products_high' or section.settings.sort == 'date_reversed' or section.settings.sort == 'alphabetical_reversed'
        assign collections = collections | reverse
      endif

      assign moduloResult = 16 | modulo: section.settings.columns_desktop
      assign paginate_by = 8
      if moduloResult == 0
        assign paginate_by = 8
      endif
    -%}
    {%- paginate collections by paginate_by -%}
      <ul
        class="AjaxinateContainer collection-list {{ section.settings.collection_layout }} grid grid--{{ section.settings.columns_desktop }}-col-desktop grid--{{ section.settings.columns_mobile }}-col-tablet-down"
        role="list"
      >
        {%- for collection in collections -%}
          <li class="collection-list__item grid__item">
            {% render 'card-collection',
              card_collection: collection,
              media_aspect_ratio: section.settings.image_ratio,
              columns: section.settings.columns_desktop
            %}
          </li>
        {%- endfor -%}
      </ul>
    <div id="AjaxinatePagination" class="pagination-method-infinit_scroll">
        {% if paginate.next %}
         <div class="load_more_btn"> 
        <a href="{{ paginate.next.url }}" class="text-center button">Loading More</a>
         </div>    
      {% endif %}
      </div>
    {%- endpaginate -%}
  </div>
</div>
{% schema %}
{
  "name": "t:sections.main-list-collections.name",
  "class": "section",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "t:sections.main-list-collections.settings.title.label",
      "default": "Collections"
    },
     {
      "type":"checkbox",
      "id":"page_full_width",
       "default": false,
      "label": "t:sections.all.page_full_width.label"
    },
    {
      "type":"checkbox",
      "id":"page_full_width_spacing",
       "default": false,
      "label": "t:sections.all.page_full_width_spacing.label"
    },
    {
      "type": "select",
      "id": "sort",
      "options": [
        {
          "value": "alphabetical",
          "label": "t:sections.main-list-collections.settings.sort.options__1.label"
        },
        {
          "value": "alphabetical_reversed",
          "label": "t:sections.main-list-collections.settings.sort.options__2.label"
        },
        {
          "value": "date_reversed",
          "label": "t:sections.main-list-collections.settings.sort.options__3.label"
        },
        {
          "value": "date",
          "label": "t:sections.main-list-collections.settings.sort.options__4.label"
        },
        {
          "value": "products_high",
          "label": "t:sections.main-list-collections.settings.sort.options__5.label"
        },
        {
          "value": "products_low",
          "label": "t:sections.main-list-collections.settings.sort.options__6.label"
        }
      ],
      "default": "alphabetical",
      "label": "t:sections.main-list-collections.settings.sort.label"
    },
     {
      "type": "select",
      "id": "collection_layout",
      "options": [
        {
          "value": "overlay",
          "label": "t:sections.main-list-collections.settings.collection_layout.options__1.label"
        },
        {
          "value": "grid",
          "label": "t:sections.main-list-collections.settings.collection_layout.options__2.label"
        }
      ],
      "default": "grid",
      "label": "t:sections.main-list-collections.settings.collection_layout.label"
    },
    {
      "type": "text",
      "id": "collection_button_label",
      "default": "View more",
      "label": "t:sections.main-list-collections.settings.collection_button_label.label"
    },
     {
      "type": "checkbox",
      "id": "collection_count",
      "default": true,
      "label": "t:sections.collection-list.settings.collection_count.label"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.main-list-collections.settings.image_ratio.options__1.label"
        },
        {
          "value": "portrait",
          "label": "t:sections.main-list-collections.settings.image_ratio.options__2.label"
        },
        {
          "value": "square",
          "label": "t:sections.main-list-collections.settings.image_ratio.options__3.label"
        }
      ],
      "default": "adapt",
      "label": "t:sections.main-list-collections.settings.image_ratio.label",
      "info": "t:sections.main-list-collections.settings.image_ratio.info"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 5,
      "step": 1,
      "default": 3,
      "label": "t:sections.main-list-collections.settings.columns_desktop.label"
    },

    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 120,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 120,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    }
  ]
}
{% endschema %}
