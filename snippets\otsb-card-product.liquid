{% comment %}
    Renders a product card

    Accepts:
    - card_product: {Object} Product Liquid object (optional)
    - show_vendor: {<PERSON><PERSON><PERSON>} Show the product vendor. Default: false
    - show_rating: {<PERSON><PERSON><PERSON>} Show the product rating. Default: false
    - loading: {<PERSON><PERSON>an} Image should be lazy loaded. Default: lazy (optional)
    - section_id: {String} The ID of the section that contains this card.
    - disable_badges: {<PERSON>ole<PERSON>} Don't show labels and badges
    - list_name: {String} The title of collection,
    - collection: {Object} Collection Liquid object (optional),
    - class_slide: {String} The class of slider,
    - index: {Number} The index of the product card in the collection,
    - product_complementary: Classes are used for product complementary
    - disable_quickview: {<PERSON><PERSON><PERSON>} Show quick view button. Default: true (optional)
    - block_id: {String} The ID of the block that contains this card.
    Usage:
    {% render 'otsb-card-product', show_vendor: section.settings.show_vendor %}
{% endcomment %}
{%- liquid
  if column_mobile == blank
    assign column_mobile = 1
  endif 
  if media_aspect_ratio == blank
  assign media_aspect_ratio = section.settings.product_image_ratio
  endif 
  assign ratio = 1 
  case media_aspect_ratio
    when "portrait"
      assign ratio = 1.5
    when "landscape"
      assign ratio = 0.75
    when "wide"
      assign ratio = 0.5625
    when "3/4"
      assign ratio = 1.33
  endcase
  assign count_media = card_product.media | size
  if section.settings.number_of_additional_images < count_media
    assign count_media = section.settings.number_of_additional_images
  endif
  assign product_featured_media = card_product.featured_media
-%}
{%- capture dataForCustomerEvent -%}
  {
    "product": {
      "title": "{{ card_product.title | escape }}",
      "listName": "{{ collection.title | default: list_name | escape }}",
      "sku": "{{ card_product.id }}",
      "currencyCode": "{{ cart.currency.iso_code }}",
      "price": {{ card_product.price }},
      "vendor": "{{ card_product.vendor | escape }}"
    }
  }
{%- endcapture %}
{%- assign main_css = '' -%}
<div
{% if section.settings.swatches_type == 'both_text_and_color' or section.settings.swatches_type == 'color' %}
  x-data="xProductCard('{{ section_id }}-{{ product_card_id }}', '{{ card_product.url | within: collection | split: "?" | first }}', '{{ card_product.id }}')"  
{% else %}
  x-data="{ showOptions: false, options: [] }"
{% endif %}
class="card-product overflow-hidden card md:list-layout:flex relative
  {%- if image_ratio_setting == 'fit_grid_layout' %} flex flex-col md:flex md:flex-col
    {%- else -%}
    {%- if mediaGallery %} flex flex-col md:block{% endif -%}
  {%- endif -%}
  {%- if is_rtl %} rtl{% endif -%}
  {%- if request.params.size > 0 %} cos pram{% endif -%}
  {%- if class_slide %} {{ class_slide }}{% endif -%}
  {%- unless swiper_on_mobile %} h-full{% endunless -%}
  {%- if extend_height %} flex flex-col{% endif -%}
  {%- unless product_complementary %}{% if edges_type == 'rounded_corners' %} rounded-[10px]{% endif %}{% endunless -%}"  
x-slide-index="{{ index | minus: 1 }}">
  <div 
    class="z-0 md:list-layout:w-2/5 md:list-layout:lg:w-1/4{% if is_rtl %} md:list-layout:pl-7{% else %} md:list-layout:pr-7{% endif %} relative{% if product_complementary %} flex{% endif %}{% if extend_height %} flex flex-col{% endif %} disable-effect"
    x-data="{effect: false}"
  >
    {% if product_complementary %}
      <div class="w-1/6">
    {% endif %}
    <a 
    {% if loading == 'eager' %}
      x-init="effect = true"
    {% else %}
      x-intersect:enter.margin.-20px.0px.-20px.0px="effect = true"
    {% endif %}
    href="{{ card_product.url | within: collection }}"
    class="ltr group link-product-variant overflow-hidden block relative z-20 cursor-pointer{% if collage %} grow{% else %} no-collage{% endif %} overflow-hidden {% if section.settings.edges_type == 'rounded_corners' %} rounded-[10px]{% endif %} before:h-0 before:block z-0{% unless media_aspect_ratio == "natural"  %} pb-[{{ ratio | times: 100.0 }}%]{% else %}{% if product_featured_media.media_type == "model" %} pb-[100%]{% endif %}{% endunless %}"
    {% if media_aspect_ratio == "natural" and product_featured_media and product_featured_media.media_type != "model" %} style="height:0; padding-bottom: {{ 1 | divided_by: product_featured_media.aspect_ratio | times: 100 }}%;"{% endif %}>
      <div :class="effect && 'active'" class="w-full h-full z-0 overflow-hidden absolute top-0 left-0{% if animation_loading %} animate_transition_card__image{% endif %} {% if section.settings.product_image_type == "slide_img" and count_media > 1 and class_slide != 'recommendations' and section.settings.show_arrow == false %} disable-label-hover{% endif %}">
        <div
        id="x-slideshow-card-product-{{ section_id }}-{{ product_card_id }}-{{ index | minus: 1 }}"
        class="block h-full visible {% if section.settings.product_image_type == "slide_img" and count_media > 1 and class_slide != 'recommendations' %} visible{% unless media_aspect_ratio == "natural"  %} card-product-img{% endunless %} w-full top-0 left-0 absolute x-splide disable-effect-hover splide cursor-grab group{% endif %}" 
        @click='$store.xCustomerEvent.fire("product_selected", $el)'
        x-customer-event-data="{{ dataForCustomerEvent | escape }}"
        {% if section.settings.product_image_type == "slide_img" and count_media > 1 and class_slide != 'recommendations' %}
          x-intersect.once.margin.200px='otsbIdleTask(()=>$store.otsb_xSplide.load($el, {
            "drag": false,
            "pauseOnFocus": false,
            "pauseOnHover": false,
            "perMove": 1,
            "focus": "center",
            "type": "fade",
            {% unless section.settings.show_arrow -%}
              "cardHover": "image-product-{{ section_id }}-{{ product_card_id }}-{{ index | minus: 1 }}", 
              "maxSlide": "{{ count_media  }}",
              "arrows": false,
              "classes": {
                "pagination": "splide__pagination pagination-card otsb-hidden grid-cols-{{ count_media }} pointer-events-none absolute gap-1 top-5 pl-5 pr-5 w-full z-0",
                "page": "button none_border btn-pagination flex items-center w-full h-1"
              }
            {% else %}
              {% if section.settings.change_slides_speed_card > 0 %}
                "rewind": true,
                "autoplay": true,
                "speed": {{ section.settings.change_slides_speed_card | times: 1000 }},
                "interval": {{ section.settings.change_slides_speed_card | times: 1000 }},
                "updateOnMove": "true",
                "playOnHover": true,
              {% endif %}
              "pagination": false
            {%- endunless %}
          }))'
        {%- endif -%}
      >


        {% if section.settings.product_image_type == "slide_img" and count_media > 1 and class_slide != 'recommendations' %}
          <div 
            id="image-product-{{ section_id }}-{{ product_card_id }}-{{ index | minus: 1 }}"
            class="splide__track overflow-hidden max-h-full h-full w-full"
          >
          <div class="w-full h-full splide__list">
        {%- endif -%}
        {%- unless card_product.has_only_default_variant -%}
          {%- for option in card_product.options_with_values -%}
            {%- assign variant_key = 'option' | append: option.position -%}
            {%- for value in option.values -%}
              {%- liquid
                assign variant_value = card_product.variants | where: variant_key, value | first
                if variant_value.featured_media.preview_image
                  assign first_preload_image = variant_value.featured_media.preview_image
                endif
                break
              -%}
              {%- break -%}
            {%- endfor -%}
          {%- endfor -%}
        {%- endunless -%}
          {%- if product_featured_media -%}
            {%- capture sizes -%}
              {%- if collage -%}
                {%- if full_width -%}
                  (min-width: 768px) {{ columns_desktop | times: 100 | divided_by: max_columns }}vw, {{ columns_mobile | times: 100 | divided_by: min_columns_mobile }}vw
                {%- else -%}
                  (min-width: {{ settings.page_width }}px) {{ settings.page_width | times: columns_desktop | divided_by: max_columns }}px, {{ columns_mobile | times: 100 | divided_by: min_columns_mobile }}vw
                {%- endif -%}
              {%- else -%}
                (min-width: {{ settings.page_width }}px) {% if enable_desktop_slider %}{{ settings.page_width | plus: 100 | divided_by: columns_desktop }}px{% else %}calc(100vw / {{ columns_desktop }}){% endif %}, (min-width: 1024px) calc((100vw) / {{ columns_desktop }}), (min-width: 768px) calc((100vw) / 2), calc(100vw /{{ column_mobile }})
              {%- endif -%}
            {%- endcapture -%}

            <div class="w-full{% if media_aspect_ratio != "natural"  %} h-full min-h-full{% endif %}{% if section.settings.product_image_type != "slide_img" or count_media <= 1 or class_slide == 'recommendations'  %} top-0 left-0 absolute{% else %} overflow-hidden splide__slide x-splide-slide max-h-full{% endif %} z-0 is-active">
              {% comment %}theme-check-disable ImgLazyLoading{% endcomment %}
              <picture>
                {%- liquid
                  if first_preload_image == blank or first_preload_image == nil
                    assign first_preload_image = product_featured_media
                  endif
                -%}
                <img
                srcset="{{ first_preload_image | image_url: width: 450 }} 450w,
                {{ first_preload_image | image_url: width: 750 }} 750w,
                {{ first_preload_image | image_url: width: 900 }} 900w,
                {{ first_preload_image | image_url: width: 1100 }} 1100w,
                {{ first_preload_image | image_url: width: 1500 }} 1500w
                {% if first_preload_image.width %},{{ first_preload_image | image_url }} {{ first_preload_image.width }}w{% endif %}"
                src="{{ first_preload_image | image_url: width: 1500 }}"
                sizes="{{ sizes }}"
                alt="{{ first_preload_image.alt | split: "#" | first | escape }}"
                class="w-full preview-img relative animate-XPulse skeleton-image{% if media_aspect_ratio != "natural" %} h-full min-h-full{% endif %} object-cover{% if card_product.media[1] != nil and section.settings.product_image_type == 'hover_to_reveal_second_image' %} image-first-hover xl:group-hover:opacity-0{% elsif section.settings.product_image_type != 'slide_img' %} image-hover{% endif %}{% if animation_loading and loading != 'eager' %} animate_transition_image{% endif %}"
                loading="{{ loading | default: 'lazy' }}"
                {% unless loading == 'lazy' %}
                  fetchpriority="high"
                  decoding="async"
                {% endunless %}
                width="{{ first_preload_image.width }}"
                height="{{ first_preload_image.height }}"
                onload="this?.classList.add('active'); 
                  this.closest('.skeleton-image')?.classList.remove('animate-XPulse', 'skeleton-image');
                  setTimeout(() => { this.closest('.animate_transition_card__image')?.classList.add('lazy_active'); }, 250);
                "
              />
              </picture>
              {% comment %}theme-check-enable ImgLazyLoading{% endcomment %}
            </div>
            {%- if card_product.media[1] != nil -%}
              {%- if section.settings.product_image_type == "hover_to_reveal_second_image" -%}
                <div class="w-full h-full top-0 left-0 absolute second_image">
                  <picture>
                    <img
                      srcset="{{ card_product.media[1] | image_url: width: 450 }} 450w,
                        {{ card_product.media[1] | image_url: width: 750 }} 750w,
                        {{ card_product.media[1] | image_url: width: 900 }} 900w,
                        {{ card_product.media[1] | image_url: width: 1100 }} 1100w,
                        {{ card_product.media[1] | image_url: width: 1500 }} 1500w
                        {% if card_product.media[1].width %},{{ card_product.media[1] | image_url }} {{ card_product.media[1].width }}w{% endif %}"
                      src="{{ card_product.media[1] | image_url: width: 1500 }}"
                      sizes="{{ sizes }}"
                      alt="{{ card_product.media[1].alt | split: "#" | first | escape }}"
                      class="motion-reduce{% if media_aspect_ratio == "natural" %} aspect-auto{% endif %} min-w-full h-full object-cover group-hover:cursor-pointer image-second-hover opacity-0 md:group-hover:opacity-100"
                      loading="lazy"
                      width="{{ card_product.media[1].width }}"
                      height="{{ card_product.media[1].height }}"
                      onload="this?.classList.add('active'); 
                        this.closest('.animate_transition_card__image')?.classList.remove('animate-XPulse', 'skeleton-image');
                        setTimeout(() => { this.closest('.animate_transition_card__image')?.classList.add('lazy_active'); }, 250);
                      "
                    />
                  </picture>
                </div>
              {%- elsif section.settings.product_image_type == "slide_img" and count_media > 1  -%}
                  {% for media in  card_product.media %}
                    {%- if forloop.index > 1 -%}
                      <div 
                        index="{{ forloop.index0 }}" 
                        media="{{ media | image_url }}"
                        class="media-slide w-full splide__slide x-splide-slide duration-300 z-0 transition ease-out{% if media_aspect_ratio != "natural"  %} h-full min-h-full{% endif %}{% if animation_loading %} opacity-0{% endif %}">
                        <picture>
                          <img
                            srcset="{{ media | image_url: width: 450 }} 450w,
                              {{ media | image_url: width: 750 }} 750w,
                              {{ media | image_url: width: 900 }} 900w,
                              {{ media | image_url: width: 1100 }} 1100w,
                              {{ media | image_url: width: 1500 }} 1500w
                              {% if media.width %},{{ media | image_url }} {{ media.width }}w{% endif %}"
                            src="{{ media | image_url: width: 1500 }}"
                            sizes="{{ sizes }}"
                            alt="{{ media.alt | split: "#" | first | escape }}"
                            class="w-full motion-reduce{% if media_aspect_ratio == "natural" %} aspect-auto{% else %} h-full min-h-full{% endif %} object-cover"
                            loading="lazy"
                            onload="this?.classList.add('active'); 
                              this.closest('.animate_transition_card__image')?.classList.remove('animate-XPulse', 'skeleton-image');
                              setTimeout(() => { this.closest('.animate_transition_card__image')?.classList.add('lazy_active'); }, 250);
                            "
                            width="{{ media.width }}"
                            height="{{ media.height }}"
                          />
                        </picture>
                      </div>
                    {% endif %}
                    {% liquid 
                      if forloop.index == count_media 
                        break 
                      endif
                    %}
                  {% endfor %} 
              {%- endif -%}
            {%- endif -%}
          {%- else -%} 
            <div class='bg-[#c9c9c9] flex justify-center{% unless media_aspect_ratio == "natural" %} absolute{% endunless %} top-0 left-0 w-full h-full items-center'>
              {%- render 'otsb-icon-placeholder', icon: 'icon-product', class: 'w-full h-full' %}
            </div>
          {%- endif -%}
          {% if section.settings.product_image_type == "slide_img" and count_media > 1 and class_slide != 'recommendations' %}
            </div>
          </div>
          {%- if section.settings.show_arrow -%}
            <div class="splide__arrows lg:inline-flex otsb-hidden">
              <button @click.prevent="prev = true" class="text-[rgba(var(--colors-transition-arrows))] splide__arrow splide__arrow--prev disable-effect-hover otsb-hidden group-hover:block absolute none_border z-30 w-[20px] h-1/2 rounded-full after:text-[20px] pl-4.5 pr-4 top-1/2 -translate-y-1/2 duration-100 opacity-60 hover:opacity-100 disabled:opacity-40 disabled:cursor-not-allowed rtl:right-0 rtl:rotate-0 rtl:left-auto left-0 rotate-180" aria-label="previous slide">
                {% render 'otsb-icon-alls', icon: 'icon-transition-arrow' %}
              </button>
              <button @click.prevent="next = true" class="text-[rgba(var(--colors-transition-arrows))] splide__arrow splide__arrow--next disable-effect-hover otsb-hidden group-hover:block absolute none_border z-30 w-[20px] h-1/2 rounded-full after:text-[20px] pl-4.5 pr-4 top-1/2 -translate-y-1/2 duration-100 opacity-60 hover:opacity-100 disabled:opacity-40 disabled:cursor-not-allowed rtl:left-0 rtl:right-auto right-0 rtl:rotate-180" aria-label="next slide">
                {% render 'otsb-icon-alls', icon: 'icon-transition-arrow' %}
              </button>
            </div>
          {%- endif -%}
        {%- endif -%}
        </div>
      </div>
      {%- unless disable_quickview == true -%}
        <div class="md:list-layout:hidden" x-show="$store.xQuickView" :class="$store.xQuickView && $store.xQuickView.btn_atc_bottom ? 'mobile-bottom':''">
            <div class="quick-view-container absolute z-20 bottom-2 right-2 rtl:right-auto rtl:left-2 rtl:lg:left-auto lg:top-auto lg:right-auto lg:bottom-4 lg:w-full lg:group-hover:opacity-100 lg:group-hover:translate-y-0 focus:opacity-100 focus:translate-y-0 lg:translate-y-full transition-all duration-200 ease-in-out lg:opacity-0"
              x-show="$store.xQuickView" x-cloak
            > 
              <div x-data="xProductCart" x-show="$store.xQuickView"
                class="w-full{% if show_icon_atc %} text-right rtl:text-left lg:pl-2 lg:pr-2{% else %} lg:px-4{% endif %}" 
                :class="if($store.xQuickView){ return ($store.xQuickView.btn_atc_bottom ? 'hidden' : '') + ($store.xQuickView.atc_btn_image_desktop ? ' lg:block' : ' lg:hidden') }"
                x-cloak
              >
                {%- assign product_form_id = 'product-form-' | append: section.id -%}
                {%- form 'product', card_product, id: product_form_id, class: 'form h-full', novalidate: 'novalidate', data-type: 'add-to-cart-form', x-ref: 'product_form' -%}
                  <input type="hidden" name="id" value="{{ card_product.selected_or_first_available_variant.id }}">
                  
                  <button
                    type="submit"
                    name="add"
                    {%- if card_product.has_only_default_variant -%}
                    @click.prevent="errorMessage == false && addToCart($event)"
                    {%- else -%}
                    @click.prevent="$store.xQuickView; showOptions =! showOptions; $store.xQuickView.showChooseOption('{{ section_id }}-{{ card_product.id }}-{{ block_id }}');"
                    
                    {%- endif -%}
                    data-id="{{ section_id }}-{{ card_product.id }}-{{ block_id }}"
                    class="btn-choose-option bg-white button button--quickview button-solid not-icon leading-normal w-full py-2.5 lg:py-3 h-full z-50 text-[12px] lg:text-[14px] lg:-translate-x-0 disabled:cursor-not-allowed disabled:opacity-60 {%- if card_product.has_only_default_variant -%} lg:otsb-hidden {% else %} lg:block {% endif %} otsb-hidden"
                    :class="errorMessage && 'opacity-60 cursor-not-allowed'"
                    {% if card_product.selected_or_first_available_variant.available == false %} 
                      disabled
                      aria-label="{{ 'products.product.sold_out' | t }}"
                    {% else %}
                      {%- if card_product.has_only_default_variant -%}
                        aria-label="{{ 'products.product.add_to_cart' | t }}"
                      {% else %}
                        aria-label="{{ 'products.product.choose_options' | t }}"
                      {% endif %}
                    {% endif %}
                  >
                    <span :class="{ 'lg:block opacity-0 is-focus-button' : loading, 'hidden' : errorMessage}">
                      {%- if card_product.selected_or_first_available_variant.available -%}
                        {%- if card_product.has_only_default_variant -%}
                          {% liquid
                            assign button_label = 'products.product.add_to_cart' | t
                          %}
                          <span class="flex items-center justify-center">
                            {% render 'otsb-button-label', button_label: button_label, show_button_style: 'primary' %}
                          </span>
                        {%- else -%}
                          {% assign button_label = 'products.product.choose_options' | t %}
                          <span class="flex items-center justify-center">
                            {% render 'otsb-button-label', button_label: button_label, show_button_style: 'primary' %}
                          </span>
                        {%- endif -%}
                      {%- else -%}
                        {{ 'products.product.sold_out' | t }}
                      {%- endif -%}
                    </span>
                    <div
                      class="flex gap-x-2 items-center justify-center"
                      role="alert"
                      x-cloak
                      x-show="errorMessage"
                    >
                      {{ 'products.product.sold_out' | t }}
                    </div>
                    <div class="lg:inline-block animate-spin w-4 h-4 md:w-5 md:h-5 absolute top-[calc(50%-8px)] left-[calc(50%-8px)] md:top-[calc(50%-10px)] md:left-[calc(50%-10px)]" x-show="loading" x-cloak>
                      {% render 'otsb-icon-alls', icon: 'icon-loading' %}
                    </div>
                  </button>
                  <button 
                    type="submit"
                    name="add"
                    {%- if card_product.has_only_default_variant -%}
                      @click.prevent="errorMessage == false && addToCart($event)"
                    {%- else -%}
                      @click.prevent="$store.xQuickView; showOptions =! showOptions; $store.xQuickView.showChooseOption('{{ section_id }}-{{ card_product.id }}-{{ block_id }}');"
                    {%- endif -%}
                    class="btn-icon-cart bg-white button label-btn-quickview button-disable-effect not-icon button--quickview rounded-full lg:leading-snug none_border{% if show_icon_atc %} xl:hidden{% else %} lg:hidden{% endif %} h-full p-[6px] disabled:cursor-not-allowed disabled:opacity-60"
                    data-id="{{ section_id }}-{{ card_product.id }}-{{ block_id }}"
                    :class="errorMessage && 'opacity-60 cursor-not-allowed'"
                    {% if card_product.selected_or_first_available_variant.available == false %}
                      disabled
                      aria-label="{{ 'products.product.sold_out' | t }}"
                    {% else %}
                      {%- if card_product.has_only_default_variant -%}
                        aria-label="{{ 'products.product.add_to_cart' | t }}"
                      {% else %}
                        aria-label="{{ 'products.product.choose_options' | t }}"
                      {% endif %}
                    {% endif %}
                    x-customer-event-data="{{ dataForCustomerEvent | escape }}"
                  >
                    <span class="w-10 h-10 flex items-center justify-center x-show="!loading">{%- render 'otsb-icon-cart-all', icon: 'icon-cart-quick-add' -%}</span>
                    <span class="block w-5 h-5 animate-spin" x-show="loading" x-cloak>
                    {% render 'otsb-icon-alls', icon: 'icon-loading' %}
                  </span>
                </button>
                {%- endform -%}
              </div>
            </div>
        </div>
      {%- endunless -%}
    </a>
    {% if product_complementary %}
      </div>
    {% endif %}
    {% capture card_info %}
      <div class="card-info{% if product_complementary %} pl-4 pt-1 pb-1{% elsif collage %} pt-3.5 pb-3.5{% else %} pt-4 lg:pt-6{% endif %} md:list-layout:pt-0 md:list-layout:lg:pt-0{% if card_full_width %} pl-5 pr-5 md:pl-0 md:pr-0{% endif %}">
        <div class="no-collage:mb-2 {% if info_alignment %} text-{{ info_alignment }} {% endif %}  {% unless is_rtl %} md:list-layout:text-left{% else %} md:list-layout:text-right{% endunless %}">
          {%- if show_vendor -%}
            <div class="mb-2.5 uppercase tracking-widest text-small p-break-words">{{ card_product.vendor }}</div>
          {%- endif -%}
          <h3 class="card__heading no-collage:mb-2 mb-1.5 leading-tight text-[{{ settings.text_base_size | times: 0.007875 }}rem] md:text-[{{ settings.text_base_size | times: 0.00875 }}rem]"{% if product_featured_media %} id="title-{{ section_id }}-{{ card_product.id }}"{% endif %}>
            <a href="{{ card_product.url | within: collection }}"
              class="full-unstyled-link link-product-variant text-[rgba(var(--colors-heading))] hover:text-[rgba(var(--colors-text-link))] cursor-pointer duration-200 p-break-words"
              @click='$store.xCustomerEvent.fire("product_selected", $el)'
              x-customer-event-data="{{ dataForCustomerEvent | escape }}"
            >
              {{ card_product.title | escape }}
            </a>
          </h3>
          <div {% unless product_complementary %} class="mt-2.5"{% endunless %}>
            <div class="loox-rating" data-id="{{ card_product.id }}" data-rating="{{ card_product.metafields.loox.avg_rating }}" data-raters="{{ card_product.metafields.loox.num_reviews }}"></div>
            {% render 'otsb-price', product: card_product, product_complementary: product_complementary %}
          </div>
        </div>
      </div>
    {% endcapture %}
    <div class="md:list-layout:hidden{% if product_complementary %} w-5/6{% endif %}">{{ card_info }}</div>
    
  </div>

  {% comment %} Show the product details when card product is in list view {% endcomment %}
  <div
    id="x-product-template-{{ card_product.id }}-{{ section_id }}-{{ product_card_id }}"
    class="pb-4 md:pb-6 list-layout:lg:w-3/4 md:list-layout:w-3/5 list-layout:md:p-5 list-layout:md:pl-0 rtl:list-layout:md:pl-5 rtl:list-layout:md:pr-0 {% if info_alignment %}{{ info_alignment }}{% endif %} {% if is_rtl %} md:list-layout:text-right{% endif %}"
  >
    <div class="hidden md:list-layout:block">
      {{ card_info }}
    </div>
    {% unless disable_color_swatch %}
      {%- if section.settings.products_color_swatches_enable_on_collection_page -%}
        {%- unless card_product.has_only_default_variant or product_complementary -%}
          {% liquid
            assign is_cbl = false
            if section.settings.enable_combined
              assign is_cbl = true
            endif
          %}
          <div
            class="x-variants-data"
            {% if section.settings.swatches_type == 'both_text_and_color'  or section.settings.swatches_type == 'color' %}
              x-data="xVariantSelect($el,'{{ section_id }}-{{ product_card_id }}', false, '{{ 'products.product.unavailable' | t }}', '{{ card_product.url | within: collection | split: "?" | first }}', '{{ card_product.id }}', false, true, false, '{{ section.id }}', '', '', '{{ product_featured_media | image_url | default: "" }}', {{ index_param }})"
              {% unless is_cbl %}x-init="initVariant()"{% endunless %}
            {% endif %}
          >
            <script type="application/json">
              {{ card_product.variants | json }}
            </script>
            <div
              id="current-variant-{{ section_id }}-{{ card_product.id }}-{{ block_id }}" 
              class="current-variant hidden"
            >
              {{ card_product.selected_or_first_available_variant.id }}
            </div>
            <div
              id="variant-update-{{ section_id }}-{{ product_card_id }}"
              class="variant-update-{{ section_id }}-{{ product_card_id }} mt-3 space-y-2 list-layout:md:text-left rtl:list-layout:md:text-right options-container"
            >
              {%- for option in card_product.options_with_values -%}
                {% liquid
                  assign variant_key = 'option' | append: option.position
                  assign show_swatch = false
                  assign show_color_swatch = false
                  assign show_text_swatch = false
                  assign color_option_name = section.settings.color_option_name | split: ", "
                  assign text_option_name = section.settings.text_option_name | split: ", "
                  for color_option_name in color_option_name
                    if color_option_name == option.name and section.settings.swatches_type == 'color'
                      assign show_color_swatch = true
                    endif
                  endfor
                  for text_option_name in text_option_name
                    if text_option_name == option.name and section.settings.swatches_type == 'text'
                    assign show_text_swatch = true
                    endif
                  endfor
                  if section.settings.swatches_type == 'both_text_and_color'
                    for color_option_name in color_option_name
                      if color_option_name == option.name
                        assign show_swatch = true
                      endif
                    endfor
                    for text_option_name in text_option_name
                      if text_option_name == option.name
                        assign show_swatch = true
                      endif
                    endfor
                  endif
                %}
                {% if show_swatch or show_text_swatch or show_color_swatch %}
                  {%- assign option_index = forloop.index | minus: 1 -%}
                  <fieldset
                    id="Option-{{ section_id }}-{{ product_card_id }}-{{ forloop.index0 }}"
                    class="product-form__input flex"
                    x-intersect.once.margin.500px="options[{{ forloop.index | minus: 1 }}] = document.querySelector('#Option-{{ section_id }}-{{ product_card_id }}-{{ forloop.index0 }} input[checked]').value" 
                  >
                    <div class="hidden">{{ option.name }}</div>
                    <div class="ot-swatch-wrapper w-full inline-flex gap-3 flex-wrap ltr items-center justify-{{ info_alignment }} __{{ section.settings.swatch_size }}">
                      {%- liquid
                        assign variants_available_arr = card_product.variants | map: 'available'
                        assign variants_option1_arr = card_product.variants | map: 'option1'
                        assign variants_option2_arr = card_product.variants | map: 'option2'
                        assign variants_option3_arr = card_product.variants | map: 'option3'
                      
                        assign color_option_values = section.settings.color_option_values | newline_to_br | strip_newlines | split: '<br />'
                        assign text_option_values = section.settings.text_option_values | newline_to_br | strip_newlines | split: '<br />'

                        if section.settings.products_color_swatches_style == 'round' 
                          assign classSwatch = ' rounded-full before:rounded-full'
                        elsif section.settings.edges_type == 'rounded_corners'
                          assign classSwatch = ' rounded-md overflow-hidden'
                        endif
                        if section.settings.swatch_show_by_default != 0
                          assign count_value = option.values.size
                          if count_value > section.settings.swatch_show_by_default
                            assign is_count = true
                            assign count_value = count_value | minus: section.settings.swatch_show_by_default
                          endif
                        endif
                        

                        assign swatch_natural = false     
                        if section.settings.products_color_swatches_style == 'natural'
                          assign swatch_natural = true
                        endif
                        assign ratio_image = false
                      -%}
                      {%- for value in option.values -%}
                        {%- liquid
                          assign preview_image = blank
                          assign variant_value = card_product.variants | where: variant_key, value | first
                          if variant_value.featured_media.preview_image
                            assign preview_image = variant_value.featured_media.preview_image
                            unless ratio_image
                              assign ratio_image = 1 | divided_by: variant_value.featured_media.preview_image.aspect_ratio | times: 100
                            endunless
                          elsif product_featured_media
                            assign preview_image = product_featured_media
                            unless ratio_image
                              assign ratio_image = 1 | divided_by: product_featured_media.aspect_ratio | times: 100
                            endunless
                          endif
                          assign option_disabled = true
                      
                          for option1_name in variants_option1_arr
                            case option.position
                              when 1
                                if variants_option1_arr[forloop.index0] == value and variants_available_arr[forloop.index0]
                                  assign option_disabled = false
                                endif
                              when 2
                                if option1_name == card_product.selected_or_first_available_variant.option1 and variants_option2_arr[forloop.index0] == value and variants_available_arr[forloop.index0]
                                  assign option_disabled = false
                                endif
                              when 3
                                if option1_name == card_product.selected_or_first_available_variant.option1 and variants_option2_arr[forloop.index0] == card_product.selected_or_first_available_variant.option2 and variants_option3_arr[forloop.index0] == value and variants_available_arr[forloop.index0]
                                  assign option_disabled = false
                                endif
                            endcase
                          endfor
                        -%}
                        {%- liquid
                          assign color_custom = ''
                          assign swatch_image = false
                          assign color_value = ''
                          for color_option_value in color_option_values reversed
                            assign option_key = color_option_value | split: ':' | first
                            assign option_value = color_option_value | split: ':' | last | strip
                    
                            if option_key == value and option_value contains '.'
                              assign color_custom = option_key
                              assign swatch_image = true
                              assign color_value = option_value
                              break
                            endif
                            assign multiColors = option_value | remove_first: '#' | split: '#'
                            if option_key == value
                              assign color_custom = option_key
                              assign color_value = option_value
                              break
                            endif
                          endfor  
                        -%}
                        {%- if loading == 'eager' and forloop.index == 1 -%}
                          <div class="hidden">
                            {% assign featured_media_alt = preview_image.alt | split: "#" | first | escape %}
                            {{ preview_image | image_url: width: 1500 | image_tag: widths: '375, 450, 750, 900, 1100, 1500', preload: true, loading: 'lazy', onload: onload, sizes: sizes, alt: featured_media_alt }}
                          </div>
                        {%- endif -%}
                        {% if preview_image and section.settings.replace_color_with_variant_images %}
                          {% if section.settings.color_option_name contains option.name %}
                            {%- capture css1 %}
                            .color-watches.variant-{{ product_card_id }}-{{ option.position }}-{{ forloop.index0 }}-{{ card_product.id }}:before {
                              background-image: var(--bg-image);
                            }
                            {%- endcapture %}
                            {%- assign main_css = main_css | append: css1 -%}
                          {% endif %}
                        {% elsif color_custom != '' %}
                          {%- capture css1 %}
                            {% if swatch_image %}
                              [data-swatch="{{ color_custom }}"]:before {
                                background-image: url({{ color_value | file_img_url: "100x100" }});
                              }
                            {% elsif multiColors.size == 3 %}
                              [data-swatch="{{ color_custom }}"]:before {
                                background: linear-gradient(45deg, {{ '#' | append: multiColors[0] }}, {{ '#' | append: multiColors[0] }} 33.3%, {{ '#' | append: multiColors[1] }} 33.3%, {{ '#' | append: multiColors[1] }} 66.6%, {{ '#' | append: multiColors[2] }} 66.6%, {{ '#' | append: multiColors[2] }});
                              }
                            {% elsif multiColors.size == 2 %}
                              [data-swatch="{{ color_custom }}"]:before {
                                background: linear-gradient(45deg, {{ '#' | append: multiColors[0] }}, {{ '#' | append: multiColors[0] }} 50%, {{ '#' | append: multiColors[1] }} 50%, {{ '#' | append: multiColors[1] }});
                              }
                            {% else %}
                              [data-swatch="{{ color_custom }}"]:before {
                                background: {{ color_value }};
                              }
                            {% endif %}
                          {%- endcapture %}
                          {%- assign main_css = main_css | append: css1 -%}
                        {% endif %}
                        <input 
                          id="{{ section_id }}-{{ product_card_id }}-{{ option.position }}-{{ forloop.index0 }}-{{ card_product.id }}"
                          section-id="{{ section_id }}-{{ product_card_id }}"
                          option-pos="{{ option.position }}"
                          forloop="{{ forloop.index0 }}"
                          type="radio"
                          name="{{ option.name }}-{{ product_card_id }}-{{ section_id }}"
                          value="{{ value | escape | replace: '&lt;', '\u003c' | replace: '&gt;', '\u003e' }}"
                          form="{{ product_form_id }}"
                          class="absolute opacity-0 h-1 w-1 input-radio{% if option_disabled and section.settings.swatches_type != 'both_text_and_color' %} disabled{% endif %}"
                          {% if section.settings.swatches_type == 'both_text_and_color' %}
                            {% if option_index > 0 %}
                              :class="{'disabled': isSelect && (!currentAvailableOptions[{{ option_index }}] || !currentAvailableOptions[{{ option_index }}].includes(`{{ value | escape }}`))}"
                            {% elsif option_disabled %}
                              :class="{'disabled': isSelect }"
                            {% endif %}
                            x-model="options[{{ option_index }}]"
                          {% endif %}
                          {% if section.settings.swatches_type == 'both_text_and_color' and card_product.options_with_values.size > 1 %}
                            x-on:change="$store.xPreviewColorSwatch.onChangeVariant($el,'{{ card_product.url | within: collection | split: "?" | first }}', '{{ product_featured_media | image_url | default: "" }}', {{ variant_value.id | default: "" }}, '{{ section_id }}-{{ product_card_id }}', {% if section.settings.swatches_type == 'color' or section.settings.swatches_type == 'both_text_and_color' and section.settings.color_option_name contains option.name %}true{% endif %})"
                          {% else %}
                            x-on:change="$store.xPreviewColorSwatch.onVariantSelect($el,'{{ card_product.url | within: collection | split: "?" | first }}', '{{ preview_image | image_url | default: "" }}', '{{ variant_value.id }}')"
                          {% endif %}
                          aria-label="{{ value | escape }}"
                          {% if forloop.first %}
                            checked
                          {% endif %}
                        >
                        {% if section.settings.swatches_type == 'color' or section.settings.swatches_type == 'both_text_and_color' and section.settings.color_option_name contains option.name %}
                          <label
                            class="color-watches variant-{{ product_card_id }}-{{ option.position }}-{{ forloop.index0 }}-{{ card_product.id }} cursor-pointer block{% if swatch_natural and section.settings.replace_color_with_variant_images and ratio_image %} color-swatch-natural{% endif %} relative{{ classSwatch }}" 
                            for="{{ section_id }}-{{ product_card_id }}-{{ option.position }}-{{ forloop.index0 }}-{{ card_product.id }}" 
                            data-swatch="{{ color_custom }}"
                            data-optionindex="{{ option_index }}"
                            data-optionvalue="{{ value | escape | replace: '&lt;', '\u003c' | replace: '&gt;', '\u003e' }}"
                            {% if preview_image == blank or section.settings.replace_color_with_variant_images == false and color_custom == '' %}
                              {% liquid
                                if value.swatch.image
                                  assign image_url = value.swatch.image | image_url: width: 50
                                  assign swatch_value = 'url(' | append: image_url | append: ')'
                                elsif value.swatch.color
                                  assign swatch_value = 'rgb(' | append: value.swatch.color.rgb | append: ')'
                                else
                                  assign swatch_value = value | split: ' ' | last | handle
                                endif
                              %}
                              style="background: {{ swatch_value }};"
                            {% elsif swatch_natural and ratio_image %}
                              style="--ratio-image: {{ ratio_image }}%; --bg-image: url('{{ preview_image | image_url: width: 100, height: 100 }}')"
                            {% else %}
                              style="--bg-image: url('{{ preview_image | image_url: width: 100, height: 100 }}')"
                            {% endif %}
                            tabindex="0"
                            data-name="{{ value | escape }}"
                            aria-label="{{ value | escape }}"
                          >
                            <span class="h-full w-full color-watches-disable block"></span>
                          </label>
                          {% if section.settings.swatch_show_by_default != 0 %}
                            {%capture enable_count %}
                              <p class="mb-2 ml-0.5">+{{ count_value }}</p>
                            {% endcapture %}
                            {% liquid
                              assign max = section.settings.swatch_show_by_default | minus: 1
                              if forloop.index > max
                                if is_count
                                  echo enable_count
                                endif
                                break
                              endif
                            %}
                          {% endif %}
                        {% elsif section.settings.swatches_type == 'text' or section.settings.swatches_type == 'both_text_and_color' and section.settings.text_option_name contains option.name %}
                          {% liquid
                            assign text_custom = blank
                            assign text_value = ''
                            for text_option_value in text_option_values
                              assign option_key = text_option_value | split: ':' | first
                              assign option_value = text_option_value | split: ':' | last
                              
                              if option_key == value
                                assign text_custom = option_key
                                assign text_value = option_value
                                break
                              endif
                            endfor
                          %}
                          <label class="flex items-center outline-none font-medium text-[13px] cursor-pointer{% if section.settings.info_alignment_card_product == 'right' %} rtl:mr-0{% endif %} pl-3.5 pr-3.5 pt-1.5 pb-1.5 border bg-[rgba(var(--background-color),1)]
                            {% if section.settings.edges_type == 'rounded_corners' %} rounded-md{% endif %}" 
                            for="{{ section_id }}-{{ product_card_id }}-{{ option.position }}-{{ forloop.index0 }}-{{ card_product.id }}"
                          >
                            <span class="p-break-words">
                              {% if text_custom != blank %}{{ text_value | escape }}{% else %}{{ value | escape }}{% endif %}
                            </span>
                          </label>
                        {% endif %}
                      {%- endfor -%}
                    </div>
                  </fieldset>
                {% elsif section.settings.swatches_type == 'both_text_and_color' %}
                  {%- assign option_index = forloop.index | minus: 1 -%}
                  <fieldset
                    id="Option-{{ section_id }}-{{ product_card_id }}-{{ forloop.index0 }}"
                    class="product-form__input pl-0 eurus gap-x-2 hidden"
                    x-init="options[{{ forloop.index | minus: 1 }}] = document.querySelector('#Option-{{ section_id }}-{{ product_card_id }}-{{ forloop.index0 }} input:checked').value"
                  >
                    <div class="inline-flex flex-wrap justify-left">
                      {%- for value in option.values -%}
                        {% if forloop.first %}
                          <input 
                            id="{{ section_id }}-{{ product_card_id }}-{{ option.position }}-{{ forloop.index0 }}-{{ card_product.id }}"
                            setion-id="{{ section_id }}-{{ product_card_id }}"
                            option-pos="{{ option.position }}"
                            forloop="{{ forloop.index0 }}"
                            type="radio"
                            name="{{ option.name }}-{{ product_card_id }}"
                            value="{{ value | escape | replace: '&lt;', '\u003c' | replace: '&gt;', '\u003e' }}"
                            form="{{ product_form_id }}"
                            class="absolute opacity-0 h-1 w-1 input-radio"
                            {% if option_index > 0 %}:class="{'disabled': !currentAvailableOptions[{{ option_index }}] || !currentAvailableOptions[{{ option_index }}].includes(`{{ value }}`)}"{% endif %}
                            x-model="options[{{ option_index }}]"
                            aria-label="{{ value | escape }}"
                            checked
                          >  
                          <label class="relative flex items-center outline-none font-medium text-[13px] cursor-pointer mb-1.5 mr-[5px]{% if section.settings.info_alignment_card_product == 'right' %} rtl:mr-0 rtl:ml-1.5{% endif %} pl-3.5 pr-3.5 pt-1.5 pb-1.5 border bg-[rgba(var(--background-color),1)]
                            {% if rounded_corner %} rounded-md{% endif %}" for="{{ section_id }}-{{ product_card_id }}-{{ option.position }}-{{ forloop.index0 }}-{{ card_product.id }}"
                          >
                            <span class="p-break-words">{{ value | escape }}</span>
                          </label>
                        {% endif %}
                      {% endfor %}
                    </div>
                  </fieldset>
                {% endif %}
              {%- endfor -%}
            </div>
          </div>
        {% else %}
          <div x-init="document.addEventListener('eurus:cart:items-changed', () => {
            Alpine.store('xUpdateVariantQuanity').updateQuantity('{{ section.id }}', '{{ card_product.url }}');
          });"></div>
          <div
            id="current-variant-{{ section_id }}-{{ card_product.id }}-{{ block_id }}" 
            class="current-variant hidden"
          >
            {{ card_product.selected_or_first_available_variant.id }}
          </div>
        {% endunless %}
      {% else %}
        <div x-init="document.addEventListener('eurus:cart:items-changed', () => {
          Alpine.store('xUpdateVariantQuanity').updateQuantity('{{ section.id }}', '{{ card_product.url }}');
        });"></div>
        <div
          id="current-variant-{{ section_id }}-{{ card_product.id }}-{{ block_id }}" 
          class="current-variant hidden"
        >
          {{ card_product.selected_or_first_available_variant.id }}
        </div>
      {%- endif -%}
    {% endunless %}
    {% if list_layout and show_description %}
      {% if card_product.description != blank %}
        <div class="hidden md:list-layout:block mt-5{% if is_rtl %} md:list-layout:text-right{% else %} md:list-layout:text-left{% endif %}">
          <div class="rte line-clamp-3">
            {{ card_product.description | replace: '<img', '<noimg' | replace: '</img>', '</noimg>' | replace: '<iframe', '<noiframe' | replace: '</iframe>', '</noiframe>' | replace: 'src=', 'nosrc=' | replace: '<h1', '<p class="h2"' | replace: '<h2', '<p class="h2"' | replace: '<h3', '<p class="h3"' | replace: '<h4', '<p class="h4"' | replace: '<h5', '<p class="h5"' | replace: '<h6', '<p class="h6"' }}
          </div>
        </div>
      {% endif %}
    {% endif %}
  </div>
</div>
{%- if main_css != '' -%}
  {%- capture styles -%}
    {%- style -%}
    {{ main_css }}
    {%- endstyle -%}
  {%- endcapture -%}
  {%- assign styles =  styles | strip_newlines | split: " " | join: " " | split: "*/" -%}
  {%- assign minified_style = "" -%}
  {%- for word in styles -%}
  {%- assign new_word = word | split: "/*" | first | strip | replace: "; ", ";" | replace: "} ", "}" | replace: "{ ", "{" | replace: " {", "{" -%}
    {%- assign minified_style = minified_style | append: new_word -%}
  {%- endfor -%}

  {{- minified_style  }}
{%- endif -%}
