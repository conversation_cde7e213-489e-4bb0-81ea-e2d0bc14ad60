{"settings_schema": {"colors": {"name": "<PERSON><PERSON>", "settings": {"colors_solid_button_labels": {"label": "Beschriftung für durchgehende Schaltfläche", "info": "Wird als Vordergrundfarbe für Akzentfarben verwendet."}, "colors_accent_1": {"label": "Akzent 1", "info": "Wird als Hintergrund für durchgehende Schaltflächen verwendet."}, "colors_accent_2": {"label": "Akzent 2"}, "header__1": {"content": "Primärfarben"}, "header__2": {"content": "Sekundärfarben"}, "colors_text": {"label": "Text", "info": "Wird als Vordergrundfarbe für Hintergrundfarben verwendet."}, "colors_outline_button_labels": {"label": "Umriss-Schaltfläche", "info": "Wird auch für Textlinks verwendet."}, "colors_background_1": {"label": "Hintergrund 1"}, "colors_background_2": {"label": "Hintergrund 2"}, "gradient_accent_1": {"label": "Akzent 1 Farbverlauf"}, "gradient_accent_2": {"label": "Akzent 2 Farbverlauf"}, "gradient_background_1": {"label": "Hintergrund 1 Farbverlauf"}, "gradient_background_2": {"label": "Hintergrund 2 Farbverlauf"}}}, "typography": {"name": "<PERSON><PERSON><PERSON><PERSON>", "settings": {"type_header_font": {"label": "<PERSON><PERSON><PERSON><PERSON>", "info": "Die Auswahl einer anderen Schriftart kann sich auf die Geschwindigkeit deines Shops auswirken. [Weitere Informationen zu Systemschriftarten.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"}, "header__1": {"content": "Überschriften"}, "header__2": {"content": "Nachricht"}, "type_body_font": {"label": "<PERSON><PERSON><PERSON><PERSON>", "info": "Die Auswahl einer anderen Schriftart kann sich auf die Geschwindigkeit deines Shops auswirken. [Weitere Informationen zu Systemschriftarten.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"}, "heading_scale": {"label": "Schriftgrößenmaßstab"}, "body_scale": {"label": "Schriftgrößenmaßstab"}}}, "styles": {"name": "Symbole", "settings": {"accent_icons": {"options__3": {"label": "Umriss-Schaltfläche"}, "options__4": {"label": "Text"}, "label": "Farbe"}}}, "social-media": {"name": "Social Media", "settings": {"social_twitter_link": {"label": "Twitter", "info": "https://twitter.com/shopify"}, "social_facebook_link": {"label": "Facebook", "info": "https://facebook.com/shopify"}, "social_pinterest_link": {"label": "Pinterest", "info": "https://pinterest.com/shopify"}, "social_instagram_link": {"label": "Instagram", "info": "http://instagram.com/shopify"}, "social_tiktok_link": {"label": "TikTok", "info": "https://vimeo.com/shopify"}, "social_tumblr_link": {"label": "Tumblr", "info": "http://shopify.tumblr.com"}, "social_snapchat_link": {"label": "Snapchat", "info": "https://www.snapchat.com/add/shopify"}, "social_youtube_link": {"label": "YouTube", "info": "https://www.youtube.com/shopify"}, "social_vimeo_link": {"label": "Vimeo", "info": "https://vimeo.com/shopify"}, "header": {"content": "Social-Media-Konten"}}}, "currency_format": {"name": "Währungsformat", "settings": {"content": "Währungscodes", "currency_code_enabled": {"label": "Währungscodes anzeigen"}, "paragraph": "Warenkorb- und Checkout-Preise zeigen immer Währungscodes an. Beispiel: 1,00 USD."}}, "favicon": {"name": "Favicon", "settings": {"favicon": {"label": "Favicon-Bild", "info": "Wird auf 32 x 32 Pixel verkleinert"}}}, "layout": {"name": "Layout", "settings": {"page_width": {"label": "Seitenbreite"}, "spacing_sections": {"label": "Vertikaler Abstand zwischen Abschnitten"}, "header__grid": {"content": "<PERSON><PERSON>"}, "paragraph__grid": {"content": "Wirkt sich auf Bereiche mit einem Layout mit mehreren Spalten aus."}, "spacing_grid_horizontal": {"label": "Horizontaler Abstand"}, "spacing_grid_vertical": {"label": "<PERSON><PERSON><PERSON><PERSON> Abstand"}}}, "search_input": {"name": "Suchverhalten", "settings": {"header": {"content": "Produktvorschläge"}, "predictive_search_enabled": {"label": "Produktvorschläge aktivieren"}, "predictive_search_show_vendor": {"label": "<PERSON><PERSON><PERSON> anzeigen", "info": "<PERSON><PERSON><PERSON>, wenn Produktvorschläge aktiviert sind."}, "predictive_search_show_price": {"label": "<PERSON><PERSON> anzeigen", "info": "<PERSON><PERSON><PERSON>, wenn Produktvorschläge aktiviert sind."}}}, "global": {"settings": {"header__border": {"content": "Rand"}, "header__shadow": {"content": "<PERSON><PERSON><PERSON>"}, "blur": {"label": "Weichzeichnen"}, "corner_radius": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "horizontal_offset": {"label": "Horizontaler Offset"}, "vertical_offset": {"label": "<PERSON><PERSON><PERSON><PERSON> Offset"}, "thickness": {"label": "<PERSON><PERSON>"}, "opacity": {"label": "Opazität"}, "image_padding": {"label": "Bild-Padding"}, "text_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Textausrichtung"}}}, "cards": {"name": "<PERSON><PERSON>", "settings": {"header__badge": {"content": "Badge"}, "style": {"options__1": {"label": "Standard"}, "options__2": {"label": "<PERSON><PERSON>"}, "label": "Optik"}}}, "badges": {"name": "Badges", "settings": {"position": {"options__1": {"label": "Unten links"}, "options__2": {"label": "Unten rechts"}, "options__3": {"label": "Oben links"}, "options__4": {"label": "<PERSON><PERSON> rechts"}, "label": "Position der Produktkarte"}, "sale_badge_color_scheme": {"label": "Farbschema für Sale-Badges"}, "sold_out_badge_color_scheme": {"label": "Farbschema für Ausverkauft-Badges"}}}, "buttons": {"name": "Schaltflächen"}, "variant_pills": {"name": "Varianten-Kapseln"}, "inputs": {"name": "Eingaben"}, "content_containers": {"name": "Inhalts-Container"}, "popups": {"name": "Dropdown-Listen und Pop-ups", "paragraph": "Wirkt sich auf Bereiche wie das Dropdown-Menü für die Navigation, modale Pop-ups und Warenkorb-Pop-ups aus."}, "media": {"name": "Medien"}, "drawers": {"name": "Einschübe"}}, "sections": {"all": {"padding": {"section_padding_heading": "Abschnitts-Padding", "padding_top": "<PERSON><PERSON><PERSON>", "padding_bottom": "<PERSON><PERSON><PERSON> Padding"}, "spacing": "Abstand", "colors": {"accent_1": {"label": "Akzent 1"}, "accent_2": {"label": "Akzent 2"}, "background_1": {"label": "Hintergrund 1"}, "background_2": {"label": "Hintergrund 2"}, "inverse": {"label": "Invertiert"}, "label": "Farbschema", "has_cards_info": "Aktualisiere deine Theme-Einstellungen, um das Farbschema der Karte zu ändern."}, "heading_size": {"label": "Größe der Überschrift", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}, "announcement-bar": {"name": "Ankündigungsleiste", "blocks": {"announcement": {"name": "Ankündigung", "settings": {"text": {"label": "Text"}, "link": {"label": "Link"}}}}}, "collage": {"name": "Collage", "settings": {"heading": {"label": "Überschrift"}, "desktop_layout": {"label": "Desktop-Layout", "options__1": {"label": "Großer Block links"}, "options__2": {"label": "Großer Block rechts"}}, "mobile_layout": {"label": "Mobiles Layout", "options__1": {"label": "Collage"}, "options__2": {"label": "<PERSON>lt<PERSON>"}}}, "blocks": {"image": {"name": "Bild", "settings": {"image": {"label": "Bild"}, "image_padding": {"label": "Original-Bildverhältnis verwenden", "info": "<PERSON><PERSON><PERSON><PERSON> dies aus, wenn du nicht möchtest, dass dein Bild zurechtgeschnitten wird."}}}, "product": {"name": "Produkt", "settings": {"product": {"label": "Produkt"}, "secondary_background": {"label": "Sekundären Hintergrund anzeigen"}, "second_image": {"label": "Hover-Effekt mit zweitem Bild"}}}, "collection": {"name": "<PERSON><PERSON><PERSON>", "settings": {"collection": {"label": "<PERSON><PERSON><PERSON>"}}}, "video": {"name": "Video", "settings": {"cover_image": {"label": "Titelbild"}, "video_url": {"label": "URL", "info": "Video wird in einem Pop-up abgespielt, wenn der Abschnitt andere Blöcke enthält.", "placeholder": "YouTube- oder Vimeo-URL verwenden"}, "image_padding": {"label": "Original-Bildverhältnis verwenden", "info": "<PERSON><PERSON><PERSON><PERSON> dies aus, wenn du nicht möchtest, dass dein Bild zurechtgeschnitten wird."}, "description": {"label": "Video-Alt-Text", "info": "Beschreibe das Video für Kunden, die Bildschirmlesegeräte benutzen. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)"}}}}, "presets": {"name": "Collage"}}, "collection-list": {"name": "Kategorieliste", "settings": {"title": {"label": "Überschrift"}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Porträt"}, "options__3": {"label": "<PERSON>uadratisch"}, "info": "Bearbeite deine Kategorien, um Bilder hinzuzufügen. [Mehr Informationen](https://help.shopify.com/manual/products/collections)"}, "swipe_on_mobile": {"label": "Wischen auf Mobilgeräten aktivieren"}, "show_view_all": {"label": "Aktiviere die Schaltfläche \"Alle anzeigen\", wenn die Liste mehr Kategorien enthält, als angezeigt werden"}, "columns_desktop": {"label": "Anzahl der Spalten auf dem Desktop"}, "header_mobile": {"content": "Mobiles Layout"}, "columns_mobile": {"label": "Anzahl der Spalten auf mobilem Gerät", "options__1": {"label": "1 Spalte"}, "options__2": {"label": "2 Spalten"}}}, "blocks": {"featured_collection": {"name": "<PERSON><PERSON><PERSON>", "settings": {"collection": {"label": "<PERSON><PERSON><PERSON>"}}}}, "presets": {"name": "Kategorieliste"}}, "contact-form": {"name": "Kontaktformular", "presets": {"name": "Kontaktformular"}}, "custom-liquid": {"name": "Benutzerdefiniertes Liquid", "settings": {"custom_liquid": {"label": "Benutzerdefiniertes Liquid", "info": "Füge App-Snippets oder anderen Liquid-Code hinzu, um fortgeschrittene Anpassungen vorzunehmen."}}, "presets": {"name": "Benutzerdefiniertes Liquid"}}, "featured-blog": {"name": "Blog-Beiträge", "settings": {"heading": {"label": "Überschrift"}, "blog": {"label": "Blog"}, "post_limit": {"label": "Anzahl der anzuzeigenden Blog-Beiträge"}, "show_view_all": {"label": "Aktiviere die Schaltfläche \"Alle anzeigen\", wenn der Blog mehr Blog-Beiträge enthält, als angezeigt werden"}, "show_image": {"label": "Feature-<PERSON><PERSON><PERSON> anzeigen", "info": "Verwende für Bilder ein Seitenverhältnis von 3:2 für optimale Ergebnisse. [Mehr Informationen](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "show_date": {"label": "<PERSON><PERSON> anzeigen"}, "show_author": {"label": "<PERSON><PERSON> <PERSON>"}, "columns_desktop": {"label": "Anzahl der Spalten auf dem Desktop"}}, "presets": {"name": "Blog-Beiträge"}}, "featured-collection": {"name": "Vorgestellte Kategorie", "settings": {"title": {"label": "Überschrift"}, "collection": {"label": "<PERSON><PERSON><PERSON>"}, "products_to_show": {"label": "Maximal anzuzeigende Produkte"}, "show_view_all": {"label": "\"Alles anzeigen\" aktivier<PERSON>, wenn die Kategorie mehr Produkte enthält, als angezeigt werden"}, "header": {"content": "Produktkarte"}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Porträt"}, "options__3": {"label": "<PERSON>uadratisch"}}, "show_secondary_image": {"label": "Hover-Effekt mit zweitem Bild"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON> anzeigen"}, "show_rating": {"label": "Produktbewertung anzeigen", "info": "Um eine Bewertung anzuzeigen, musst du eine Produktbewertungs-App hinzufügen. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-collection-show-product-rating)"}, "enable_quick_buy": {"label": "Schaltfläche zum schnellen Hinzufügen aktivieren", "info": "Optimal bei Warenkörben des Typs Pop-up oder Einschub."}, "columns_desktop": {"label": "Anzahl der Spalten auf dem Desktop"}, "description": {"label": "Beschreibung"}, "show_description": {"label": "Kategoriebeschreibung im Adminbereich anzeigen"}, "description_style": {"label": "Beschreibungsstil", "options__1": {"label": "Nachricht"}, "options__2": {"label": "Untertitel"}, "options__3": {"label": "Großbuchstaben"}}, "view_all_style": {"options__1": {"label": "Link"}, "options__2": {"label": "Umriss-Schaltfläche"}, "options__3": {"label": "Durchgehende Schaltfläche"}, "label": "Stil \"Alles anzeigen\""}, "enable_desktop_slider": {"label": "Ka<PERSON>ell auf Desktop aktivieren"}, "full_width": {"label": "Produkte auf gesamte Breite auslegen"}, "header_mobile": {"content": "Mobiles Layout"}, "columns_mobile": {"label": "Anzahl der Spalten auf mobilem Gerät", "options__1": {"label": "1 Spalte"}, "options__2": {"label": "2 Spalten"}}, "swipe_on_mobile": {"label": "Wischen auf Mobilgeräten aktivieren"}}, "presets": {"name": "Vorgestellte Kategorie"}}, "footer": {"name": "Fußzeile", "blocks": {"link_list": {"name": "<PERSON><PERSON>", "settings": {"heading": {"label": "Überschrift", "info": "Überschrift ist erforderlich, um das Menü anzuzeigen."}, "menu": {"label": "<PERSON><PERSON>", "info": "<PERSON>eigt nur Top-Level-Menüpunkte an."}}}, "text": {"name": "Text", "settings": {"heading": {"label": "Überschrift"}, "subtext": {"label": "Subtext"}}}}, "settings": {"newsletter_enable": {"label": "E-Mail-Anmeldung anzeigen"}, "newsletter_heading": {"label": "Überschrift"}, "header__1": {"content": "E-Mail-Anmeldung", "info": "<PERSON><PERSON><PERSON><PERSON>, die automatisch zu deiner Kundenliste \"Akzeptiert Marketing\" hinzugefügt wurden. [Mehr Informationen](https://help.shopify.com/manual/customers/manage-customers)"}, "header__2": {"content": "Social Media-Symbole", "info": "Um deine Social Media-Konten anzuzeigen, verlinke sie in deinen Theme-Einstellungen."}, "show_social": {"label": "Social-Media-Symbole anzeigen"}, "header__3": {"content": "Auswahl für Land/Region"}, "header__4": {"info": "Gehe zu den [Zahlungseinstellungen](/admin/settings/payments), um ein Land / eine Region hinzuzufügen."}, "enable_country_selector": {"label": "Auswahl für Land/Region aktivieren"}, "header__5": {"content": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "header__6": {"info": "Gehe zu den [Spracheinstellungen](/admin/settings/languages), um eine Sprache hinzuzufügen."}, "enable_language_selector": {"label": "Sprachauswahl aktivieren"}, "header__7": {"content": "Zahlungsmethoden"}, "payment_enable": {"label": "Zahlungssymbole anzeigen"}, "margin_top": {"label": "<PERSON>berer Rand"}}}, "header": {"name": "Header", "settings": {"logo": {"label": "Logo-Bild"}, "logo_width": {"unit": "Pixel", "label": "Breite des benutzerdefinierten Logos"}, "logo_position": {"label": "Desktop-Logoposition", "options__1": {"label": "Mitte links"}, "options__2": {"label": "Oben links"}, "options__3": {"label": "<PERSON><PERSON>"}, "info": "Positionen werden automatisch für die mobile Nutzung optimiert."}, "menu": {"label": "<PERSON><PERSON>"}, "show_line_separator": {"label": "Trennlinie anzeigen"}, "enable_sticky_header": {"label": "Fixierten Header aktivieren", "info": "Der Header wird auf dem Bildschirm angezeigt, wenn der Kunde nach oben scrollt."}, "margin_bottom": {"label": "Un<PERSON>er Rand"}, "menu_type_desktop": {"label": "Desktop-Menütyp", "info": "Der Menütyp wird automatisch für die mobile Nutzung optimiert.", "options__1": {"label": "Dropdown"}, "options__2": {"label": "Mega-Menü"}}}}, "image-banner": {"name": "Bild-Banner", "settings": {"image": {"label": "<PERSON><PERSON><PERSON> Bild"}, "image_2": {"label": "Zweites Bild"}, "color_scheme": {"info": "<PERSON><PERSON><PERSON>, wenn Container angezeigt wird."}, "stack_images_on_mobile": {"label": "Gestapelte Bilder auf Mobilgeräten"}, "adapt_height_first_image": {"label": "Abschnittshöhe an Größe des ersten Bildes anpassen", "info": "Überschreibt bei Überprüfung die Einstellung für die Höhe des Bild-Banners."}, "show_text_box": {"label": "Container auf dem Desktop anzeigen"}, "image_overlay_opacity": {"label": "Deckkraft der Bildüberlagerung"}, "header": {"content": "Mobiles Layout"}, "show_text_below": {"label": "Container auf Mobilgerät anzeigen"}, "image_height": {"label": "Bannerhöhe", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "info": "Verwende für Bilder ein Seitenverhältnis von 3:2 für optimale Ergebnisse. [Mehr Informationen](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "desktop_content_position": {"options__1": {"label": "Oben links"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON> rechts"}, "options__4": {"label": "Mitte links"}, "options__5": {"label": "<PERSON><PERSON>"}, "options__6": {"label": "<PERSON><PERSON> rechts"}, "options__7": {"label": "Unten links"}, "options__8": {"label": "<PERSON>ten zent<PERSON>t"}, "options__9": {"label": "Unten rechts"}, "label": "Desktopinhaltsposition"}, "desktop_content_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Desktopinhaltsausrichtung"}, "mobile_content_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Mobile Inhaltsausrichtung"}}, "blocks": {"heading": {"name": "Titel", "settings": {"heading": {"label": "Überschrift"}}}, "text": {"name": "Text", "settings": {"text": {"label": "Beschreibung"}, "text_style": {"options__1": {"label": "Nachricht"}, "options__2": {"label": "Untertitel"}, "options__3": {"label": "Großbuchstaben"}, "label": "Textstil"}}}, "buttons": {"name": "Schaltflächen", "settings": {"button_label_1": {"label": "Erste Beschriftung der Schaltfläche", "info": "Lasse die Beschriftung leer, um die Schaltfläche auszublenden."}, "button_link_1": {"label": "Erster Link der Schaltfläche"}, "button_style_secondary_1": {"label": "Umriss-Stil für Schaltfläche verwenden"}, "button_label_2": {"label": "Zweite Beschriftung der Schaltfläche", "info": "Lasse die Beschriftung leer, um die Schaltfläche auszublenden."}, "button_link_2": {"label": "Zweiter Link der Schaltfläche"}, "button_style_secondary_2": {"label": "Umriss-Stil für Schaltfläche verwenden"}}}}, "presets": {"name": "Bild-Banner"}}, "image-with-text": {"name": "Bild mit Text", "settings": {"image": {"label": "Bild"}, "height": {"options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Bildhöhe"}, "layout": {"options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Zweites Bild"}, "label": "Desktop-Bildplatzierung", "info": "Das Standardlayout für Mobilgeräte ist \"Bild zuerst\"."}, "desktop_image_width": {"options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Desktop-Bildbreite", "info": "Bild wird automatisch für die mobile Nutzung optimiert."}, "desktop_content_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Desktop-Inhaltsausrichtung"}, "desktop_content_position": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "Unten"}, "label": "Desktop-Inhaltsposition"}, "content_layout": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Überlappung"}, "label": "Layout des Inhalts"}, "mobile_content_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Mobile Inhaltsausrichtung"}}, "blocks": {"heading": {"name": "Titel", "settings": {"heading": {"label": "Überschrift"}}}, "text": {"name": "Text", "settings": {"text": {"label": "Inhalt"}, "text_style": {"label": "Textstil", "options__1": {"label": "Nachricht"}, "options__2": {"label": "Untertitel"}}}}, "button": {"name": "Schaltfläche", "settings": {"button_label": {"label": "Schaltflächenbeschriftung", "info": "Lasse die Beschriftung leer, um die Schaltfläche auszublenden."}, "button_link": {"label": "Schaltflächenlink"}}}, "caption": {"name": "Bildtext", "settings": {"text": {"label": "Text"}, "text_style": {"label": "Textstil", "options__1": {"label": "Untertitel"}, "options__2": {"label": "Großbuchstaben"}}, "caption_size": {"label": "Textgröße", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "presets": {"name": "Bild mit Text"}}, "main-article": {"name": "Blog-Beitrag", "blocks": {"featured_image": {"name": "Feature-Bild", "settings": {"image_height": {"label": "<PERSON><PERSON><PERSON> des Feature-Bildes", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "info": "Verwende für Bilder ein Seitenverhältnis von 16:9 für optimale Ergebnisse. [Mehr Informationen](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)", "options__4": {"label": "<PERSON><PERSON><PERSON>"}}}}, "title": {"name": "Titel", "settings": {"blog_show_date": {"label": "<PERSON><PERSON> anzeigen"}, "blog_show_author": {"label": "<PERSON><PERSON> <PERSON>"}}}, "content": {"name": "Inhalt"}, "share": {"name": "Teilen", "settings": {"featured_image_info": {"content": "<PERSON><PERSON> <PERSON> e<PERSON> Link in Social-Media-Posts einfügst, wird das Feature-Bild der Seite als Vorschaubild angezeigt. [Mehr Informationen](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)"}, "title_info": {"content": "Ein Titel und eine Beschreibung des Shops sind im Vorschaubild enthalten. [Mehr Informationen](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"}, "text": {"label": "Text"}}}}}, "main-blog": {"name": "Blog-Beiträge", "settings": {"header": {"content": "Blog-Beitrags-Karte"}, "show_image": {"label": "Feature-<PERSON><PERSON><PERSON> anzeigen"}, "paragraph": {"content": "Bearbeite deine Blog-Beiträge, um Auszüge zu ändern. [Mehr Informationen](https://help.shopify.com/manual/online-store/blogs/writing-blogs#display-an-excerpt-from-a-blog-post)"}, "show_date": {"label": "<PERSON><PERSON> anzeigen"}, "show_author": {"label": "<PERSON><PERSON> <PERSON>"}, "layout": {"label": "Desktop-Layout", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Collage"}, "info": "Beiträge werden bei der mobilen Nutzung gestapelt."}, "image_height": {"label": "<PERSON><PERSON><PERSON> des Feature-Bildes", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}, "info": "Verwende für Bilder ein Seitenverhältnis von 3:2 für optimale Ergebnisse. [Mehr Informationen](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "main-cart-footer": {"name": "Zwischensumme", "settings": {"show_cart_note": {"label": "Warenkorbanmerkung aktivieren"}}, "blocks": {"subtotal": {"name": "Zwischensumme"}, "buttons": {"name": "Checkout-Schaltfläche"}}}, "main-cart-items": {"name": "Artikel", "settings": {"show_vendor": {"label": "<PERSON><PERSON><PERSON> anzeigen"}}}, "main-collection-banner": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"paragraph": {"content": "Bearbeite deine Kategorien, um eine Beschreibung oder ein Bild hinzuzufügen. [Mehr Informationen](https://help.shopify.com/manual/products/collections/collection-layout)"}, "show_collection_description": {"label": "Kategoriebeschreibung anzeigen"}, "show_collection_image": {"label": "Kategoriebild anzeigen", "info": "Verwende für Bilder ein Seitenverhältnis von 16:9 für optimale Ergebnisse. [Mehr Informationen](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "main-collection-product-grid": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"products_per_page": {"label": "Produkte pro Seite"}, "enable_filtering": {"label": "Filtern aktivieren", "info": "[Filter](/admin/menus) anpassen"}, "enable_sorting": {"label": "Sortieren aktivieren"}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Porträt"}, "options__3": {"label": "<PERSON>uadratisch"}}, "show_secondary_image": {"label": "Hover-Effekt mit zweitem Bild"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON> anzeigen"}, "header__1": {"content": "Filtern und Sortieren"}, "header__3": {"content": "Produktkarte"}, "enable_tags": {"label": "Filtern aktivieren", "info": "[Filter anpassen](/admin/menus)"}, "collapse_on_larger_devices": {"label": "Auf Desktop minimieren"}, "show_rating": {"label": "Produktbewertung anzeigen", "info": "Um eine Bewertung anzuzeigen, musst du eine Produktbewertungs-App hinzufügen. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-grid-section-settings)"}, "enable_quick_buy": {"label": "Schaltfläche zum schnellen Hinzufügen aktivieren", "info": "Optimal bei Warenkörben des Typs Pop-up oder Einschub."}, "columns_desktop": {"label": "Anzahl der Spalten auf dem Desktop"}, "header_mobile": {"content": "Mobiles Layout"}, "columns_mobile": {"label": "Anzahl der Spalten auf mobilem Gerät", "options__1": {"label": "1 Spalte"}, "options__2": {"label": "2 Spalten"}}, "filter_type": {"label": "Desktopfilterlayout", "options__1": {"label": "Horizontal"}, "options__2": {"label": "Vertikal"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "info": "Das Standardlayout für Mobilgeräte ist \"Einschub\"."}}}, "main-list-collections": {"name": "Listenseite für Kategorien", "settings": {"title": {"label": "Überschrift"}, "sort": {"label": "<PERSON><PERSON><PERSON> sortieren nach:", "options__1": {"label": "Alphabetisch, A-Z"}, "options__2": {"label": "Alphabetisch, Z-A"}, "options__3": {"label": "<PERSON><PERSON>, neu zu alt"}, "options__4": {"label": "<PERSON><PERSON>, alt zu neu"}, "options__5": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>, hoch zu niedrig"}, "options__6": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ni<PERSON><PERSON> zu hoch"}}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Porträt"}, "options__3": {"label": "<PERSON>uadratisch"}, "info": "Bearbeite deine Kategorien, um Bilder hinzuzufügen. [Mehr Informationen](https://help.shopify.com/manual/products/collections)"}, "columns_desktop": {"label": "Anzahl der Spalten auf dem Desktop"}, "header_mobile": {"content": "Mobiles Layout"}, "columns_mobile": {"label": "Anzahl der Spalten auf mobilem Gerät", "options__1": {"label": "1 Spalte"}, "options__2": {"label": "2 Spalten"}}}}, "main-page": {"name": "Seite"}, "main-password-footer": {"name": "Passwort-Fußzeile"}, "main-password-header": {"name": "Passwort-Header", "settings": {"logo": {"label": "Logo-Bild"}, "logo_max_width": {"label": "Breite des benutzerdefinierten Logos", "unit": "Pixel"}}}, "main-product": {"blocks": {"text": {"name": "Text", "settings": {"text": {"label": "Text"}, "text_style": {"label": "Textstil", "options__1": {"label": "Nachricht"}, "options__2": {"label": "Untertitel"}, "options__3": {"label": "Großbuchstaben"}}}}, "title": {"name": "Titel"}, "price": {"name": "Pre<PERSON>"}, "quantity_selector": {"name": "Men<PERSON>aus<PERSON><PERSON>"}, "variant_picker": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"picker_type": {"label": "Art", "options__1": {"label": "Dropdown"}, "options__2": {"label": "<PERSON><PERSON>ln"}}}}, "buy_buttons": {"name": "Buy Buttons", "settings": {"show_dynamic_checkout": {"label": "Dynamischer Checkout-<PERSON><PERSON> anzeigen", "info": "Wenn sie die Zahlungsmethoden verwenden, die in deinem Shop verfügbar sind, sehen <PERSON> ihre bevorzugte Option, z. B. PayPal oder Apple Pay. [Mehr Informationen](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}}}, "pickup_availability": {"name": "Verfügbarkeit von Abholungen"}, "description": {"name": "Beschreibung"}, "share": {"name": "Teilen", "settings": {"featured_image_info": {"content": "<PERSON><PERSON> <PERSON> e<PERSON> Link in Social-Media-Posts einfügst, wird das Feature-Bild der Seite als Vorschaubild angezeigt. [Mehr Informationen](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "Ein Titel und eine Beschreibung des Shops sind im Vorschaubild enthalten. [Mehr Informationen](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}, "text": {"label": "Text"}}}, "collapsible_tab": {"name": "Einklappbare Reihe", "settings": {"heading": {"info": "Füge eine Überschrift ein, die den Inhalt erklärt.", "label": "Überschrift"}, "content": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "page": {"label": "Reiheninhalt der Seite"}, "icon": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON>"}, "options__4": {"label": "Flasche"}, "options__5": {"label": "Box"}, "options__6": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__7": {"label": "Chat-Blase"}, "options__8": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__9": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__10": {"label": "<PERSON><PERSON><PERSON>"}, "options__11": {"label": "Laktosef<PERSON>i"}, "options__12": {"label": "<PERSON><PERSON><PERSON>"}, "options__13": {"label": "<PERSON><PERSON>"}, "options__14": {"label": "<PERSON><PERSON>"}, "options__15": {"label": "Glutenfrei"}, "options__16": {"label": "<PERSON><PERSON>"}, "options__17": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options__18": {"label": "<PERSON><PERSON>"}, "options__19": {"label": "<PERSON><PERSON>"}, "options__20": {"label": "Blitz"}, "options__21": {"label": "Lippenstift"}, "options__22": {"label": "<PERSON><PERSON><PERSON>"}, "options__23": {"label": "Pinnnadel"}, "options__24": {"label": "<PERSON><PERSON>"}, "label": "Symbol", "options__25": {"label": "<PERSON><PERSON>"}, "options__26": {"label": "Pfotenabdruck"}, "options__27": {"label": "<PERSON><PERSON><PERSON>"}, "options__28": {"label": "Parfüm"}, "options__29": {"label": "Flugzeug"}, "options__30": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__31": {"label": "<PERSON><PERSON><PERSON>"}, "options__32": {"label": "Fragezeichen"}, "options__33": {"label": "Recyclen"}, "options__34": {"label": "Rückgabe"}, "options__35": {"label": "Lineal"}, "options__36": {"label": "Servierteller"}, "options__37": {"label": "<PERSON><PERSON><PERSON>"}, "options__38": {"label": "<PERSON><PERSON><PERSON>"}, "options__39": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__40": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options__41": {"label": "Stern"}, "options__42": {"label": "Stoppuhr"}, "options__43": {"label": "Lieferwagen"}, "options__44": {"label": "<PERSON><PERSON><PERSON>"}}}}, "popup": {"name": "Pop-up", "settings": {"link_label": {"label": "Link-Label"}, "page": {"label": "Seite"}}}, "custom_liquid": {"name": "Benutzerdefiniertes Liquid", "settings": {"custom_liquid": {"label": "Benutzerdefiniertes Liquid", "info": "Füge App-Snippets oder anderen Liquid-Code hinzu, um fortgeschrittene Anpassungen vorzunehmen."}}}, "rating": {"name": "Produktbewertung", "settings": {"paragraph": {"content": "Um eine Bewertung anzuzeigen, musst du eine Produktbewertungs-App hinzufügen. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-rating-block)"}}}}, "settings": {"header": {"content": "Medien", "info": "Mehr Informationen über [Medienarten](https://help.shopify.com/manual/products/product-media)."}, "enable_video_looping": {"label": "Videoschleife aktivieren"}, "enable_sticky_info": {"label": "Fixierte Inhalte für Desktop aktivieren"}, "hide_variants": {"label": "Medien anderer <PERSON> aus<PERSON>, sobald eine Variante ausgewählt wurde"}, "gallery_layout": {"label": "Desktop-Layout", "options__1": {"label": "Gestapelt"}, "options__2": {"label": "Vorschaubilder"}, "options__3": {"label": "Karussell mit Vorschaubildern"}}, "media_size": {"label": "Mediengröße für Desktop", "options__1": {"label": "<PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "info": "Medien werden automatisch für die mobile Nutzung optimiert."}, "mobile_thumbnails": {"label": "Mobiles Layout", "options__1": {"label": "Vorschaubilder anzeigen"}, "options__2": {"label": "Vorschaubilder ausblenden"}}}, "name": "Produktinformationen"}, "main-search": {"name": "Suchergebnisse", "settings": {"image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Porträt"}, "options__3": {"label": "<PERSON>uadratisch"}}, "show_secondary_image": {"label": "Hover-Effekt mit zweitem Bild"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON> anzeigen"}, "header__1": {"content": "Produktkarte"}, "header__2": {"content": "Blog-Karte"}, "article_show_date": {"label": "<PERSON><PERSON> anzeigen"}, "article_show_author": {"label": "<PERSON><PERSON> <PERSON>"}, "show_rating": {"label": "Produktbewertung anzeigen", "info": "Um eine Bewertung anzuzeigen, musst du eine Produktbewertungs-App hinzufügen. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#search-results-section-settings)"}, "columns_desktop": {"label": "Anzahl der Spalten auf dem Desktop"}, "header_mobile": {"content": "Mobiles Layout"}, "columns_mobile": {"label": "Anzahl der Spalten auf mobilem Gerät", "options__1": {"label": "1 Spalte"}, "options__2": {"label": "2 Spalten"}}}}, "multicolumn": {"name": "<PERSON>t mehreren <PERSON>", "settings": {"title": {"label": "Überschrift"}, "image_width": {"label": "Bildbreite", "options__1": {"label": "Drittelbreite der Spalte"}, "options__2": {"label": "Halbe Breite der Spalte"}, "options__3": {"label": "Ganze Breite der Spalte"}}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Porträt"}, "options__3": {"label": "<PERSON>uadratisch"}, "options__4": {"label": "Kreis"}}, "column_alignment": {"label": "Spaltenausrichtung", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}}, "background_style": {"label": "Sekundärer Hintergrund", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Als Spaltenhintergrund anzeigen"}}, "button_label": {"label": "Schaltflächenbeschriftung"}, "button_link": {"label": "Schaltflächenlink"}, "swipe_on_mobile": {"label": "Wischen auf Mobilgeräten aktivieren"}, "columns_desktop": {"label": "Anzahl der Spalten auf dem Desktop"}, "header_mobile": {"content": "Mobiles Layout"}, "columns_mobile": {"label": "Anzahl der Spalten auf mobilem Gerät", "options__1": {"label": "1 Spalte"}, "options__2": {"label": "2 Spalten"}}}, "blocks": {"column": {"name": "<PERSON>lt<PERSON>", "settings": {"image": {"label": "Bild"}, "title": {"label": "Überschrift"}, "text": {"label": "Beschreibung"}, "link_label": {"label": "Link-Label"}, "link": {"label": "Link"}}}}, "presets": {"name": "<PERSON>t mehreren <PERSON>"}}, "newsletter": {"name": "E-Mail-Anmeldung", "settings": {"full_width": {"label": "Abschnitt über die gesamte Breite"}, "paragraph": {"content": "Durch jedes E-Mail-Abonnement wird ein Kundenkonto erstellt. [Mehr Informationen](https://help.shopify.com/manual/customers)"}}, "blocks": {"heading": {"name": "Titel", "settings": {"heading": {"label": "Überschrift"}}}, "paragraph": {"name": "Unter-Überschrift", "settings": {"paragraph": {"label": "Beschreibung"}}}, "email_form": {"name": "E-Mail-Formular"}}, "presets": {"name": "E-Mail-Anmeldung"}}, "page": {"name": "Seite", "settings": {"page": {"label": "Seite"}}, "presets": {"name": "Seite"}}, "product-recommendations": {"name": "Produktempfehlungen", "settings": {"heading": {"label": "Überschrift"}, "header__2": {"content": "Produktkarte"}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "Porträt"}, "options__3": {"label": "<PERSON>uadratisch"}}, "show_secondary_image": {"label": "Hover-Effekt mit zweitem Bild"}, "show_vendor": {"label": "<PERSON><PERSON><PERSON> anzeigen"}, "paragraph__1": {"content": "Dynamische Empfehlungen nutzen Bestell- und Produktinformationen, um sich mit der Zeit zu verändern und zu verbessern. [Weitere Informationen](https://help.shopify.com/themes/development/recommended-products)"}, "show_rating": {"label": "Produktbewertung anzeigen", "info": "Um eine Bewertung anzuzeigen, musst du eine Produktbewertungs-App hinzufügen. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-recommendations-section-settings)"}, "columns_desktop": {"label": "Anzahl der Spalten auf dem Desktop"}, "header_mobile": {"content": "Mobiles Layout"}, "columns_mobile": {"label": "Anzahl der Spalten auf mobilem Gerät", "options__1": {"label": "1 Spalte"}, "options__2": {"label": "2 Spalten"}}, "products_to_show": {"label": "Maximal anzuzeigende Produkte"}}}, "rich-text": {"name": "Rich Text", "settings": {"full_width": {"label": "Abschnitt über die gesamte Breite"}}, "blocks": {"heading": {"name": "Titel", "settings": {"heading": {"label": "Überschrift"}}}, "text": {"name": "Text", "settings": {"text": {"label": "Beschreibung"}}}, "button": {"name": "Schaltfläche", "settings": {"button_label": {"label": "Schaltflächenbeschriftung"}, "button_link": {"label": "Schaltflächenlink"}, "button_style_secondary": {"label": "Umriss-Stil für Schaltfläche verwenden"}}}}, "presets": {"name": "Rich Text"}}, "apps": {"name": "Apps", "settings": {"include_margins": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> so gestalten wie das Theme"}}, "presets": {"name": "Apps"}}, "video": {"name": "Video", "settings": {"heading": {"label": "Titel"}, "cover_image": {"label": "Titelbild"}, "video_url": {"label": "URL", "placeholder": "YouTube- oder Vimeo-URL verwenden", "info": "Video wird auf der Seite abgespielt."}, "description": {"label": "Video-Alt-Text", "info": "Beschreibe das Video für Kunden, die Bildschirmlesegeräte benutzen. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video)"}, "image_padding": {"label": "Bild-Padding hinzufügen", "info": "<PERSON><PERSON><PERSON>e Bild-Padding aus, wenn du nicht möchtest, dass dein Titelbild abgeschnitten wird."}, "full_width": {"label": "Abschnitt über die gesamte Breite"}}, "presets": {"name": "Video"}}, "featured-product": {"name": "Vorgestelltes Produkt", "blocks": {"text": {"name": "Text", "settings": {"text": {"label": "Text"}, "text_style": {"label": "Textstil", "options__1": {"label": "Nachricht"}, "options__2": {"label": "Untertitel"}, "options__3": {"label": "Großbuchstaben"}}}}, "title": {"name": "Titel"}, "price": {"name": "Pre<PERSON>"}, "quantity_selector": {"name": "Men<PERSON>aus<PERSON><PERSON>"}, "variant_picker": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"picker_type": {"label": "Art", "options__1": {"label": "Dropdown"}, "options__2": {"label": "<PERSON><PERSON>ln"}}}}, "buy_buttons": {"name": "Buy Buttons", "settings": {"show_dynamic_checkout": {"label": "Dynamischen Checkout-<PERSON><PERSON> anzeigen", "info": "Wenn sie die Zahlungsmethoden verwenden, die in deinem Shop verfügbar sind, sehen <PERSON> ihre bevorzugte Option, z. B. PayPal oder Apple Pay. [Mehr Informationen](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}}}, "description": {"name": "Beschreibung"}, "share": {"name": "Teilen", "settings": {"featured_image_info": {"content": "<PERSON><PERSON> <PERSON> e<PERSON> Link in Social-Media-Posts einfügst, wird das Feature-Bild der Seite als Vorschaubild angezeigt. [Mehr Informationen](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)"}, "title_info": {"content": "Ein Titel und eine Beschreibung des Shops sind im Vorschaubild enthalten. [Mehr Informationen](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"}, "text": {"label": "Text"}}}, "custom_liquid": {"name": "Benutzerdefiniertes Liquid", "settings": {"custom_liquid": {"label": "Benutzerdefiniertes Liquid"}}}, "rating": {"name": "Produktbewertung", "settings": {"paragraph": {"content": "Um eine Bewertung anzuzeigen, musst du eine Produktbewertungs-App hinzufügen. [Mehr Informationen](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-product-rating)"}}}}, "settings": {"product": {"label": "Produkt"}, "secondary_background": {"label": "Sekundären Hintergrund anzeigen"}, "header": {"content": "Medien", "info": "Mehr Informationen zu [Medienarten](https://help.shopify.com/manual/products/product-media)"}, "enable_video_looping": {"label": "Videoschleife aktivieren"}, "hide_variants": {"label": "Medien von nicht ausgewählten Varianten auf dem Desktop ausblenden"}}, "presets": {"name": "Vorgestelltes Produkt"}}, "email-signup-banner": {"name": "Banner für E-Mail-Anmeldung", "settings": {"paragraph": {"content": "Durch jedes E-Mail-Abonnement wird ein Kundenkonto erstellt. [Mehr Informationen](https://help.shopify.com/manual/customers)"}, "image": {"label": "Hintergrundbild"}, "show_background_image": {"label": "Hintergrundbild anzeigen"}, "show_text_box": {"label": "Container auf dem Desktop anzeigen"}, "image_overlay_opacity": {"label": "Deckkraft der Bildüberlagerung"}, "color_scheme": {"info": "<PERSON><PERSON><PERSON>, wenn Container angezeigt wird."}, "show_text_below": {"label": "Auf Mobilgeräten Inhalt unterhalb der Bilder anzeigen", "info": "Verwende Bilder mit einem Seitenverhältnis von 16:9 für optimale Ergebnisse. [Mehr Informationen](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "image_height": {"label": "Bannerhöhe", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}, "info": "Verwende Bilder mit einem Seitenverhältnis von 16:9 für optimale Ergebnisse. [Mehr Informationen](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "desktop_content_position": {"options__1": {"label": "Oben links"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON> rechts"}, "options__4": {"label": "Mitte links"}, "options__5": {"label": "<PERSON><PERSON>"}, "options__6": {"label": "<PERSON><PERSON> rechts"}, "options__7": {"label": "Unten links"}, "options__8": {"label": "<PERSON>ten zent<PERSON>t"}, "options__9": {"label": "Unten rechts"}, "label": "Desktopinhaltsposition"}, "desktop_content_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Desktopinhaltsausrichtung"}, "header": {"content": "Mobiles Layout"}, "mobile_content_alignment": {"options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "label": "Mobile Inhaltsausrichtung"}}, "blocks": {"heading": {"name": "Titel", "settings": {"heading": {"label": "Titel"}}}, "paragraph": {"name": "Absatz", "settings": {"paragraph": {"label": "Beschreibung"}, "text_style": {"options__1": {"label": "Nachricht"}, "options__2": {"label": "Untertitel"}, "label": "Textstil"}}}, "email_form": {"name": "E-Mail-Formular"}}, "presets": {"name": "Banner für E-Mail-Anmeldung"}}, "slideshow": {"name": "Slideshow", "settings": {"layout": {"label": "Layout", "options__1": {"label": "Volle Breite"}, "options__2": {"label": "<PERSON><PERSON>"}}, "slide_height": {"label": "Diahö<PERSON>", "options__1": {"label": "An erstes Bild anpassen"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}, "options__4": {"label": "<PERSON><PERSON><PERSON>"}}, "slider_visual": {"label": "Seitennummerierungsstil", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Punkte"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "auto_rotate": {"label": "Autorotieren der Slides"}, "change_slides_speed": {"label": "Slides überall ändern"}, "show_text_below": {"label": "Auf Mobilgeräten Inhalt unterhalb der Bilder anzeigen"}, "mobile": {"content": "Mobiles Layout"}, "accessibility": {"content": "Barrierefreiheit", "label": "Slideshow-Beschreibung", "info": "Beschreibe die Slideshow für Kunden, die Bildschirmlesegeräte benutzen."}}, "blocks": {"slide": {"name": "Folie", "settings": {"image": {"label": "Bild"}, "heading": {"label": "Titel"}, "subheading": {"label": "Unter-Überschrift"}, "button_label": {"label": "Schaltflächenbeschriftung", "info": "Lasse die Beschriftung leer, um die Schaltfläche auszublenden."}, "link": {"label": "Schaltflächenlink"}, "secondary_style": {"label": "Umriss-Stil für Schaltfläche verwenden"}, "box_align": {"label": "Desktop-Inhaltsposition", "options__1": {"label": "Oben links"}, "options__2": {"label": "<PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON> rechts"}, "options__4": {"label": "Mitte links"}, "options__5": {"label": "<PERSON><PERSON> mittig"}, "options__6": {"label": "<PERSON><PERSON> rechts"}, "options__7": {"label": "Unten links"}, "options__8": {"label": "Unten mittig"}, "options__9": {"label": "Unten rechts"}, "info": "Positionen werden automatisch für die mobile Nutzung optimiert."}, "show_text_box": {"label": "Container auf dem Desktop anzeigen"}, "text_alignment": {"label": "Desktop-Inhaltsausrichtung", "option_1": {"label": "Links"}, "option_2": {"label": "<PERSON><PERSON><PERSON>"}, "option_3": {"label": "<PERSON><PERSON><PERSON>"}}, "image_overlay_opacity": {"label": "Deckkraft der Bildüberlagerung"}, "color_scheme": {"info": "<PERSON><PERSON><PERSON>, wenn Container angezeigt wird."}, "text_alignment_mobile": {"label": "Mobile Inhaltsausrichtung", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "presets": {"name": "Slideshow"}}, "collapsible_content": {"name": "Einklappbarer Inhalt", "settings": {"caption": {"label": "Bildtext"}, "heading": {"label": "Überschrift"}, "heading_alignment": {"label": "Ausrichtung der Überschrift", "options__1": {"label": "Links"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "layout": {"label": "Layout", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Reihencontainer"}, "options__3": {"label": "Abschnittscontainer"}}, "open_first_collapsible_row": {"label": "<PERSON><PERSON><PERSON> e<PERSON>bare Reihe ö<PERSON>"}, "header": {"content": "Bildlayout"}, "image": {"label": "Bild"}, "image_ratio": {"label": "Bildverhältnis", "options__1": {"label": "An Bild anpassen"}, "options__2": {"label": "<PERSON>"}, "options__3": {"label": "<PERSON><PERSON><PERSON>"}}, "desktop_layout": {"label": "Desktop-Layout", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Bild an zweiter Stelle"}, "info": "Das Bild wird auf mobilen Geräte immer zuerst angezeigt."}, "container_color_scheme": {"label": "Farbschema für Container", "info": "Wird ange<PERSON>, wenn für das Layout Zeilen- oder Abschnitts-Container festgelegt wird."}}, "blocks": {"collapsible_row": {"name": "Einklappbare Reihe", "settings": {"heading": {"info": "Füge eine Überschrift ein, die den Inhalt erklärt.", "label": "Überschrift"}, "row_content": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "page": {"label": "Reiheninhalt der Seite"}, "icon": {"label": "Symbol", "options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "<PERSON><PERSON><PERSON>"}, "options__3": {"label": "<PERSON><PERSON>"}, "options__4": {"label": "Flasche"}, "options__5": {"label": "Box"}, "options__6": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__7": {"label": "Chat-Blase"}, "options__8": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__9": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__10": {"label": "<PERSON><PERSON><PERSON>"}, "options__11": {"label": "Laktosef<PERSON>i"}, "options__12": {"label": "<PERSON><PERSON><PERSON>"}, "options__13": {"label": "<PERSON><PERSON>"}, "options__14": {"label": "<PERSON><PERSON>"}, "options__15": {"label": "Glutenfrei"}, "options__16": {"label": "<PERSON><PERSON>"}, "options__17": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options__18": {"label": "<PERSON><PERSON>"}, "options__19": {"label": "<PERSON><PERSON>"}, "options__20": {"label": "Blitz"}, "options__21": {"label": "Lippenstift"}, "options__22": {"label": "<PERSON><PERSON><PERSON>"}, "options__23": {"label": "Pinnnadel"}, "options__24": {"label": "<PERSON><PERSON>"}, "options__25": {"label": "<PERSON><PERSON>"}, "options__26": {"label": "Pfotenabdruck"}, "options__27": {"label": "<PERSON><PERSON><PERSON>"}, "options__28": {"label": "Parfüm"}, "options__29": {"label": "Flugzeug"}, "options__30": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__31": {"label": "<PERSON><PERSON><PERSON>"}, "options__32": {"label": "Fragezeichen"}, "options__33": {"label": "Recyclen"}, "options__34": {"label": "Rückgabe"}, "options__35": {"label": "Lineal"}, "options__36": {"label": "Servierteller"}, "options__37": {"label": "<PERSON><PERSON><PERSON>"}, "options__38": {"label": "<PERSON><PERSON><PERSON>"}, "options__39": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__40": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "options__41": {"label": "Stern"}, "options__42": {"label": "Stoppuhr"}, "options__43": {"label": "Lieferwagen"}, "options__44": {"label": "<PERSON><PERSON><PERSON>"}}}}}, "presets": {"name": "Einklappbarer Inhalt"}}}}