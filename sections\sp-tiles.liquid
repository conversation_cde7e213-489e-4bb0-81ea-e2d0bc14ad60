{% comment %}
  © Sections Pro. You are free to use this section in your store. You may not redistribute this section in another Shopify app.
{% endcomment %}
<style>

  

  {%- capture sp_content -%} 

  {% if section.settings.override_fonts %}
      {{ section.settings.text_font | font_face }}
      {{ section.settings.headline_font | font_face }}
  {% endif %}

  #spro-{{ section.id }} p {
      {% if section.settings.override_text_sizes %}
      font-size: {{ section.settings.text_size }}px;
      {% endif %}
      {% if section.settings.override_fonts %}
      font-family: {{ section.settings.text_font.family }}, {{ section.settings.text_font.fallback_families }};
      font-weight: {{ section.settings.text_font.weight }};
      {% endif %}
      {% if section.settings.text_line_height != 'inherit' %}line-height: {{ section.settings.text_line_height }};{% endif %}
      margin: 0 0 {{ section.settings.element_spacing }}px 0;
      padding: 0;
      {% if section.settings.override_text_colors %}
      color: {{ section.settings.text_color }};
      {% endif %}
  }

  #spro-{{ section.id }} div.spro-richtext {
    margin: 0 0 {{ section.settings.element_spacing }}px 0;
  }

  #spro-{{ section.id }} ul, #spro-{{ section.id }} ol {
    margin: 0 0 {{ section.settings.element_spacing }}px 0;
  }

  #spro-{{ section.id }} li {
    {% if section.settings.override_text_sizes %}
    font-size: {{ section.settings.text_size }}px;
    {% endif %}
    {% if section.settings.override_fonts %}
    font-family: {{ section.settings.text_font.family }}, {{ section.settings.text_font.fallback_families }};
    font-weight: {{ section.settings.text_font.weight }};
    {% endif %}
    {% if section.settings.text_line_height != 'inherit' %}line-height: {{ section.settings.text_line_height }};{% endif %}
    margin: 0 0 5px 0;
    padding: 0;
    {% if section.settings.override_text_colors %}
    color: {{ section.settings.text_color }};
    {% endif %}
  }

  #spro-{{ section.id }} li:last-child {
    margin: 0;
  }

  #spro-{{ section.id }} p a,
  #spro-{{ section.id }} p a:visited
  #spro-{{ section.id }} li a,
  #spro-{{ section.id }} li a:visited {
    {% if section.settings.override_text_colors %}
    color: {{ section.settings.link_color }};
    {% endif %}
  }

  @media (max-width: 767px) {
      #spro-{{ section.id }} p,
      #spro-{{ section.id }} li {
        {% if section.settings.override_text_sizes %}
        font-size: {{ section.settings.mobile_text_size }}px;
        {% endif %}
      }
  }

  #spro-{{ section.id }} h1,
  #spro-{{ section.id }} h2,
  #spro-{{ section.id }} h3,
  #spro-{{ section.id }} h4,
  #spro-{{ section.id }} h5 {
      {% if section.settings.override_text_sizes %}
      font-size: {{ section.settings.headline_size }}px;
      {% endif %}
      {% if section.settings.override_fonts %}
      font-family: {{ section.settings.headline_font.family }}, {{ section.settings.headline_font.fallback_families }};
      font-weight: {{ section.settings.headline_font.weight }};
      {% endif %}
      {% if section.settings.headline_line_height != 'inherit' %}line-height: {{ section.settings.headline_line_height }};{% endif %}
      margin: 0 0 {{ section.settings.element_spacing }}px 0;
      padding: 0;
      {% if section.settings.override_text_colors %}
      color: {{ section.settings.text_color }};
      {% endif %}
  }

  @media (max-width: 767px) {
      #spro-{{ section.id }} h1,
      #spro-{{ section.id }} h2,
      #spro-{{ section.id }} h3,
      #spro-{{ section.id }} h4,
      #spro-{{ section.id }} h5 {
        {% if section.settings.override_text_sizes %}
          font-size: {{ section.settings.mobile_headline_size }}px;
        {% endif %}
      }
  }

  #spro-{{ section.id }} h2 {
    {% if section.settings.override_text_sizes %}
      font-size: {{ section.settings.headline_size | minus:5  | at_least:13 }}px;
    {% endif %}
  }

  #spro-{{ section.id }} h3 {
    {% if section.settings.override_text_sizes %}
      font-size: {{ section.settings.headline_size | minus:10 | at_least:13 }}px;
    {% endif %}
  }

  #spro-{{ section.id }} h4 {
    {% if section.settings.override_text_sizes %}
      font-size: {{ section.settings.headline_size | minus:15 | at_least:13 }}px;
    {% endif %}
  }

  #spro-{{ section.id }} h5 {
    {% if section.settings.override_text_sizes %}
      font-size: {{ section.settings.headline_size | minus:20 | at_least:13 }}px;
    {% endif %}
  }

  @media (max-width: 767px) {
      #spro-{{ section.id }} h2 {
        {% if section.settings.override_text_sizes %}
          font-size: {{ section.settings.mobile_headline_size | minus:5 | at_least:13 }}px;
        {% endif %}
      }

      #spro-{{ section.id }} h3 {
        {% if section.settings.override_text_sizes %}
          font-size: {{ section.settings.mobile_headline_size | minus:10 | at_least:13 }}px;
        {% endif %}
      }

      #spro-{{ section.id }} h4 {
        {% if section.settings.override_text_sizes %}
        font-size: {{ section.settings.mobile_headline_size | minus:15 | at_least:13 }}px;
      {% endif %}
      }

      #spro-{{ section.id }} h5 {
        {% if section.settings.override_text_sizes %}
          font-size: {{ section.settings.mobile_headline_size | minus:20 | at_least:13 }}px;
        {% endif %}
      }
  }

  #spro-{{ section.id }} {
      background-image: {{ section.settings.section_background_color }};
      {% if section.settings.section_background_image %}
          background: {% if section.settings.section_background_image_color %}{{ section.settings.section_background_image_color }}{%endif%} url({{ section.settings.section_background_image | image_url }});
          background-repeat: no-repeat;
          background-position: center;
          width: 100%;
          background-size: {{ section.settings.section_background_size }};
      {% endif %}
      width: 100%;
      box-sizing: border-box;
      padding: {{ section.settings.section_padding_top_bottom }}px {{ section.settings.section_padding_left_right }}px;
      overflow: hidden;
  }

  {% if section.settings.mobile_section_background_image %}
  @media (max-width: 767px) {
    #spro-{{ section.id }} {
          background: {% if section.settings.mobile_section_background_image_color %}{{ section.settings.mobile_section_background_image_color }}{%endif%} url({{ section.settings.mobile_section_background_image | image_url }});
          background-repeat: no-repeat;
          background-position: center;
          width: 100%;
          background-size: {{ section.settings.mobile_section_background_size }};
    }
  }
  {% endif %}

  @media (max-width: 767px) {
      #spro-{{ section.id }} {
        padding: {{ section.settings.mobile_section_padding_top_bottom }}px {{ section.settings.mobile_section_padding_left_right }}px;
      }
  }

  {% if section.settings.show_on_device == 'mobile' %}
    @media (min-width: 768px) {
        #spro-{{ section.id }} {
            display: none;
        }
    }
  {% endif %}

  {% if section.settings.show_on_device == 'desktop' %}
    @media (max-width: 767px) {
        #spro-{{ section.id }} {
            display: none;
        }
    }
  {% endif %}

  #spro-{{ section.id }} .spro-container {
      position: relative;
      margin: 0 auto;
      background-image: {{ section.settings.container_background_color }};
      border-radius: {{ section.settings.container_radius }}px;
      {% if section.settings.container_shadow %}box-shadow: 0 0 5px 0 rgba(0,0,0,0.20);{% endif %}
      border: {{ section.settings.container_border_size }}px solid {{ section.settings.container_border_color }};
      max-width: {{ section.settings.container_max_width }}px;
      padding: {{ section.settings.container_padding_top_bottom }}px {{ section.settings.container_padding_left_right }}px;
  }

  @media (max-width: 767px) {
      #spro-{{ section.id }} .spro-container {
      padding: {{ section.settings.mobile_container_padding_top_bottom }}px {{ section.settings.mobile_container_padding_left_right }}px;
      }
  }


  {% assign short_id = section.id | slice: -3, 3 %}

  #spro-{{ section.id }} .spro-headline {
    margin: 0;
    padding: 0 0 {{ section.settings.headline_spacing }}px 0;
  }

  #spro-{{ section.id }} .spro-headline * {
    text-align: {{ section.settings.text_alignment }};
  }

  @media only screen and (max-width: 800px) {
    #spro-{{ section.id }} .spro-headline * {
      text-align: {{ section.settings.mobile_text_alignment }};
    }
  }

  /* grid */
  #spro-{{ section.id }}  .spro-container .spro-grid {
    display: grid;
    align-items: center;
    gap: {{ section.settings.grid_gap}}px;
    position: relative;
    z-index: 2;
  }

  @media only screen and (min-width: 801px) {
    #spro-{{ section.id }} .spro-container .spro-grid {
      display: grid;
      grid-template-columns: 60% 1fr;
    }

    #spro-{{ section.id }} .spro-container .spro-grid .spro-tile:first-child {
      grid-row: 1 / {{ section.blocks.size }};
    }
  }

  @media only screen and (max-width: 800px) {
    #spro-{{ section.id }} .spro-container .spro-grid {
      display: grid;
      grid-template-columns: {{ section.settings.mobile_columns }};
      gap: {{ section.settings.mobile_gap }}px;
    }
  }

  #spro-{{ section.id }}  .spro-container .spro-cover {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;
    width: 100%;
    height: 100%;
    background-image: {{section.settings.cover_background}};
    opacity: {{section.settings.cover_opacity}}%;
    transition: all .5s;
  }

  #spro-{{ section.id }}  .spro-tile:hover .spro-cover {
    opacity: {{section.settings.cover_opacity | plus: 15 }}%;
  }

  #spro-{{ section.id }} .spro-tile {
    display: block;
    position: relative;
    min-height: {{ section.settings.tile_height }}px;
    height: 100%;
    padding: 0;
    overflow: hidden;
    border-radius: {{ section.settings.media_radius }}px;
    {% if section.settings.media_shadow %}box-shadow: 0 0 5px 0 rgba(0,0,0,0.20);{% endif %}
  }

  #spro-{{ section.id }} .spro-img {
    position: absolute;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 1;
    transition: all .5s;
  }

  #spro-{{ section.id }} .spro-tile:hover .spro-img {
    transform: scale(1.2);
  }
  
  #spro-{{ section.id }} .spro-content {
    z-index: 3;
    padding: 15px;
    background-size: cover;
    background-position: center;
    position: absolute;
    bottom: 0;
    left: 0;
  }

  #spro-{{ section.id }} .spro-content h4 {
    margin: 0 0 5px 0;
    padding: 0;
    color: {{ section.settings.tile_text_color }};
    font-size: {{ section.settings.tile_text_size }}px;
    {% if section.settings.show_text_shadow %}text-shadow: 0 2px 4px rgba(0,0,0,0.25);{% endif %}
    text-align: left;
  }

  #spro-{{ section.id }} .spro-content p {
    margin: 0;
    padding: 0;
    color: {{ section.settings.tile_text_color }};
    {% if section.settings.show_text_shadow %}text-shadow: 0 2px 4px rgba(0,0,0,0.25);{% endif %}
    text-align: left;
  }

  #spro-{{ section.id }} .spro-content p {
    margin: 0;
    padding: 0;
    text-align: left;
  }

  /* carousel */
  #spro-{{ section.id }} .spro-snap-links {
    padding: 0;
}

  #spro-snap-links-{{ section.id }} a {
      display: inline-block;
      background-color: {{ section.settings.highlight_color }};
      width: 12px;
      height: 12px;
      border-radius: 50%;
      text-indent: -9999px;
      padding: 0;
      margin: 0 2.5px; 
      cursor: pointer;
      opacity: 0.25;
  }

  #spro-snap-links-{{ section.id }} a[active] {
      background-color: {{ section.settings.highlight_color }};
      opacity: 1;
  }

  #spro-snap-links-{{ section.id }} {
    display: none;
  }

  @media only screen and (max-width: 800px) {
    #spro-snap-links-{{ section.id }} {
      display: block;
      text-align: center;
    }
  }

  {%- endcapture -%} 

  {%- liquid
    assign chunks = sp_content | strip_newlines | split: ' ' | join: ' ' | split: '*/'
    for chunk in chunks
      assign mini = chunk | split: '/*' | first | strip | replace: ': ', ':' | replace: '; ', ';' | replace: '} ', '}' | replace: '{ ', '{' | replace: ' {', '{' | replace: ';}', '}'
      echo mini
    endfor
  %}
</style>

<div id="spro-{{ section.id }}" class="spro-section" spro-section>

<div class="spro-container" spro-container>

  {% if section.settings.headline != '' or section.settings.text != '' %}
    <div class="spro-headline" spro-column>
      {% if section.settings.headline != '' %}<h2>{{ section.settings.headline }}</h2>{% endif %}
      {% if section.settings.text != '' %}<p>{{ section.settings.text }}</p>{% endif %}
    </div>
  {% endif %}

  <div id="spro-grid-{{ section.id }}" class="spro-grid">

    {% for block in section.blocks %}
      <{% if block.settings.link %}a{% else %}div{% endif %} id="spro-block-{{ section.id }}-{{ forloop.index }}" data-index="{{ forloop.index | minus: 1 }}" {% if block.settings.link %}href="{{ block.settings.link }}"{% endif %} class="spro-tile" spro-column>
          {% if block.settings.background_image %}
          <img loading="lazy" class="spro-img"
            src="{{ block.settings.background_image | image_url: width: section.settings.image_width }}"
            srcset="{{ block.settings.background_image| image_url: width: section.settings.mobile_image_width }} 600w,
              {{ block.settings.background_image | image_url: width: section.settings.mobile_image_width }} 800w,
              {{ block.settings.background_image | image_url: width: section.settings.image_width }} 1000w,
              {{ block.settings.background_image | image_url: width: section.settings.image_width }} 1100w">
          {% else %}
            <img class="spro-img" src="data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg version='1.1' viewBox='0 0 500 500' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Crect x='2' y='2' width='496' height='496' fill='%23D8D8D8' stroke='%23D8D8D8' stroke-width='4'/%3E%3C/g%3E%3C/svg%3E">
          {% endif %}
        <div class="spro-cover"></div>
        <div class="spro-content">
          <h4>{{ block.settings.headline }}</h4>
          {% if block.settings.text %}<p>{{ block.settings.text }}</p>{% endif %} 
        </div>
      </{% if block.settings.link %}a{% else %}div{% endif %}>
      <!-- /.spro-col -->

      {% endfor %}

  </div>
  <!-- /.spro-grid -->

  <div id="spro-snap-links-{{ section.id }}" class="spro-snap-links">
    {% for block in section.blocks %}
      <a {% if forloop.index == 1 %}active{% endif %} data-index="{{ forloop.index | minus: 1 }}"></a>
    
    {% endfor %}
  </div>
  
</div>
<!-- /.spro-container -->

</div>
<!-- /.spro-section -->


{% schema %}
  {
    "name": "🚀 Tiles",
    "settings": [
      
    {
        "type": "header",
        "content": "Font",
        "info": "Set the fonts for your section. If overriding, the theme fonts will be ignored."
    },
    {
        "type": "checkbox",
        "id": "override_fonts",
        "label": "Override theme fonts",
        "default": false
    },
    {
        "type": "font_picker",
        "id": "headline_font",
        "label": "Headline Font",
        "default": "sans-serif",
        "visible_if": "{{ section.settings.override_fonts == true }}"
    },
    {
        "type": "font_picker",
        "id": "text_font",
        "label": "Text Font",
        "default": "sans-serif",
        "visible_if": "{{ section.settings.override_fonts == true }}"
    },
    {
        "type": "header",
        "content": "Text",
        "info": "Set the text for your section. If overriding, the theme text styles will be ignored."
    },
    {
        "type": "checkbox",
        "id": "override_text_sizes",
        "label": "Override text sizes",
        "default": false
    },
    {
        "type": "range",
        "id": "text_size",
        "min": 10,
        "max": 50,
        "step": 1,
        "unit": "px",
        "label": "Text Size",
        "default": 15,
        "visible_if": "{{ section.settings.override_text_sizes == true }}"
    },
    {
        "type": "range",
        "id": "mobile_text_size",
        "min": 10,
        "max": 50,
        "step": 1,
        "unit": "px",
        "label": "Mobile Text Size",
        "default": 15,
        "visible_if": "{{ section.settings.override_text_sizes == true }}"
    },
    {
        "type": "select",
        "id": "text_line_height",
        "label": "Text Line Height",
        "options": [
            {
                "value": "0.75",
                "label": "0.75"
            },
            {
                "value": "1.0",
                "label": "1.0"
            },
            {
                "value": "1.1",
                "label": "1.1"
            },
            {
                "value": "1.2",
                "label": "1.2"
            },
            {
                "value": "1.3",
                "label": "1.3"
            },
            {
                "value": "1.4",
                "label": "1.4"
            },
            {
                "value": "1.5",
                "label": "1.5"
            },
            {
                "value": "1.6",
                "label": "1.6"
            },
            {
                "value": "1.7",
                "label": "1.7"
            },
            {
                "value": "1.8",
                "label": "1.8"
            },
            {
                "value": "1.9",
                "label": "1.9"
            },
            {
                "value": "2.0",
                "label": "2.0"
            },
            {
                "value": "inherit",
                "label": "Inherit"
            }
        ],
        "default": "inherit",
        "visible_if": "{{ section.settings.override_text_sizes == true }}"
    },
    {
        "type": "range",
        "id": "headline_size",
        "min": 10,
        "max": 75,
        "step": 1,
        "unit": "px",
        "label": "Headline Size",
        "default": 40,
        "visible_if": "{{ section.settings.override_text_sizes == true }}"
    },
    {
        "type": "range",
        "id": "mobile_headline_size",
        "min": 10,
        "max": 75,
        "step": 1,
        "unit": "px",
        "label": "Mobile Headline Size",
        "default": 40,
        "visible_if": "{{ section.settings.override_text_sizes == true }}"
    },
    {
        "type": "select",
        "id": "headline_line_height",
        "label": "Headline Line Height",
        "options": [
            {
                "value": "0.75",
                "label": "0.75"
            },
            {
                "value": "1.0",
                "label": "1.0"
            },
            {
                "value": "1.1",
                "label": "1.1"
            },
            {
                "value": "1.2",
                "label": "1.2"
            },
            {
                "value": "1.3",
                "label": "1.3"
            },
            {
                "value": "1.4",
                "label": "1.4"
            },
            {
                "value": "1.5",
                "label": "1.5"
            },
            {
                "value": "1.6",
                "label": "1.6"
            },
            {
                "value": "1.7",
                "label": "1.7"
            },
            {
                "value": "1.8",
                "label": "1.8"
            },
            {
                "value": "1.9",
                "label": "1.9"
            },
            {
                "value": "2.0",
                "label": "2.0"
            },
            {
                "value": "inherit",
                "label": "Inherit"
            }
        ],
        "default": "inherit",
        "visible_if": "{{ section.settings.override_text_sizes == true }}"
    },
    {
        "type": "checkbox",
        "id": "override_text_colors",
        "label": "Override text colors",
        "default": false
    },
    {
        "type": "color",
        "id": "text_color",
        "default": "#111",
        "label": "Text Color",
        "visible_if": "{{ section.settings.override_text_colors == true }}"
    },
    {
        "type": "color",
        "id": "link_color",
        "default": "#005bd3",
        "label": "Link Color",
        "visible_if": "{{ section.settings.override_text_colors == true }}"
    },
    {
        "type": "header",
        "content": "Section Design",
        "info": "Set the design for the section"
    },
    {
        "type": "select",
        "id": "show_on_device",
        "label": "Show Section",
        "options": [
            {
                "value": "all",
                "label": "All Devices"
            },
            {
                "value": "mobile",
                "label": "Mobile Only"
            },
            {
                "value": "desktop",
                "label": "Desktop Only"
            }
        ],
        "default": "all"
    },
    {
        "type": "color_background",
        "id": "section_background_color",
        "default": "linear-gradient(127deg, rgba(241, 246, 251, 1) 11%, rgba(241, 246, 251, 1) 81%)",
        "label": "Background Color"
    },
    {
        "type": "paragraph",
        "content": "Set the Background Image (optional)"
    },
    {
        "type": "image_picker",
        "id": "section_background_image",
        "label": "Background Image"
    },
    {
        "type": "color",
        "id": "section_background_image_color",
        "label": "Background Image Color"
    },
    {
        "type": "select",
        "id": "section_background_size",
        "default": "cover",
        "label": "Background Image Size",
        "options": [
          {
            "value": "auto",
            "label": "Auto"
          },
          {
            "value": "cover",
            "label": "Cover"
          },
          {
            "value": "contain",
            "label": "Contain"
          }
        ]
    },
    {
        "type": "paragraph",
        "content": "Set the Mobile Background Image (optional)"
    },
    {
        "type": "image_picker",
        "id": "mobile_section_background_image",
        "label": "Mobile Background Image"
    },
    {
        "type": "color",
        "id": "mobile_section_background_image_color",
        "label": "Mobile Background Image Color"
    },
    {
        "type": "select",
        "id": "mobile_section_background_size",
        "default": "cover",
        "label": "Mobile Background Image Size",
        "options": [
          {
            "value": "auto",
            "label": "Auto"
          },
          {
            "value": "cover",
            "label": "Cover"
          },
          {
            "value": "contain",
            "label": "Contain"
          }
        ]
    },
    {
        "type": "paragraph",
        "content": "Set the padding for the section"
    },
    {
        "type": "number",
        "id": "section_padding_top_bottom",
        "default": 25,
        "label": "Padding Top/Bottom"
    },
    {
        "type": "number",
        "id": "mobile_section_padding_top_bottom",
        "default": 25,
        "label": "Mobile Padding Top/Bottom"
    },
    {
        "type": "number",
        "id": "section_padding_left_right",
        "default": 25,
        "label": "Padding Left/Right"
    },
    {
        "type": "number",
        "id": "mobile_section_padding_left_right",
        "default": 25,
        "label": "Mobile Padding Left/Right"
    },
    {
        "type": "header",
        "content": "Container Design",
        "info": "Set the design for your inner container"
    },
    {
        "type": "color_background",
        "id": "container_background_color",
        "label": "Background Color"
    },
    {
        "type": "number",
        "id": "container_max_width",
        "default": 1000,
        "label": "Max Width"
    },
    {
        "type": "number",
        "id": "container_padding_top_bottom",
        "default": 10,
        "label": "Padding Top/Bottom"
    },
    {
        "type": "number",
        "id": "mobile_container_padding_top_bottom",
        "default": 10,
        "label": "Mobile Padding Top/Bottom"
    },
    {
        "type": "number",
        "id": "container_padding_left_right",
        "default": 10,
        "label": "Padding Left/Right"
    },
    {
        "type": "number",
        "id": "mobile_container_padding_left_right",
        "default": 10,
        "label": "Mobile Padding Left/Right"
    },
    {
        "type": "number",
        "id": "element_spacing",
        "default": 15,
        "label": "Spacing Between Elements"
    },
    {
        "type": "range",
        "id": "container_radius",
        "min": 0,
        "max": 50,
        "step": 1,
        "unit": "px",
        "label": "Round Borders on Container",
        "default": 0
    },
    {
        "type": "checkbox",
        "id": "container_shadow",
        "default": false,
        "label": "Subtle Shadow on Container"
    },
    {
        "type": "range",
        "id": "container_border_size",
        "min": 0,
        "max": 50,
        "step": 1,
        "unit": "px",
        "label": "Border Size on Container",
        "default": 0
    },
    {
        "type": "color",
        "id": "container_border_color",
        "default": "#888",
        "label": "Border Color on Container"
    }
,
      {
        "type": "header",
        "content": "Tile Design",
        "info": "Set settings for the grid"
      },
      {
        "type": "checkbox",
        "id": "show_text_shadow",
        "default": false,
        "label": "Show Text Shadow"
      },
      {
        "type": "color",
        "id": "tile_text_color",
        "default": "#fff",
        "label": "Tile Text Color"
      },
      {
        "type": "range",
        "id": "tile_text_size",
        "min": 10,
        "max": 50,
        "step": 1,
        "unit": "px",
        "label": "Tile Text Size",
        "default": 22
    },
      {
        "type": "number",
        "id": "tile_height",
        "default": 250,
        "label": "Tile Height"
      },
      {
        "type": "number",
        "id": "grid_gap",
        "default": 20,
        "label": "Gap Between Tiles"
      },
      {
        "type": "header",
        "content": "Media Design",
        "info": "Set settings for the media in the grid"
      },
      {
        "type": "range",
        "id": "media_radius",
        "min": 0,
        "max": 50,
        "step": 1,
        "unit": "px",
        "label": "Round Borders on Media",
        "default": 5
      },
      {
        "type": "checkbox",
        "id": "media_shadow",
        "default": true,
        "label": "Subtle Shadow on Media"
      },
      {
        "type": "number",
        "id": "image_width",
        "default": 1000,
        "label": "Image Width"
      },
      {
        "type": "number",
        "id": "mobile_image_width",
        "default": 1000,
        "label": "Mobile Image Width"
      },
      {
        "type": "header",
        "content": "Mobile Display",
        "info": "Set the mobile display"
      },
      {
        "type": "select",
        "id": "mobile_columns",
        "label": "Mobile Columns",
        "options": [
          {
            "value": "1fr",
            "label": "1 Column"
          },
          {
            "value": "1fr 1fr",
            "label": "2 Columns"
          }
        ]
      },
      {
        "type": "number",
        "id": "mobile_gap",
        "default": 10,
        "label": "Mobile Gap Between Tiles"
      },
      {
        "type": "header",
        "content": "Headline",
        "info": "Set text for the headline"
      },
      {
          "type": "text_alignment",
          "id": "text_alignment",
          "label": "Text Alignment",
          "default": "center"
      },
      {
          "type": "text_alignment",
          "id": "mobile_text_alignment",
          "label": "Mobile Text Alignment",
          "default": "center"
      },
      {
        "type": "inline_richtext",
        "id": "headline",
        "label": "Headline",
        "default": "<b>Sample Headline</b>"
      },
      {
        "type": "inline_richtext",
        "id": "text",
        "label": "Text",
        "default": "Use this block to add a description. Leave blank to remove."
      },
      {
        "type": "number",
        "id": "headline_spacing",
        "default": 15,
        "label": "Spacing between Headline and Features"
      }
    ],
	"blocks": [
      {
       "name": "Tile",
       "type": "tile",
       "settings": [
          {
            "type": "image_picker",
            "id": "background_image",
            "label": "Background Image"
          },
          {
            "type": "inline_richtext",
            "id": "headline",
            "label": "Headline",
            "default": "<b>Feature Headline</b>"
          },
          {
            "type": "inline_richtext",
            "id": "text",
            "label": "Text (optional)"
          },
          {
            "type": "url",
            "id": "link",
            "label": "Tile link (optional)"
          }
       ]
      }
    ],
	"presets": [
      {
        "name": "🚀 Tiles",
        "blocks": [
          {
            "type": "tile"
          },
          {
            "type": "tile"
          },
          {
            "type": "tile"
          }
        ]
      }
    ]
  }
{% endschema %}