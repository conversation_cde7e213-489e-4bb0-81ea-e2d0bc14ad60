.collection-list-type-2 .card-wrapper {color:inherit; height:100%; position:relative; text-decoration:none;}
.collection-list-type-2 .card {text-decoration:none; text-align:var(--text-alignment);}
.collection-list-type-2 .card:not(.ratio) {display:flex; flex-direction:column; height:100%;}
.collection-list-type-2 .card--card {height:100%;}
.collection-list-type-2 .card--card, .card--standard .card__inner {position:relative; box-sizing:border-box; border-radius:var(--border-radius); border:var(--border-width) solid rgba(var(--color-foreground), var(--border-opacity));}
.collection-list-type-2 .card--card:after, .card--standard .card__inner:after {content:''; position:absolute; z-index:-1; width:calc(var(--border-width) * 2 + 100%); height:calc(var(--border-width) * 2 + 100%); top:calc(var(--border-width) * -1); left:calc(var(--border-width) * -1); border-radius:var(--border-radius); box-shadow:var(--shadow-horizontal-offset) var(--shadow-vertical-offset) var(--shadow-blur-radius) rgba(var(--color-shadow), var(--shadow-opacity));}
.collection-list-type-2 .card .card__inner .card__media {overflow:hidden; z-index:0; border-radius:calc(var(--border-radius) - var(--border-width) - var(--card-image-padding));}
.collection-list-type-2 .card--standard.card--text {background-color:transparent;}
.collection-list-type-2 .card-information {text-align:var(--text-alignment);}
.collection-list-type-2 .card__media, .card .media {bottom:0; position:absolute; top:0;}
.collection-list-type-2 .card .media {width:100%;}
.collection-list-type-2 .card__media {margin:var(--card-image-padding); width:calc(100% - 2 * var(--card-image-padding));}
.collection-list-type-2 .card--standard .card__media {margin:var(--card-image-padding);}
.collection-list-type-2 .card__inner {width:100%; overflow:hidden;}
.collection-list-type-2 .card__content .grid-view-hidden {display:none;}
.collection-list-type-2 .list-view-filter .card__content .rte.grid-view-hidden {position:relative; margin:0; line-height:3rem; display:block; line-height:normal; margin-bottom:0.5rem; max-width:800px;}
.collection-list-type-2 .card--media .card__inner .card__content {padding:calc(var(--card-card-image-padding) + 1rem); position:relative; pointer-events:none; overflow:hidden; border-radius:calc(var(--border-radius) - var(--border-width) - var(--card-image-padding));}
.collection-list-type-2 .card__content {display:grid; grid-template-rows:minmax(0, 1fr) max-content minmax(0, 1fr); padding:1rem; width:100%; flex-grow:1;}
.collection-list-type-2 .card__content--auto-margins {grid-template-rows:minmax(0, auto) max-content minmax(0, auto);}
.collection-list-type-2 .card__information {grid-row-start:1;}
.collection-list-type-2 .card:not(.ratio) > .card__content {grid-template-rows:max-content minmax(0, 1fr) max-content auto;}
.collection-list-type-2 .product-icons a:empty {display:block;}
.collection-list-type-2 .product-icons a.add-compare:before, .product-icons a.add-wishlist:before {display:block; content:""; width:20px; height:20px; line-height:15px; -webkit-mask-repeat:no-repeat; -webkit-mask-size:contain; -webkit-mask-position:center;}
.collection-list-type-2 .product-icons {z-index:2; pointer-events:none; right:0; top:10px; position:absolute; justify-content:center; opacity:0; display:flex; transition:0.3s linear all; list-style:none; flex-direction:column; padding:0; margin:0;}
.collection-list-type-2 .product-icons li {margin:5px; pointer-events:all; position:relative; transition:all 0.3s linear;}
ul.product-icons.top-aligned {top:0; bottom:auto; transform:initial;}
.collection-list-type-2 .product-icons.center-aligned {z-index:2; pointer-events:none; left:0; right:0; top:50%; transform:translateY(-50%); padding:15px; position:absolute; justify-content:center; opacity:0; display:flex; transition:0.3s linear all; list-style:none;}
.collection-list-type-2 .product-icons li a:not(.adding).add-compare:before {-webkit-mask-image:url("compare-icons.svg"); mask-image:url("compare-icons.svg"); background:currentColor;}
.collection-list-type-2 .product-icons li a:not(.adding).added.add-compare:before {-webkit-mask-image:url("compare-fill.svg"); mask-image:url("compare-fill.svg"); background:currentColor;}
.collection-list-type-2 .product-icons li a:not(.adding).add-wishlist:before {-webkit-mask-image:url("wishlist-icons.svg"); mask-image:url("wishlist-icons.svg"); background:currentColor;}
.collection-list-type-2 .product-icons li a:not(.adding).added.add-wishlist:before {-webkit-mask-image:url("wishlist-fill-icons.svg"); mask-image:url("wishlist-fill-icons.svg"); background:currentColor;}
.collection-list-type-2 .product-icons li a.adding:before {position:absolute; z-index:1; content:""; width:15px; height:15px; background-color:currentColor; -webkit-mask-image:url(loading-icon.gif); mask-image:url(loading-icon.gif); -webkit-mask-position:center; left:0; right:0; bottom:0; top:0; margin:auto;}
.collection-list-type-2 .product-icons a.add-wishlist:before, .product-icons a.add-compare:before {content:""; width:18px; height:18px; line-height:18px; -webkit-mask-repeat:no-repeat; -webkit-mask-size:contain; -webkit-mask-position:center; color:currentcolor;}
.collection-list-type-2 .card-wrapper.underline-links-hover .card:hover .card__inner .product-icons {opacity:1; right:10px;}
.collection-list-type-2 .card__inner .product-icons button.quick-add__submit:disabled, .quick-add__submit:disabled, .quick-add__submit[aria-disabled="true"], .quick-add__submit.disabled, .quick-add__submit:disabled, .quick-add__submit[aria-disabled="true"], .quick-add__submit.disabled {cursor:not-allowed; opacity:0.5;}
.collection-list-type-2 .card__inner .product-icons button span.sold-out-message {display:none;}
/* .collection-list-type-2 .card__inner .product-icons a, .card__inner .product-icons button {display:grid; place-items:center; border-radius:0%; width:30px; height:30px; margin:0; border:none; cursor:pointer; transition:var(--duration-default) linear all; background-color:rgb(var(--color-base-solid-button-labels)); color:rgb(var(--color-solid-text)); opacity:1; padding:0;} */

@media screen and (max-width: 480px) {
.collection-list-type-2 .card-wrapper.underline-links-hover .card .card__inner .product-icons {opacity:1; right:10px;}
}

@media screen and (max-width: 991px) {}
.collection-list-type-2 .card__badge {align-self:flex-end; grid-row-start:3; justify-self:flex-start;}
.collection-list-type-2 .card__badge.top {align-self:flex-start; grid-row-start:1;}
.collection-list-type-2 .card__badge.right {justify-self:flex-end;}
.collection-list-type-2 .card > .card__content > .card__badge {margin:1.3rem;}
.collection-list-type-2 .card__media .media img {height:100%; object-fit:cover; object-position:top center; width:100%;}
.collection-list-type-2 .card__media .media .motion-reduce {opacity:0; transition:all var(--duration-default) linear;}
.collection-list-type-2 .card__media .media .motion-reduce.loaded-image:first-child {animation:2s cubic-bezier(.26,.54,.32,1) forwards fadeIn; -webkit-animation:2s cubic-bezier(.26,.54,.32,1) forwards fadeIn;}

@-webkit-keyframes fadeIn {
from {opacity:0;}
to {opacity:1;}
}

@keyframes fadeIn {
from {opacity:0;}
to {opacity:1;}
}

.collection-list-type-2 .fadeIn {-webkit-animation-name:fadeIn; animation-name:fadeIn;}
.collection-list-type-2 .card__inner:not(.ratio) > .card__content {height:100%;}
.collection-list-type-2 .card__heading {margin-top:0; margin-bottom:0;}
.collection-list-type-2 .card__heading:last-child {margin-bottom:0;}
.collection-list-type-2 .card--card.card--media > .card__content {margin-top:calc(0rem - var(--card-image-padding));}
.collection-list-type-2 .card--standard > .card__content .card__information h3.card__heading {margin:15px 0;}
.collection-list-type-2 .card__heading a:after {outline-offset:0.3rem;}
.collection-list-type-2 .card__heading a:focus:after {box-shadow:0 0 0 0.3rem rgb(var(--color-background)), 0 0 0.5rem 0.4rem rgba(var(--color-foreground), 0.3); outline:0.2rem solid rgba(var(--color-foreground), 0.5);}
.collection-list-type-2 .card__heading a:focus-visible:after {box-shadow:0 0 0 0.3rem rgb(var(--color-background)), 0 0 0.5rem 0.4rem rgba(var(--color-foreground), 0.3); outline:0.2rem solid rgba(var(--color-foreground), 0.5);}
.collection-list-type-2 .card__heading a:focus:not(:focus-visible):after {box-shadow:none; outline:0;}
.collection-list-type-2 .card__heading a:focus {box-shadow:none; outline:0;}

@media screen and (min-width: 990px) {
.collection-list-type-2 .card .media.media--hover-effect > img:only-child, .card-wrapper .media.media--hover-effect > img:only-child {transition:transform var(--duration-long) ease;}
.collection-list-type-2 .card:hover .media.media--hover-effect > img:first-child:only-child, .card-wrapper:hover .media.media--hover-effect > img:first-child:only-child {transform:scale(1.1);}
.collection-list-type-2 .card-wrapper:hover .media.media--hover-effect > img:first-child:not(:only-child) {opacity:0;}
.collection-list-type-2 .card-wrapper:hover .media.media--hover-effect > img + img {opacity:1; transition:all var(--duration-long) linear; transform:scale(1.03);}
.collection-list-type-2 .underline-links-hover a {transition:all 0.3s linear;}
}

.collection-list-type-2 .card--standard.card--media .card__inner .card__information, .card--standard > .card__content .card__badge, .card--standard.card--text.article-card > .card__content .card__information, .card--standard > .card__content .card__caption {display:none;}
.collection-list-type-2 .card--standard > .card__content {padding:0;}
.collection-list-type-2 .card--standard > .card__content .card__information {padding-left:0; padding-right:0;}
.collection-list-type-2 .card--card.card--media .card__inner .card__information, .card--card.card--text .card__inner, .card--card.card--media > .card__content .card__badge, .list-view-filter .card-wrapper .card__inner .card__information {display:none;}
.collection-list-type-2 .card--extend-height {height:100%;}
.collection-list-type-2 .card--extend-height.card--standard.card--text, .card--extend-height.card--media {display:flex; flex-direction:column;}
.collection-list-type-2 .card--extend-height.card--standard.card--text .card__inner, .card--extend-height.card--media .card__inner {flex-grow:1;}
.collection-list-type-2 .card .icon-wrap {margin-left:0.8rem; white-space:nowrap; transition:transform var(--duration-short) ease; overflow:hidden;}
.collection-list-type-2 .card-information > * + * {margin-top:0.5rem;}
.collection-list-type-2 .card-information {width:100%; line-height:normal;}
.collection-list-type-2 .card__information > * {margin-bottom:6px;}
.collection-list-type-2 .card-information > * {line-height:calc(1 + 0.4 / var(--font-body-scale)); color:rgb(var(--color-foreground));}
.collection-list-type-2 .card-information > .price {color:rgb(var(--color-foreground));}
.collection-list-type-2 .card-information > .rating {margin-top:0rem;}
.collection-list-type-2 .card-information > *:not(.visually-hidden:first-child) + *:not(.rating) {margin-top:0.7rem;}
.collection-list-type-2 .card-information .caption {letter-spacing:0.07rem;}
.collection-list-type-2 .card-article-info {margin-top:1rem;}
.collection-list-type-2 .card__content .variant-option-color li.color-values a {position:relative; display:flex; align-items:center; justify-content:center; margin:0px 5px 0px 0; width:30px; height:30px; border-radius:50%; transition:all 0.3s linear; border:1px solid transparent; background-color:transparent;}
.collection-list-type-2 .card__content ul.variant-option-color li.color-values a.active {border:1px solid rgba(var(--color-base-accent-1), 0.3);}
.collection-list-type-2 .card__content ul[class*="variant-option-color"] {height:max-content; margin:0px 0 6px 0;}
.collection-list-type-2 .card__content ul[class*="variant-option-color"] a {margin:0px 8px 0px 0px; border-radius:50%; cursor:pointer;}
.collection-list-type-2 .list-view-filter .card__content ul[class*=variant-option-color] {margin:2px 0 0;}
.collection-list-type-2 .card__content ul.variant-option-color li.color-values a.active {border:1px solid rgb(var(--color-border));}
.collection-list-type-2 .card__content ul.variant-option-color li span {min-width:22px; min-height:22px; padding:0; border-radius:50%; margin:0;}
.collection-list-type-2 .card__content ul.variant-option-color li [type="radio"] {position:absolute; opacity:0; width:100%; height:100%; cursor:pointer;}
.collection-list-type-2 .card__content ul.variant-option-color li {display:flex; align-items:center; justify-content:center; position:relative; transition:all var(--duration-default) linear; border-radius:var(--DTRadius);}
tooltip.tooltip {position:absolute; pointer-events:none; opacity:0; padding:3px 10px; left:50%; transform:translateY(-8px) translateX(-50%); -webkit-transform:translateY(-8px) translateX(-50%); bottom:100%; white-space:nowrap; margin-bottom:5px; visibility:hidden; z-index:1000; background-color:rgba(var(--color-hover-button)); color:rgba(var(--color-button-text)); font-size:calc(var(--font-base-size) - 4px); line-height:normal; transition:all var(--duration-default) linear; border-radius:var(--buttons-radius); box-shadow:rgb(99 99 99 / 20%) 0px 2px 8px 0px;}
tooltip.tooltip:before {left:10px; border-top:6px solid rgba(var(--color-hover-button)); border-left:6px solid transparent; border-right:6px solid transparent; transform:translateX(-50%); left:50%; content:""; position:absolute; bottom:-5px; transition:all 0.3s linear;}
.collection-list-type-2 .card__content .variant-option-color li a:hover tooltip.tooltip {opacity:1; visibility:visible; transform:translateY(-4px) translateX(-50%); -webkit-transform:translateY(-4px) translateX(-50%);}
.collection-list-type-2 .product-icons li:hover tooltip.tooltip {opacity:1; visibility:visible; transform:translate(-50%);}
.collection-list-type-2 .product-icons.right-aligned tooltip.tooltip {position:absolute; pointer-events:none; opacity:0; padding:4px 6px; left:unset; right:100%; bottom:-50%; white-space:nowrap; margin-bottom:15px; visibility:hidden; z-index:-1; background:rgb(var(--color-base-outline-button-labels)); color:rgb(var(--color-outline-text)); font-size:calc(var(--font-base-size) - 2px); font-weight:500; line-height:normal; transition:all var(--duration-default) linear; transform:none; border-radius:var(--border-radius); box-shadow:rgb(99 99 99 / 20%) 0px 2px 8px 0px;}
.collection-list-type-2 .product-icons.right-aligned tooltip.tooltip:before {left:unset; border-top:6px solid rgb(var(--color-base-outline-button-labels)); border-left:6px solid transparent; border-right:6px solid transparent; transform:rotate(270deg); right:-8px; content:""; position:absolute; bottom:40%; opacity:0;}
.collection-list-type-2 .product-icons.right-aligned li:hover tooltip.tooltip {opacity:1; visibility:visible; transform:translateX(-6px); -webkit-transform:translateX(-6px);}
.collection-list-type-2 .product-icons.right-aligned li:hover tooltip.tooltip:before {opacity:1;}
.collection-list-type-2 .card__content ul[class*="variant-option"] span {transition:all linear 0.3s; border-radius:50%; min-width:20px; min-height:20px; line-height:normal; padding:0 5px; display:flex; justify-content:center; align-items:center; margin:auto;}
.collection-list-type-2 .card__content .variant-option-size li:hover a span, .card__content .variant-option-size li.size-values a.active span {color:rgb(var(--color-base-background-1)); background:rgb(var(--color-base-accent-1));}
.collection-list-type-2 .card__content .variant-option-size li a {border:1px solid transparent; position:relative;}
.collection-list-type-2 .card__content [class*="variant-option"] {display:flex; justify-content:var(--text-alignment); margin:0; padding:0; flex-wrap:wrap;}
.collection-list-type-2 .card__content ul[class*="variant-option-size"] a {margin:1px 5px 5px 0px; border-radius:50%; cursor:pointer;}
.collection-list-type-2 .card__content ul.variant-option-size li span {border-radius:var(--border-radius); background:transparent; box-shadow:none; padding:0px 5px; font-size:calc(var(--font-base-size) - 2px); min-width:25px; min-height:25px; background:rgba(var(--color-foreground), 0.15);}
.collection-list-type-2 .card__content ul.variant-option-size li [type="radio"] {position:absolute; opacity:0; width:100%; height:100%; cursor:pointer;}
.collection-list-type-2 .card__content ul.variant-option-size li {display:flex; align-items:center; justify-content:center; position:relative; transition:all var(--duration-default) linear; border-radius:var(--DTRadius); margin-top:0;}
.collection-list-type-2 .card__content ul[class*=variant-option-size] {margin-top:10px;}
.collection-list-type-2 .list-view-filter .card__content ul[class*=variant-option-size] {margin:10px 0 0;}
.collection-list-type-2 .quick-add-modal__content-info .dT_bundleSelector {display:none;}
.collection-list-type-2 .card__inner .product-icons button svg {position:relative; width:18px; height:18px;}
.collection-list-type-2 .card__inner .product-icons a:hover, .card__inner .product-icons button:hover {background:rgb(var(--color-base-outline-button-labels)); color:rgb(var(--color-outline-text));}
.collection-list-type-2 .card__information .card__heading {font-size:var(--font-h5-size); transition:all var(--duration-default) linear; font-weight:var( --font-heading-weight);}
.collection-list-type-2 .card__information .caption-with-letter-spacing {font-weight:500; color:rgba(var(--color-foreground));}
.collection-list-type-2 .card__information .card__heading a {transition:all var(--duration-default) linear; font-size:var(--font-h5-size);}
.collection-list-type-2 .card__information .card__heading a:hover {color:rgb(var(--color-link-hover));}
.collection-list-type-2 .card__information .card-information.new--tag span.badge.badge--new {border:none; border-radius:0; padding:4px 12px 2px; transition:all 0.3s linear; margin-bottom:10px; color:rgb(var(--color-button-text)); background:rgba(var(--color-button), var(--alpha-button-background)); border-radius:var(--badge-corner-radius);}
.collection-list-type-2 .card-information.new--tag {margin-bottom:0px;}
.collection-list-type-2 .card__information .card-information.new--tag span.badge__text {font-family:var(--font-additional-family); letter-spacing:0.2rem;}
.collection-list-type-2 .card__badge .badge {border-radius:0; border:none; padding:8px 10px 6px; position:absolute; font-size:calc(var(--font-base-size) - 2px); font-family:var(--font-heading-family); font-weight:500; transition:all 0.3s linear; border-radius:var(--badge-corner-radius); letter-spacing:0px; text-transform:uppercase;}
.collection-list-type-2 .card__badge .badge.badge-sale {background:rgb(var(--sale_badge_color)); color:rgb(var(--sale_badge_color_text));}
.collection-list-type-2 .card__badge .badge.badge-sold {background:rgb(var(--soldout_badge_color)); color:rgb(var(--soldout_badge_color_text));}
.collection-list-type-2 .card__badge.bottom-right .badge {bottom:12px; right:12px;}
.collection-list-type-2 .card__badge.bottom-left .badge {bottom:12px; left:12px;}
.collection-list-type-2 .card__badge.top-right .badge {top:12px; right:12px;}
.collection-list-type-2 .card__badge.top-left .badge {top:12px; left:12px;}
.collection-list-type-2 .cart-drawer .cart-items thead th {opacity:1; font-weight:700; font-size:calc(var(--font-base-size) - 2px);}
.collection-list-type-2 .optional-sidebar ul.product-list-style .card__badge .badge {display:none;}
li.color-values-plus a {font-size:calc(var(--font-base-size) - 4px); min-width:auto; min-height:20px; display:flex; align-items:center; justify-content:center; color:var(--color-icon);}
#swiper-sidebar-carousel {overflow:hidden;}
.collection-list-type-2 .widget.product-sidebar-type-collection .product-list-style .quick-add {position:absolute; left:0;}
.collection-list-type-2 .widget.product-sidebar-type-collection ul.product-list-style li:not(:last-child) {margin-bottom:20px;}
.collection-list-type-2 .widget.product-sidebar-type-collection .product-list-style .card--card .quick-add {margin:1rem 0rem 1rem;}
.collection-list-type-2 .card .product-deal-count .deal-lable {display:none;}
.collection-list-type-2 .card .product-deal-count .deal-clock {display:inline-block; text-align:center; width:calc(100% - 40px); position:absolute; bottom:20px; left:0; right:0; margin:auto; z-index:2; transition:all 0.3s linear;}
.collection-list-type-2 .card .product-deal-count .deal-clock ul {padding:5px; list-style:none; text-align:center; width:100%; margin:0; display:grid; grid-template-columns:repeat(4, 1fr); gap:5px; margin-top:0.5rem;}
.collection-list-type-2 .card .product-deal-count .deal-clock ul li {padding:0.6rem; margin:0; display:inline-block; text-align:center; border:none; line-height:normal; background:rgba(var(--color-button)); color:rgba(var(--color-button-text)); font-weight:500;}
.collection-list-type-2 .card .product-deal-count .deal-clock ul li span {border:none; font-size:calc(var(--font-base-size) - 2px); display:block; min-width:auto; min-height:auto; color:rgba(var(--color-button-text), 0.8);}
.collection-list-type-2 .card .product-deal-count .deal-clock ul li i {display:block;}
.collection-list-type-2 .card-wrapper.underline-links-hover .card:hover .product-deal-count {opacity:0;}
.collection-list-type-2 .card-wrapper.underline-links-hover .card .product-deal-count {opacity:1; transition:all 0.3s linear}
.collection-list-type-2 .card-wrapper.underline-links-hover .card:hover .product-deal-count .deal-clock {bottom:-50px}
.collection-list-type-2 .card-wrapper .card__inner .quick-add.button-quick-add {position:absolute; bottom:0%; left:20px; right:20px; opacity:0; z-index:2; transition:all 0.3s linear; flex-direction:column; pointer-events:all; margin:0;}
.collection-list-type-2 .card-wrapper .card:hover .card__inner .quick-add.button-quick-add {bottom:20px; opacity:1;}
.collection-list-type-2 .card-wrapper .quick-add__submit.button {margin-top:10px;}
.collection-list-type-2 .product__info-wrapper .dT_VProdWishList a:not(.adding).add-wishlist:before {content:""; -webkit-mask-image:url("wishlist-icons.svg"); mask-image:url("wishlist-icons.svg"); background:currentColor; width:18px; height:18px; position:absolute; top:0; bottom:0; left:0; right:0; margin:auto; -webkit-mask-repeat:no-repeat; -webkit-mask-size:contain;}
.collection-list-type-2 .product__info-wrapper .dT_VProdWishList a:not(.adding).added.add-wishlist:before {content:""; -webkit-mask-image:url("wishlist-fill-icons.svg"); mask-image:url("wishlist-fill-icons.svg"); background:currentColor; width:18px; height:18px; position:absolute; top:0; bottom:0; left:0; right:0; margin:auto; -webkit-mask-repeat:no-repeat;}
.collection-list-type-2 .product__info-wrapper a.add-wishlist.button--secondary {position:relative;}
.collection-list-type-2 .product__info-wrapper .dT_VProdCompareList a:not(.adding).add-compare:before {content:""; -webkit-mask-image:url("compare-icons.svg"); mask-image:url("compare-icons.svg"); background:currentColor; width:18px; height:18px; position:absolute; top:0; bottom:0; left:0; right:0; margin:auto; -webkit-mask-repeat:no-repeat; -webkit-mask-size:contain;}
.collection-list-type-2 .product__info-wrapper .dT_VProdCompareList a:not(.adding).added.add-compare:before {content:""; -webkit-mask-image:url("compare-fill.svg"); mask-image:url("compare-fill.svg"); background:currentColor; width:18px; height:18px; position:absolute; top:0; bottom:0; left:0; right:0; margin:auto; -webkit-mask-repeat:no-repeat;}
.collection-list-type-2 .product__info-wrapper .dT_VProdWishList a.adding:before, .product__info-wrapper .dT_VProdCompareList a.adding:before {position:absolute; z-index:1; content:""; width:20px; height:20px; background-color:currentColor; -webkit-mask-image:url(loading-icon.gif); mask-image:url(loading-icon.gif); -webkit-mask-position:center; left:0; right:0; bottom:0; top:0; margin:auto; -webkit-mask-size:100%; -webkit-mask-repeat:no-repeat;}
.collection-list-type-2 .product__info-wrapper a.add-compare.button--secondary {position:relative;}
.collection-list-type-2 .custom-featured-collection .title-wrapper-with-link > .description {max-width:341px; margin:15px 0 0; text-align:center;}
.collection-list-type-2 .custom-featured-collection .title-wrapper-with-link {margin-bottom:40px;}
.collection-list-type-2 .collection.custom-arrow-featured-collection .swiper-button-prev {display:none;}
.collection-list-type-2 .collection.custom-arrow-featured-collection .swiper-button-next {top:-70px;}
.collection-list-type-2 .collection.custom-arrow-featured-collection .swiper {}
.collection-list-type-2 .collection.custom-arrow-featured-collection .row {overflow:hidden;}

@media screen and (max-width: 576px) {
.collection-list-type-2 .collection.custom-arrow-featured-collection .swiper-button-next, .collection.custom-arrow-featured-collection .swiper-button-prev {top:unset; bottom:-50px; left:0; right:0; margin:auto;}
.collection-list-type-2 .collection.custom-arrow-featured-collection .swiper-button-next {left:40px;}
.collection-list-type-2 .collection.custom-arrow-featured-collection .swiper-button-prev {display:flex; right:40px;}
.collection-list-type-2 .collection.custom-arrow-featured-collection .swiper {margin-bottom:50px;}
.collection-list-type-2 .optional-sidebar .card__information .card__heading a {font-size:Calc(var(--font-h5-size) - 4px);}
.collection-list-type-2 .optional-sidebar .card--card.card--media>.card__content {padding:15px 0;}
}

.collection-list-type-2 .rating {display:inline-block; margin:0;}
.collection-list-type-2 .product .rating-star {--letter-spacing:0.2; --font-size:1.5;}
.collection-list-type-2 .card-wrapper .card-information.review {display:flex; justify-content:var(--text-alignment);}
.collection-list-type-2 .card-wrapper .rating-star {--letter-spacing:0.1; --font-size:1.4;}
.collection-list-type-2 .rating-star {--percent:calc( ( var(--rating) / var(--rating-max) + var(--rating-decimal) * var(--font-size) / (var(--rating-max) * (var(--letter-spacing) + var(--font-size))) ) * 100% ); letter-spacing:calc(var(--letter-spacing) * 1rem); font-size:calc(var(--font-size) * 1rem); line-height:1; display:flex; font-family:Times; margin:0 0 2px;}
p.rating-count.caption span {font-size:calc(var(--font-base-size) - 2px); display:none;}
.collection-list-type-2 .rating-star::before {content:"★★★★★"; background:linear-gradient(90deg, rgb(242 181 0 / 100%) var(--percent), rgb(242 181 0 / 15%)var(--percent)); -webkit-background-clip:text; -webkit-text-fill-color:rgb(242 181 0 / 40%); font-size:var(--font-base-size);}
.collection-list-type-2 .rating-text {display:none;}
.collection-list-type-2 .rating-count {display:inline-block; margin:0;}

@media (forced-colors: active) {
.collection-list-type-2 .rating {display:none;}
.collection-list-type-2 .rating-text {display:block;}
}

.collection-list-type-2 .product-card-placeholder .card:not(.ratio) {height:auto;}
.collection-list-type-2 .card_style-card_with_overlay .card__content ul.variant-option-size {justify-content:center; align-items:center; margin:auto; transition:all 0.3s linear; pointer-events:all; position:absolute; left:0; right:0; bottom:-20px; padding:15px 10px 10px; background:rgba(var(--color-base-accent-1), 0.3); opacity:0; transition:all 0.3s linear;}
.collection-list-type-2 .card_style-card_with_overlay .card:hover .card__content ul.variant-option-size {opacity:1; bottom:0;}
.collection-list-type-2 .card_style-card_with_overlay .card__content ul.variant-option-size li span {border-radius:0; background:transparent; box-shadow:none; padding:0; font-size:calc(var(--font-base-size) - 2px); line-height:calc(var(--font-base-size) - 2px); min-width:max-content; min-height:max-content;}
.collection-list-type-2 .card_style-card_with_overlay .card .card__inner .card__media:before {content:""; width:100%; height:100%; background:rgba(var(--color-base-background-1), .0); position:absolute; z-index:1; opacity:0; transition:all .3s linear; left:0;}
.collection-list-type-2 .card_style-card_with_overlay .card:hover .card__inner .card__media:before {opacity:1;}
.collection-list-type-2 .card_style-card_with_overlay .card__content .variant-option-size li.size-values a {background:rgba(var(--color-button)); color:rgba(var(--color-button-text)); border-radius:var(--buttons-radius); padding:5px 10px; display:flex; align-items:center;}
.collection-list-type-2 .card_style-card_with_overlay .card__content .variant-option-size li.size-values a:hover, .card_style-card_with_overlay .card__content .variant-option-size li.size-values a.active {background:rgba(var(--color-hover-button)); color:rgba(var(--color-button-hover-text));}
.collection-list-type-2 .card_style-card_with_buttons .card-wrapper.underline-links-hover .card .card__inner .product-icons {opacity:1; right:20px; top:20px;}
.collection-list-type-2 .card_style-card_with_buttons .card-wrapper.underline-links-hover .card:hover .card__inner .product-icons {right:20px;}
.collection-list-type-2 .card_style-card_with_buttons .card-wrapper .card__inner .quick-add.button-quick-add {display:flex; flex-wrap:wrap; flex-direction:row; justify-content:center; grid-gap:10px; text-align:center; margin:0 auto;}
.collection-list-type-2 .card_style-card_with_buttons .card-wrapper .card .quick-add .button {padding:5px 20px; font-size:calc(var(--font-base-size) - 2px); border-radius:var(--border-radius); font-weight:500; margin:0; line-height:20px;}
.collection-list-type-2 .card_style-card_with_buttons .card .card__inner .card__media:before {content:""; width:100%; height:100%; background:rgba(var(--color-base-background-1), .8); position:absolute; z-index:1; opacity:0; transition:all .3s linear; left:0;}
.collection-list-type-2 .card_style-card_with_buttons .card:hover .card__inner .card__media:before {opacity:1;}
.collection-list-type-2 .collection .product-grid:not(.list-view-filter) .card--card.card--media>.card__content {display:block;}
.collection-list-type-2 .product-sidebar-type-collection .card--card.card--media>.card__content {align-self:center;}
.collection-list-type-2 .quick-add-modal__content-info .page-full-width.page-full-width_spacing {padding:0;}
.collection-list-type-2 .quick-add-modal__content-info .page-full-width.page-full-width_spacing .row {margin:0;}
#add-to-cart-icon .product-icons li >:not(product-form, tooltip) {display:none;}
#add-to-cart-icon .product-icons li {margin:0;}

@media screen and (min-width: 1541px) {
.collection-list-type-2 .facets-vertical.sidebar-left .product-grid-container .list-view-filter li.grid__item, .facets-vertical.sidebar-right .product-grid-container .list-view-filter li.grid__item {width:100%; max-width:100%;}
.collection-list-type-2 .list-view-filter .card-wrapper .card .product-grid-container .list-view-filter li.grid__item {width:calc(50% - var(--grid-desktop-horizontal-spacing) * 2 / 3); max-width:calc(50% - var(--grid-desktop-horizontal-spacing) * 2 / 3);}
}

@media screen and (max-width: 768px) {
.collection-list-type-2 .card__information .card__heading a {font-size:calc(var(--font-h5-size) - 2px);}
}

@media screen and (max-width: 480px) {
.collection-list-type-2 .card .product-deal-count .deal-clock ul li {font-size:calc(var(--font-base-size) - 4px); padding:0.2rem;}
.collection-list-type-2 .card .product-deal-count .deal-clock ul li span {font-size:calc(var(--font-base-size) - 6px);}
.collection-list-type-2 .card_style-standard .card-wrapper .card:hover .card__inner .quick-add.button-quick-add, .card_style-card_with_buttons .card-wrapper .card:hover .card__inner .quick-add.button-quick-add, .card_style-button_width_icons .card-wrapper .card:hover .card__inner .quick-add.button-quick-add {bottom:10px;}
.collection-list-type-2 .card_style-standard .card-wrapper .card__inner .quick-add.button-quick-add, .card_style-card_with_buttons .card-wrapper .card .card__inner .quick-add.button-quick-add, .card_style-button_width_icons .card-wrapper .card .card__inner .quick-add.button-quick-add {bottom:-20px;}
.collection-list-type-2 .product-icons.right-aligned tooltip.tooltip {display:none;}
.collection-list-type-2 .card .product-deal-count .deal-clock ul {gap:2px;}
.collection-list-type-2 .product-icons li {margin:3px;}
.collection-list-type-2 .card-wrapper .quick-add__submit.button {padding:0; min-width:8rem; font-size:calc(var(--font-base-size) - 4px);}
.collection-list-type-2 .card-wrapper .card:hover .card__inner .quick-add.button-quick-add {bottom:20px;}
.collection-list-type-2 .card_style-card_with_buttons .card-wrapper .card__inner .quick-add.button-quick-add modal-opener, .card_style-card_with_overlay .card-wrapper .card__inner .quick-add.button-quick-add modal-opener, .card_style-standard .card-wrapper .card__inner .quick-add.button-quick-add modal-opener, .card-wrapper .card__inner .quick-add.button-quick-add modal-opener {display:none;}
.collection-list-type-2 .card_style-card_with_overlay .card-wrapper .card:hover .card__inner .varient-buttons-wrapper {bottom:10px;}
.collection-list-type-2 .card__information .card__heading a {font-size:calc(var(--font-h5-size) - 4px);}
}

@media screen and (max-width: 389px) {
.collection-list-type-2 .card_style-standard .card-wrapper .card:hover .card__inner .quick-add.button-quick-add, .card_style-card_with_buttons .card-wrapper .card:hover .card__inner .quick-add.button-quick-add, .card_style-button_width_icons .card-wrapper .card:hover .card__inner .quick-add.button-quick-add {bottom:20px;}
}

@media screen and (max-width: 1800px) {
.collection-list-type-2 .list-view-filter .card-wrapper .card {grid-template-columns:2fr 4fr;}
}

@media screen and (min-width: 481px) {
.collection-list-type-2 .collection-list .collection-list.overlay + .swiper-navigation .swiper:hover .swiper-button-next, .collection-list .collection-list.overlay .swiper:hover .swiper-button-prev {top:0 !important; transform:none;}
.collection-list-type-2 .collection-list .collection-list.overlay + .swiper-navigation .swiper-button-next, .collection-list .collection-list.overlay + .swiper-navigation .swiper-button-prev {top:0 !important; bottom:0; margin:auto; transform:none;}
}



.collection-list-type-2 .collection-list.home-custom-collection .card-wrapper .card__inner{ background:rgba(var(--color-foreground));}
.collection-list-type-2 .card .media.media--hover-effect > img:only-child,
.collection-list-type-2 .card-wrapper .media.media--hover-effect > img:only-child {  transition:all 0.3s linear;  }
.collection-list-type-2 .collection-list .card .card__inner .card__media img { filter: grayscale(.9); }
.collection-list-type-2 .custom-featured-collection .card .card__inner .card__media img{  filter: grayscale(.9); }
.collection-list-type-2 .custom-featured-collection .card:hover .card__inner .card__media img{  filter: grayscale(0); }
.collection-list-type-2 .home-custom-collection .card:hover .media.media--hover-effect > img:first-child:only-child,
.collection-list-type-2 .home-custom-collection .card-wrapper:hover .media.media--hover-effect > img:first-child:only-child {    transform: scale(1);    margin-left: 20px;   filter: grayscale(0);    margin-top: 20px;  }