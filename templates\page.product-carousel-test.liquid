{% comment %}
  Product Carousel Test Page
  
  This template is for testing the product carousel section.
  Create a page with template suffix 'product-carousel-test' to use this template.
{% endcomment %}

<div class="page-width page-content">
  <header class="section-header text-center">
    <h1 class="section-header__title">{{ page.title }}</h1>
    {% if page.content != blank %}
      <div class="rte">
        {{ page.content }}
      </div>
    {% endif %}
  </header>
</div>

{% comment %} Test different carousel configurations {% endcomment %}

{% section 'product-carousel' %}

{% comment %} Add some spacing between test sections {% endcomment %}
<div style="height: 4rem;"></div>

{% comment %} Test with different settings {% endcomment %}
<div class="page-width">
  <h2 class="h1 text-center">Carousel Variations</h2>
  <p class="text-center">Testing different configurations and styles</p>
</div>

{% comment %} Compact mobile version {% endcomment %}
<div class="color-background-2 gradient">
  <div class="page-width section-padding">
    <h3 class="h2">Compact Mobile Version</h3>
    <p>Optimized for mobile viewing with minimal spacing</p>
  </div>
</div>

{% section 'product-carousel' %}

{% comment %} Full width version {% endcomment %}
<div style="height: 4rem;"></div>

<div class="page-width">
  <h3 class="h2">Full Width Version</h3>
  <p>Spans the entire viewport width for maximum impact</p>
</div>

{% section 'product-carousel' %}

{% comment %} Auto-play version {% endcomment %}
<div style="height: 4rem;"></div>

<div class="color-accent-1 gradient">
  <div class="page-width section-padding">
    <h3 class="h2">Auto-play Version</h3>
    <p>Automatically advances slides with pause on hover</p>
  </div>
</div>

{% section 'product-carousel' %}

{% comment %} Testing instructions {% endcomment %}
<div style="height: 4rem;"></div>

<div class="page-width">
  <div class="content-container">
    <h2 class="h1">Testing Checklist</h2>
    
    <div class="grid grid--2-col-desktop grid--1-col-tablet-down">
      <div>
        <h3 class="h3">Functionality Tests</h3>
        <ul>
          <li>✓ Navigation arrows work</li>
          <li>✓ Pagination dots are clickable</li>
          <li>✓ Touch/swipe gestures work on mobile</li>
          <li>✓ Auto-play starts and stops correctly</li>
          <li>✓ Loop functionality works</li>
          <li>✓ Product cards display correctly</li>
          <li>✓ Quick add/view buttons function</li>
          <li>✓ Collection links work</li>
        </ul>
      </div>
      
      <div>
        <h3 class="h3">Responsive Tests</h3>
        <ul>
          <li>✓ Desktop: 4-5 products visible</li>
          <li>✓ Laptop: 3 products visible</li>
          <li>✓ Tablet: 2-3 products visible</li>
          <li>✓ Mobile: 1-2 products visible</li>
          <li>✓ Spacing adjusts appropriately</li>
          <li>✓ Navigation adapts to screen size</li>
          <li>✓ Cards maintain consistent height</li>
          <li>✓ Images load and display properly</li>
        </ul>
      </div>
    </div>
    
    <div class="grid grid--2-col-desktop grid--1-col-tablet-down" style="margin-top: 2rem;">
      <div>
        <h3 class="h3">Accessibility Tests</h3>
        <ul>
          <li>✓ Keyboard navigation works (Arrow keys, Tab, Enter)</li>
          <li>✓ Screen reader announcements</li>
          <li>✓ Focus indicators visible</li>
          <li>✓ ARIA labels present</li>
          <li>✓ Skip links function</li>
          <li>✓ Color contrast meets standards</li>
          <li>✓ Reduced motion respected</li>
          <li>✓ Semantic HTML structure</li>
        </ul>
      </div>
      
      <div>
        <h3 class="h3">Performance Tests</h3>
        <ul>
          <li>✓ Images lazy load</li>
          <li>✓ Smooth animations</li>
          <li>✓ No layout shifts</li>
          <li>✓ Fast initial load</li>
          <li>✓ Efficient memory usage</li>
          <li>✓ No console errors</li>
          <li>✓ Good Core Web Vitals</li>
          <li>✓ Works offline (cached)</li>
        </ul>
      </div>
    </div>
    
    <div style="margin-top: 2rem;">
      <h3 class="h3">Browser Compatibility</h3>
      <p>Test in the following browsers:</p>
      <ul style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 1rem;">
        <li>✓ Chrome (latest)</li>
        <li>✓ Firefox (latest)</li>
        <li>✓ Safari (latest)</li>
        <li>✓ Edge (latest)</li>
        <li>✓ iOS Safari</li>
        <li>✓ Chrome Mobile</li>
        <li>✓ Samsung Internet</li>
        <li>✓ Opera</li>
      </ul>
    </div>
    
    <div style="margin-top: 2rem; padding: 1.5rem; background: var(--gradient-background); border-radius: 8px; border: 1px solid rgba(var(--color-border), 0.2);">
      <h3 class="h3">Testing Tools</h3>
      <p>Use these tools to validate the implementation:</p>
      <ul>
        <li><strong>Accessibility:</strong> WAVE, axe DevTools, Lighthouse</li>
        <li><strong>Performance:</strong> PageSpeed Insights, GTmetrix, WebPageTest</li>
        <li><strong>Responsive:</strong> Browser DevTools, BrowserStack</li>
        <li><strong>Screen Readers:</strong> NVDA, JAWS, VoiceOver</li>
        <li><strong>Validation:</strong> W3C Markup Validator, CSS Validator</li>
      </ul>
    </div>
  </div>
</div>

<style>
  .content-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 0;
  }
  
  .section-padding {
    padding: 2rem 0;
  }
  
  .text-center {
    text-align: center;
  }
  
  ul {
    list-style-type: none;
    padding-left: 0;
  }
  
  ul li {
    padding: 0.25rem 0;
    position: relative;
    padding-left: 1.5rem;
  }
  
  ul li::before {
    content: "•";
    color: var(--gradient-base-accent-1);
    font-weight: bold;
    position: absolute;
    left: 0;
  }
</style>
