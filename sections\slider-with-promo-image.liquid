{{ 'component-image-banner.css' | asset_url | stylesheet_tag }}
{{ 'component-slider.css' | asset_url | stylesheet_tag }}
{{ 'component-slider-with-promo-image.css' | asset_url | stylesheet_tag }}

{%- liquid
   assign ColumnWidth = section.settings.content_width | split: '/' 
   assign leftColumn = ColumnWidth[0] 
   assign rightColumn = ColumnWidth[1]  

  assign listWidth = section.settings.lap_content_width | split: '/'
  assign lap_leftColumn = listWidth[0]
  assign lap_rightColumn = listWidth[1]

  assign listWidth = section.settings.tab_content_width | split: '/' 
  assign tab_leftColumn = listWidth[0] 
  assign tab_rightColumn = listWidth[1]
  
%}



{%- style -%}
{%- if section.settings.slide_height == 'adapt_image' and section.blocks.first.settings.image != blank -%}

@media screen and (max-width: 749px) {
#Slider-{{ section.id }}::before,
#Slider-{{ section.id }} .media::before,
#Slider-{{ section.id }}:not(.banner--mobile-bottom) .banner__content::before {
padding-bottom: {{ 1 | divided_by: section.blocks.first.settings.image.aspect_ratio | times: 100 }}%;
content: '';
display: block;
}
}
@media screen and (min-width: 750px) {
#Slider-{{ section.id }}::before,
#Slider-{{ section.id }} .media::before {
padding-bottom: {{ 1 | divided_by: section.blocks.first.settings.image.aspect_ratio | times: 100 }}%;
content: '';
display: block;
}
}     

{%- endif -%}
{%- for block in section.blocks -%}      
#Slide-{{ section.id }}-{{ forloop.index }} .slideshow__media::after {
opacity: {{ block.settings.image_overlay_opacity | divided_by: 100.0 }};
}
{%  endfor %}  
     .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.50 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.50 | round: 0 }}px;
  }
   @media screen and (min-width: 576px) and (max-width: 749px){
   .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }
   }
  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
  
  .slideshow-with-promo-images  .grouped-content .main-block {
    {% if section.settings.enable_promo_block %} width:{{leftColumn}}% {% endif %}
  }
  .slideshow-with-promo-images  .grouped-content .additional-block {
    width:calc({{rightColumn}}% - {{ section.settings.grouped_column_gap }}px);
    gap: {{ section.settings.grouped_column_gap }}px; margin-bottom: 0;
  }
  .slideshow-with-promo-images .grouped-content .additional-block.overlay .promo-block {
    min-height: {{section.settings.promo_height_desktop}}px;
  }
  
  @media (max-width: 1540px) { 
    .slideshow-with-promo-images .grouped-content .main-block {
          {% if section.settings.enable_promo_block %} width:{{lap_leftColumn}}% {% endif %}
        }
    .slideshow-with-promo-images .grouped-content .additional-block {
          width:calc({{lap_rightColumn}}% - {{ section.settings.grouped_column_gap }}px);
        }
    .slideshow-with-promo-images .grouped-content .additional-block.overlay .promo-block {
    min-height: {{section.settings.promo_height_lap}}px;
  }
  }
   @media (max-width: 1199px) { 
    .slideshow-with-promo-images .grouped-content .main-block {
          {% if section.settings.enable_promo_block %} width:{{tab_leftColumn}}% {% endif %}
        }
    .slideshow-with-promo-images .grouped-content .additional-block {
          width:calc({{tab_rightColumn}}% - {{ section.settings.grouped_column_gap }}px);
        }
    .slideshow-with-promo-images .grouped-content .additional-block.overlay .promo-block {min-height: {{section.settings.promo_height_tab}}px; }
  }
 @media (max-width: 767px) {
   .slideshow-with-promo-images .grouped-content .additional-block.overlay .promo-block {min-height: {{section.settings.promo_height_mobile}}px; }
   .slideshow-with-promo-images .grouped-content .main-block { margin-bottom: {{ section.settings.grouped_column_gap }}px; }
   .slideshow-with-promo-images .grouped-content.dt-sc-reverse-columns .main-block { margin-top: {{ section.settings.grouped_column_gap }}px; }
   .slideshow-with-promo-images .grouped-content .main-block,
   .slideshow-with-promo-images .grouped-content .additional-block { width: 100%; }
 }

{%- endstyle -%}  
<div class="slideshow-with-promo-images {% if section.settings.page_full_width %} page-full-width {% else %} page-width {% endif %} {% if section.settings.page_full_width_spacing %} page-full-width_spacing {% endif %} section-{{ section.id }}-padding isolate ">
   <div class="row">
    {% if section.settings.enable_promo_block %}
      <div class="grouped-content {% if section.settings.reverse_column %}dt-sc-reverse-columns{% endif %}">
         {% endif %}
         {% if section.settings.enable_promo_block %}
         <div class="main-block">
            {% endif %}
        <slideshow-component>            
   <div class="slide-promo" data-slider-options='{"loop": "true","effect": "{{ section.settings.slider_effect }}", "auto_play": "{{ section.settings.auto_play }}"}'>
      <div class="swiper" data-swiper-slider>
         <div class="swiper-wrapper slide-banner banner--{{ section.settings.slide_height }} {% if section.settings.show_text_below %} banner--mobile-bottom{% endif %}{% if section.blocks.first.settings.image == blank %} slideshow--placeholder{% endif %}" id="Slider-{{ section.id }}">
            {%- for block in section.blocks -%}      
            <div class="swiper-slide"
            id="Slide-{{ section.id }}-{{ forloop.index }}"
            {{ block.shopify_attributes }}
            role="group"
            aria-roledescription="{{ 'sections.slideshow.slide' | t }}"
            aria-label="{{ forloop.index }} {{ 'general.slider.of' | t }} {{ forloop.length }}"
            tabindex="-1"
            >
            <div class="slideshow__media {% if block.settings.image == blank %} placeholder{% endif %}">
               {%- if block.settings.image -%}
               <img
                  srcset="{%- if block.settings.image.width >= 375 -%}{{ block.settings.image | image_url: width: 375 }} 375w,{%- endif -%}
                  {%- if block.settings.image.width >= 550 -%}{{ block.settings.image | image_url: width: 550 }} 550w,{%- endif -%}
                  {%- if block.settings.image.width >= 750 -%}{{ block.settings.image | image_url: width: 750 }} 750w,{%- endif -%}
                  {%- if block.settings.image.width >= 1100 -%}{{ block.settings.image | image_url: width: 1100 }} 1100w,{%- endif -%}
                  {%- if block.settings.image.width >= 1500 -%}{{ block.settings.image | image_url: width: 1500 }} 1500w,{%- endif -%}
                  {%- if block.settings.image.width >= 1780 -%}{{ block.settings.image | image_url: width: 1780 }} 1780w,{%- endif -%}
                  {%- if block.settings.image.width >= 2000 -%}{{ block.settings.image | image_url: width: 2000 }} 2000w,{%- endif -%}
                  {%- if block.settings.image.width >= 3000 -%}{{ block.settings.image | image_url: width: 3000 }} 3000w,{%- endif -%}
                  {%- if block.settings.image.width >= 3840 -%}{{ block.settings.image | image_url: width: 3840 }} 3840w,{%- endif -%}
                  {{ block.settings.image | image_url }} {{ block.settings.image.width }}w"
                  sizes="100vw"
                  src="{{ block.settings.image | image_url: width: 1500 }}"
                  loading="lazy"
                  alt="{{ block.settings.image.alt | escape }}"
                  width="{{ block.settings.image.width }}"
                  height="{{ block.settings.image.width | divided_by: block.settings.image.aspect_ratio | round }}"
                  class="desktop-slider"
                  >
               {%- else -%}
               {{ 'lifestyle-2' | placeholder_svg_tag: 'placeholder-svg' }}
               {%- endif -%}
               {%- if block.settings.mobile_image -%}
               <img
                  srcset="{%- if block.settings.mobile_image.width >= 375 -%}{{ block.settings.mobile_image | image_url: width: 375 }} 375w,{%- endif -%}
                  {%- if block.settings.mobile_image.width >= 550 -%}{{ block.settings.mobile_image | image_url: width: 550 }} 550w,{%- endif -%}
                  {%- if block.settings.mobile_image.width >= 750 -%}{{ block.settings.mobile_image | image_url: width: 750 }} 750w,{%- endif -%}
                  {%- if block.settings.mobile_image.width >= 1100 -%}{{ block.settings.mobile_image | image_url: width: 1100 }} 1100w,{%- endif -%}
                  {%- if block.settings.mobile_image.width >= 1500 -%}{{ block.settings.mobile_image | image_url: width: 1500 }} 1500w,{%- endif -%}
                  {%- if block.settings.mobile_image.width >= 1780 -%}{{ block.settings.mobile_image | image_url: width: 1780 }} 1780w,{%- endif -%}
                  {%- if block.settings.mobile_image.width >= 2000 -%}{{ block.settings.mobile_image | image_url: width: 2000 }} 2000w,{%- endif -%}
                  {%- if block.settings.mobile_image.width >= 3000 -%}{{ block.settings.mobile_image | image_url: width: 3000 }} 3000w,{%- endif -%}
                  {%- if block.settings.mobile_image.width >= 3840 -%}{{ block.settings.mobile_image | image_url: width: 3840 }} 3840w,{%- endif -%}
                  {{ block.settings.mobile_image | image_url }} {{ block.settings.mobile_image.width }}w"
                  sizes="100vw"
                  src="{{ block.settings.mobile_image | image_url: width: 1500 }}"
                  loading="lazy"
                  alt="{{ block.settings.mobile_image.alt | escape }}"
                  width="{{ block.settings.mobile_image.width }}"
                  height="{{ block.settings.mobile_image.width | divided_by: block.settings.mobile_image.aspect_ratio | round }}"
                  class="mobile-slider"
                  >          
               {%- endif -%}
            </div>
            <div class="slideshow__text-wrapper banner__content banner__content--{{ block.settings.box_align }} page-width{% if block.settings.show_text_box == false %} banner--desktop-transparent{% endif %}">
               <div class="slideshow__text banner__box content-container content-container--full-width-mobile color-{{ block.settings.color_scheme }} gradient slideshow__text--{{ block.settings.text_alignment }} slideshow__text-mobile--{{ block.settings.text_alignment_mobile }}">
                  {%- if block.settings.subheading != blank -%}
                  <h2 class="banner__sub_heading">{{ block.settings.subheading }}</h2>
                  {%- endif -%}
                  {%- if block.settings.heading != blank -%}
                  <h2 class="banner__heading {{ block.settings.heading_size }}">{{ block.settings.heading }}</h2>
                  {%- endif -%}           
                  {%- if block.settings.text != blank -%}
                  <div class="banner__text" {{ block.shopify_attributes }}>
                  <span>{{ block.settings.text | escape }}</span>
               </div>
               {%- endif -%}
               {%- if block.settings.button_label != blank -%}
               <div class="banner__buttons">
                  <a{% if block.settings.link %} href="{{ block.settings.link }}"{% else %} role="link" aria-disabled="true"{% endif %} class="button {% if block.settings.button_style_secondary %}button--secondary{% else %}button--primary{% endif %}">{{ block.settings.button_label | escape }}</a>
               </div>
               {%- endif -%}
            </div>
         </div>
      </div>
      {%- endfor -%}
   </div>
   {% if section.settings.swiper_navigation != blank %}
   <div class="swiper-button-next"><span></span></div>
   <div class="swiper-button-prev"><span></span></div>
   {% endif %}    
   {% if section.settings.swiper_pagination != blank %}
   <div class="swiper-pagination"></div>
   {% endif %}
</div>
</div>
</slideshow-component>
    {% if section.settings.enable_promo_block %}
   </div>
   {% endif %} 
   {% if section.settings.enable_promo_block %}
   <div class="additional-block {{ section.settings.promo_block_style }}">
      {% if section.settings.enable_additional_block_1 %}        
      <div class="promo-block">
         {% if section.settings.link_1 != blank %}
         <a href="{{ section.settings.link_1 }}">
            {% endif%}
            <div class="promo-image">
               {% if section.settings.image_1 != blank   %}
                <img
                              srcset="{%- if section.settings.image_1.width >= 375 -%}{{ section.settings.image_1 | image_url: width: 375 }} 375w,{%- endif -%}
                              {%- if section.settings.image_1.width >= 550 -%}{{ section.settings.image_1 | image_url: width: 550 }} 550w,{%- endif -%}
                              {%- if section.settings.image_1.width >= 750 -%}{{ section.settings.image_1 | image_url: width: 750 }} 750w,{%- endif -%}
                              {%- if section.settings.image_1.width >= 1100 -%}{{ section.settings.image_1 | image_url: width: 1100 }} 1100w,{%- endif -%}
                              {%- if section.settings.image_1.width >= 1500 -%}{{ section.settings.image_1 | image_url: width: 1500 }} 1500w,{%- endif -%}
                              {%- if section.settings.image_1.width >= 1780 -%}{{ section.settings.image_1 | image_url: width: 1780 }} 1780w,{%- endif -%}
                              {%- if section.settings.image_1.width >= 2000 -%}{{ section.settings.image_1 | image_url: width: 2000 }} 2000w,{%- endif -%}
                              {%- if section.settings.image_1.width >= 3000 -%}{{ section.settings.image_1 | image_url: width: 3000 }} 3000w,{%- endif -%}
                              {%- if section.settings.image_1.width >= 3840 -%}{{ section.settings.image_1 | image_url: width: 3840 }} 3840w,{%- endif -%}
                              {{ section.settings.image_1 | image_url }} {{ section.settings.image_1.width }}w"
                              sizes="100vw"
                              src="{{ section.settings.image_1 | image_url: width: 1500 }}"
                              loading="lazy"
                              alt="{{ section.settings.image_1.alt | escape }}"
                              width="{{ section.settings.image_1.width }}"
                              height="{{ section.settings.image_1.width | divided_by: section.settings.image_1.aspect_ratio | round }}"
                              class="dt-sc-block-image"
                              >
               {% else %}
                {{ 'lifestyle-2' | placeholder_svg_tag: 'placeholder-svg' }}
               {% endif %}            
            </div>
            {% if section.settings.link_1 != blank %}  
         </a>
         {% endif%}
         <div class="featured-content">
            {% if section.settings.title_1 != blank %}
            <h3>{{ section.settings.title_1 }}</h3>
            {% endif %}
            {% if section.settings.text_1 != blank %}
            <p>{{ section.settings.text_1}}</p>
            {% endif %}  
            {% if section.settings.button_1 != blank %}
            <a class="button" href="{{ section.settings.link_1 }}">{{ section.settings.button_1}}</a>    
            {% endif %}               
         </div>
      </div>
      {% endif %}
      {% if section.settings.enable_additional_block_2 %}
      <div class="promo-block">
         {% if section.settings.link_2 != blank %}
         <a href="{{ section.settings.link_2 }}">
            {% endif%}
            <div class="promo-image">
               {% if section.settings.image_2 != blank   %}
               <img
                              srcset="{%- if section.settings.image_2.width >= 375 -%}{{ section.settings.image_2 | image_url: width: 375 }} 375w,{%- endif -%}
                              {%- if section.settings.image_2.width >= 550 -%}{{ section.settings.image_2 | image_url: width: 550 }} 550w,{%- endif -%}
                              {%- if section.settings.image_2.width >= 750 -%}{{ section.settings.image_2 | image_url: width: 750 }} 750w,{%- endif -%}
                              {%- if section.settings.image_2.width >= 1100 -%}{{ section.settings.image_2 | image_url: width: 1100 }} 1100w,{%- endif -%}
                              {%- if section.settings.image_2.width >= 1500 -%}{{ section.settings.image_2 | image_url: width: 1500 }} 1500w,{%- endif -%}
                              {%- if section.settings.image_2.width >= 1780 -%}{{ section.settings.image_2 | image_url: width: 1780 }} 1780w,{%- endif -%}
                              {%- if section.settings.image_2.width >= 2000 -%}{{ section.settings.image_2 | image_url: width: 2000 }} 2000w,{%- endif -%}
                              {%- if section.settings.image_2.width >= 3000 -%}{{ section.settings.image_2 | image_url: width: 3000 }} 3000w,{%- endif -%}
                              {%- if section.settings.image_2.width >= 3840 -%}{{ section.settings.image_2 | image_url: width: 3840 }} 3840w,{%- endif -%}
                              {{ section.settings.image_2 | image_url }} {{ section.settings.image_2.width }}w"
                              sizes="100vw"
                              src="{{ section.settings.image_2 | image_url: width: 1500 }}"
                              loading="lazy"
                              alt="{{ section.settings.image_2.alt | escape }}"
                              width="{{ section.settings.image_2.width }}"
                              height="{{ section.settings.image_2.width | divided_by: section.settings.image_2.aspect_ratio | round }}"
                              class="dt-sc-block-image"
                              >
               {% else %}
                {{ 'lifestyle-2' | placeholder_svg_tag: 'placeholder-svg' }}
               {% endif %}            
            </div>
            {% if section.settings.link_2 != blank %}  
         </a>
         {% endif%}
         <div class="featured-content">
            {% if section.settings.title_2 != blank %}
            <h3>{{ section.settings.title_2 }}</h3>
            {% endif %}
            {% if section.settings.text_2 != blank %}
            <p>{{ section.settings.text_2}}</p>
            {% endif %}  
            {% if section.settings.button_2 != blank %}
            <a class="button" href="{{ section.settings.link_2 }}">{{ section.settings.button_2}}</a>    
            {% endif %}               
         </div>
      </div>
      {% endif %}
      {% if section.settings.enable_additional_block_3 %}
      <div class="promo-block">
         {% if section.settings.link_3 != blank %}
         <a href="{{ section.settings.link_3 }}">
            {% endif%}
            <div class="promo-image">
               {% if section.settings.image_3 != blank   %}
                <img
                              srcset="{%- if section.settings.image_3.width >= 375 -%}{{ section.settings.image_3 | image_url: width: 375 }} 375w,{%- endif -%}
                              {%- if section.settings.image_3.width >= 550 -%}{{ section.settings.image_3 | image_url: width: 550 }} 550w,{%- endif -%}
                              {%- if section.settings.image_3.width >= 750 -%}{{ section.settings.image_3 | image_url: width: 750 }} 750w,{%- endif -%}
                              {%- if section.settings.image_3.width >= 1100 -%}{{ section.settings.image_3 | image_url: width: 1100 }} 1100w,{%- endif -%}
                              {%- if section.settings.image_3.width >= 1500 -%}{{ section.settings.image_3 | image_url: width: 1500 }} 1500w,{%- endif -%}
                              {%- if section.settings.image_3.width >= 1780 -%}{{ section.settings.image_3 | image_url: width: 1780 }} 1780w,{%- endif -%}
                              {%- if section.settings.image_3.width >= 2000 -%}{{ section.settings.image_3 | image_url: width: 2000 }} 2000w,{%- endif -%}
                              {%- if section.settings.image_3.width >= 3000 -%}{{ section.settings.image_3 | image_url: width: 3000 }} 3000w,{%- endif -%}
                              {%- if section.settings.image_3.width >= 3840 -%}{{ section.settings.image_3 | image_url: width: 3840 }} 3840w,{%- endif -%}
                              {{ section.settings.image_3 | image_url }} {{ section.settings.image_3.width }}w"
                              sizes="100vw"
                              src="{{ section.settings.image_3 | image_url: width: 1500 }}"
                              loading="lazy"
                              alt="{{ section.settings.image_3.alt | escape }}"
                              width="{{ section.settings.image_3.width }}"
                              height="{{ section.settings.image_3.width | divided_by: section.settings.image_3.aspect_ratio | round }}"
                              class="dt-sc-block-image"
                              >
               {% else %}
               {{ 'lifestyle-2' | placeholder_svg_tag: 'placeholder-svg' }}
               {% endif %}            
            </div>
            {% if section.settings.link_3 != blank %}  
         </a>
         {% endif%}
         <div class="featured-content">
            {% if section.settings.title_3 != blank %}
            <h3>{{ section.settings.title_3 }}</h3>
            {% endif %}
            {% if section.settings.text_3 != blank %}
            <p>{{ section.settings.text_3}}</p>
            {% endif %}  
            {% if section.settings.button_3 != blank %}
            <a class="button" href="{{ section.settings.link_3 }}">{{ section.settings.button_3}}</a>    
            {% endif %}               
         </div>
      </div>
      {% endif %}
   </div>
   {% endif %} 
   {% if section.settings.enable_promo_block %}
</div>
{% endif %}
</div>
</div>        
{%- if request.design_mode -%}
<script src="{{ 'theme-editor.js' | asset_url }}" defer="defer"></script>
{%- endif -%}
{% schema %}
{
"name": "t:sections.slideshow-with-promo-image.name",
"tag": "section",
"class": "section slideshow-with-promo-image",
"settings": [
{
"type":"checkbox",
"id":"page_full_width",
"default": false,
"label": "t:sections.all.page_full_width.label"
},
{
"type":"checkbox",
"id":"page_full_width_spacing",
"default": false,
"label": "t:sections.all.page_full_width_spacing.label"
},
{
"type": "header",
"content": "t:sections.all.swiper.swiper_slider_title"
},
{
"type": "checkbox",
"id": "swiper_pagination",
"default": false,
"label": "t:sections.all.swiper.swiper_pagination"
},
{
"type": "checkbox",
"id": "swiper_navigation",
"default": false,
"label": "t:sections.all.swiper.swiper_navigation"
},
{
"type": "range",
"id": "auto_play",
"min": 0,
"max": 5,
"step": 1,
"label": "t:sections.all.swiper.auto_play",
"default": 0
},
{
"type": "select",
"id": "slider_effect",
"options": [
{
"value": "slide",
"label": "t:sections.all.swiper.slide"
},
{
"value": "fade",
"label": "t:sections.all.swiper.fade"
}
],
"default": "slide",
"label": "t:sections.all.swiper.effects"
},
{
"type": "header",
"content": "t:sections.slideshow-with-promo-image.settings.accessibility.content"
},
{
"type": "text",
"id": "accessibility",
"label": "t:sections.slideshow-with-promo-image.settings.accessibility.label",
"info": "t:sections.slideshow-with-promo-image.settings.accessibility.info",
"default": "slideshow-with-promo-image about our brand"
},
{
"type": "header",
"content": "t:sections.slideshow-with-promo-image.settings.grouped_content_settings.content"
},
{	
"type": "text",	
"id": "content_width",	
"label": "t:sections.slideshow-with-promo-image.settings.content_width.label",	
"default":"70/30"
},	
{	
"type": "text",	
"id": "lap_content_width",	
"label": "t:sections.slideshow-with-promo-image.settings.lap_content_width.label",	
"default":"65/35"
},
{	
"type": "text",	
"id": "tab_content_width",	
"label": "t:sections.slideshow-with-promo-image.settings.tab_content_width.label",	
"default":"50/50",	
"info": "For Mobile resolutions 100/100 comes by default"	
},
{	
"type": "text",	
"id": "grouped_column_gap",	
"label": "t:sections.slideshow-with-promo-image.settings.grouped_column_gap.label",	
"default":"30",	
"info": "Units not needed"	
},
{
"type" : "checkbox",
"id" : "reverse_column",
"label" : "t:sections.slideshow-with-promo-image.settings.reverse_column.label",
"default": false
},  
{
"type": "header",
"content": "t:sections.slideshow-with-promo-image.settings.promo_block_settings.content"
},
{
"type" : "checkbox",
"id" : "enable_promo_block",
"label" : "t:sections.slideshow-with-promo-image.settings.enable_promo_block.label",
"default": true
},
{
"type": "select",
"id": "promo_block_style",
"label": "t:sections.slideshow-with-promo-image.settings.promo_block_style.label",
"options": [
{
"value": "grid",
"label": "t:sections.slideshow-with-promo-image.settings.promo_block_style.grid.label"
},
{
"value": "overlay",
"label": "t:sections.slideshow-with-promo-image.settings.promo_block_style.overlay.label"
}
]
},
{
"type": "range",
"id": "promo_height_desktop",
"label": "t:sections.slideshow-with-promo-image.settings.promo_height_desktop.label",
"min": 100,
"max": 1000,
"step": 10,
"default": 300,	
"unit": "px"	
},
{
"type": "range",
"id": "promo_height_lap",
"label": "t:sections.slideshow-with-promo-image.settings.promo_height_lap.label",
"min": 100,
"max": 1000,
"step": 10,
"default": 300,	
"unit": "px"	
},
{
"type": "range",
"id": "promo_height_tab",
"label": "t:sections.slideshow-with-promo-image.settings.promo_height_tab.label",
"min": 100,
"max": 1000,
"step": 10,
"default": 300,	
"unit": "px"	
},
{
"type": "range",
"id": "promo_height_mobile",
"label": "t:sections.slideshow-with-promo-image.settings.promo_height_mobile.label",
"min": 100,
"max": 1000,
"step": 10,
"default": 300,	
"unit": "px"	
},
{
"type": "header",
"content": "t:sections.slideshow-with-promo-image.settings.additional_block_1.content"
},
{
"type" : "checkbox",
"id" : "enable_additional_block_1",
"label" : "t:sections.slideshow-with-promo-image.settings.enable_additional_block_1.label",
"default": true
},
{
"type": "image_picker",
"id": "image_1",
"label": "t:sections.slideshow-with-promo-image.settings.image.label",
"info": "Size: 650x380"
},
{
"type": "text",
"id": "title_1",
"label": "t:sections.slideshow-with-promo-image.settings.heading.label",
"default": "Heading"
},
{
"type": "text",
"id": "text_1",
"label": "t:sections.slideshow-with-promo-image.settings.description.label",
"default": "Short description"
},
{
"type": "text",
"id": "button_1",
"label": "t:sections.slideshow-with-promo-image.settings.button_text.label",
"default": "Shop Now"
},
{
"type": "url",
"id": "link_1",
"label": "t:sections.slideshow-with-promo-image.settings.link_url.label"
},
{
"type": "header",
"content": "t:sections.slideshow-with-promo-image.settings.additional_block_2.content"
},
{
"type" : "checkbox",
"id" : "enable_additional_block_2",
"label" : "t:sections.slideshow-with-promo-image.settings.enable_additional_block_2.label",
"default": true
},
{
"type": "image_picker",
"id": "image_2",
"label": "t:sections.slideshow-with-promo-image.settings.image.label",
"info": "Size: 325x380"
},
{
"type": "text",
"id": "title_2",
"label": "t:sections.slideshow-with-promo-image.settings.heading.label",
"default": "Heading"
},
{
"type": "text",
"id": "text_2",
"label": "t:sections.slideshow-with-promo-image.settings.description.label",
"default": "Short description"
},
{
"type": "text",
"id": "button_2",
"label": "t:sections.slideshow-with-promo-image.settings.button_text.label",
"default": "Shop Now"
},
{
"type": "url",
"id": "link_2",
"label": "t:sections.slideshow-with-promo-image.settings.link_url.label"
},
{
"type": "header",
"content": "t:sections.slideshow-with-promo-image.settings.additional_block_3.content"
},
{
"type" : "checkbox",
"id" : "enable_additional_block_3",
"label" : "t:sections.slideshow-with-promo-image.settings.enable_additional_block_3.label",
"default": false
},
{
"type": "image_picker",
"id": "image_3",
"label": "t:sections.slideshow-with-promo-image.settings.image.label",
"info": "Size: 325x380"
},
{
"type": "text",
"id": "title_3",
"label": "t:sections.slideshow-with-promo-image.settings.heading.label",
"default": "Heading"
},
{
"type": "text",
"id": "text_3",
"label": "t:sections.slideshow-with-promo-image.settings.description.label",
"default": "Short Description"
},
{
"type": "text",
"id": "button_3",
"label": "t:sections.slideshow-with-promo-image.settings.button_text.label",
"default": "Shop Now"
},
{
"type": "url",
"id": "link_3",
"label": "t:sections.slideshow-with-promo-image.settings.link_url.label"
},
{
"type": "header",
"content": "t:sections.all.padding.section_padding_heading"
},
{
"type": "range",
"id": "padding_top",
"min": 0,
"max": 100,
"step": 4,
"unit": "px",
"label": "t:sections.all.padding.padding_top",
"default": 36
},
{
"type": "range",
"id": "padding_bottom",
"min": 0,
"max": 100,
"step": 4,
"unit": "px",
"label": "t:sections.all.padding.padding_bottom",
"default": 36
}
],
"blocks": [
{
"type": "slide",
"name": "t:sections.slideshow-with-promo-image.blocks.slide.name",
"limit": 5,
"settings": [
{
"type": "image_picker",
"id": "image",
"label": "t:sections.slideshow-with-promo-image.blocks.slide.settings.image.label"
},
{
"type": "image_picker",
"id": "mobile_image",
"label": "t:sections.slideshow-with-promo-image.blocks.slide.settings.mobile_image.label"
},
{
"type": "text",
"id": "heading",
"default": "Image slide",
"label": "t:sections.slideshow-with-promo-image.blocks.slide.settings.heading.label"
},
{
"type": "select",
"id": "heading_size",
"options": [
{
"value": "h2",
"label": "t:sections.all.heading_size.options__1.label"
},
{
"value": "h1",
"label": "t:sections.all.heading_size.options__2.label"
},
{
"value": "h0",
"label": "t:sections.all.heading_size.options__3.label"
}
],
"default": "h1",
"label": "t:sections.all.heading_size.label"
},
{
"type": "text",
"id": "subheading",
"default": "Image sub heading",
"label": "t:sections.slideshow-with-promo-image.blocks.slide.settings.subheading.label"
},
{
"type": "text",
"id": "text",
"default": "Tell your brand's story through images",
"label": "t:sections.slideshow-with-promo-image.blocks.slide.settings.text.label"
},
{
"type": "text",
"id": "button_label",
"default": "Button label",
"label": "t:sections.slideshow-with-promo-image.blocks.slide.settings.button_label.label",
"info": "t:sections.slideshow-with-promo-image.blocks.slide.settings.button_label.info"
},
{
"type": "url",
"id": "link",
"label": "t:sections.slideshow-with-promo-image.blocks.slide.settings.link.label"
},
{
"type": "checkbox",
"id": "button_style_secondary",
"label": "t:sections.slideshow-with-promo-image.blocks.slide.settings.secondary_style.label",
"default": false
},
{
"type": "select",
"id": "box_align",
"options": [
{
"value": "top-left",
"label": "t:sections.slideshow-with-promo-image.blocks.slide.settings.box_align.options__1.label"
},
{
"value": "top-center",
"label": "t:sections.slideshow-with-promo-image.blocks.slide.settings.box_align.options__2.label"
},
{
"value": "top-right",
"label": "t:sections.slideshow-with-promo-image.blocks.slide.settings.box_align.options__3.label"
},
{
"value": "middle-left",
"label": "t:sections.slideshow-with-promo-image.blocks.slide.settings.box_align.options__4.label"
},
{
"value": "middle-center",
"label": "t:sections.slideshow-with-promo-image.blocks.slide.settings.box_align.options__5.label"
},
{
"value": "middle-right",
"label": "t:sections.slideshow-with-promo-image.blocks.slide.settings.box_align.options__6.label"
},
{
"value": "bottom-left",
"label": "t:sections.slideshow-with-promo-image.blocks.slide.settings.box_align.options__7.label"
},
{
"value": "bottom-center",
"label": "t:sections.slideshow-with-promo-image.blocks.slide.settings.box_align.options__8.label"
},
{
"value": "bottom-right",
"label": "t:sections.slideshow-with-promo-image.blocks.slide.settings.box_align.options__9.label"
}
],
"default": "middle-center",
"label": "t:sections.slideshow-with-promo-image.blocks.slide.settings.box_align.label",
"info": "t:sections.slideshow-with-promo-image.blocks.slide.settings.box_align.info"
},
{
"type": "checkbox",
"id": "show_text_box",
"label": "t:sections.slideshow-with-promo-image.blocks.slide.settings.show_text_box.label",
"default": true
},
{
"type": "select",
"id": "text_alignment",
"options": [
{
"value": "left",
"label": "t:sections.slideshow-with-promo-image.blocks.slide.settings.text_alignment.option_1.label"
},
{
"value": "center",
"label": "t:sections.slideshow-with-promo-image.blocks.slide.settings.text_alignment.option_2.label"
},
{
"value": "right",
"label": "t:sections.slideshow-with-promo-image.blocks.slide.settings.text_alignment.option_3.label"
}
],
"default": "center",
"label": "t:sections.slideshow-with-promo-image.blocks.slide.settings.text_alignment.label"
},
{
"type": "range",
"id": "image_overlay_opacity",
"min": 0,
"max": 100,
"step": 10,
"unit": "%",
"label": "t:sections.slideshow-with-promo-image.blocks.slide.settings.image_overlay_opacity.label",
"default": 0
},
{
"type": "select",
"id": "color_scheme",
"options": [
{
"value": "accent-1",
"label": "t:sections.all.colors.accent_1.label"
},
{
"value": "accent-2",
"label": "t:sections.all.colors.accent_2.label"
},
{
"value": "background-1",
"label": "t:sections.all.colors.background_1.label"
},
{
"value": "background-2",
"label": "t:sections.all.colors.background_2.label"
},
{
"value": "inverse",
"label": "t:sections.all.colors.inverse.label"
}
],
"default": "background-1",
"label": "t:sections.all.colors.label",
"info": "t:sections.slideshow-with-promo-image.blocks.slide.settings.color_scheme.info"
},
{
"type": "header",
"content": "t:sections.slideshow-with-promo-image.settings.mobile.content"
},
{
"type": "select",
"id": "text_alignment_mobile",
"options": [
{
"value": "left",
"label": "t:sections.slideshow-with-promo-image.blocks.slide.settings.text_alignment_mobile.options__1.label"
},
{
"value": "center",
"label": "t:sections.slideshow-with-promo-image.blocks.slide.settings.text_alignment_mobile.options__2.label"
},
{
"value": "right",
"label": "t:sections.slideshow-with-promo-image.blocks.slide.settings.text_alignment_mobile.options__3.label"
}
],
"default": "center",
"label": "t:sections.slideshow-with-promo-image.blocks.slide.settings.text_alignment_mobile.label"
}
]
}
],
"presets": [
{
"name": "t:sections.slideshow-with-promo-image.presets.name",
"blocks": [
{
"type": "slide"
},
{
"type": "slide"
}
]
}
]
}
{% endschema %}