{{ 'collage.css' | asset_url | stylesheet_tag }}
{{ 'component-card.css' | asset_url | stylesheet_tag }}
{{ 'component-price.css' | asset_url | stylesheet_tag }}
{{ 'component-modal-video.css' | asset_url | stylesheet_tag }}

{%- style -%}
    .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.50 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.50 | round: 0 }}px;
  }
   @media screen and (min-width: 576px) and (max-width: 749px){
   .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }
   }
  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}

<link rel="stylesheet" href="{{ 'component-deferred-media.css' | asset_url }}" media="print" onload="this.media='all'">

<div class="{% if section.settings.page_full_width %} page-full-width {% else %} page-width {% endif %} {% if section.settings.page_full_width_spacing %} page-full-width_spacing {% endif %}section-{{ section.id }}-padding isolate">
<div class="row">  
<div class="color-{{ section.settings.color_scheme }} gradient isolate">
  <div class="{% if section.settings.heading == blank %} no-heading{% endif %} section-{{ section.id }}-padding">
    {%- unless section.settings.title == blank -%}
      <div class="title-wrapper-with-link title-wrapper--self-padded-mobile title-wrapper--no-top-margin content-align--{{ section.settings.column_alignment }}">
         {%- if section.settings.sub_heading != blank -%}  
         <h6 class="sub-heading">{{ section.settings.sub_heading | escape }}</h6>
         {%- endif -%} 
         {%- if section.settings.title != blank -%}  
          <h2 class="title {{ section.settings.heading_size }}">
            {{ section.settings.title | escape }}
          </h2>
          {%- endif -%} 
          {%- if section.settings.description != blank -%}  
          <p class="description">{{ section.settings.description }}</p>
          {%- endif -%}   
          {%- if section.settings.button_label != blank -%}
            <a {% if section.settings.button_link == blank %} role="link" aria-disabled="true"{% else %} href="{{ section.settings.button_link }}"{% endif %} class="button{% if section.settings.button_style_secondary %} button--secondary{% else %} button--primary{% endif %}">{{ section.settings.button_label | escape }}</a>
          {%- endif -%}
          </div>
    {%- endunless -%}
    <div class="collage{% if section.settings.mobile_layout == 'collage' %} collage--mobile{% endif %}">
      {%- for block in section.blocks -%}
        <div
          class="collage__item collage__item--{{ block.type }} collage__item--{{ section.settings.desktop_layout }}"
          {{ block.shopify_attributes }}
        >
          {%- case block.type -%}
            {%- when 'image' -%}
              <div class="collage-card {% if section.settings.card_styles == 'none' %}global-media-settings{% else %}card-wrapper {{ section.settings.card_styles }} color-{{ settings.card_color_scheme }}{% endif %}">
                <div
                  class="media media--transparent ratio"
                  {% if block.settings.image != blank %}
                    style="--ratio-percent: {{ 1 | divided_by: block.settings.image.aspect_ratio | times: 100 }}%"
                  {% else %}
                    style="--ratio-percent: 100%"
                  {% endif %}
                >
                  {%- if block.settings.image != blank -%}
                    {%- capture sizes -%}(min-width: {{ settings.page_width }}px) {% if section.blocks.size == 1 %}calc({{ settings.page_width }}px - 100px){% else %}{{ settings.page_width | minus: 100 | times: 0.67 | round }}px{% endif %}, (min-width: 750px){% if section.blocks.size == 1 %} calc(100vw - 100px){% else %} 500px{% endif %}, calc(100vw - 30px){%- endcapture -%}
                    {{
                      block.settings.image
                      | image_url: width: 3000
                      | image_tag: loading: 'lazy', sizes: sizes, widths: '550, 720, 990, 1100, 1500, 2200, 3000'
                    }}
                  {%- else -%}
                    {{ 'image' | placeholder_svg_tag: 'placeholder-svg placeholder' }}
                  {%- endif -%}
                </div>
              </div>
            {%- when 'product' -%}
              {% render 'card-product',
                card_product: block.settings.product,
                media_aspect_ratio: 'adapt',
                show_secondary_image: block.settings.second_image,
                extend_height: true
              %}
            {%- when 'collection' -%}
              {% render 'card-collection',
                card_collection: block.settings.collection,
                media_aspect_ratio: 'adapt',
                columns: 2,
                extend_height: true,
                wrapper_class: section.settings.card_styles
              %}
            {%- when 'video' -%}
              <div class="collage-card {% if section.settings.card_styles == 'none' %}global-media-settings{% else %}{{ section.settings.card_styles }} color-{{ settings.card_color_scheme }}{% endif %}">
                <noscript>
                  <a
                    href="{{ block.settings.video_url }}"
                    class="collage-card__link"
                  >
                    <div
                      class="media media--transparent ratio"
                      {% if block.settings.cover_image != blank %}
                        style="--ratio-percent: {{ 1 | divided_by: block.settings.cover_image.aspect_ratio | times: 100 }}%"
                      {% else %}
                        style="--ratio-percent: 100%"
                      {% endif %}
                    >
                      {%- if block.settings.cover_image != blank -%}
                        {%- capture sizes -%}
                          (min-width: {{ settings.page_width }}px)
                          {% if section.blocks.size == 1 -%}
                            calc({{ settings.page_width }}px - 100px)
                          {%- else -%}
                            {{- settings.page_width | minus: 100 | times: 0.67 | round }}px
                          {%- endif -%}
                          , (min-width: 750px)
                          {%- if section.blocks.size == 1 %} calc(100vw - 100px){% else %} 500px{% endif -%}
                          , calc(100vw - 30px)
                        {%- endcapture -%}
                        {{
                          block.settings.cover_image
                          | image_url: width: 3000
                          | image_tag: loading: 'lazy', sizes: sizes, widths: '550, 720, 990, 1100, 1500, 2200, 3000'
                        }}
                      {%- else -%}
                        {{ 'collection-2' | placeholder_svg_tag: 'placeholder-svg placeholder' }}
                      {%- endif -%}
                    </div>
                  </a>
                </noscript>
                <modal-opener class="no-js-hidden" data-modal="#PopupModal-{{ block.id }}">
                  <div class="deferred-media">
                    <button
                      class="deferred-media__poster full-unstyled-link"
                      type="button"
                      aria-label="{{ 'sections.video.load_video' | t: description: block.settings.description | escape }}"
                      aria-haspopup="dialog"
                      data-media-id="{{ block.settings.video_url.id }}"
                    >
                      <div
                        class="media media--transparent ratio"
                        {% if block.settings.cover_image != blank %}
                          style="--ratio-percent: {{ 1 | divided_by: block.settings.cover_image.aspect_ratio | times: 100 }}%"
                        {% else %}
                          style="--ratio-percent: 100%"
                        {% endif %}
                      >
                        <span class="deferred-media__poster-button motion-reduce">
                          {%- render 'icon-play' -%}
                        </span>

                        {%- if block.settings.cover_image != blank -%}
                          {%- capture sizes -%}
                            (min-width: {{ settings.page_width }}px)
                            {% if section.blocks.size == 1 -%}
                              calc({{ settings.page_width }}px - 100px)
                            {%- else -%}
                              {{- settings.page_width | minus: 100 | times: 0.67 | round }}px
                            {%- endif -%}
                            , (min-width: 750px)
                            {%- if section.blocks.size == 1 %} calc(100vw - 100px){% else %} 500px{% endif -%}
                            , calc(100vw - 30px)
                          {%- endcapture -%}
                          {{
                            block.settings.cover_image
                            | image_url: width: 3000
                            | image_tag: loading: 'lazy', sizes: sizes, widths: '550, 720, 990, 1100, 1500, 2200, 3000'
                          }}
                        {%- else -%}
                          {{ 'collection-2' | placeholder_svg_tag: 'placeholder-svg placeholder' }}
                        {%- endif -%}
                      </div>
                    </button>
                  </div>
                </modal-opener>
                <modal-dialog id="PopupModal-{{ block.id }}" class="modal-video media-modal color-background-1">
                  <div
                    class="modal-video__content"
                    role="dialog"
                    aria-label="{{ block.settings.description | escape }}"
                    aria-modal="true"
                    tabindex="-1"
                  >
                    <button
                      id="ModalClose-{{ block.id }}"
                      type="button"
                      class="modal-video__toggle"
                      aria-label="{{ 'accessibility.close' | t }}"
                    >
                      {% render 'icon-close' %}
                    </button>
                    <div class="modal-video__content-info">
                      <deferred-media class="modal-video__video template-popup">
                        <template>
                          {%- if block.settings.video_url.type == 'youtube' -%}
                            <iframe
                              src="https://www.youtube.com/embed/{{ block.settings.video_url.id }}?enablejsapi=1"
                              class="js-youtube"
                              allow="autoplay; encrypted-media"
                              allowfullscreen
                              title="{{ block.settings.description | escape }}"
                            ></iframe>
                          {%- else -%}
                            <iframe
                              src="https://player.vimeo.com/video/{{ block.settings.video_url.id }}"
                              class="js-vimeo"
                              allow="autoplay; encrypted-media"
                              allowfullscreen
                              title="{{ block.settings.description | escape }}"
                            ></iframe>
                          {%- endif -%}
                        </template>
                      </deferred-media>
                    </div>
                  </div>
                </modal-dialog>
              </div>
          {%- endcase -%}
        </div>
      {%- endfor -%}
    </div>
  </div>
</div>
</div>
</div>
{% schema %}
{
  "name": "t:sections.collage.name",
  "tag": "section",
  "class": "section section-collage",
  "settings": [
    {
      "type":"checkbox",
      "id":"page_full_width",
       "default": false,
      "label": "t:sections.all.page_full_width.label"
    },
    {
      "type":"checkbox",
      "id":"page_full_width_spacing",
       "default": false,
      "label": "t:sections.all.page_full_width_spacing.label"
    },
   {
      "type": "text",
      "id": "title",
      "default": "Multimedia Colllage",
      "label": "t:sections.all.title.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__3.label"
        }
      ],
      "default": "h1",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "text",
      "id": "sub_heading",
      "default": "Sub Heading", 
      "label": "t:sections.all.sub_heading.label"
    },
     {
      "type": "text",
      "id": "description",
      "default": "Use This Text To Share The Information Which You Like!.", 
      "label": "t:sections.all.description.label"
    },
    {
      "type": "text",
      "id": "button_label",
      "default": "Button label",
      "label": "t:sections.all.button_label.label"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "t:sections.all.button_link.label"
    },
    {
      "type": "checkbox",
      "id": "button_style_secondary",
      "default": false,
      "label": "t:sections.all.button_style_secondary.label"
      },
    {
      "type": "select",
      "id": "column_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.collage.settings.column_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.collage.settings.column_alignment.options__2.label"
        }
      ],
      "default": "center",
      "label": "t:sections.collage.settings.column_alignment.label"
    },
    {
      "type": "select",
      "id": "desktop_layout",
      "options": [
        {
          "value": "left",
          "label": "t:sections.collage.settings.desktop_layout.options__1.label"
        },
        {
          "value": "right",
          "label": "t:sections.collage.settings.desktop_layout.options__2.label"
        }
      ],
      "default": "left",
      "label": "t:sections.collage.settings.desktop_layout.label"
    },
    {
      "type": "select",
      "id": "mobile_layout",
      "options": [
        {
          "value": "collage",
          "label": "t:sections.collage.settings.mobile_layout.options__1.label"
        },
        {
          "value": "column",
          "label": "t:sections.collage.settings.mobile_layout.options__2.label"
        }
      ],
      "default": "column",
      "label": "t:sections.collage.settings.mobile_layout.label"
    },
    {
      "type": "select",
      "id": "card_styles",
      "options": [
        {
          "value": "none",
          "label": "t:sections.collage.settings.card_styles.options__1.label"
        },
        {
          "value": "product-card-wrapper",
          "label": "t:sections.collage.settings.card_styles.options__2.label"
        }
      ],
      "default": "product-card-wrapper",
      "info": "t:sections.collage.settings.card_styles.info",
      "label": "t:sections.collage.settings.card_styles.label"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "t:sections.all.colors.accent_1.label"
        },
        {
          "value": "accent-2",
          "label": "t:sections.all.colors.accent_2.label"
        },
        {
          "value": "background-1",
          "label": "t:sections.all.colors.background_1.label"
        },
        {
          "value": "background-2",
          "label": "t:sections.all.colors.background_2.label"
        },
        {
          "value": "inverse",
          "label": "t:sections.all.colors.inverse.label"
        }
      ],
      "default": "background-1",
      "label": "t:sections.all.colors.label",
      "info": "t:sections.all.colors.has_cards_info"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    }
  ],
  "blocks": [
    {
      "type": "image",
      "name": "t:sections.collage.blocks.image.name",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.collage.blocks.image.settings.image.label"
        }
      ]
    },
    {
      "type": "product",
      "name": "t:sections.collage.blocks.product.name",
      "settings": [
        {
          "type": "product",
          "id": "product",
          "label": "t:sections.collage.blocks.product.settings.product.label"
        },
        {
          "type": "checkbox",
          "id": "second_image",
          "default": false,
          "label": "t:sections.collage.blocks.product.settings.second_image.label"
        }
      ]
    },
    {
      "type": "collection",
      "name": "t:sections.collage.blocks.collection.name",
      "settings": [
        {
          "type": "collection",
          "id": "collection",
          "label": "t:sections.collage.blocks.collection.settings.collection.label"
        }
      ]
    },
    {
      "type": "video",
      "name": "t:sections.collage.blocks.video.name",
      "settings": [
        {
          "type": "image_picker",
          "id": "cover_image",
          "label": "t:sections.collage.blocks.video.settings.cover_image.label"
        },
        {
          "type": "video_url",
          "id": "video_url",
          "accept": ["youtube", "vimeo"],
          "default": "https://www.youtube.com/watch?v=_9VUPq3SxOc",
          "label": "t:sections.collage.blocks.video.settings.video_url.label",
          "placeholder": "t:sections.collage.blocks.video.settings.video_url.placeholder",
          "info": "t:sections.collage.blocks.video.settings.video_url.info"
        },
        {
          "type": "text",
          "id": "description",
          "default": "Describe the video",
          "label": "t:sections.collage.blocks.video.settings.description.label",
          "info": "t:sections.collage.blocks.video.settings.description.info"
        }
      ]
    }
  ],
  "max_blocks": 3,
  "presets": [
    {
      "name": "t:sections.collage.presets.name",
      "blocks": [
        {
          "type": "image"
        },
        {
          "type": "product"
        },
        {
          "type": "collection"
        }
      ]
    }
  ]
}
{% endschema %}