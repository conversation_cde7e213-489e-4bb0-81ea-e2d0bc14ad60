/* deal timer start */

.featured-product .deal-clock {
  text-align: center;
}

.featured-product .deal-clock ul{
  list-style: none;
  padding: 0;
}

.featured-product .days, .featured-product .hours, .featured-product .minutes, .featured-product .seconds {
  position: relative;
  display: inline-block;
  margin: 5px;
  height: 70px;
  width: 70px;
  border-radius: 100px;
  background-color: var(--gradient-base-accent-2);
  color: var(--gradient-base-background-2);
  font-weight: 500;
  line-height: 40px;
  font-size: 1.5em;
}

.featured-product .days span, .featured-product .hours span, .featured-product .minutes span, .featured-product .seconds span {
  display: block;
  line-height: normal;
  font-size: 0.6em;
  font-weight: 900;
}

/* deal timer end */

.featured-product .product__media-list {
  width: 100%;
  margin: 0;
  padding-bottom: 0;
}

.featured-product .global-media-settings:after {
  z-index: 0;
}

.featured-product .product__modal-opener {
  margin-bottom: var(--media-shadow-vertical-offset);
}

.featured-product .product__media-item {
  padding-left: 0;
  width: 100%;
}

.featured-product .product__media-item:not(:first-child) {
  display: none;
}

.background-secondary .featured-product {
  padding: 2.5rem;
}

.featured-product .share-button:nth-last-child(2) {
  display: inline-flex;margin:0;
}

.share-button + .product__view-details {
  display: inline-flex;
  float: right;
  align-items: center;margin:0;
}

.share-button + .product__view-details::after {
  content: "";
  clear: both;
  display: table;
}

@media screen and (min-width: 750px) {
  .featured-product .product__media-item {
    padding-bottom: 0;
  }

  .background-secondary .featured-product {
    padding: 5rem;
  }
}

@media screen and (min-width: 990px) {
  .background-secondary .featured-product:not(.product--no-media) > .product__info-wrapper {
    padding: 0 0 0 5rem;
  }

  .featured-product:not(.product--no-media) > .product__info-wrapper {
    padding: 0 0 0 4rem;
  }

  .background-secondary .featured-product {
    padding: 6rem 7rem;
    position: relative;
    z-index: 1;
  }
}
@media screen and (max-width: 989px) {
  .featured-product:not(.product--no-media) > .product__info-wrapper {
    padding: 40px 0 0;
  }
}
@media screen and (max-width: 749px) {
.featured-product .product__xr-button[data-shopify-xr-hidden] {display: none;}
}
@media screen and (min-width: 576px) and (max-width: 1199px){
.collection.custom-featured-collection .grid--2-col-tablet-down .grid__item{
    width: calc(50% - var(--grid-desktop-horizontal-spacing) * 2 / 2);
    max-width: calc(50% - var(--grid-desktop-horizontal-spacing) * 2 / 2);
}
}

.featured-product .product-deal-count .deal-clock{position:relative; margin:unset;}
.featured-product .product-deal-count .deal-clock ul{display:grid;grid-template-columns:repeat(4, 1fr);gap:5px;margin:0; padding:0;}
.featured-product .product-deal-count .deal-clock ul li{background:rgba(var(--color-base-accent-1),.05);padding:0.6rem; border-radius: 0; width: 100%;height:auto; align-items: center; display: flex; flex-direction: column; justify-content: center;    font-size: 34px;font-weight: 600;}
.featured-product .product-deal-count .deal-clock ul li span{color:var(--gradient-base-accent-1);font-size:10px;font-weight:500;}
