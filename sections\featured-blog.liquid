<link rel="stylesheet" href="{{ 'component-slider.css' | asset_url }}" media="print" onload="this.media='all'">
<link rel="stylesheet" href="{{ 'component-card.css' | asset_url }}" media="print" onload="this.media='all'">
<link rel="stylesheet" href="{{ 'component-article-card.css' | asset_url }}" media="print" onload="this.media='all'">
<link rel="stylesheet" href="{{ 'section-featured-blog.css' | asset_url }}" media="print" onload="this.media='all'">

<noscript>{{ 'component-slider.css' | asset_url | stylesheet_tag }}</noscript>
<noscript>{{ 'component-card.css' | asset_url | stylesheet_tag }}</noscript>
<noscript>{{ 'component-article-card.css' | asset_url | stylesheet_tag }}</noscript>

{{ 'section-featured-blog.css' | asset_url | stylesheet_tag }}

{%- style -%}

    .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.50 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.50 | round: 0 }}px;
  }
   @media screen and (min-width: 576px) and (max-width: 749px){
   .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }
   }
 .blog__posts.articles-wrapper .article.blog-overlay-style .card-wrapper { height:{{ section.settings.overlay_style_height }}px } 
  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  } 
  .blog__posts .blog__post.blog-overlay-style .card-wrapper:before {  opacity: {{ section.settings.overlay_opacity }};  }
   @media screen and (min-width: 1541px) {
    .blog__post.blog-list-style .card:not(.ratio)>.card__content { width: {{ leftColumn }}%; }
    .blog__post.blog-list-style .article-card-wrapper .card .card__inner { width: {{ rightColumn }}%; }
    }
   @media screen and (max-width: 1540px) {
    .blog__post.blog-list-style .card:not(.ratio)>.card__content { width: {{ lap_leftColumn }}%; }
    .blog__post.blog-list-style .article-card-wrapper .card .card__inner { width: {{ lap_rightColumn }}% }
    }
  @media screen and (max-width: 989px) {
  .blog .slider.slider--mobile{flex-wrap: inherit;}
  }
   @media screen and (max-width: 749px) {
   .blog__post.blog-list-style .article-card-wrapper .card .card__inner { width:100%; }
   .blog__post.blog-list-style .card:not(.ratio)>.card__content { width:100%; }
    }
.blog__posts .blog__post.blog-overlay-style .card-wrapper:before {   content: ""; display: block; width: 100%; height: 100%; -webkit-transition: all linear 0.3s; 
  transition: all linear 0.3s; position: absolute; pointer-events: none; z-index: 1; background: rgba(var(--color-overlay), 0.8); }
  .article__image {aspect-ratio: var(--aspect-ratio);}
  .blog  .slider-buttons .slider-button svg{    width: 11px;height: 11px;fill: currentColor;}
{%- endstyle -%}
     
{%- liquid
   assign posts_displayed = section.settings.blog.articles_count
  if section.settings.post_limit >= section.settings.blog.articles_count
    assign posts_exceed_limit = true
    assign posts_displayed = section.settings.post_limit
  endif
    

   if section.settings.block_banner_style == 'grid-style'
  assign item_style = 'grid-style'
  elsif section.settings.block_banner_style == 'list-style'
  assign item_style = 'list-style'
  elsif section.settings.block_banner_style == 'overlay-style'
  assign item_style = 'overlay-style'
  endif

  assign listWidth = section.settings.content_width | split: '/'
   
   assign leftColumn = listWidth[0]
   assign rightColumn = listWidth[1]

  assign listWidth = section.settings.masonry_content_width | split: '/'
  assign masonryleftColumn = listWidth[0]
  assign masonryrightColumn = listWidth[1]

  assign listWidth = section.settings.lap_content_width | split: '/'
  assign lap_leftColumn = listWidth[0]
  assign lap_rightColumn = listWidth[1]

  assign listWidth = section.settings.lap_masonry_content_width | split: '/'
  assign lap_masonryleftColumn = listWidth[0]
  assign lap_masonryrightColumn = listWidth[1]

  
   assign columns_mobile_int = section.settings.columns_mobile | plus: 0
  assign show_mobile_slider = false
  if section.settings.swipe_on_mobile and posts_displayed > columns_mobile_int
    assign show_mobile_slider = true
  endif
    
  
  assign show_desktop_slider = false
  if section.settings.enable_desktop_slider and posts_displayed > section.settings.columns_desktop
    assign show_desktop_slider = true
  endif

  if section.settings.overlay_style == 'use_overlay'
  assign overlay_class = 'with-overlay-normal'
  elsif section.settings.overlay_style == 'use_gradient_overlay'
  assign overlay_class = 'with-overlay-gradient'
  endif

  if section.settings.swiper_enable
  assign enable_slider = true  
  endif

-%}


  <div class="blog color-{{ section.settings.color_scheme }} gradient{% if section.settings.heading == blank %} no-heading{% endif %}">
<div class="{% if section.settings.page_full_width %} page-full-width {% else %} page-width {% endif %} {% if section.settings.page_full_width_spacing %} page-full-width_spacing {% endif %}section-{{ section.id }}-padding isolate">
 <div class="row"> 
  <div class=" {{ section.settings.custom_class_name }}{% if posts_displayed < 3 %} page-width-tablet{% endif %} {% if show_mobile_slider %} title-wrapper--self-padded-tablet-down{% endif %}{% if show_desktop_slider %} blog__title--desktop-slider{% endif %}">
    {%- unless section.settings.title == blank -%}
      <div class="title-wrapper-with-link{% if posts_displayed > 2 %} title-wrapper--self-padded-tablet-down{% else %} title-wrapper--self-padded-mobile{% endif %} title-wrapper--no-top-margin content-align--{{ section.settings.column_alignment }}">
        {%- if section.settings.sub_heading != blank -%}  
         <h6 class="sub-heading">{{ section.settings.sub_heading | escape }}</h6>
         {%- endif -%} 
         {%- if section.settings.title != blank -%}  
          <h2 class="title {{ section.settings.heading_size }}">
            {{ section.settings.title | escape }}
          </h2>
          {%- endif -%} 
          {%- if section.settings.description != blank -%}  
          <p class="description">{{ section.settings.description }}</p>
          {%- endif -%}   
          {%- if section.settings.button_label != blank -%}
            <a {% if section.settings.button_link == blank %} role="link" aria-disabled="true"{% else %} href="{{ section.settings.button_link }}"{% endif %} class="button{% if section.settings.button_style_secondary %} button--secondary{% else %} button--primary{% endif %}">{{ section.settings.button_label | escape }}</a>
          {%- endif -%}
       
      </div>
    {%- endunless -%}    
    {%- if section.settings.blog != blank and section.settings.blog.articles_count > 0 -%}
      {% unless enable_slider %}
      <slider-component class="slider-mobile-gutter{% if show_mobile_slider == false and section.settings.page_full_width == false %} {% endif %}{% if show_desktop_slider == false and section.settings.full_width == false %} page-width-desktop{% endif %}{% if show_desktop_slider %} slider-component-desktop{% endif %}">
        {% else %}
    <blog-slider class="{% if show_mobile_slider == false %} page-width{% endif %}{% if show_desktop_slider == false and section.settings.full_width == false %} page-width-desktop{% endif %}">
      <div data-slider-options='{"loop": "true","desktop": "{{ section.settings.desktop_column }}", "laptop": "{{ section.settings.laptop_column }}", "tablet": "{{ section.settings.tablet_column }}","mobile": "{{ section.settings.mobile_column }}","auto_play": "{{ section.settings.auto_play }}"}'>
      <div class="swiper" data-swiper-slider>
      {%- endunless -%}
        <div id="Slider-{{ section.id }}"
          class="blog__posts articles-wrapper contains-card contains-card--article{% if settings.card_style == 'standard' %} contains-card--standard{% endif %} {% if enable_slider %} swiper-wrapper{% else %} grid grid--peek grid--{{ section.settings.columns_mobile }}-col-tablet-down  grid--{{ section.settings.columns_desktop }}-col-desktop {% if show_mobile_slider or show_desktop_slider %} slider{% if show_desktop_slider %} slider--tablet-up{% endif %}{% if show_mobile_slider %} slider--mobile grid--peek{% endif %}{% endif %}{% endif %}"
          role="list"
        >
          {%- for article in section.settings.blog.articles limit: section.settings.post_limit -%}
            <div id="Slide-{{ section.id }}-{{ forloop.index }}" class="blog__post article   {% if enable_slider %} swiper-slide{% else %} grid__item{% if show_desktop_slider %} slider--desktop{% endif %}{% if show_mobile_slider %} slider--mobile{% endif %}{% endif %} blog-{{ item_style }}">
              {% render 'article-card', blog: section.settings.blog, article: article, media_aspect_ratio: 1.66, show_image: section.settings.show_image, show_date: section.settings.show_date,show_comment:section.settings.enable_comments ,show_author: section.settings.show_author,show_excerpt: section.settings.show_excerpts,blog_style: item_style %}
            </div>
          {%- endfor -%}
        </div>
        {% unless enable_slider %}
         {%- if show_mobile_slider -%}
          {% if section.settings.arrow_on_mobile %}
        <div class="slider-buttons no-js-hidden large-up-hide">
          <button type="button" class="slider-button slider-button--prev" name="previous" aria-label="{{ 'general.slider.previous_slide' | t }}">{% render 'icon-caret' %}</button>
          <div class="slider-counter caption">
          </div>
          <button type="button" class="slider-button slider-button--next" name="next" aria-label="{{ 'general.slider.next_slide' | t }}">{% render 'icon-caret' %}</button>
        </div>
      {% endif %}
        {%- endif -%}
      </slider-component>
      {% else %}
       {% if section.settings.swiper_navigation != blank %}
        <div class="swiper-button-next"><span></span></div>
        <div class="swiper-button-prev"><span></span></div>
        {% endif %}
    </div>
    {% if section.settings.swiper_pagination != blank %}
    <div class="swiper-pagination"></div>
    {% endif %}
    </div>
    </blog-slider>
    {%- endunless -%}
      
      {%- if section.settings.show_view_all and section.settings.post_limit < section.settings.blog.articles_count -%}
        <div class="blog__view-all center small-hide medium-hide">
          <a href="{{ section.settings.blog.url }}" id="ViewAll-{{ section.id }}" class="blog__button button" aria-labelledby="ViewAll-{{ section.id }} SectionHeading-{{ section.id }}">
            {{ 'sections.featured_blog.view_all' | t }}
          </a>
        </div>
      {%- endif -%}
    {%- else -%}
      <div class="blog-placeholder">
        <div class="placeholder media media--landscape">
          {{ 'image' | placeholder_svg_tag: 'placeholder-svg' }}
        </div>
        <div class="blog-placeholder__content">
          <h2>
            {{ 'sections.featured_blog.onboarding_title' | t }}
          </h2>
          <p class="rte-width">
            {{ 'sections.featured_blog.onboarding_content' | t }}
          </p>
        </div>
      </div>  
    {%- endif -%}
  </div>
</div>
</div>
</div>
{% javascript %}  
document.querySelectorAll('[id^="shopify-section-"]').forEach((card, index) => {
   console.log = function(){};
   if(card.classList.contains('featured-blog')) {     
    const topHeading = card.querySelectorAll('.for-arrow-alignment');        
    const cardHeight = parseInt(topHeading[0].clientHeight);
    const topValue = cardHeight / 2 + 5;
    const cardTopCSS = "calc(50% - "+topValue+'px)';     
    const swiperContainer = card.querySelector('.swiper');    
     if(!swiperContainer) return;
    const swiperPrevElement = swiperContainer.querySelector('.swiper-button-prev');    
    const swiperNextElement = swiperContainer.querySelector('.swiper-button-next');     
    if(!swiperPrevElement || !swiperNextElement) return
    swiperPrevElement.setAttribute('style', 'top:'+cardTopCSS);    
    swiperNextElement.setAttribute('style', 'top:'+cardTopCSS);
  }        
});
{% endjavascript %}
{% schema %}
{
  "name": "t:sections.featured-blog.name",
  "tag": "section",
  "class": "section featured-blog",
  "settings": [
    {
      "type":"checkbox",
      "id":"page_full_width",
       "default": false,
      "label": "t:sections.all.page_full_width.label"
    },
    {
      "type":"checkbox",
      "id":"page_full_width_spacing",
       "default": false,
      "label": "t:sections.all.page_full_width_spacing.label"
    },
 {
      "type": "text",
      "id": "title",
      "default": "Featured Blog",
      "label": "t:sections.all.title.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__3.label"
        }
      ],
      "default": "h1",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "text",
      "id": "sub_heading",
     "default":"Sub heading",  
      "label": "t:sections.all.sub_heading.label"
    },
     {
      "type": "text",
      "id": "description",
      "default":"Use this text to share the information which you like!.",    
      "label": "t:sections.all.description.label"
    },
    {
      "type": "text",
      "id": "button_label",
      "default": "Button label",
      "label": "t:sections.all.button_label.label"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "t:sections.all.button_link.label"
    },
    {
      "type": "checkbox",
      "id": "button_style_secondary",
      "default": false,
      "label": "t:sections.all.button_style_secondary.label"
      },
    {
      "type": "select",
      "id": "column_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.featured-blog.settings.column_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.featured-blog.settings.column_alignment.options__2.label"
        }
      ],
      "default": "center",
      "label": "t:sections.featured-blog.settings.column_alignment.label"
    },
    {
      "type": "blog",
      "id": "blog",
      "label": "t:sections.featured-blog.settings.blog.label"
    },
    {
      "type": "range",
      "id": "post_limit",
      "min": 2,
      "max": 10,
      "step": 1,
      "default": 3,
      "label": "t:sections.featured-blog.settings.post_limit.label"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 4,
      "step": 1,
      "default": 3,
      "label": "t:sections.featured-blog.settings.columns_desktop.label"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "t:sections.all.colors.accent_1.label"
        },
        {
          "value": "accent-2",
          "label": "t:sections.all.colors.accent_2.label"
        },
        {
          "value": "background-1",
          "label": "t:sections.all.colors.background_1.label"
        },
        {
          "value": "background-2",
          "label": "t:sections.all.colors.background_2.label"
        },
        {
          "value": "inverse",
          "label": "t:sections.all.colors.inverse.label"
        }
      ],
      "default": "background-1",
      "label": "t:sections.all.colors.label",
      "info": "t:sections.all.colors.has_cards_info"
    },
    {
      "type": "checkbox",
      "id": "show_image",
      "default": true,
      "label": "t:sections.featured-blog.settings.show_image.label",
      "info": "t:sections.featured-blog.settings.show_image.info"
    },
    {
      "type": "checkbox",
      "id": "show_date",
      "default": true,
      "label": "t:sections.featured-blog.settings.show_date.label"
    },
    {
      "type": "checkbox",
      "id": "show_author",
      "default": false,
      "label": "t:sections.featured-blog.settings.show_author.label"
    },
    {
      "type": "checkbox",
      "id": "show_excerpts",
      "label": "t:sections.featured-blog.settings.show_excerpts.label"
      },
    {
      "type": "checkbox",
      "id": "enable_comments",
      "label": "t:sections.featured-blog.settings.enable_comments.label"
      },
    {
      "type": "checkbox",
      "id": "show_view_all",
      "default": true,
      "label": "t:sections.featured-blog.settings.show_view_all.label"
    },
    {
    "type": "select",
    "id": "block_banner_style",
    "label": "Banner Style",
    "options": [
    {
    "value": "grid-style",
    "label": "t:sections.featured-blog.settings.block_banner_style.options__1.label"
    },
    {
    "value": "list-style",
    "label": "t:sections.featured-blog.settings.block_banner_style.options__2.label"
    },
    {
    "value": "overlay-style",
    "label": "t:sections.featured-blog.settings.block_banner_style.options__3.label"
    }
    ]
  },
  {
  "type": "header",
  "content": "Settings for List Style Banners"
  },
  {
  "type": "text",
  "id": "content_width",
  "label": "Content Width - Desktop",
  "default":"50/50"
  },
  {
  "type": "text",
  "id": "lap_content_width",
  "label": "Content Width - Laptop (Small Screen)",
  "default":"40/60"
  },
    {
  "type": "header",
  "content": "Settings for Overlay Style Banners"
  },
  {
  "type": "range",
  "id": "overlay_style_height",
  "label": "Minimum Height",
  "min": 200,
  "max": 1000,
  "step": 10,
  "default": 400,
  "unit": "px"
  },  
  {
  "type": "text",
  "id": "overlay_opacity",
  "label": "Overlay Opacity (0.01 to 1)",
  "default": "0.5"
  },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
  {
    "type": "header",
    "content": "t:sections.all.custom_class_heading.content"
    },
    {
    "type": "text",
    "id": "custom_class_name",
    "label": "t:sections.all.custom_class_name.label"
    },
  {
    "type": "header",
    "content": "t:sections.all.swiper.swiper_slider_title"
  },
  {
    "type": "checkbox",
    "id": "swiper_enable",
    "default": false,
    "label": "t:sections.all.swiper.swiper_slider_enable"
  },
  {
    "type": "range",
    "id": "desktop_column",
    "min": 1,
    "max": 10,
    "step": 1,
    "label": "t:sections.all.swiper.desktop_column",
    "default": 4
  },
  {
    "type": "range",
    "id": "laptop_column",
    "min": 1,
    "max": 10,
    "step": 1,
    "label": "t:sections.all.swiper.laptop_column",
    "default": 4
  },
  {
    "type": "range",
    "id": "tablet_column",
    "min": 1,
    "max": 5,
    "step": 1,
    "label": "t:sections.all.swiper.tablet_column",
    "default": 3
  },
  {
    "type": "range",
    "id": "mobile_column",
    "min": 1,
    "max": 3,
    "step": 1,
    "label": "t:sections.all.swiper.mobile_column",
    "default": 1
  },
  {
    "type": "checkbox",
    "id": "swiper_pagination",
    "default": false,
    "label": "t:sections.all.swiper.swiper_pagination"
  },
  {
    "type": "checkbox",
    "id": "swiper_navigation",
    "default": false,
    "label": "t:sections.all.swiper.swiper_navigation"
  },
  {
    "type": "range",
    "id": "auto_play",
    "min": 0,
    "max": 5,
    "step": 1,
    "label": "t:sections.all.swiper.auto_play",
    "default": 0
  },
  {
      "type": "checkbox",
      "id": "swipe_on_mobile",
      "default": false,
      "label": "t:sections.featured-blog.settings.swipe_on_mobile.label"
  },
  {
      "type": "checkbox",
      "id": "arrow_on_mobile",
      "default": false,
      "label": "t:sections.all.swiper.controls"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "options": [
        {
          "value": "1",
          "label": "t:sections.all.swiper.options__1"
        },
        {
          "value": "2",
          "label": "t:sections.all.swiper.options__2"
        }
      ],
      "default": "1",
      "label": "t:sections.all.swiper.columns_mobile"
    }
  ],
  "presets": [
    {
      "name": "t:sections.featured-blog.presets.name",
      "settings": {
        "blog": "News"
      }
    }
  ]
}
{% endschema %}
