{{ 'component-loading-overlay.css' | asset_url | stylesheet_tag }}
{{ 'component-card.css' | asset_url | stylesheet_tag }}
{{ 'component-price.css' | asset_url | stylesheet_tag }}
{{ 'component-slider.css' | asset_url | stylesheet_tag }}
{{ 'component-swiper.css' | asset_url | stylesheet_tag }}
{{ 'swiper-bundle.min.css' | asset_url | stylesheet_tag }}
{{ 'section-product-carousel.css' | asset_url | stylesheet_tag }}

{%- if settings.enable_quickadd or settings.enable_quickview -%}
  <link rel="stylesheet" href="{{ 'quick-add.css' | asset_url }}" media="print" onload="this.media='all'">
  <script src="{{ 'quick-add.js' | asset_url }}" defer="defer"></script>
  <script src="{{ 'product-form.js' | asset_url }}" defer="defer"></script>
{%- endif -%}

<script src="{{ 'swiper-bundle.min.js' | asset_url }}" defer="defer"></script>

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.50 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.50 | round: 0 }}px;
  }
  
  @media screen and (min-width: 576px) and (max-width: 749px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
      padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
    }
  }
  
  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  .product-carousel-{{ section.id }} {
    position: relative;
    overflow: hidden;
  }

  .product-carousel-{{ section.id }} .swiper {
    overflow: visible;
  }

  .product-carousel-{{ section.id }} .swiper-slide {
    height: auto;
    display: flex;
    align-items: stretch;
  }

  .product-carousel-{{ section.id }} .swiper-slide .card-wrapper {
    width: 100%;
    height: 100%;
  }

  .product-carousel-{{ section.id }} .swiper-button-next,
  .product-carousel-{{ section.id }} .swiper-button-prev {
    color: var(--gradient-base-accent-1);
    background: rgba(var(--color-background), 0.9);
    border-radius: 50%;
    width: 44px;
    height: 44px;
    margin-top: -22px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
  }

  .product-carousel-{{ section.id }} .swiper-button-next:hover,
  .product-carousel-{{ section.id }} .swiper-button-prev:hover {
    background: rgba(var(--color-background), 1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: scale(1.05);
  }

  .product-carousel-{{ section.id }} .swiper-button-next::after,
  .product-carousel-{{ section.id }} .swiper-button-prev::after {
    font-size: 16px;
    font-weight: bold;
  }

  .product-carousel-{{ section.id }} .swiper-pagination {
    position: static;
    margin-top: 2rem;
  }

  .product-carousel-{{ section.id }} .swiper-pagination-bullet {
    background: rgba(var(--color-foreground), 0.3);
    opacity: 1;
    transition: all 0.3s ease;
  }

  .product-carousel-{{ section.id }} .swiper-pagination-bullet-active {
    background: var(--gradient-base-accent-1);
    transform: scale(1.2);
  }

  {% if section.settings.hide_arrows_mobile %}
    @media screen and (max-width: 749px) {
      .product-carousel-{{ section.id }} .swiper-button-next,
      .product-carousel-{{ section.id }} .swiper-button-prev {
        display: none;
      }
    }
  {% endif %}

  {% if section.settings.hide_pagination_desktop %}
    @media screen and (min-width: 750px) {
      .product-carousel-{{ section.id }} .swiper-pagination {
        display: none;
      }
    }
  {% endif %}

  /* Responsive grid variables */
  .product-carousel-{{ section.id }} .grid {
    --grid-desktop-columns: {{ section.settings.columns_desktop }};
    --grid-tablet-columns: {{ section.settings.columns_tablet }};
    --grid-mobile-columns: {{ section.settings.columns_mobile }};
  }

  /* Enhanced mobile touch experience */
  @media screen and (max-width: 749px) {
    .product-carousel-{{ section.id }} .swiper {
      padding-left: 1rem;
      padding-right: 1rem;
    }

    .product-carousel-{{ section.id }} .swiper-slide {
      margin-right: 1rem;
    }

    .product-carousel-{{ section.id }} .swiper-slide:last-child {
      margin-right: 0;
    }
  }

  /* Improved card spacing */
  .product-carousel-{{ section.id }} .card {
    margin-bottom: 0;
  }

  /* Loading state */
  .product-carousel-{{ section.id }} .loading-overlay {
    min-height: 400px;
  }
{%- endstyle -%}

{%- liquid
  assign products_to_display = section.settings.collection.all_products_count
  if section.settings.collection.all_products_count > section.settings.products_to_show
    assign products_to_display = section.settings.products_to_show
  endif

  assign enable_carousel = true
  if products_to_display <= section.settings.columns_desktop and section.settings.force_carousel == false
    assign enable_carousel = false
  endif
-%}

<div class="color-{{ section.settings.color_scheme }} gradient">
  <div class="product-carousel section-{{ section.id }}-padding{% unless section.settings.full_width %} page-width{% endunless %}">
    
    {%- if section.settings.title != blank -%}
      <div class="title-wrapper-with-link title-wrapper--self-padded-mobile title-wrapper--no-top-margin">
        <h2 id="SectionHeading-{{ section.id }}" class="collection-hero__title">
          {{ section.settings.title }}
        </h2>
        
        {%- if section.settings.show_view_all and section.settings.collection != blank -%}
          <a href="{{ section.settings.collection.url }}" 
             id="ViewAll-{{ section.id }}"
             class="link underlined-link large-up-hide"
             aria-labelledby="ViewAll-{{ section.id }} SectionHeading-{{ section.id }}">
            {{ 'sections.featured_collection.view_all' | t }}
          </a>
        {%- endif -%}
      </div>
    {%- endif -%}

    {%- if section.settings.description != blank -%}
      <div class="collection-hero__description rte">
        {{ section.settings.description }}
      </div>
    {%- endif -%}

    {%- if section.settings.collection != blank and section.settings.collection.products.size > 0 -%}
      <div class="product-carousel-{{ section.id }}{% if enable_carousel %} swiper-container{% endif %}"
           {% if enable_carousel %}
           data-slider-options='{
             "desktop": {{ section.settings.columns_desktop }},
             "laptop": {{ section.settings.columns_laptop }},
             "tablet": {{ section.settings.columns_tablet }},
             "mobile": {{ section.settings.columns_mobile }},
             "auto_play": {{ section.settings.auto_play }},
             "loop": {{ section.settings.enable_loop }},
             "options": {
               "spaceBetween": {{ section.settings.space_between }},
               "grabCursor": true,
               "watchSlidesProgress": true
             }
           }'
           {% endif %}>
        
        <div class="{% if enable_carousel %}swiper-wrapper{% else %}grid grid--{{ section.settings.columns_desktop }}-col-desktop grid--{{ section.settings.columns_mobile }}-col-tablet-down{% endif %}" 
             role="list" 
             aria-label="{{ section.settings.title | default: 'Product carousel' | escape }}">
          
          {%- for product in section.settings.collection.products limit: section.settings.products_to_show -%}
            <div class="{% if enable_carousel %}swiper-slide{% else %}grid__item{% endif %} card_style-{{ section.settings.card_style }}">
              {%- case section.settings.card_style -%}
                {%- when 'standard' -%}
                  {%- assign placeholder_image_index = forloop.index0 | modulo: 4 | plus: 1 -%}
                  {%- assign placeholder_image = 'product-apparel-' | append: placeholder_image_index -%}
                  {% render 'card-product',
                    card_product: product,
                    media_aspect_ratio: section.settings.image_ratio,
                    show_secondary_image: section.settings.show_secondary_image,
                    show_vendor: section.settings.show_vendor,
                    show_rating: section.settings.show_rating,
                    show_quick_add: settings.enable_quickadd,
                    show_quick_view: settings.enable_quickview,
                    show_new_tag: section.settings.show_new_tag,
                    placeholder_image: placeholder_image,
                    section_id: section.id,
                    lazy_load: true,
                    extend_height: true
                  %}
                {%- when 'button_width_icons' -%}
                  {% render 'card-product-2',
                    card_product: product,
                    media_aspect_ratio: section.settings.image_ratio,
                    show_secondary_image: section.settings.show_secondary_image,
                    show_vendor: section.settings.show_vendor,
                    show_rating: section.settings.show_rating,
                    show_quick_add: settings.enable_quickadd,
                    show_quick_view: settings.enable_quickview,
                    show_new_tag: section.settings.show_new_tag,
                    section_id: section.id,
                    lazy_load: true,
                    extend_height: true
                  %}
                {%- when 'card_with_icons' -%}
                  {% render 'card-product-3',
                    card_product: product,
                    media_aspect_ratio: section.settings.image_ratio,
                    show_secondary_image: section.settings.show_secondary_image,
                    show_vendor: section.settings.show_vendor,
                    show_rating: section.settings.show_rating,
                    show_quick_add: settings.enable_quickadd,
                    show_quick_view: settings.enable_quickview,
                    show_new_tag: section.settings.show_new_tag,
                    section_id: section.id,
                    lazy_load: true,
                    extend_height: true
                  %}
                {%- when 'card_with_buttons' -%}
                  {% render 'card-product-4',
                    card_product: product,
                    media_aspect_ratio: section.settings.image_ratio,
                    show_secondary_image: section.settings.show_secondary_image,
                    show_vendor: section.settings.show_vendor,
                    show_rating: section.settings.show_rating,
                    show_quick_add: settings.enable_quickadd,
                    show_quick_view: settings.enable_quickview,
                    show_new_tag: section.settings.show_new_tag,
                    section_id: section.id,
                    lazy_load: true,
                    extend_height: true
                  %}
                {%- when 'card_with_overlay' -%}
                  {% render 'card-product-5',
                    card_product: product,
                    media_aspect_ratio: section.settings.image_ratio,
                    show_secondary_image: section.settings.show_secondary_image,
                    show_vendor: section.settings.show_vendor,
                    show_rating: section.settings.show_rating,
                    show_quick_add: settings.enable_quickadd,
                    show_quick_view: settings.enable_quickview,
                    show_new_tag: section.settings.show_new_tag,
                    section_id: section.id,
                    lazy_load: true,
                    extend_height: true
                  %}
              {%- endcase -%}
            </div>
          {%- else -%}
            {%- for i in (1..section.settings.products_to_show) -%}
              <div class="{% if enable_carousel %}swiper-slide{% else %}grid__item{% endif %}">
                {%- assign placeholder_image_index = forloop.index0 | modulo: 4 | plus: 1 -%}
                {%- assign placeholder_image = 'product-apparel-' | append: placeholder_image_index -%}
                {% render 'card-product', 
                  show_vendor: section.settings.show_vendor, 
                  placeholder_image: placeholder_image 
                %}
              </div>
            {%- endfor -%}
          {%- endfor -%}
        </div>

        {%- if enable_carousel and section.settings.show_navigation -%}
          <div class="swiper-button-next" aria-label="{{ 'general.slider.next' | t }}"></div>
          <div class="swiper-button-prev" aria-label="{{ 'general.slider.previous' | t }}"></div>
        {%- endif -%}

        {%- if enable_carousel and section.settings.show_pagination -%}
          <div class="swiper-pagination"></div>
        {%- endif -%}
      </div>
    {%- else -%}
      <div class="collection collection--empty page-width" id="product-grid" data-id="{{ section.id }}">
        <div class="loading-overlay gradient"></div>
        <div class="title-wrapper center">
          <h2 class="title title--primary">
            {{ 'sections.collection_template.empty' | t }}<br>
            {{ 'sections.collection_template.use_fewer_filters_html' | t: link: section.settings.collection.url, class: "underlined-link link" }}
          </h2>
        </div>
      </div>
    {%- endif -%}

    {%- if section.settings.show_view_all and section.settings.collection != blank -%}
      <div class="center collection-hero__button">
        <a class="button"
           href="{{ section.settings.collection.url }}"
           aria-labelledby="SectionHeading-{{ section.id }}">
          {{ 'sections.featured_collection.view_all' | t }}
        </a>
      </div>
    {%- endif -%}

    <!-- Skip link target for accessibility -->
    <div id="after-carousel-{{ section.id }}" class="sr-only" tabindex="-1" aria-label="End of carousel"></div>
  </div>
</div>

{%- if enable_carousel -%}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const carouselElement = document.querySelector('.product-carousel-{{ section.id }}[data-slider-options]');
    if (!carouselElement) return;

    const options = JSON.parse(carouselElement.getAttribute('data-slider-options'));

    const swiperConfig = {
      init: false,
      spaceBetween: options.options.spaceBetween || 30,
      grabCursor: options.options.grabCursor || true,
      watchSlidesProgress: options.options.watchSlidesProgress || true,
      loop: options.loop,
      lazy: {
        loadPrevNext: true,
        loadPrevNextAmount: 2,
        loadOnTransitionStart: true
      },
      preloadImages: false,
      updateOnImagesReady: true,
      autoplay: options.auto_play > 0 ? {
        delay: options.auto_play * 1000,
        disableOnInteraction: false,
        pauseOnMouseEnter: true,
        waitForTransition: true
      } : false,
      navigation: {
        nextEl: '.product-carousel-{{ section.id }} .swiper-button-next',
        prevEl: '.product-carousel-{{ section.id }} .swiper-button-prev',
      },
      pagination: {
        el: '.product-carousel-{{ section.id }} .swiper-pagination',
        clickable: true,
        dynamicBullets: true,
        renderBullet: function (index, className) {
          return '<span class="' + className + '" aria-label="Go to slide ' + (index + 1) + '" role="button" tabindex="0"></span>';
        }
      },
      a11y: {
        enabled: true,
        prevSlideMessage: 'Previous product',
        nextSlideMessage: 'Next product',
        firstSlideMessage: 'This is the first product',
        lastSlideMessage: 'This is the last product',
        paginationBulletMessage: 'Go to product {{index}}'
      },
      keyboard: {
        enabled: true,
        onlyInViewport: true,
        pageUpDown: false
      },
      mousewheel: {
        forceToAxis: true,
        sensitivity: 0.5,
        releaseOnEdges: true
      },
      breakpoints: {
        320: {
          slidesPerView: options.mobile,
          spaceBetween: Math.max(options.options.spaceBetween - 10, 10),
          slidesPerGroup: 1
        },
        576: {
          slidesPerView: options.tablet,
          spaceBetween: options.options.spaceBetween,
          slidesPerGroup: Math.min(options.tablet, 2)
        },
        992: {
          slidesPerView: options.laptop,
          spaceBetween: options.options.spaceBetween,
          slidesPerGroup: Math.min(options.laptop, 3)
        },
        1200: {
          slidesPerView: options.desktop,
          spaceBetween: options.options.spaceBetween,
          slidesPerGroup: Math.min(options.desktop, 4)
        }
      },
      on: {
        init: function() {
          this.update();

          // Add accessibility attributes
          this.slides.forEach((slide, index) => {
            slide.setAttribute('role', 'group');
            slide.setAttribute('aria-label', `Product ${index + 1} of ${this.slides.length}`);
            slide.setAttribute('aria-roledescription', 'slide');

            // Add focus management
            const focusableElements = slide.querySelectorAll('a, button, [tabindex]:not([tabindex="-1"])');
            focusableElements.forEach(el => {
              if (index !== this.activeIndex) {
                el.setAttribute('tabindex', '-1');
              }
            });
          });

          // Add live region for announcements
          if (!document.getElementById('carousel-live-region-{{ section.id }}')) {
            const liveRegion = document.createElement('div');
            liveRegion.id = 'carousel-live-region-{{ section.id }}';
            liveRegion.setAttribute('aria-live', 'polite');
            liveRegion.setAttribute('aria-atomic', 'true');
            liveRegion.className = 'sr-only';
            carouselElement.appendChild(liveRegion);
          }

          // Pause autoplay on focus
          if (this.autoplay && this.autoplay.running) {
            carouselElement.addEventListener('focusin', () => {
              this.autoplay.pause();
            });
            carouselElement.addEventListener('focusout', () => {
              this.autoplay.resume();
            });
          }
        },
        slideChange: function() {
          // Update focus management
          this.slides.forEach((slide, index) => {
            const focusableElements = slide.querySelectorAll('a, button, [tabindex]:not([tabindex="-1"])');
            focusableElements.forEach(el => {
              if (index === this.activeIndex) {
                el.removeAttribute('tabindex');
              } else {
                el.setAttribute('tabindex', '-1');
              }
            });
          });

          // Announce slide change to screen readers
          const activeSlide = this.slides[this.activeIndex];
          const liveRegion = document.getElementById('carousel-live-region-{{ section.id }}');

          if (activeSlide && liveRegion) {
            const productTitle = activeSlide.querySelector('.card__heading a');
            if (productTitle) {
              liveRegion.textContent = `Now showing: ${productTitle.textContent.trim()}`;
            }
          }
        },
        autoplayStart: function() {
          const liveRegion = document.getElementById('carousel-live-region-{{ section.id }}');
          if (liveRegion) {
            liveRegion.textContent = 'Carousel autoplay started';
          }
        },
        autoplayStop: function() {
          const liveRegion = document.getElementById('carousel-live-region-{{ section.id }}');
          if (liveRegion) {
            liveRegion.textContent = 'Carousel autoplay stopped';
          }
        },
        reachBeginning: function() {
          const prevButton = carouselElement.querySelector('.swiper-button-prev');
          if (prevButton) {
            prevButton.setAttribute('aria-disabled', 'true');
          }
        },
        reachEnd: function() {
          const nextButton = carouselElement.querySelector('.swiper-button-next');
          if (nextButton) {
            nextButton.setAttribute('aria-disabled', 'true');
          }
        },
        fromEdge: function() {
          const prevButton = carouselElement.querySelector('.swiper-button-prev');
          const nextButton = carouselElement.querySelector('.swiper-button-next');
          if (prevButton) prevButton.removeAttribute('aria-disabled');
          if (nextButton) nextButton.removeAttribute('aria-disabled');
        }
      }
    };

    const swiper = new Swiper(carouselElement, swiperConfig);
    swiper.init();

    // Enhanced keyboard navigation
    carouselElement.addEventListener('keydown', function(e) {
      // Only handle keys when carousel container is focused
      if (document.activeElement === carouselElement) {
        switch(e.key) {
          case 'ArrowLeft':
            e.preventDefault();
            swiper.slidePrev();
            break;
          case 'ArrowRight':
            e.preventDefault();
            swiper.slideNext();
            break;
          case 'Home':
            e.preventDefault();
            swiper.slideTo(0);
            break;
          case 'End':
            e.preventDefault();
            swiper.slideTo(swiper.slides.length - 1);
            break;
          case 'Enter':
          case ' ':
            e.preventDefault();
            // Focus on the first focusable element in the active slide
            const activeSlide = swiper.slides[swiper.activeIndex];
            const firstFocusable = activeSlide.querySelector('a, button, [tabindex]:not([tabindex="-1"])');
            if (firstFocusable) {
              firstFocusable.focus();
            }
            break;
        }
      }
    });

    // Intersection Observer for performance optimization
    if ('IntersectionObserver' in window) {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            // Preload images in visible slides
            const images = entry.target.querySelectorAll('img[data-src]');
            images.forEach(img => {
              if (img.dataset.src) {
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
                img.classList.add('loaded');
              }
            });
          }
        });
      }, {
        rootMargin: '50px'
      });

      swiper.slides.forEach(slide => {
        observer.observe(slide);
      });
    }

    // Reduce motion for users who prefer it
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
      swiper.autoplay && swiper.autoplay.stop();
      // Reduce transition duration
      swiper.params.speed = 200;
      swiper.update();
    }

    // Make carousel focusable for keyboard navigation
    carouselElement.setAttribute('tabindex', '0');
    carouselElement.setAttribute('role', 'region');
    carouselElement.setAttribute('aria-label', 'Product carousel. Use arrow keys to navigate, Enter to interact with products.');
    carouselElement.setAttribute('aria-roledescription', 'carousel');

    // Add skip link for accessibility
    const skipLink = document.createElement('a');
    skipLink.href = '#after-carousel-{{ section.id }}';
    skipLink.textContent = 'Skip carousel';
    skipLink.className = 'sr-only sr-only-focusable';
    skipLink.style.cssText = 'position: absolute; top: -40px; left: 6px; z-index: 1000; padding: 8px 16px; background: var(--color-background); color: var(--color-foreground); text-decoration: none; border-radius: 4px;';
    skipLink.addEventListener('focus', function() {
      this.style.top = '6px';
    });
    skipLink.addEventListener('blur', function() {
      this.style.top = '-40px';
    });
    carouselElement.insertBefore(skipLink, carouselElement.firstChild);

    // Performance monitoring
    if (window.performance && window.performance.mark) {
      window.performance.mark('carousel-{{ section.id }}-initialized');
    }
  });
</script>
{%- endif -%}

{% schema %}
{
  "name": "Product Carousel",
  "tag": "section",
  "class": "section",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "settings": [
    {
      "type": "inline_richtext",
      "id": "title",
      "default": "Featured Products",
      "label": "Heading"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description"
    },
    {
      "type": "collection",
      "id": "collection",
      "label": "Collection"
    },
    {
      "type": "range",
      "id": "products_to_show",
      "min": 2,
      "max": 25,
      "step": 1,
      "default": 12,
      "label": "Maximum products to show"
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "card_style",
      "options": [
        {
          "value": "standard",
          "label": "Standard"
        },
        {
          "value": "button_width_icons",
          "label": "Button with icons"
        },
        {
          "value": "card_with_icons",
          "label": "Card with icons"
        },
        {
          "value": "card_with_buttons",
          "label": "Card with buttons"
        },
        {
          "value": "card_with_overlay",
          "label": "Card with overlay"
        }
      ],
      "default": "standard",
      "label": "Product card style"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "Adapt to image"
        },
        {
          "value": "portrait",
          "label": "Portrait"
        },
        {
          "value": "square",
          "label": "Square"
        }
      ],
      "default": "adapt",
      "label": "Image ratio"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 6,
      "step": 1,
      "default": 4,
      "label": "Number of columns on desktop"
    },
    {
      "type": "range",
      "id": "columns_laptop",
      "min": 1,
      "max": 5,
      "step": 1,
      "default": 3,
      "label": "Number of columns on laptop"
    },
    {
      "type": "range",
      "id": "columns_tablet",
      "min": 1,
      "max": 4,
      "step": 1,
      "default": 2,
      "label": "Number of columns on tablet"
    },
    {
      "type": "range",
      "id": "columns_mobile",
      "min": 1,
      "max": 3,
      "step": 1,
      "default": 1,
      "label": "Number of columns on mobile"
    },
    {
      "type": "checkbox",
      "id": "full_width",
      "default": false,
      "label": "Make section full width"
    },
    {
      "type": "header",
      "content": "Carousel Settings"
    },
    {
      "type": "checkbox",
      "id": "force_carousel",
      "default": true,
      "label": "Always show as carousel",
      "info": "When disabled, carousel will only show if there are more products than columns"
    },
    {
      "type": "checkbox",
      "id": "enable_loop",
      "default": true,
      "label": "Enable infinite loop"
    },
    {
      "type": "range",
      "id": "auto_play",
      "min": 0,
      "max": 10,
      "step": 1,
      "unit": "s",
      "label": "Auto-play speed",
      "info": "Set to 0 to disable auto-play",
      "default": 0
    },
    {
      "type": "range",
      "id": "space_between",
      "min": 0,
      "max": 50,
      "step": 5,
      "unit": "px",
      "label": "Space between slides",
      "default": 30
    },
    {
      "type": "header",
      "content": "Navigation"
    },
    {
      "type": "checkbox",
      "id": "show_navigation",
      "default": true,
      "label": "Show navigation arrows"
    },
    {
      "type": "checkbox",
      "id": "hide_arrows_mobile",
      "default": true,
      "label": "Hide arrows on mobile"
    },
    {
      "type": "checkbox",
      "id": "show_pagination",
      "default": true,
      "label": "Show pagination dots"
    },
    {
      "type": "checkbox",
      "id": "hide_pagination_desktop",
      "default": false,
      "label": "Hide pagination on desktop"
    },
    {
      "type": "header",
      "content": "Product Information"
    },
    {
      "type": "checkbox",
      "id": "show_secondary_image",
      "default": false,
      "label": "Show second product image on hover"
    },
    {
      "type": "checkbox",
      "id": "show_vendor",
      "default": false,
      "label": "Show product vendor"
    },
    {
      "type": "checkbox",
      "id": "show_rating",
      "default": false,
      "label": "Show product rating",
      "info": "To display a rating, add a product rating app. Learn more"
    },
    {
      "type": "checkbox",
      "id": "show_new_tag",
      "default": false,
      "label": "Show 'New' tag"
    },
    {
      "type": "checkbox",
      "id": "show_view_all",
      "default": true,
      "label": "Show 'View all' button"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        },
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        }
      ],
      "default": "background-1",
      "label": "Color scheme"
    },
    {
      "type": "header",
      "content": "Section padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 120,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 120,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 36
    }
  ],
  "presets": [
    {
      "name": "Product Carousel"
    }
  ]
}
{% endschema %}
