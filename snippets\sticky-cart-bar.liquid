{%- assign sticky_form_id = 'product-form-' | append: section.id -%}
<product-form
  class="product-form sticky-bar-form"
  style="{% if section.settings.enable_sticky_bar %}display:block; {% else %} display:none; {% endif %}"
  id="sticky-bar-form"
>
  {%- form 'product',
    product,
    id: sticky_form_id,
    class: 'form',
    novalidate: 'novalidate',
    data-type: 'add-to-cart-form'
  -%}
    <input
      type="hidden"
      name="id"
      value="{{ product.selected_or_first_available_variant.id }}"
      disabled
    >

    <div class="sticky-bar" >
      <div class="product-title-sticky">
        <ul
          id="Slider-Gallery-{{ section.id }}"
          class="product__media-list contains-media grid grid--peek list-unstyled slider slider--mobile"
          role="list"
        >
          {%- liquid
            assign featured_media = product.selected_or_first_available_variant.featured_media
            assign variant_images = product.images | where: 'attached_to_variant?', true | map: 'src'
            assign media_width = 0.75
          -%}
          {%- if product.selected_or_first_available_variant.featured_media != null -%}
            <li
              id="Slide-{{ section.id }}-{{ featured_media.id }}"
              class="product__media-item grid__item slider__slide is-active{% if featured_media.media_type != 'image' %} product__media-item--full{% endif %}{% if section.settings.hide_variants and variant_images contains featured_media.src %} product__media-item--variant{% endif %}"
              data-media-id="{{ section.id }}-{{ featured_media.id }}"
            >
              <img
                srcset="
                  {% if featured_media.preview_image.width >= 100 %}{{ featured_media.preview_image | image_url: width: 100 }} 100w,{% endif %}
                  {% if featured_media.preview_image.width >= 200 %}{{ featured_media.preview_image | image_url: width: 200 }} 200w,{% endif %}
                  {{ featured_media.preview_image | image_url }} 100w
                "
                src="{{ featured_media | image_url: width: 100 }}"
                sizes="(min-width: {{ settings.page_width }}px) 200px, (min-width: 990px) calc(100vw), (min-width: 750px) calc(100vw), calc(100vw)"
                width="100"
                height="{{ 100 | divided_by: featured_media.preview_image.aspect_ratio | ceil }}"
                alt="{{ featured_media.preview_image.alt | escape }}"
              >
            </li>
          {%- endif -%}
        </ul>
        <div class="sticky-title-price">
        <div class="sticky-bar-title">
          <span class="sticky-title h3">{{ product.title }}</span>
        </div>
        <div class="no-js-hidden price-wrapper" id="sticky-price-{{ section.id }}" role="status" {{ block.shopify_attributes }}>
             {%- render 'price', product: product, use_variant: true -%}
        </div>
        </div>
      </div>

      <div class="sticky-bar-price">
        <div class="variant-drop-down">
          <div 
            class="hidden product-form__input product-form__quantity{% if settings.inputs_shadow_vertical_offset != 0 and settings.inputs_shadow_vertical_offset < 0 %} product-form__quantity-top{% endif %}"
            {{ block.shopify_attributes }}
          >
            <label class="form__label" for="Quantity-{{ section.id }}">
              {{ 'products.product.quantity.label' | t }}
            </label>

            <quantity-input class="quantity">
              <button class="quantity__button no-js-hidden" name="minus" type="button">
                <span class="visually-hidden">
                  {{- 'products.product.quantity.decrease' | t: product: product.title | escape -}}
                </span>
                {% render 'icon-minus' %}
              </button>
              <input
                class="quantity__input"
                type="number"
                name="quantity"
                id="sticky-Quantity-{{ section.id }}"
                min="1"
                value="1"
                form="{{ product_form_id }}"
              >
              <button class="quantity__button no-js-hidden" name="plus" type="button">
                <span class="visually-hidden">
                  {{- 'products.product.quantity.increase' | t: product: product.title | escape -}}
                </span>
                {% render 'icon-plus' %}
              </button>
            </quantity-input>
          </div>
          {% if product.has_only_default_variant %}
            <span class="sticky-bar-price">
                <div class="no-js-hidden price-wrapper" id="sticky-price-{{ section.id }}" role="status" {{ block.shopify_attributes }}>
              {%- render 'price', product: product, use_variant: true -%}
                </div>
            </span>
          {% endif %}
          {%- unless product.has_only_default_variant -%}
             
           <variant-selects
                          class="no-js-hidden"
                          data-section="{{ section.id }}"
                          data-url="{{ product.url }}"
                          {{ block.shopify_attributes }}
                        >
                          {%- for option in product.options_with_values -%}
                            <div class="product-form__input product-form__input--dropdown">
                              <label class="form__label" for="Option-{{ section.id }}-{{ forloop.index0 }}">
                                {{ option.name }}
                              </label>
                              <div class="select">
                                <select
                                  id="Option-{{ section.id }}-{{ forloop.index0 }}"
                                  class="select__select"
                                  name="options[{{ option.name | escape }}]"
                                  form="{{ product_form_id }}"
                                >
                                  {%- for value in option.values -%}
                                    <option
                                      value="{{ value | escape }}"
                                      {% if option.selected_value == value %}
                                        selected="selected"
                                      {% endif %}
                                    >
                                      {{ value }}
                                    </option>
                                  {%- endfor -%}
                                </select>
                                {% render 'icon-caret' %}
                              </div>
                            </div>
                          {%- endfor -%}

                          <script type="application/json">
                            {{ product.variants | json }}
                          </script>
                        </variant-selects>
          {%- endunless -%}

          <div class="product-form__buttons">
            <button
              type="submit"
              name="add"
              class="product-form__submit  button button--full-width {% if block.settings.show_dynamic_checkout and product.selling_plan_groups == empty %}button--secondary{% else %}button--primary{% endif %}"
              {% if product.selected_or_first_available_variant.available == false %}
                disabled
              {% endif %}
              id="sticky-bar-button"
            >
              <span>
                {%- if product.selected_or_first_available_variant.available -%}
                  {{ 'products.product.add_to_cart' | t }}
                {%- else -%}
                  {{ 'products.product.sold_out' | t }}
                {%- endif -%}
              </span>
              <div class="loading-overlay__spinner hidden">
                <svg
                  aria-hidden="true"
                  focusable="false"
                  role="presentation"
                  class="spinner"
                  viewBox="0 0 66 66"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle class="path" fill="none" stroke-width="4" cx="33" cy="33" r="30"></circle>
                </svg>
              </div>
            </button>
          </div>
        </div>
      </div>
       <span class="sticky-bar-close">{% render 'icon-close' %}</span>
    </div>
  {% endform %} 
</product-form>
<script>
  document.querySelector('.sticky-bar-close').addEventListener('click', function () {
  const stikcyForm = document.getElementById('sticky-bar-form');
  stikcyForm.classList.add('visibility-hidden');
  });
</script>
