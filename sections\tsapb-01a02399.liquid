<!-- --VERSION-20-- -->

{% style %}
:root {
--vtl-neutral-70: #5e5e5e;
--vtl-neutral-03: #f8f8f8;
--vtl-neutral-01: #fff;
--vtl-size-1: 1px;
--vtl-size-2: 2px;
--vtl-size-4: 4px;
--vtl-size-8: 8px;
--vtl-size-12: 12px;
--vtl-size-14: 14px;
--vtl-size-16: 16px;
--vtl-size-20: 20px;
--vtl-size-24: 24px;
--vtl-size-32: 32px;
--vtl-size-40: 40px;
--vtl-size-48: 48px;
--vtl-font-weight-400: 400;
--vtl-font-weight-600: 600;
--vtl-line-height-140: 1.4;
--vtl-text-decoration-none: none;
--vtl-text-decoration-underline: underline;
--vtl-border-width-1: var(--vtl-size-1);
--vtl-color-bg-fill-inverse-on-light: var(--vtl-neutral-01);
--vtl-color-bg-surface-tertiary-on-light: var(--vtl-neutral-03);
--vtl-color-text-disable-on-light: var(--vtl-neutral-70);
--vtl-space-2: var(--vtl-size-2);
--vtl-space-4: var(--vtl-size-4);
--vtl-space-8: var(--vtl-size-8);
--vtl-space-12: var(--vtl-size-12);
--vtl-space-16: var(--vtl-size-16);
--vtl-space-20: var(--vtl-size-20);
--vtl-space-24: var(--vtl-size-24);
--vtl-space-32: var(--vtl-size-32);
--vtl-space-40: var(--vtl-size-40);
--vtl-font-size-12: var(--vtl-size-12);
--vtl-font-size-14: var(--vtl-size-14);
--vtl-font-size-16: var(--vtl-size-16);
--vtl-font-size-20: var(--vtl-size-20);
--vtl-font-size-24: var(--vtl-size-24);
--vtl-font-size-32: var(--vtl-size-32);
--vtl-font-size-48: var(--vtl-size-48);
}


{% endstyle %}

<!-- --UUID-01a02399-- -->

{%- liquid
	assign timer_block = section.blocks | where: "type", "timer" | first
	assign caption_block = section.blocks | where: "type", "caption" | first
	assign heading_block = section.blocks | where: "type", "heading" | first
	assign text_block = section.blocks | where: "type", "text" | first
	assign button_block = section.blocks | where: "type", "button" | first

	assign is_block_text_empty = false

	assign is_caption_empty = false
	assign is_heading_empty = false
	assign is_text_empty = false
	assign is_button_empty = false

	if caption_block == blank or caption_block.settings.caption == blank
		assign is_caption_empty = true
	endif

	if heading_block == blank or heading_block.settings.heading == blank
		assign is_heading_empty = true
	endif

	if button_block == blank or button_block.settings.primary_button_label == blank
		assign is_text_empty = true
	endif

	if text_block == blank or text_block.settings.description == blank
		assign is_button_empty = true
	endif

	if is_caption_empty == true and is_heading_empty == true and is_text_empty == true and is_button_empty == true
		assign is_block_text_empty = true
	endif
-%}

<section class="Vtls-{{ section.id | handle }} VtlsFlashTimer">
	<div class="VtlsFlashTimerContainer">
		<div class="VtlsFlashCountdownTimer VtlsFlashCountdownTimer--desktopPosition-{{ section.settings.timer_position_desktop }} VtlsFlashCountdownTimer--mobilePosition-{{ section.settings.timer_position_mobile }}">
			<div class="VtlsFlashCountdownTimer__Content {% if is_block_text_empty == true %}VtlsFlashCountdownTimer__Content--onlyTimer{% endif %}">
				<div class="VtlsTextBox">
					{%- for block in section.blocks -%}
						{%- case block.type -%}
							{%- when "heading" -%}
								{%- if block.settings.heading_alternative_font -%}
									<style>
										{{- block.settings.heading_font | font_face: font_display: 'swap' -}}
									</style>
								{%- endif -%}
								{%- capture heading_css_variables -%}
                                    --block-heading-size: {{- block.settings.heading_size -}}px;
                                    --block-heading-color: {{- block.settings.heading_color -}};
                                    --block-heading-font: {{- block.settings.heading_font.family -}};
                                  {%- endcapture -%}
								<h2
									style="{{- heading_css_variables | strip_newlines | escape -}}"
									class="VtlsTextBox__Heading VtlsTextBox__Heading--style-{{ block.settings.heading_style }} {% if block.settings.heading_alternative_font == true %}VtlsTextBox__Heading--alternativeFont{% endif %}"
									{{ block.shopify_attributes }}
								>
									{{- block.settings.heading -}}
								</h2>
							{%- when "caption" -%}
								{%- if block.settings.caption_alternative_font -%}
									<style>
										{{- block.settings.caption_font | font_face: font_display: 'swap' -}}
									</style>
								{%- endif -%}
								{%- capture caption_css_variables -%}
                                    --block-caption-size: {{- block.settings.caption_size -}}px;
                                    --block-caption-color: {{- block.settings.caption_color -}};
                                    --block-caption-font: {{- block.settings.caption_font.family -}};
                                    --block-caption-weight: {{- block.settings.caption_weight -}};
                                  {%- endcapture -%}
								<p
									style="{{- caption_css_variables | strip_newlines | escape -}}"
									class="VtlsTextBox__Caption {% if block.settings.caption_alternative_font == true %}VtlsTextBox__Caption--alternativeFont{% endif %}"
									{{ block.shopify_attributes }}
								>
									{{- block.settings.caption -}}
								</p>
							{%- when "text" -%}
								{%- if block.settings.description_alternative_font -%}
									{%- liquid
										assign body_font_bold = block.settings.description_font | font_modify: 'weight', 'bold'
										assign body_font_italic = block.settings.description_font | font_modify: 'style', 'italic'
										assign body_font_bold_italic = body_font_bold | font_modify: 'style', 'italic'
									%}
									<style>
										{{- block.settings.description_font | font_face: font_display: 'swap' -}}
										{{ body_font_bold | font_face: font_display: 'swap' }}
										{{ body_font_italic | font_face: font_display: 'swap' }}
										{{ body_font_bold_italic | font_face: font_display: 'swap' }}
									</style>
								{%- endif -%}
								{%- capture description_css_variables -%}
                                    --block-description-size: {{- block.settings.description_size -}}px;
                                    --block-description-color: {{- block.settings.description_color -}};
                                    --block-description-font: {{- block.settings.description_font.family -}};
                                  {%- endcapture -%}
								<div
									style="{{- description_css_variables | strip_newlines | escape -}}"
									class="VtlsTextBox__Description VtlsTextBox__Description--alignment-{{ section.settings.text_alignment }} {% if block.settings.description_alternative_font == true %}VtlsTextBox__Description--alternativeFont{% endif %}"
									{{ block.shopify_attributes }}
								>
									{{- block.settings.description -}}
								</div>
							{%- when "button" -%}
								{%- capture buttons_css_variables -%}
                                    --block-primary-button-text-color: {{- block.settings.primary_text_color -}};
                                    --block-primary-button-background-color: {{- block.settings.primary_background_color -}};
                                    --block-primary-button-corner-radius: {{- block.settings.primary_button_corner -}}px;
                                    --block-primary-button-border-color: {{- block.settings.primary_border_color -}};
                                  {%- endcapture -%}
								<div
									style="{{- buttons_css_variables | strip_newlines | escape -}}"
									class="VtlsImgTextButtons"
									{{ block.shopify_attributes }}
								>
									{%- if block.settings.primary_button_label != blank -%}
										<a
											{% if block.settings.primary_new_tab == true %}
												target="_blank"
												rel="noopener noreferrer"
											{% endif %}
											class="VtlsImgTextButtons__Primary VtlsImgTextButtons__Primary--{{ block.settings.primary_button_style }}"
											href="{{ block.settings.primary_button_link | default: "#" }}"
										>
											{{- block.settings.primary_button_label | escape -}}
										</a>
									{%- endif -%}
								</div>
						{%- endcase -%}
					{%- endfor -%}
				</div>
			</div>
			{%- if timer_block -%}
				<vtls-countdown-timer
					class="
						VtlsFlashCountdownTimer__Timer
						{% if timer_block.settings.show_numbers == true %}VtlsFlashCountdownTimer__Timer--onlyNumbers{% endif %}
						{% if is_block_text_empty == true %}VtlsFlashCountdownTimer__Timer--onlyTimer{% endif %}
					"
					{{ timer_block.shopify_attributes }}
				>
					{%- assign end_date = timer_block.settings.end_date -%}
					{%- assign end_time = timer_block.settings.end_time -%}

					{%- capture heading_css_variables -%}
                        --block-numbers-color: {{- timer_block.settings.numbers_color -}};
                        --block-text-color: {{- timer_block.settings.text_color -}};
                    {%- endcapture -%}
					<div
						style="{{- heading_css_variables | strip_newlines | escape -}}"
						id="VtlsCountdownTimer"
						class="VtlsCountdownTimer"
						{%- if end_date != blank and end_time != blank -%}
							data-end-date="{{ end_date }}"
							data-end-time="{{ end_time }}"
						{%- endif -%}
						data-completed-text="{{ section.settings.completed_message }}"
						data-hide-section="{{ section.settings.hide_section }}"
					>
						<div
							class="
								VtlsCountdownTimerActive
								VtlsCountdownTimerActive--{{ timer_block.settings.countdown_size }}
								VtlsCountdownTimerActive--{{ timer_block.settings.numbers_style }}
							"
						>
							<div class="VtlsCountdownTimerActive__Item">
								<h6 class="VtlsCountdownTimerActive__Number VtlsDays">00</h6>
								<span class="VtlsCountdownTimerActive__Text">days</span>
							</div>
							<span class="VtlsCountdownTimerActive__Divider">:</span>
							<div class="VtlsCountdownTimerActive__Item">
								<h6 class="VtlsCountdownTimerActive__Number VtlsHours">00</h6>
								<span class="VtlsCountdownTimerActive__Text">hours</span>
							</div>
							<span class="VtlsCountdownTimerActive__Divider">:</span>
							<div class="VtlsCountdownTimerActive__Item">
								<h6 class="VtlsCountdownTimerActive__Number VtlsMinutes">00</h6>
								<span class="VtlsCountdownTimerActive__Text">min</span>
							</div>
							<span class="VtlsCountdownTimerActive__Divider">:</span>
							<div class="VtlsCountdownTimerActive__Item">
								<h6 class="VtlsCountdownTimerActive__Number VtlsSeconds">00</h6>
								<span class="VtlsCountdownTimerActive__Text">sec</span>
							</div>
						</div>
					</div>
				</vtls-countdown-timer>
			{%- endif -%}
		</div>
	</div>
</section>

{% schema %}
{
	"name": "❤️ Flash Sale Countdown",
	"settings": [
		{
			"type": "header",
			"content": "General"
		},
		{
			"type": "color",
			"id": "section_background",
			"label": "Section background",
			"default": "#F8F8F8"
		},
		{
			"type": "select",
			"id": "timer_position_desktop",
			"label": "Timer position on desktop",
			"options": [
				{
					"value": "left",
					"label": "Left"
				},
				{
					"value": "right",
					"label": "Right"
				}
			],
			"default": "right"
		},
		{
			"type": "select",
			"id": "timer_position_mobile",
			"label": "Timer position on mobile",
			"options": [
				{
					"value": "above",
					"label": "Above content"
				},
				{
					"value": "bellow",
					"label": "Bellow content"
				}
			],
			"default": "above"
		},
		{
			"type": "select",
			"id": "text_alignment",
			"label": "Text alignment on desktop",
			"options": [
				{
					"value": "left",
					"label": "Left"
				},
				{
					"value": "center",
					"label": "Center"
				},
				{
					"value": "right",
					"label": "Right"
				}
			],
			"default": "left"
		},
		{
			"type": "select",
			"id": "text_alignment_mobile",
			"label": "Text alignment on mobile",
			"options": [
				{
					"value": "left",
					"label": "Left"
				},
				{
					"value": "center",
					"label": "Center"
				},
				{
					"value": "right",
					"label": "Right"
				}
			],
			"default": "center"
		},
		{
			"type": "checkbox",
			"id": "hide_section",
			"label": "Hide entire section after countdown ends",
			"default": true
		},
		{
			"type": "text",
			"id": "completed_message",
			"label": "Completed message",
			"info": "Visible if the setting to hide the section after countdown ends is inactive"
		},
		{
			"type": "color",
			"id": "completed_message_color",
			"label": "Completed message color",
			"default": "#222222"
		},
		{
			"type": "header",
			"content": "Layout"
		},
		{
			"type": "range",
			"id": "section_width",
			"min": 70,
			"max": 100,
			"step": 1,
			"unit": "%",
			"label": "Section width",
			"default": 70
		},
		{
			"type": "text",
			"id": "section_max_width",
			"label": "Custom max width (optional)",
			"info": "Section max width in pixels"
		},
		{
			"type": "range",
			"id": "vertical_padding",
			"min": 0,
			"max": 100,
			"step": 2,
			"unit": "px",
			"label": "Top - bottom padding (desktop)",
			"default": 40
		},
		{
			"type": "range",
			"id": "vertical_margin",
			"min": 0,
			"max": 100,
			"step": 2,
			"unit": "px",
			"label": "Top - bottom margin (desktop)",
			"default": 0
		},
		{
			"type": "range",
			"id": "vertical_padding_mobile",
			"min": 0,
			"max": 100,
			"step": 2,
			"unit": "px",
			"label": "Top - bottom padding (mobile)",
			"default": 20
		},
		{
			"type": "range",
			"id": "vertical_margin_mobile",
			"min": 0,
			"max": 100,
			"step": 2,
			"unit": "px",
			"label": "Top - bottom margin (mobile)",
			"default": 0
		}
	],
	"blocks": [
		{
			"type": "heading",
			"name": "Heading",
			"limit": 1,
			"settings": [
				{
					"type": "text",
					"id": "heading",
					"default": "Flash Sale Countdown",
					"label": "Heading"
				},
				{
					"type": "select",
					"id": "heading_size",
					"label": "Size",
					"options": [
						{
							"value": "24",
							"label": "Small"
						},
						{
							"value": "32",
							"label": "Medium"
						},
						{
							"value": "48",
							"label": "Large"
						}
					],
					"default": "32"
				},
				{
					"type": "select",
					"id": "heading_style",
					"label": "Heading style",
					"options": [
						{
							"value": "regular",
							"label": "Regular"
						},
						{
							"value": "bold",
							"label": "Bold"
						}
					],
					"default": "regular"
				},
				{
					"type": "color",
					"id": "heading_color",
					"label": "Text color",
					"default": "#222222"
				},
				{
					"type": "header",
					"content": "Alternative font"
				},
				{
					"type": "checkbox",
					"id": "heading_alternative_font",
					"label": "Apply alternative font",
					"default": false
				},
				{
					"type": "font_picker",
					"id": "heading_font",
					"label": "Font",
					"default": "sans_serif_n4"
				}
			]
		},
		{
			"type": "caption",
			"name": "Caption",
			"limit": 1,
			"settings": [
				{
					"type": "text",
					"id": "caption",
					"label": "Caption"
				},
				{
					"type": "select",
					"id": "caption_size",
					"label": "Size",
					"options": [
						{
							"value": "12",
							"label": "Small"
						},
						{
							"value": "16",
							"label": "Medium"
						},
						{
							"value": "20",
							"label": "Large"
						}
					],
					"default": "16"
				},
				{
					"type": "select",
					"id": "caption_weight",
					"label": "Caption style",
					"options": [
						{
							"value": "400",
							"label": "Regular"
						},
						{
							"value": "600",
							"label": "Bold"
						}
					],
					"default": "400"
				},
				{
					"type": "color",
					"id": "caption_color",
					"label": "Text color",
					"default": "#222222"
				},
				{
					"type": "header",
					"content": "Alternative font"
				},
				{
					"type": "checkbox",
					"id": "caption_alternative_font",
					"label": "Apply alternative font",
					"default": false
				},
				{
					"type": "font_picker",
					"id": "caption_font",
					"label": "Font",
					"default": "sans_serif_n4"
				}
			]
		},
		{
			"type": "text",
			"name": "Description",
			"limit": 1,
			"settings": [
				{
					"type": "richtext",
					"id": "description",
					"default": "<p>Share details about your exclusive limited-time offer or special promotion.</p>",
					"label": "Text"
				},
				{
					"type": "select",
					"id": "description_size",
					"label": "Size",
					"options": [
						{
							"value": "12",
							"label": "Small"
						},
						{
							"value": "16",
							"label": "Medium"
						},
						{
							"value": "20",
							"label": "Large"
						}
					],
					"default": "16"
				},
				{
					"type": "color",
					"id": "description_color",
					"label": "Text color",
					"default": "#222222"
				},
				{
					"type": "header",
					"content": "Alternative font"
				},
				{
					"type": "checkbox",
					"id": "description_alternative_font",
					"label": "Apply alternative font",
					"default": false
				},
				{
					"type": "font_picker",
					"id": "description_font",
					"label": "Font",
					"default": "sans_serif_n4"
				}
			]
		},
		{
			"type": "button",
			"name": "Button",
			"limit": 1,
			"settings": [
				{
					"type": "header",
					"content": "Primary button"
				},
				{
					"type": "text",
					"id": "primary_button_label",
					"label": "Label"
				},
				{
					"type": "url",
					"id": "primary_button_link",
					"label": "URL"
				},
				{
					"type": "select",
					"id": "primary_button_style",
					"label": "Button style",
					"options": [
						{
							"value": "solid",
							"label": "Filled"
						},
						{
							"value": "outline",
							"label": "Outline"
						},
						{
							"value": "link",
							"label": "Link"
						}
					],
					"default": "solid"
				},
				{
					"type": "color",
					"id": "primary_text_color",
					"label": "Text color",
					"default": "#ffffff"
				},
				{
					"type": "color",
					"id": "primary_background_color",
					"label": "Background color",
					"default": "#222222",
					"visible_if": "{{ block.settings.primary_button_style == 'solid' }}"
				},
				{
					"type": "color",
					"id": "primary_border_color",
					"label": "Border color",
					"default": "#222222",
					"visible_if": "{{ block.settings.primary_button_style == 'outline' }}"
				},
				{
					"type": "range",
					"id": "primary_button_corner",
					"min": 0,
					"max": 100,
					"step": 2,
					"unit": "px",
					"label": "Corner radius",
					"visible_if": "{{ block.settings.primary_button_style != 'link' }}",
					"default": 0
				},
				{
					"type": "checkbox",
					"id": "primary_new_tab",
					"label": "Open link in a new tab",
					"default": false
				}
			]
		},
		{
			"type": "timer",
			"name": "Countdown timer",
			"limit": 1,
			"settings": [
				{
					"type": "text",
					"id": "end_date",
					"label": "End date",
					"info": "Use format 'MM-DD-YYYY'. End date is based on the [store primary timezone](/admin/settings/general)"
				},
				{
					"type": "select",
					"id": "end_time",
					"label": "End time",
					"options": [
						{
							"value": "12am",
							"label": "12:00 am (0:00)"
						},
						{
							"value": "12:30am",
							"label": "12:30 am (0:30)"
						},
						{
							"value": "1am",
							"label": "1:00 am (1:00)"
						},
						{
							"value": "1:30am",
							"label": "1:30 am (1:30)"
						},
						{
							"value": "2am",
							"label": "2:00 am (2:00)"
						},
						{
							"value": "2:30am",
							"label": "2:30 am (2:30)"
						},
						{
							"value": "3am",
							"label": "3:00 am (3:00)"
						},
						{
							"value": "3:30am",
							"label": "3:30 am (3:30)"
						},
						{
							"value": "4am",
							"label": "4:00 am (4:00)"
						},
						{
							"value": "4:30am",
							"label": "4:30 am (4:30)"
						},
						{
							"value": "5am",
							"label": "5:00 am (5:00)"
						},
						{
							"value": "5:30am",
							"label": "5:30 am (5:30)"
						},
						{
							"value": "6am",
							"label": "6:00 am (6:00)"
						},
						{
							"value": "6:30am",
							"label": "6:30 am (6:30)"
						},
						{
							"value": "7am",
							"label": "7:00 am (7:00)"
						},
						{
							"value": "7:30am",
							"label": "7:30 am (7:30)"
						},
						{
							"value": "8am",
							"label": "8:00 am (8:00)"
						},
						{
							"value": "8:30am",
							"label": "8:30 am (8:30)"
						},
						{
							"value": "9am",
							"label": "9:00 am (9:00)"
						},
						{
							"value": "9:30am",
							"label": "9:30 am (9:30)"
						},
						{
							"value": "10am",
							"label": "10:00 am (10:00)"
						},
						{
							"value": "10:20am",
							"label": "10:20 am (10:20)"
						},
						{
							"value": "10:30am",
							"label": "10:30 am (10:30)"
						},
						{
							"value": "11am",
							"label": "11:00 am (11:00)"
						},
						{
							"value": "11:30am",
							"label": "11:30 am (11:30)"
						},
						{
							"value": "12pm",
							"label": "12:00 pm (12:00)"
						},
						{
							"value": "12:30pm",
							"label": "12:30 pm (12:30)"
						},
						{
							"value": "1pm",
							"label": "1:00 pm (13:00)"
						},
						{
							"value": "1:30pm",
							"label": "1:30 pm (13:30)"
						},
						{
							"value": "2pm",
							"label": "2:00 pm (14:00)"
						},
						{
							"value": "2:30pm",
							"label": "2:30 pm (14:30)"
						},
						{
							"value": "3pm",
							"label": "3:00 pm (15:00)"
						},
						{
							"value": "3:30pm",
							"label": "3:30 pm (15:30)"
						},
						{
							"value": "4pm",
							"label": "4:00 pm (16:00)"
						},
						{
							"value": "4:30pm",
							"label": "4:30 pm (16:30)"
						},
						{
							"value": "5pm",
							"label": "5:00 pm (17:00)"
						},
						{
							"value": "5:30pm",
							"label": "5:30 pm (17:30)"
						},
						{
							"value": "6pm",
							"label": "6:00 pm (18:00)"
						},
						{
							"value": "6:30pm",
							"label": "6:30 pm (18:30)"
						},
						{
							"value": "7pm",
							"label": "7:00 pm (19:00)"
						},
						{
							"value": "7:30pm",
							"label": "7:30 pm (19:30)"
						},
						{
							"value": "8pm",
							"label": "8:00 pm (20:00)"
						},
						{
							"value": "8:30pm",
							"label": "8:00 pm (20:30)"
						},
						{
							"value": "9pm",
							"label": "9:00 pm (21:00)"
						},
						{
							"value": "9:30pm",
							"label": "9:30 pm (21:30)"
						},
						{
							"value": "10pm",
							"label": "22:00 pm (22:00)"
						},
						{
							"value": "10:30pm",
							"label": "22:30 pm (22:30)"
						},
						{
							"value": "11pm",
							"label": "23:00 pm (23:00)"
						},
						{
							"value": "11:30pm",
							"label": "11:30 pm (23:30)"
						}
					],
					"default": "11:30pm"
				},
				{
					"type": "checkbox",
					"id": "show_numbers",
					"label": "Show only numbers",
					"default": false
				},
				{
					"type": "select",
					"id": "numbers_style",
					"label": "Numbers style",
					"options": [
						{
							"value": "regular",
							"label": "Regular"
						},
						{
							"value": "bold",
							"label": "Bold"
						}
					],
					"default": "bold"
				},
				{
					"type": "select",
					"id": "countdown_size",
					"label": "Countdown timer size",
					"options": [
						{
							"value": "small",
							"label": "Small"
						},
						{
							"value": "medium",
							"label": "Medium"
						},
						{
							"value": "large",
							"label": "Large"
						}
					],
					"default": "medium"
				},
				{
					"type": "header",
					"content": "Colors"
				},
				{
					"type": "color",
					"id": "numbers_color",
					"label": "Numbers",
					"default": "#222222"
				},
				{
					"type": "color",
					"id": "text_color",
					"label": "Text",
					"default": "#222222"
				}
			]
		}
	],
	"presets": [
		{
			"name": "❤️ Square: Flash Sale Countdown",
			"category": "Square sections",
			"blocks": [
				{
					"type": "caption"
				},
				{
					"type": "heading"
				},
				{
					"type": "text"
				},
				{
					"type": "timer"
				},
				{
					"type": "button"
				}
			]
		}
	]
}
{% endschema %}

{% style %}
	.Vtls-{{ section.id | handle }} {
	 --section-vertical-margin: {{- section.settings.vertical_margin -}}px;
	 --section-vertical-margin-mobile: {{- section.settings.vertical_margin_mobile -}}px;
	 --section-background-color: {{- section.settings.section_background -}};
	    --section-completed-message-color: {{- section.settings.completed_message_color -}};
	 --section-background-image: url({{- section.settings.background_image | img_url: 'master' -}});
	 --section-vertical-padding: {{- section.settings.vertical_padding -}}px;
	 --section-vertical-padding-mobile: {{- section.settings.vertical_padding_mobile -}}px;
	 --section-text-background: {{- section.settings.text_box_background -}};
	 --section-text-alignment: {{- section.settings.text_alignment -}};
	 --section-text-alignment-mobile: {{- section.settings.text_alignment_mobile -}};
	 --section-max-width: {{- section.settings.section_max_width -}}px;
	 --section-width: {{- section.settings.section_width -}}%;
	 --section-text-box-width: {{- section.settings.text_box_width -}}%;
	}

	

.Vtls-{{ section.id | handle }}.VtlsFlashTimer{margin:var(--section-vertical-margin-mobile) 0;background-color:var(--section-background-color, var(--vtl-color-bg-surface-tertiary-on-light))}@media(min-width: 992px){.Vtls-{{ section.id | handle }}.VtlsFlashTimer{margin:var(--section-vertical-margin) 0}}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashTimerContainer{padding:var(--section-vertical-padding-mobile) 0;display:flex;max-width:var(--section-max-width);width:var(--section-width);margin:0 auto}@media(max-width: 480px){.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashTimerContainer{max-width:100%;width:100%;margin:0 auto;padding:var(--section-vertical-padding-mobile) var(--vtl-space-20)}}@media(min-width: 992px){.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashTimerContainer{display:flex;padding:var(--section-vertical-padding) 0;max-width:var(--section-max-width);width:var(--section-width);margin:0 auto}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashTimerContainer--desktopPosition-left{flex-direction:row}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashTimerContainer--desktopPosition-right{flex-direction:row-reverse}}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer{display:flex;align-items:center;width:100%;gap:var(--vtl-space-8)}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer--mobilePosition-above{flex-direction:column}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer--mobilePosition-bellow{flex-direction:column-reverse}@media(min-width: 992px){.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer--desktopPosition-left{flex-direction:row-reverse}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer--desktopPosition-left .VtlsFlashCountdownTimer__Timer{justify-content:flex-start}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer--desktopPosition-right{flex-direction:row}}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content{width:100%;display:flex;align-items:center;justify-content:var(--section-text-alignment-mobile)}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content--onlyTimer{display:none}@media(min-width: 992px){.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content{justify-content:var(--section-text-alignment)}}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox{text-align:var(--section-text-alignment-mobile);display:flex;flex-direction:column;gap:var(--vtl-space-8)}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox p:empty,.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox div:empty,.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox h2:empty{display:none}@media(min-width: 992px){.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox{text-align:var(--section-text-alignment)}}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox__Heading{margin:0;font-size:calc(var(--block-heading-size)*.75);color:var(--block-heading-color)}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox__Heading--style-regular{font-weight:var(--vtl-font-weight-400)}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox__Heading--style-bold{font-weight:var(--vtl-font-weight-600)}@media(min-width: 992px){.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox__Heading{font-size:var(--block-heading-size);text-align:var(--section-text-alignment)}}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox__Heading--alternativeFont{font-family:var(--block-heading-font)}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox__Description p,.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox__Description ul,.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox__Description ol{margin:0;max-width:400px;font-size:calc(var(--block-description-size)*.88);color:var(--block-description-color)}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox__Description h1,.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox__Description h2,.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox__Description h3,.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox__Description h4,.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox__Description h5,.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox__Description h6{color:var(--block-description-color);margin:0;padding:0}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox__Description a{color:var(--block-description-color);margin:0;padding:0;text-decoration:underline;text-underline-offset:var(--vtl-space-2);transition:opacity .3s ease-in}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox__Description a:hover{opacity:.85}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox__Description ul,.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox__Description ol{padding-left:var(--vtl-space-20)}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox__Description--alignment-center ul,.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox__Description--alignment-center ol,.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox__Description--alignment-right ul,.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox__Description--alignment-right ol{list-style-position:inside}@media(min-width: 992px){.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox__Description p,.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox__Description ul,.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox__Description ol{font-size:var(--block-description-size)}}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox__Description--alternativeFont p,.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox__Description--alternativeFont ul,.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox__Description--alternativeFont ol,.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox__Description--alternativeFont h1,.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox__Description--alternativeFont h2,.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox__Description--alternativeFont h3,.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox__Description--alternativeFont h4,.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox__Description--alternativeFont h5,.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox__Description--alternativeFont h6{font-family:var(--block-description-font)}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox__Caption{margin:0;font-size:var(--block-caption-size);color:var(--block-caption-color);font-weight:var(--block-caption-weight)}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsTextBox__Caption--alternativeFont{font-family:var(--block-caption-font)}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsImgTextButtons{display:flex;gap:var(--vtl-space-12);flex-wrap:wrap;justify-content:var(--section-text-alignment-mobile)}@media(min-width: 992px){.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsImgTextButtons{justify-content:var(--section-text-alignment)}}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsImgTextButtons__Primary{display:inline-flex;justify-content:center;align-items:center;cursor:pointer;font:inherit;font-size:var(--vtl-font-size-14);text-decoration:var(--vtl-text-decoration-none);-webkit-appearance:none;appearance:none;width:fit-content;padding:var(--vtl-space-16) var(--vtl-space-20);transition:all .3s ease-in;line-height:1}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsImgTextButtons__Primary:hover{opacity:.85}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsImgTextButtons__Primary--solid{color:var(--block-primary-button-text-color);background:var(--block-primary-button-background-color);border-radius:var(--block-primary-button-corner-radius)}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsImgTextButtons__Primary--outline{color:var(--block-primary-button-text-color);background:rgba(0,0,0,0);border:var(--vtl-border-width-1) solid var(--block-primary-button-border-color);border-radius:var(--block-primary-button-corner-radius)}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Content .VtlsImgTextButtons__Primary--link{color:var(--block-primary-button-text-color);background:rgba(0,0,0,0);border:0;padding:0;border-radius:0;text-decoration:var(--vtl-text-decoration-underline);text-underline-offset:var(--vtl-space-4)}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Timer{width:100%;display:flex;justify-content:var(--section-text-alignment-mobile)}@media(min-width: 992px){.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Timer{font-size:var(--vtl-font-size-32);justify-content:end}}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Timer--onlyNumbers .VtlsCountdownTimerActive__Text{display:none}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Timer--onlyTimer{justify-content:center}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Timer .VtlsCountdownTimer{color:var(--section-completed-message-color);font-size:var(--vtl-font-size-20);letter-spacing:normal;text-align:var(--section-text-alignment-mobile)}@media(min-width: 992px){.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer__Timer .VtlsCountdownTimer{font-size:var(--vtl-font-size-24)}}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer .VtlsCountdownTimerActive{display:grid;gap:var(--vtl-space-8);grid:auto/auto-flow minmax(0, 1fr) auto}@media(max-width: 479px){.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer .VtlsCountdownTimerActive{gap:var(--vtl-space-4)}}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer .VtlsCountdownTimerActive--regular .VtlsCountdownTimerActive__Number{font-weight:var(--vtl-font-weight-400)}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer .VtlsCountdownTimerActive--bold .VtlsCountdownTimerActive__Number{font-weight:var(--vtl-font-weight-600)}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer .VtlsCountdownTimerActive--small .VtlsCountdownTimerActive__Number{font-size:var(--vtl-font-size-20)}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer .VtlsCountdownTimerActive--small .VtlsCountdownTimerActive__Text{font-size:var(--vtl-font-size-12)}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer .VtlsCountdownTimerActive--small .VtlsCountdownTimerActive__Divider{font-size:var(--vtl-font-size-20)}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer .VtlsCountdownTimerActive--medium .VtlsCountdownTimerActive__Number{font-size:var(--vtl-font-size-24)}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer .VtlsCountdownTimerActive--medium .VtlsCountdownTimerActive__Text{font-size:var(--vtl-font-size-14)}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer .VtlsCountdownTimerActive--medium .VtlsCountdownTimerActive__Divider{font-size:var(--vtl-font-size-24)}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer .VtlsCountdownTimerActive--large .VtlsCountdownTimerActive__Number{font-size:var(--vtl-font-size-32)}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer .VtlsCountdownTimerActive--large .VtlsCountdownTimerActive__Text{font-size:var(--vtl-font-size-16)}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer .VtlsCountdownTimerActive--large .VtlsCountdownTimerActive__Divider{font-size:var(--vtl-font-size-32)}@media(min-width: 992px){.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer .VtlsCountdownTimerActive--small .VtlsCountdownTimerActive__Number{font-size:var(--vtl-font-size-24)}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer .VtlsCountdownTimerActive--small .VtlsCountdownTimerActive__Text{font-size:var(--vtl-font-size-12)}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer .VtlsCountdownTimerActive--small .VtlsCountdownTimerActive__Divider{font-size:var(--vtl-font-size-24)}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer .VtlsCountdownTimerActive--medium .VtlsCountdownTimerActive__Number{font-size:var(--vtl-font-size-32)}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer .VtlsCountdownTimerActive--medium .VtlsCountdownTimerActive__Text{font-size:var(--vtl-font-size-16)}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer .VtlsCountdownTimerActive--medium .VtlsCountdownTimerActive__Divider{font-size:var(--vtl-font-size-32)}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer .VtlsCountdownTimerActive--large .VtlsCountdownTimerActive__Number{font-size:var(--vtl-font-size-48)}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer .VtlsCountdownTimerActive--large .VtlsCountdownTimerActive__Text{font-size:var(--vtl-font-size-20)}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer .VtlsCountdownTimerActive--large .VtlsCountdownTimerActive__Divider{font-size:var(--vtl-font-size-48)}}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer .VtlsCountdownTimerActive__Item{display:flex;flex-direction:column;align-items:center}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer .VtlsCountdownTimerActive__Number{color:var(--block-numbers-color);font-size:var(--vtl-font-size-24);line-height:1.3;padding:0;margin:0}@media(min-width: 992px){.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer .VtlsCountdownTimerActive__Number{font-size:var(--vtl-font-size-32)}}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer .VtlsCountdownTimerActive__Text{text-transform:uppercase;color:var(--block-text-color);font-size:var(--vtl-font-size-14)}@media(min-width: 992px){.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer .VtlsCountdownTimerActive__Text{font-size:var(--vtl-font-size-16)}}.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer .VtlsCountdownTimerActive__Divider{color:var(--block-numbers-color);line-height:1.2;font-size:var(--vtl-font-size-24);vertical-align:middle}@media(min-width: 992px){.Vtls-{{ section.id | handle }}.VtlsFlashTimer .VtlsFlashCountdownTimer .VtlsCountdownTimerActive__Divider{font-size:var(--vtl-font-size-32)}}

{% endstyle %}

<script>
	(() => {
		class VtlsFlashCountdown extends HTMLElement {
			constructor() {
				super();
				this.timerElement = null;
				this.targetDate = null;
				this.endDate = null;
				this.endTime = null;
				this.interval = null;
			}

			connectedCallback() {
				this.initCountdownTimer();
			}

			disconnectedCallback() {
				clearInterval(this.interval);
			}

			initCountdownTimer() {
				this.timerElement = this.querySelector('.VtlsCountdownTimer');
				this.endDate = this.timerElement?.getAttribute('data-end-date');
				this.endTime = this.timerElement?.getAttribute('data-end-time');

				if (!this.endDate || !this.endTime) {
					return;
				}

				if (!this.isValidDate(this.endDate)) {
					return;
				}

				const { hours, minutes } = this.convertTo24Hour(this.endTime);
				const [month, day, year] = this.endDate.split('-');
				const isoDate = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
				this.targetDate = new Date(
					`${isoDate}T${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:00`
				);

				if (isNaN(this.targetDate.getTime())) {
					return;
				}

				this.updateCountdown();
				this.interval = setInterval(() => this.updateCountdown(), 1000);
			}

			isValidDate(dateStr) {
				const dateParts = dateStr.split('-');

				if (dateParts.length !== 3) {
					return false;
				}

				const [month, day, year] = dateParts.map(Number);

				if (isNaN(year) || isNaN(month) || isNaN(day)) {
					return false;
				}

				if (month < 1 || month > 12) {
					return false;
				}

				const lastDayOfMonth = new Date(year, month, 0).getDate();

				if (day < 1 || day > lastDayOfMonth) {
					return false;
				}

				return true;
			}

			convertTo24Hour(timeStr) {
				const match = timeStr.match(/(\d+)(?::(\d+))?(am|pm)/);

				if (!match) {
					return { hours: 0, minutes: 0 };
				}

				let [_, hours, minutes, period] = match;
				hours = parseInt(hours, 10);
				minutes = minutes ? parseInt(minutes, 10) : 0;

				if (period === 'pm' && hours !== 12) {
					hours += 12;
				}

				if (period === 'am' && hours === 12) {
					hours = 0;
				}

				return { hours, minutes };
			}

			updateCountdown() {
				if (!this.targetDate) {
					return;
				}

				const now = new Date();
				const timeLeft = this.targetDate - now;
				const completedMessage = this.timerElement.getAttribute('data-completed-text') || 'Expired';
				const sectionElement = this.closest('section');

				if (timeLeft <= 0) {
					this.timerElement.innerHTML = completedMessage.trim() !== '' ? completedMessage : 'Expired';
					clearInterval(this.interval);

					if (
						sectionElement &&
						this.timerElement.dataset.hideSection === 'true' &&
						!window.Shopify.designMode
					) {
						sectionElement.remove();
					}

					return;
				}

				const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
				const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
				const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
				const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

				this.timerElement.querySelector('.VtlsDays').textContent = String(days).padStart(2, '0');
				this.timerElement.querySelector('.VtlsHours').textContent = String(hours).padStart(2, '0');
				this.timerElement.querySelector('.VtlsMinutes').textContent = String(minutes).padStart(2, '0');
				this.timerElement.querySelector('.VtlsSeconds').textContent = String(seconds).padStart(2, '0');
			}
		}

		document.addEventListener('DOMContentLoaded', () => {
			if (!customElements.get('vtls-countdown-timer')) {
				customElements.define('vtls-countdown-timer', VtlsFlashCountdown);
			}
		});
	})();
</script>

<script>
	(function () {
		const uninstallMessage = 'Displaying this section requires an active Square: Theme Sections & AI Pages subscription. Reinstall the <a href="https://apps.shopify.com/theme-sections-ai-page-builder" target="_blank">Square: Theme Sections & AI Pages app</a> to activate it or consider removing the section.';
		const downgradeMessage = 'You downgraded to a free plan. You need to upgrade your <a href="https://apps.shopify.com/theme-sections-ai-page-builder" target="_blank">Square: Theme Sections & AI Pages app</a> plan to activate it or consider removing the section.';
		const projectId = '5';
		const sectionsSelector = '.shopify-section';
		const apps = {
			'1': {
				prefix: 'vitals',
				hostname: 'https://appsolve.io'
			},
			'5': {
				prefix: 'tsapb',
				hostname: 'https://theme-sections.square-apps.com'
			}
		};

		const cacheKeysMetaFields = '{{ shop.metafields.vitals.tsck }}';
		const shopId = '{{ shop.id }}';

		const getHostname = () => {
			if (apps[projectId].hostname) {
				return apps[projectId].hostname;
			}

			console.error(`hostname for appId ${projectId} cannot be determined.`);
			return undefined;
		}

		const disableSection = (message, selector) => {
			const mainContainer = document.querySelector(selector);
			const contentContainer = document.querySelector(`${selector} > section`);

			if ('{{ request.design_mode }}' === 'true') {
				contentContainer.innerHTML = `<h2 class="VtlsUninstallBG"><div class="VtlsUninstallBG__Text">${message}</div></h2>`;
			} else {
				mainContainer.style.display = 'none';
				mainContainer.remove();
			}
		};

		const getThemeSectionIdsByUUID = (installedThemeSections, uuid, appId) => {
			const listIds = [];

			installedThemeSections.forEach((installedThemeSection) => {
				if (installedThemeSection.id.includes(`${apps[appId].prefix}_${uuid}`)) {
					listIds.push(installedThemeSection.id);
				}
			})

			return listIds;
		}

		const getThemeSectionsStatusList = (url) => fetch(url, {
			method: 'GET',
			headers: { 'Content-Type': 'application/json' },
		}).then((response) => response.json());

		const evaluateThemeSections = (url) => {
			getThemeSectionsStatusList(url).then(response => {
				const { data, f, i } = response;
				const installedThemeSections = document.querySelectorAll(sectionsSelector);

				data.forEach((themeSection) => {
					const { e, u, a } = themeSection;

					if (!e) {
						const themeSectionsWithUUID = getThemeSectionIdsByUUID(installedThemeSections, u, a);
						let message = '';

						if (f) {
							message = downgradeMessage;
						}

						if (!i) {
							message = uninstallMessage;
						}

						themeSectionsWithUUID.forEach((themeSectionSelector) => {
							disableSection(message, `#${themeSectionSelector}`);
						})
					}
				});
			})
		}

		if (shopId) {
			if (window.vtlsSecurityCheckStarted === undefined) {
				window.vtlsSecurityCheckStarted = true;

				let url = '';
				const hostname = getHostname();

				if (!hostname) {
					return;
				}

				const baseUrl = `${hostname}/bundle/api/v2/sf/ts`;

				if (cacheKeysMetaFields) {
					url = `${baseUrl}/${shopId}/${cacheKeysMetaFields}.json`;
				} else {
					const timestamp = Math.round(new Date().getTime() / 1000 / 10000) * 10000;
					url = `${baseUrl}/${shopId}/${timestamp}.json`;
				}

				evaluateThemeSections(url);
			}
		}
	})();
</script>

{% style %}
	

.VtlsUninstallBG{background-color:var(--vtl-color-bg-fill-inverse-on-light);background-image:url("data:image/avif;base64,iVBORw0KGgoAAAANSUhEUgAABOcAAABkCAQAAABnAJxfAAAAIGNIUk0AAHomAACAhAAA+gAAAIDoAAB1MAAA6mAAADqYAAAXcJy6UTwAAAACYktHRAD/h4/MvwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAAd0SU1FB+gLFQgmAgUXFkMAAARaSURBVHja7daxbSRBEATB8ZzvKw0h1dcIlJS4jl25Azlavffn9/3v++e//+vvCwqFQqFQKBRKSklEUCgUCoVCoVAMOgqFQqFQKJSzSiKCQqFQKBQKhWLQUSgUCoVCoZxVEhEUCoVCoVAoFIOOQqFQKBQK5VOURASFQqFQKBQKZVUSERQKhUKhUCiUVUlEUCgUCoVCoVBWZbtOPoVCoVAoFArlovIKERQKhUKhUCiUVXmFCAqFQqFQKBTKqrxCBIVCoVAoFAplVV4hgkKhUCgUCoUyK4kICoVCoVAoFMqqJCIoFAqFQqFQKKuSiKBQKBQKhUKhrEoigkKhUCgUCoVi0FEoFAqFQqGcVRIRFAqFQqFQKBSDjkKhUCgUCuWskoigUCgUCoVCoRh0FAqFQqFQKGeVRASFQqFQKBQKxaCjUCgUCoVCOaskIigUCoVCoVAoBh2FQqFQKBTKWSURQaFQKBQKhUIx6CgUCoVCoVDOKokICoVCoVAoFIpBR6FQKBQKhXJWSURQKBQKhUKhUAw6CoVCoVAolLNKIoJCoVAoFAqFYtBRKBQKhUKhnFUSERQKhUKhUCgUg45CoVAoFArlrJKIoFAoFAqFQqEYdBQKhUKhUChnlUQEhUKhUCgUCsWgo1AoFAqFQvkUJRFBoVAoFAqFQlmVRASFQqFQKBQKZVUSERQKhUKhUCiUVdmuk0+hUCgUCoVCuai8QgSFQqFQKBQKZVVeIYJCoVAoFAqFsiqvEEGhUCgUCoVCWZVXiKBQKBQKhUKhzEoigkKhUCgUCoWyKokICoVCoVAoFMqqJCIoFAqFQqFQKKuSiKBQKBQKhUKhGHQUCoVCoVAoZ5VEBIVCoVAoFArFoKNQKBQKhUI5qyQiKBQKhUKhUCgGHYVCoVAoFMpZJRFBoVAoFAqFQjHoKBQKhUKhUM4qiQgKhUKhUCgUikFHoVAoFAqFclZJRFAoFAqFQqFQDDoKhUKhUCiUs0oigkKhUCgUCoVi0FEoFAqFQqGcVRIRFAqFQqFQKBSDjkKhUCgUCuWskoigUCgUCoVCoRh0FAqFQqFQKGeVRASFQqFQKBQKxaCjUCgUCoVCOaskIigUCoVCoVAoBh2FQqFQKBTKWSURQaFQKBQKhUIx6CgUCoVCoVA+RUlEUCgUCoVCoVBWJRFBoVAoFAqFQlmVRASFQqFQKBQKZVW26+RTKBQKhUKhUC4qrxBBoVAoFAqFQlmVV4igUCgUCoVCoazKK0RQKBQKhUKhUFblFSIoFAqFQqFQKLOSiKBQKBQKhUKhrEoigkKhUCgUCoWyKokICoVCoVAoFMqqJCIoFAqFQqFQKAYdhUKhUCgUylklEUGhUCgUCoVCMegoFAqFQqFQziqJCAqFQqFQKBSKQUehUCgUCoVyVklEUCgUCoVCoVAMOgqFQqFQKJSzSiKCQqFQKBQKhWLQUSgUCoVCoZxVEhEUCoVCoVAoFIOOQqFQKBQK5dOVX5nMB52iezhxAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDI0LTExLTIxVDA4OjM4OjAyKzAwOjAw30WrCwAAACV0RVh0ZGF0ZTptb2RpZnkAMjAyNC0xMS0yMVQwODozODowMiswMDowMK4YE7cAAAAodEVYdGRhdGU6dGltZXN0YW1wADIwMjQtMTEtMjFUMDg6Mzg6MDIrMDA6MDD5DTJoAAAAAElFTkSuQmCC");background-position:center;background-size:cover;text-align:center;padding:var(--vtl-space-32);margin:0}.VtlsUninstallBG__Text{color:var(--vtl-color-text-disable-on-light);font-family:Inter,sans-serif;font-size:var(--vtl-font-size-14);font-weight:var(--vtl-font-weight-400);line-height:var(--vtl-line-height-140);padding:var(--vtl-space-16) var(--vtl-space-16) var(--vtl-space-16) var(--vtl-space-40);background-color:var(--vtl-color-bg-fill-inverse-on-light);display:inline-block}.VtlsUninstallBG__Text::before{content:url("data:image/svg+xml;utf8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2220%22%20height%3D%2220%22%20viewBox%3D%220%200%2020%2020%22%20fill%3D%22none%22%3E%0A%09%09%09%3Cpath%20d%3D%22M9.99994%206.75C10.4142%206.75%2010.7499%207.08579%2010.7499%207.5V11C10.7499%2011.4142%2010.4142%2011.75%209.99994%2011.75C9.58573%2011.75%209.24994%2011.4142%209.24994%2011V7.5C9.24994%207.08579%209.58573%206.75%209.99994%206.75Z%22%20fill%3D%22%23B28400%22%2F%3E%0A%09%09%09%3Cpath%20d%3D%22M11%2013.5C11%2014.0523%2010.5523%2014.5%2010%2014.5C9.44772%2014.5%209%2014.0523%209%2013.5C9%2012.9477%209.44772%2012.5%2010%2012.5C10.5523%2012.5%2011%2012.9477%2011%2013.5Z%22%20fill%3D%22%23B28400%22%2F%3E%0A%09%09%09%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M10%203.5C8.95471%203.5%208.21616%204.20232%207.84762%204.94672C7.65565%205.33448%206.75984%207.0441%205.84305%208.79353L5.81531%208.84646C4.9229%2010.5493%204.01714%2012.2777%203.80716%2012.702C3.4351%2013.4537%203.32931%2014.4519%203.89953%2015.316C4.46911%2016.1791%205.44222%2016.5%206.36356%2016.5L13.6364%2016.5C14.5577%2016.5%2015.5308%2016.1791%2016.1004%2015.316C16.6707%2014.4519%2016.5649%2013.4537%2016.1928%2012.702C15.9832%2012.2784%2015.08%2010.555%2014.1889%208.85476L14.1569%208.79352C13.24%207.044%2012.3443%205.33454%2012.1524%204.94693C11.784%204.20242%2011.0454%203.5%2010%203.5ZM9.1919%205.61225C9.59605%204.79592%2010.4041%204.79592%2010.8081%205.61225C11.0102%206.02061%2011.9201%207.75686%2012.8296%209.49243C13.7383%2011.2262%2014.6466%2012.9594%2014.8485%2013.3673C15.2525%2014.1837%2014.8485%2015%2013.6364%2015L6.36356%2015C5.1515%2015%204.74746%2014.1837%205.1515%2013.3673C5.35376%2012.9587%206.26468%2011.2205%207.1748%209.4838C8.08283%207.75111%208.99005%206.01994%209.1919%205.61225Z%22%20fill%3D%22%23B28400%22%2F%3E%0A%09%09%3C%2Fsvg%3E");position:absolute;width:var(--vtl-space-20);height:var(--vtl-space-20);margin-left:calc(-1*var(--vtl-space-24))}.VtlsUninstallBG__Text a{color:var(--vtl-color-text-disable-on-light)}.VtlsUninstallBG__Text a:hover{text-decoration:none}

{% endstyle %}

