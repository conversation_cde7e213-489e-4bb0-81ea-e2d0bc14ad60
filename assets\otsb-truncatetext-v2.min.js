(()=>{var c=()=>({truncateEl:"",truncateInnerEl:"",truncated:!0,truncatable:!1,label:"",expanded:!1,load(t){let i=t.getBoundingClientRect();t.style.setProperty("--truncate-height",`${i.height}px`)},setTruncate(t){t.offsetHeight<t.scrollHeight||t.offsetWidth<t.scrollWidth?(this.truncated=!0,this.truncatable=!0,this.expanded=!1):(this.truncated=!1,this.truncatable=!1,this.expanded=!0)},open(t,i){let n=t.closest(".truncate-container").querySelector(".truncate-text");if(this.expanded=!0,this.label=i,n.classList.contains("truncate-expanded"))this.truncated=!0;else{let e=n.querySelector(".truncate-inner");window.requestAnimationFrame(()=>{let r=e.getBoundingClientRect();n.style.setProperty("--truncate-height-expanded",`${r.height}px`),n.classList.add("truncate-expanded")}),this.truncated=!1}},close(t,i,n=!1){this.label=i;let e=t.closest(".truncate-container").querySelector(".truncate-text"),r=()=>{let s=e.getBoundingClientRect();return s.top>=0&&s.left>=0&&s.bottom<=(window.innerHeight||document.documentElement.clientHeight)&&s.right<=(window.innerWidth||document.documentElement.clientWidth)};if(this.truncated=!0,!r()&&!n){let s=e.getBoundingClientRect().top+window.scrollY-500;window.scrollTo({top:s,behavior:"smooth"}),e.style.transition="none",setTimeout(()=>{e.style.transition=""},1e3)}e.classList.remove("truncate-expanded"),this.expanded=!1}});window.otsb.loadedScript.includes("otsb-truncatetext-v2.js")||(window.otsb.loadedScript.push("otsb-truncatetext-v2.js"),document.addEventListener("alpine:init",()=>{Alpine.data("xTruncateTextNew",c)}));})();
