{% comment %}
    Renders a list of product's price (regular, sale)

    Accepts:
    - product: {Object} Product Liquid object (optional)
    - use_variant: {Boolean} Renders selected or first variant price instead of overall product pricing (optional)
    - show_badges: {<PERSON>olean} Renders 'Sale' and 'Sold Out' tags if the product matches the condition (optional)
    - price_class: {String} Adds a price class to the price element (optional)

    Usage:
    {% render 'price', product: product %}
{% endcomment %}
{%- liquid
  if use_variant
    assign target = product.selected_or_first_available_variant
  else
    assign target = product
  endif

  assign compare_at_price = target.compare_at_price
  assign price = target.price | default: 1999
  assign available = target.available | default: false
  assign money_price = price | money
  assign data_money_price =  money_price
  if settings.currency_code_enabled
    assign money_price = price | money_with_currency
  endif

  if target == product and product.price_varies
    assign money_price = 'products.product.price.from_price_html' | t: price: money_price
    assign data_money_price =  money_price
  endif
   
  assign on_sale = false
  if product.compare_at_price > product.price
  assign on_sale = true
  endif
-%}

<div class="price
  {%- if price_class %} {{ price_class }}{% endif -%}
  {%- if available == false %} price--sold-out {% endif -%}
  {%- if compare_at_price > price %} price--on-sale {% endif -%}
  {%- if product.price_varies == false and product.compare_at_price_varies %} price--no-compare{% endif -%}
  {%- if show_badges %} price--show-badge{% endif %} product-price-current" data-price="{{ data_money_price }}">
  <div class="price__container">    
    <div class="price__regular">
      <span class="visually-hidden visually-hidden--inline">{{ 'products.product.price.regular_price' | t }}</span>
      <span class="price-item price-item--regular">
       {{ money_price }}
      </span>
    </div>
    <div class="price__sale">      
      <span class="visually-hidden visually-hidden--inline">{{ 'products.product.price.sale_price' | t }}</span>
      <span class="price-item price-item--sale price-item--last">
       {{ money_price }}
      </span>
      {%- unless product.price_varies == false and product.compare_at_price_varies %}
        <span class="visually-hidden visually-hidden--inline">{{ 'products.product.price.regular_price' | t }}</span>
        <span>
          <s class="price-item price-item--regular">
            {% if settings.currency_code_enabled %}
              {{ compare_at_price | money_with_currency }}
            {% else %}
              {{ compare_at_price | money }}
            {% endif %}
          </s>
        </span>
      {%- endunless -%}
      {%  comment %}
      <div class="product_right_tag  {% if on_sale %} offer_exist {% endif %}">
          {% if on_sale %}
          {% if product.compare_at_price > product.price %}
          <span class="offer-price"> 
            {% if request.page_type == 'product' %}
              sale
              {% else %}
            ( {{ product.compare_at_price | minus:product.price | times:100 | divided_by:product.compare_at_price }}% {{ 'products.product.save' |t }} )
            
             {% endif %} 
          </span>
          {% endif %}
          {% endif %}         
        </div>
      {%  endcomment %}
    </div>
    <small class="unit-price caption{% if product.selected_or_first_available_variant.unit_price_measurement == nil %} hidden{% endif %}">
      <span class="visually-hidden">{{ 'products.product.price.unit_price' | t }}</span>
      <span class="price-item price-item--last">
        <span>{{- product.selected_or_first_available_variant.unit_price | money -}}</span>
        <span aria-hidden="true">/</span>
        <span class="visually-hidden">&nbsp;{{ 'accessibility.unit_price_separator' | t }}&nbsp;</span>
        <span>
          {%- if product.selected_or_first_available_variant.unit_price_measurement.reference_value != 1 -%}
            {{- product.selected_or_first_available_variant.unit_price_measurement.reference_value -}}
          {%- endif -%}
          {{ product.selected_or_first_available_variant.unit_price_measurement.reference_unit }}
        </span>
      </span>
    </small>
  </div>
  {%- if show_badges -%}
     {% if request.page_type == 'product' %}
      <span class="badge price__badge-sale color-{{ settings.sale_badge_color_scheme }}">
      {{ 'products.product.sale' | t }}
    </span>
    {% else %}  
    <span class="badge price__badge-sale color-{{ settings.sale_badge_color_scheme }}">
      {{ 'products.product.on_sale' | t }}
    </span>
   {% endif %} 
    <span class="badge price__badge-sold-out color-{{ settings.sold_out_badge_color_scheme }}">
      {{ 'products.product.sold_out' | t }}
    </span>
  {%- endif -%}
</div>
