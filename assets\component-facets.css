.facets-container {
  display: grid;
  grid-template-columns: repeat(2, auto);
  grid-template-rows: repeat(2, auto);
  /*   padding-top: 1rem; */
}

.active-facets-mobile {
  margin-bottom: 0.5rem;
}

.mobile-facets__list {
  overflow-y: auto;
}

@media screen and (min-width: 750px) {
  .facets-container > * + * {
    margin-top: 0;
  }

  .facets__form .product-count {
    grid-column-start: 3;
    align-self: flex-start;
  }
}

@media screen and (max-width: 989px) {
  .facets-container {
    grid-template-columns: auto minmax(0, max-content);
    column-gap: 2rem;
  }

}

.facet-filters {
  align-items: flex-start;
  display: flex;
  grid-column: 2;
  grid-row: 1;
  padding-left: 0rem;
}

@media screen and (min-width: 990px) {
  .facet-filters {
    padding-left: 0rem;
  }
}

.facet-filters__label {
  display: block;
  color: var(--color-foreground-85);
  font-size: 1.8rem;
  margin: 0 2rem 0 0;
  font-weight: 700;
}

.facet-filters__summary {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 1.4rem;
  cursor: pointer;
  height: 4.5rem;
  padding: 0 1.5rem;
  min-width: 25rem;
  margin-top: 2.4rem;
  border: 0.1rem solid rgba(var(--color-foreground), 0.55);
}

.facet-filters__summary::after {
  position: static;
}

.facet-filters__field {
  align-items: center;
  display: flex;
  flex-grow: 1;
  justify-content: flex-end;
}

.facet-filters__field .select {
  width: auto;
}

.facet-filters__field .select:after,
.facet-filters__field .select:before,
.mobile-facets__sort .select:after,
.mobile-facets__sort .select:before {
  content: none;
}

.facet-filters__field .select__select,
.mobile-facets__sort .select__select {
  border-radius: 0;
  min-width: auto;
  min-height: auto;
  transition: none;
}

.facet-filters button {
  margin-left: 2.5rem;
}

.facet-filters__sort {
  background-color: transparent;
  border: 0;
  border-radius: 0;
  font-size: 1.8rem;
  height: auto;
  line-height: calc(1 + 0.5 / var(--font-body-scale));
  margin: 0;
  padding-left: 1.75rem;
  padding-right: 1.75rem;
  font-weight: 700;
}

.facet-filters__sort + .icon-caret {
  right: 0;
}

@media screen and (forced-colors: active) {
  .facet-filters__sort {
    border: none;
  }
}

.facet-filters__sort,
.facet-filters__sort:hover {
  box-shadow: none;
  filter: none;
  transition: none;
}

/* .mobile-facets__sort .select__select:focus-visible {
  outline: 0.0rem solid rgba(var(--color-foreground), 0.5);
  outline-offset: 0.0rem;
  box-shadow: 0 0 0 0.0rem rgb(var(--color-background)),
    0 0 0.5rem 0.4rem rgba(var(--color-foreground), 0.3);
}

.mobile-facets__sort .select__select.focused,
.no-js .mobile-facets__sort .select__select:focus {
  outline: 0.2rem solid rgba(var(--color-foreground), 0.5);
  outline-offset: 0.3rem;
  box-shadow: 0 0 0 0.3rem rgb(var(--color-background)),
    0 0 0.5rem 0.4rem rgba(var(--color-foreground), 0.3);
}

.facet-filters__sort:focus-visible {
   outline: 0.2rem solid rgba(var(--color-foreground), 0.5);
  outline-offset: 1rem;
  box-shadow: 0 0 0 1rem rgb(var(--color-background)),
    0 0 0.2rem 1.2rem rgba(var(--color-foreground), 0.3); 
   outline: none;
  outline-offset: unset;
  box-shadow: none; 
}

.facet-filters__sort.focused,
.no-js .facet-filters__sort:focus {
  outline: 0.2rem solid rgba(var(--color-foreground), 0.5);
  outline-offset: 1rem;
  box-shadow: 0 0 0 1rem rgb(var(--color-background)),
    0 0 0.2rem 1.2rem rgba(var(--color-foreground), 0.3);
}
 */
.no-js .facet-filters__sort:focus:not(:focus-visible),
.no-js .mobile-facets__sort .select__select:focus:not(:focus-visible) {
  outline: 0;
  box-shadow: none;
}

.facets {
  display: block;
  grid-column-start: span 2;
}

.facets__form {
  display: grid;
  gap: 0 3.5rem;
  grid-template-columns: 1fr max-content max-content;
  margin-bottom: 0.5rem;
}

.facets__wrapper {
  align-items: center;
  align-self: center;
  grid-column: 1;
  grid-row: 1;
  display: flex;
  flex-wrap: wrap;
}

.facets__heading {
  display: block;
  color: rgba(var(--color-foreground), 1);
  font-size: 2.4rem;
  margin: 0rem 2rem 0 0;
  font-family: var(--font-heading-family);
  font-weight:500;
}

.facets__reset {
  margin-left: auto;
}

.horizontal .facets__disclosure {
  margin-right: 20px;
}
.horizontal .facets__list-color {
  padding: 2rem 2rem 0.8rem;
}
.horizontal summary .icon-caret {
  right: 0;
  top: calc(50% - 0rem);
}
.facets__summary {
  color: rgba(var(--color-foreground), 1);
  font-size: 2rem;
  padding: 0 1.75rem 0 0;
  margin-bottom: 1.5rem;
  font-weight: 400;
  font-family: var(--font-heading-family);
}

.facets__disclosure fieldset {
  padding: 0;
  margin: 0;
  border: 0;
}

.facets__disclosure[open] .facets__summary,
.facets__summary:hover {
  color: rgb(var(--color-foreground));
}

.facets__disclosure[open] .facets__display,
.facets__disclosure-vertical[open] .facets__display-vertical {
  animation: animateMenuOpen var(--duration-default) ease;
}

.facets__summary span {
  transition: text-decoration var(--duration-short) ease;
}

/* .facets__summary:hover span {
  text-decoration: underline;
  text-underline-offset: 0.3rem;
} */

.disclosure-has-popup[open] > .facets__summary::before {
  z-index: 2;
}

.facets__summary > span {
  line-height: calc(1 + 0.3 / var(--font-body-scale));
}

/* .facets__summary .icon-caret {
  right: 0;
} */

.facets__display {
  border-width: var(--popup-border-width);
  border-style: solid;
  border-color: rgba(var(--color-foreground), var(--popup-border-opacity));
  border-radius: var(--popup-corner-radius);
  box-shadow: var(--popup-shadow-horizontal-offset)
    var(--popup-shadow-vertical-offset) var(--popup-shadow-blur-radius)
    rgba(var(--color-shadow), var(--popup-shadow-opacity));
  background-color: rgb(var(--color-background));
  position: absolute;
  top: calc(100% + 0.5rem);
  left: -1.2rem;
  width: 35rem;
  max-height: 55rem;
  overflow-y: auto;
}

.facets__header {
  border-bottom: 1px solid rgba(var(--color-foreground), 0.2);
  padding: 2rem 2rem 1.5rem;
  display: flex;
  justify-content: space-between;
  font-size: 1.6rem;
  position: sticky;
  top: 0;
  background-color: rgb(var(--color-background));
  z-index: 1;
  line-height: normal;
}

.facets__list {
  padding: 2rem;
}

.facets__item {
  display: flex;
  align-items: center;
  transition: var(--duration-default) linear;
}
.facets .facets__item:hover {
  color: rgb(var(--color-base-outline-button-labels));
}

.facets__item label,
.facets__item input[type="checkbox"] {
  cursor: pointer;
}

.facet-checkbox {
  padding: 0rem 0rem 1.2rem 0;
  flex-grow: 1;
  position: relative;
  font-size: 1.6rem;
  display: flex;
  word-break: break-word;
  line-height: normal;
}
.facets__item:last-child .facet-checkbox {
  padding-bottom: 0;
}
.facet-checkbox input[type="checkbox"] {
  position: absolute;
  opacity: 1;
  width: 1.6rem;
  height: 1.6rem;
  top: 0.7rem;
  left: -0.4rem;
  z-index: -1;
  appearance: none;
  -webkit-appearance: none;
}

.no-js .facet-checkbox input[type="checkbox"] {
  z-index: 0;
}

.facet-checkbox > svg {
  background-color: rgb(var(--color-background));
  margin-right: 1.2rem;
  flex-shrink: 0;
}

.facet-checkbox .icon-checkmark {
  visibility: hidden;
  position: absolute;
  left: 0.3rem;
  z-index: 5;
  top: 1.4rem;
}

.menu-opening .facet-filters-form-expanded-view .mobile-facets__inner.gradient {
  transition-timing-function: ease-in-out;
  transition-duration: 1s;
}


.facet-checkbox > input[type="checkbox"]:checked ~ .icon-checkmark {
  visibility: visible;
}

@media screen and (forced-colors: active) {
  .facet-checkbox > svg {
    background-color: inherit;
    border: 0.1rem solid rgb(var(--color-background));
  }

  .facet-checkbox > input[type="checkbox"]:checked ~ .icon-checkmark {
    border: none;
  }
}

.facet-checkbox--disabled {
  color: rgba(var(--color-foreground), 0.4);
}

.facets__price {
  display: flex;
  padding: 2rem;
}

.facets__price .field + .field-currency {
  margin-left: 2rem;
}

.facets__price .field {
  align-items: center;
}

.facets__price .field-currency {
  align-self: center;
  margin-right: 0.6rem;
}

.facets__price .field__label {
  left: 1.5rem;
}

button.facets__button {
  min-height: 0;
  margin: 0 0 0 0.5rem;
  box-shadow: none;
  padding-top: 1.4rem;
  padding-bottom: 1.4rem;
}

.facets__button-no-js {
  transform: translateY(-0.6rem);
}

.active-facets {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  grid-column: 1 / -1;
  grid-row: 2;
  margin-top: 0;align-items:center;
}

.active-facets__button {
  display: block;
  margin-right: 1.5rem;
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
  padding-left: 0.2rem;
  padding-right: 0.2rem;
  text-decoration: none;
}

span.active-facets__button-inner {
  color: rgb(var(--color-foreground));
  box-shadow: 0 0 0 0.1rem rgb(var(--color-foreground));
  border-radius: 0.6rem;
/*   font-size: 1rem; */
  min-height: 0;
  min-width: 0;
  padding: 0.5rem 1rem;
  display: flex;
  align-items: stretch;
  letter-spacing: 0;
  text-transform: capitalize;
  font-size:14px;
}

span.active-facets__button-inner:before,
span.active-facets__button-inner:after {
  display: none;
}

.active-facets__button-wrapper {
  align-items: center;
  display: flex;
  justify-content: center;
  padding-top: 0rem;
}

.active-facets__button-wrapper * {
  font-size: 1.2rem;
}

@media screen and (min-width: 990px) {
  .active-facets__button {
    margin-right: 1.5rem;
  }

  .active-facets__button-wrapper *,
  span.active-facets__button-inner {
    font-size: 1.4rem;
  }
}

@media screen and (max-width: 989px) {
  .active-facets {
    margin: 0 -1.2rem -1.2rem;
  }

  .active-facets__button,
  .active-facets__button-remove {
    margin: 0;
    padding: 1.2rem;
  }
/* 
  span.active-facets__button-inner {
    padding-bottom: 0.3rem;
    padding-top: 0.3rem;
  } */

  .active-facets__button-wrapper {
    padding-top: 0;
    margin-left: 1.2rem;
  }
}

.active-facets__button:hover .active-facets__button-inner {
  box-shadow: 0 0 0 0.2rem rgb(var(--color-foreground));
}

.active-facets__button--light .active-facets__button-inner {
  box-shadow: 0 0 0 0.1rem rgba(var(--color-foreground), 0.2);
}

.active-facets__button--light:hover .active-facets__button-inner {
  box-shadow: 0 0 0 0.2rem rgba(var(--color-foreground), 0.4);
}

a.active-facets__button:focus-visible {
  outline: none;
  box-shadow: none;
}

a.active-facets__button.focused,
.no-js a.active-facets__button:focus {
  outline: none;
  box-shadow: none;
}

a.active-facets__button:focus-visible .active-facets__button-inner {
  box-shadow: 0 0 0 0.1rem rgba(var(--color-foreground), 0.2),
    0 0 0 0.2rem rgb(var(--color-background)),
    0 0 0 0.4rem rgb(var(--color-foreground));
  outline: none;
}

a.active-facets__button.focused .active-facets__button-inner,
.no-js a.active-facets__button:focus .active-facets__button-inner {
  box-shadow: 0 0 0 0.1rem rgba(var(--color-foreground), 0.2),
    0 0 0 0.2rem rgb(var(--color-background)),
    0 0 0 0.4rem rgb(var(--color-foreground));
  outline: none;
}

.active-facets__button svg {
  align-self: center;
  flex-shrink: 0;
  margin-left: 0.6rem;
  margin-right: -0.2rem;
  pointer-events: none;
  width: 1.2rem;
}

@media all and (min-width: 990px) {
  .active-facets__button svg {
    margin-right: -0.4rem;
    margin-top: 0.1rem;
    width: 1.4rem;
  }
}

.active-facets facet-remove:only-child {
  display: none;
}

.facets-vertical
  .active-facets
  .active-facets-vertical-filter:only-child
  > facet-remove {
  display: none;
}

.facets-vertical .active-facets-vertical-filter {
  display: flex;
  width: 100%;
  justify-content: space-between;
}

.facets-vertical .active-facets-vertical-filter .active-facets__button-wrapper {
  padding-top: 0;
  display: flex;
  align-items: center;
}

.facets-vertical .active-facets__button {
  margin-top: 0;
}

.active-facets__button.disabled,
.mobile-facets__clear.disabled {
  pointer-events: none;
}

.mobile-facets__clear-wrapper {
  align-items: center;
  display: flex;
  justify-content: center;
}

.mobile-facets {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
  background-color: rgba(var(--color-foreground), 0.5);
  pointer-events: none;
}

.mobile-facets__disclosure {
  display: flex;
}

.mobile-facets__wrapper {
  margin-left: 0;
  display: flex;
  align-items:center;
}

.mobile-facets__wrapper .disclosure-has-popup[open] > summary::before {
  height: 100vh;
  z-index: 3;
}

.mobile-facets__inner {
  background-color: rgb(var(--color-background));
  width: calc(100% - 5rem);
  margin-left: auto;
  height: 100%;
  overflow-y: auto;
  pointer-events: all;
  transition: transform var(--duration-short) ease;
  max-width: 37.5rem;
  display: flex;
  flex-direction: column;
  border-color: rgba(var(--color-foreground), var(--drawer-border-opacity));
  border-style: solid;
  border-width: 0 0 0 var(--drawer-border-width);
  filter: drop-shadow(
    var(--drawer-shadow-horizontal-offset) var(--drawer-shadow-vertical-offset)
      var(--drawer-shadow-blur-radius)
      rgba(var(--color-shadow), var(--drawer-shadow-opacity))
  );
}

.menu-opening .mobile-facets__inner {
  transform: translateX(0);
}

.js .disclosure-has-popup:not(.menu-opening) .mobile-facets__inner {
  transform: translateX(105vw);
}

.mobile-facets__header {
  border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.08);
  padding: 1.2rem 2.5rem 1rem;
  text-align: center;
  display: flex;
  position: sticky;
  top: 0;
  z-index: 2;
}

.mobile-facets__header-inner {
  flex-grow: 1;
  position: relative;
}

.mobile-facets__info {
  padding: 0 2.6rem;
}

.mobile-facets__heading {
  font-size: calc(var(--font-heading-scale) * 1.8rem);
  margin: 0;
  font-weight: 500;
}

.mobile-facets__count {
  color: rgba(var(--color-foreground), 0.7);
  font-size: 1.3rem;
  margin: 0;
  flex-grow: 1;
}

.mobile-facets__open-wrapper {
  display: inline-block;
}

.mobile-facets__open {
  text-align: left;
  width: 100%;
  padding-top: 0;
  padding-bottom: 0;
  display: flex;
  align-items: center;
  color: rgba(var(--color-link), var(--alpha-link));
  outline: 0;
  box-shadow: none;
}

.mobile-facets__open:hover {
  color: rgb(var(--color-link));
}
.mobile-facets__open:hover .button-label:hover:before {
  opacity: 0;
}
.mobile-facets__open:hover line,
.mobile-facets__open:hover circle {
  stroke: rgb(var(--color-link));
}

.mobile-facets__open-label {
  transition: text-decoration var(--duration-short) ease;
  font-size: 1.6rem;
  letter-spacing: 0;
  text-transform: capitalize;
  font-weight: 500;
  padding: 0;
  display: flex;
  align-items: center;
  width: max-content;
  min-width: max-content;
  min-height: fit-content;
  position:relative;
}
.mobile-facets__open .mobile-facets__open-label:after {
  content: "";
  width: 0%;
  height: 2px;
  background: currentColor;
  position: absolute;
  bottom: 0px;
  left:0;
  transition: all 0.3s linear;
}
.mobile-facets__summary>div, .facets__summary>div{
  transition:all 0.3s linear;
}
.mobile-facets__summary>div:hover, .facets__summary>div:hover{
  color:rgba(var(--color-base-outline-button-labels));
}
.mobile-facets__open:hover .mobile-facets__open-label:after {
  width: 100%;
}

.mobile-facets__open > * + * {
  margin-left: 0.5rem;
}

.mobile-facets__open svg {
  width: 2rem;
  height: 2rem;
}

.mobile-facets__open line,
.mobile-facets__open circle {
  stroke: rgba(var(--color-link), var(--alpha-link));
}

.mobile-facets__close {
  display: none;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0.7rem;
  right: 1rem;
  width: 4.4rem;
  height: 4.4rem;
  z-index: 101;
  opacity: 0;
  transition: opacity var(--duration-short) ease;
}

.mobile-facets__close svg {
  width: 1.8rem;
  transition: all 0.3s linear;
}
details.menu-opening .mobile-facets__close:hover svg {
 color: rgb(var(--color-base-outline-button-labels));
}
details.menu-opening .mobile-facets__close {
  display: flex;
  opacity: 1;
}

details.menu-opening .mobile-facets__close svg {
  margin: 0;
}

.mobile-facets__close-button {
  align-items: center;
  background-color: transparent;
  display: flex;
  font-size: 1.4rem;
  font: inherit;
  letter-spacing: inherit;
  margin-top: 1.5rem;
  padding: 1.2rem 2.6rem;
  text-decoration: none;
  width: calc(100% - 5.2rem);
}

.no-js .mobile-facets__close-button {
  display: none;
}

.mobile-facets__close-button .icon-arrow {
  transform: rotate(180deg);
  margin-right: 1rem;
}

.mobile-facets__main {
  padding: 2.7rem 0 0;
  position: relative;
  z-index: 1;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.mobile-facets__details[open] .icon-caret {
  transform: rotate(180deg);
}

.no-js .mobile-facets__details {
  border-bottom: 1px solid rgba(var(--color-foreground), 0.04);
}

.mobile-facets__highlight {
  opacity: 0;
  visibility: hidden;
}

.mobile-facets__checkbox:checked + .mobile-facets__highlight {
  visibility: visible;
  opacity: 1;
  position: absolute;
  top: 0px;
  left: 0px;
  right: 0px;
  bottom: 0px;
  display: block;
  background-color: rgba(var(--color-foreground), 0.04);
}

.mobile-facets__summary {
  padding: 1.3rem 2.5rem;
}

.mobile-facets__summary svg {
  margin-left: auto;
}

.mobile-facets__summary > div,
.facets__summary > div {
  display: flex;
  align-items: center;
}
.facets__summary>div{
    display: inline-flex;
  
}
.js .mobile-facets__submenu {
  position: absolute;
  top: 0;
  width: 100%;
  bottom: 0;
  left: 0;
  z-index: 3;
  transform: translateX(100%);
  visibility: hidden;
  display: flex;
  flex-direction: column;
  background: var(--gradient-base-background-1);
  transition: all 0.3s linear;
}

.js details[open] > .mobile-facets__submenu {
  transition: transform 0.4s cubic-bezier(0.29, 0.63, 0.44, 1),
    visibility 0.4s cubic-bezier(0.29, 0.63, 0.44, 1);
}

.js details[open].menu-opening > .mobile-facets__submenu {
  transform: translateX(0);
  visibility: visible;
}

.js .menu-drawer__submenu .mobile-facets__submenu {
  overflow-y: auto;
}

.js .mobile-facets .submenu-open {
  visibility: hidden; /* hide menus from screen readers when hidden by submenu */
}

.mobile-facets__item {
  position: relative;
}

input.mobile-facets__checkbox {
  border: 0;
  position: absolute;
  width: 1.6rem;
  height: 1.6rem;
  position: absolute;
  left: 2.1rem;
  top: 1.2rem;
  z-index: 0;
  appearance: none;
  -webkit-appearance: none;
}

.mobile-facets__label {
  padding: 1.5rem 2rem 1.5rem 2.5rem;
  width: 100%;
  transition: background-color 0.2s ease;
  word-break: break-word;
  display: flex;
  align-items:center;    
  cursor: pointer;
  transition:all 0.3s linear;
}
.mobile-facets__label:hover {color:rgb(var(--color-base-outline-button-labels));}
.mobile-facets__label > svg {
  display:none;
  background-color: rgb(var(--color-background));
  position: relative;
  z-index: 2;
  margin-right: 1.2rem;
  flex-shrink: 0;
}

.mobile-facets__label .icon-checkmark {
  position: absolute;
  top: 2rem;
  left: 2.8rem;
  visibility: hidden;
  height: 1rem;
  width: 1rem;
}

.mobile-facets__label > input[type="checkbox"]:checked ~ .icon-checkmark {
  visibility: visible;
}

.mobile-facets__arrow,
.mobile-facets__summary .icon-caret {
  margin-left: auto;
  display: block;
}

.mobile-facets__label--disabled {
  opacity: 0.5;
}
/* .has-submenu:not(.submenu-open) .mobile-facets__footer {position: fixed;} */
.mobile-facets__footer {
  border-top: 0.1rem solid rgba(var(--color-foreground), 0.08);
  padding: 2rem;
  bottom: 0;
  position: relative;
  display: flex;
  z-index: 2;
  width: 100%;
  margin-top: auto;
  background-color: rgb(var(--color-background));
  background: var(--gradient-background);
  margin-top: auto;
}

.mobile-facets__footer > * + * {
  margin-left: 1rem;
}

.mobile-facets__footer > * {
  width: 50%;
}

.mobile-facets__footer noscript .button {
  width: 100%;
}

.mobile-facets__sort {
  display: flex;
  justify-content: space-between;
}

.mobile-facets__sort label {
  flex-shrink: 0;
}

.mobile-facets__sort .select {
  width: auto;
}

.no-js .mobile-facets__sort .select {
  position: relative;
  right: -1rem;
}

.mobile-facets__sort .select .icon-caret {
  right: 25px;
}

.mobile-facets__sort .select__select {
  background-color: transparent;
  border-radius: 0;
  box-shadow: none;
  filter: none;
  margin-left: 0.5rem;
  margin-right: 0.5rem;
  padding-left: 2rem;
  padding-right: 5rem;
  height:45px;
}

.product-count {
  align-self: center;
  position: relative;
  text-align: right;
}

.product-count__text {
  font-size: 1.4rem;
  line-height: normal;
  margin: 0;
  font-family: var(--font-heading-family);
  font-weight: 500;
  color: var(--gradient-base-accent-1);
}

.product-count__text.loading {
  visibility: hidden;
}

.product-count .loading-overlay__spinner,
.product-count-vertical .loading-overlay__spinner {
  display: none;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1.8rem;
}

.product-count__text.loading + .loading-overlay__spinner {
  display: block;
}
.facets__form-vertical {
  display: flex;
  flex-direction: column;
  /*     width: 34rem; */
  width: var(--sidebar-width);
}
 
@media screen and (max-width: 991px) {
  .facets__form-vertical {
    display: flex;
    flex-direction: column;
    width: 27rem;
  }
}
@media screen and (min-width: 990px) {
.facets-vertical {
    display: flex;
    justify-content: space-between;
  }
}
@media screen and (min-width: 990px) {
 

  .facets-wrap-vertical {
    border: none;
    padding-left: 0;
    margin: 0;
    padding: 0;
  }

  .facets__disclosure-vertical {
    /*     border-top: solid rgba(var(--color-foreground), 0.1); */
    margin-right: 0;
    margin-bottom: 0;
  }
  .facets__disclosure-vertical.open{
      margin-bottom: 2.4rem;  
  }

  .facets-vertical .facets__summary {
    padding-top: 1rem;
    margin-bottom: 0;
    padding-bottom: 1rem;
  }
  .facets__summary {
    margin-bottom: 0;
  }
  .facets__heading--vertical {
    margin: 0 ;
    font-size: 2.4rem;
  }
.facets-vertical .active-facets-vertical-filter{
      margin: 0 0 1.5rem;
}
  .facets__header-vertical {
    padding: 0.5rem 2rem 1.5rem 0;
    font-size: 1.6rem;
  }

  .facets__display-vertical {
    padding-bottom: 1.5rem;
    transition: transform var(--duration-short) ease;
  }

  /*   .facets-vertical .facets-wrapper {
    padding-right: 3rem;
  } */
/*   .facets-vertical.sidebar-right aside {
    padding-left: 3rem;
  } */
  .facets-vertical .facets-wrapper--no-filters {
    display: none;
  }

  .no-js .facets-vertical .facets-wrapper--no-filters {
    display: block;
  }

  

  .facets-vertical-form {
    display: flex;
    justify-content: flex-end;
  }
/* 
  .product-count-vertical {
    /*     margin-left: 3.5rem;
    margin-right: auto;
  } */

  .facets-vertical .active-facets__button-wrapper {
    margin-bottom: 2rem;
  }

  .facets-vertical .no-js .facets__button-no-js {
    transform: none;
    margin-left: 0;
  }

  .facets-vertical .no-js .facet-filters__field {
    justify-content: flex-start;
    padding-bottom: 1rem;
    padding-top: 2rem;
  }

  .facets-vertical .facets__price {
    padding: 0.5rem 0.5rem 0.5rem 0;
  }

  .facets-vertical .facets__price .field:last-of-type {
    margin-left: 1rem;
  }

  .facets-vertical .active-facets__button {
    margin-bottom: 1.5rem;
  }

  .no-js .facets-vertical .facet-filters.sorting {
    padding-left: 0;
    flex-direction: column;
  }

  .facets-vertical .facet-checkbox input[type="checkbox"] {
    z-index: 0;
  }

  .no-js .facets-vertical .facets-container {
    display: flex;
    flex-direction: column;
  }

  .facets-vertical .active-facets facet-remove:last-of-type {
    margin-bottom: 0rem;
  }

  .facets-vertical .active-facets {
    margin: 0;
    align-items: center;
  }

  .facets__disclosure-vertical[open] .facets__summary .icon-caret {
    transform: rotate(180deg);
  }

  .facets-container-drawer {
    display: flex;
    flex-flow: row wrap;
    align-items: center;
    column-gap: 0;
  }

  .facets-container-drawer .mobile-facets__wrapper {
    margin-right: 2rem;
    flex-grow: 1;
    margin-bottom: 2rem;
  }

  .facets-container-drawer .product-count {
    margin: 0 0 0.5rem 3.5rem;
  }

  .facets-container-drawer .facets-pill {
    width: 100%;
  }

  .facets-container-drawer .facets__form {
    display: block;
  }
}
@media screen and (min-width: 990px){
  .facets-vertical .product-grid-container {
    width: calc(100% - Calc(var(--sidebar-width) + var(--grid-desktop-vertical-spacing)));
    position: sticky;
    top: 0;
    height: fit-content;
  }
}

@media screen and (min-width: 750px) and (max-width: 989px) {
  .facets-vertical .active-facets__button {
    padding: 1rem;
    margin-bottom: 0;
    margin-left: -0.5rem;
  }
/* 
  .facets-vertical .active-facets__button-remove {
    padding: 0 1rem 1rem;
  } */
}
@media screen and (min-width: 750px) and (max-width: 982px) {
  .facets-vertical-form {
    display: flex;
    justify-content: center;
    flex-direction: column;
    margin: 0 auto;
    text-align: center;
  }
  .filter-style {
    margin: 0 auto;
  }
  /*   .facet-filters__label{ margin: 0 1rem 0 0;} */
  .product-count-vertical {
    margin: 0rem 0 2rem;
  }
  /* facet-filters-form.facets.facets-vertical-sort {
    flex-direction: column;
    margin-bottom: 40px;
  } */
  /*   .facets-container{justify-content:center;} */
}
@media screen and (max-width: 989px){
  facet-filters-form.facets.facets-vertical-sort {justify-content:flex-end;margin-bottom:0;}
 .facets-container-drawer{display:flex;}
}

/*facts*/
.facets-container
  facet-filters-form.facets.small-hide.horizontal
  .facet-filters {
  display: none;
}
.facets-container
  facet-filters-form.facets.small-hide.horizontal
  .product-count {
  display: none;
}
.facets-container.facets-container-drawer
  facet-filters-form.facets.small-hide
  .facet-filters {
  display: none;
}
.facets-container.facets-container-drawer .product-count {
  display: none;
}
.facets label.facet-checkbox span {
  margin-top: 1px;
}
details.disclosure-has-popup.facets__disclosure.facet-filters__sort
  summary.facets__summary {
  padding: 0;
  padding-right: 0;
  line-height: normal;
  height: 36px;
  outline: 0;
  box-shadow: none;
}
details.disclosure-has-popup.facets__disclosure.facet-filters__sort summary.facets__summary .button {
  color: var(--gradient-background);
}
details.disclosure-has-popup.facets__disclosure.facet-filters__sort {
  padding: 0;
}

.facets-vertical.no-sidebar .product-grid-container {
  width: 100%;
}
details.disclosure-has-popup.facets__disclosure.facet-filters__sort summary.facets__summary{
  background: var(--gradient-button-background-1);
}
.detail-Size ul.list-unstyled .facet-checkbox input[type=checkbox]:checked~span{color:rgb(var(--color-background));}
.facets__list-color label span {
  transition: all linear 0.3s;
  box-shadow: 0px 0px 0px 1px transparent, inset 0 0 0 4px var(--DTBodyBGColor);
  border-radius: 50%;
  min-width: 30px;
  min-height: 30px;
  line-height: normal;
  padding: 0 5px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: auto;
  border: none;
}
.facets__list-color .facet-checkbox {
  margin: 0 1.2rem 0 0;
}
.facets__list-color .facet-checkbox > svg {
  opacity: 0;
  margin-right: 0;
}
.facets__list-color .facet-checkbox > input[type="checkbox"]:checked ~ span {
      border: 3px solid var(--gradient-background);
    box-shadow: 0 0 0 1px rgb(var(--color-base-solid-button-labels));
}
.facet-filters__field
  .facets__item
  .facet-checkbox
  input[checked="checked"]
  + span.label {
    opacity: 1;
    color: rgba(var(--color-base-outline-button-labels));
}
/* .filter-panel-menu {
  margin-bottom: 4.5rem;
} */
/* .facets__form-vertical .active-facets-vertical-filter, */
.facet-checkbox > svg {
  display: none;
}
.detail-Size ul.list-unstyled,
.detail-Brand ul.list-unstyled {
  display: flex;
  flex-wrap: wrap;
  
}
facet-filters-form.horizontal .detail-Size ul.list-unstyled,
facet-filters-form.horizontal .detail-Brand ul.list-unstyled{padding: 2rem 2rem 0.8rem;}
.detail-Size ul.list-unstyled .facet-checkbox,
.detail-Brand ul.list-unstyled .facet-checkbox {
  padding: 5px 10px;
}
.detail-Size ul.list-unstyled .facets__item,
.detail-Brand ul.list-unstyled .facets__item {
  margin: 0 12px 12px 0;
  background: var(--gradient-base-background-2);
  border-radius: var(--buttons-radius);
  transition: all 0.3s linear;
}
.detail-Size ul.list-unstyled .facets__item:hover,
.detail-Brand ul.list-unstyled .facets__item:hover {
  background: var(--gradient-base-accent-1);
  color: var(--gradient-base-background-1);
}
.facets-container span.product-count {
  font-size: 10px;
}
.facet-filters.sorting .facet-checkbox {
  font-size: 14px;
  font-weight: 500;
}
.facets__list-color {
  padding: 1rem 0;
}
details.disclosure-has-popup.facets__disclosure.facet-filters__sort
  summary.facets__summary
  .button {
  line-height: normal;
  min-height: 36px;
  height: 36px;
  /* border-bottom: 1px solid; */
  padding: 0 50px 0 20px;   
  /* border: 1px solid;
  padding: 0rem 2.6rem 0 1.4rem; */
  min-width: 200px;
  justify-content: space-between;
  transform: translateY(0);
  text-transform: capitalize;
  font-size: 14px;
  letter-spacing: 0;
  /* font-weight: 600; */
}
/* .facets-vertical.sidebar-left aside {
  padding-right: 8rem;
} */
details.disclosure-has-popup.facets__disclosure.facet-filters__sort
  summary.facets__summary
  .button:before {
  display: none;
}
/* @media screen and (max-width: 1540px) {
  .facets-vertical.sidebar-left aside {
    padding-right: 3rem;
  }
} */
@media screen and (max-width: 990px) {
  .horizontal .facets__wrapper details:nth-last-child(2) .facets__display {
    right: 0;
    left: unset;
  }
}
@media screen and (max-width: 989px) {
  .facets-vertical.sidebar-left aside {
    padding-right: 0rem;
  }
  
  .product-count__text {
    font-weight: 500;
  }
}
@media screen and (max-width: 989px) {
  .facets-container {
    margin-bottom: 20px;
  }
}
span.mobile-facets__open-label.button-label.small-hide:before {
  display: none;
}
facet-filters-form.facets.small-hide.horizontal {
  margin-bottom: 3.5rem;
}
.facets__list-color .facet-checkbox > input[type="checkbox"]:checked ~ span {
  color: var(--gradient-base-accent-2);
}
.facets__list-color .facets__item:last-child .facet-checkbox{    margin-bottom: 1.2rem;}
.detail-Size ul.list-unstyled .facets__item:hover .facet-checkbox--disabled {
    color: rgba(var(--color-background),.8);
}
.detail-Brand ul.list-unstyled .facets__item:hover .facet-checkbox--disabled {
    color: rgba(var(--color-background),.8);
}
.facet-checkbox>input[type=checkbox]:checked~span{color:rgba(var(--color-base-outline-button-labels));}
.facets__disclosure-vertical{display:block; margin: 1rem 0; }
.overflow-hidden-mobile .header ul.dt-sc-list-inline.dt-desktop-menu.dt-nav{overflow:hidden;}
.facets-horizontal .filter-panel-menu {
    display: none;
}
 .detail-Product.rating.count.facets__disclosure-vertical {
    display: block;
}
.detail-Size ul.list-unstyled .facet-checkbox.active {
    background: var(--gradient-base-accent-1);
}
.mobile-facets__details.js-filter .mobile-facets__sort:hover {
    color: rgba(var(--color-foreground), 1);
}
 .active-facets__button-wrapper a.active-facets__button-remove span{font-size: 14px;font-family: var(--font-heading-family);}
.facets-horizontal  .facets__wrapper details:last-child .facets__display { right: 10px;left: auto;}


/*filter - button*/

.mobile-facets__clear-wrapper.button--primary .mobile-facets__clear, .mobile-facets__clear-wrapper.button--primary + button.button--primary {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    border: 0;
    cursor: pointer;
    text-decoration: none;
    color: rgb(var(--color-button-text));
    transition: box-shadow var(--duration-short) ease;
    -webkit-appearance: none;
    white-space: nowrap;
    appearance: none;
    background-color: rgba(var(--color-button), var(--alpha-button-background));
    transition: all 0.3s linear;
    font-family: var(--font-heading-family);
    font-weight: 500;
    transition: all var(--duration-default) linear;
    min-width: calc(12rem + var(--buttons-border-width) * 2);
    min-height: calc(4rem + var(--buttons-border-width) * 2);
    font-size: 1.4rem;
    letter-spacing: 0;
    line-height: normal;
    text-transform: uppercase;
    padding: 0 5rem;
}
.mobile-facets__clear-wrapper.button--primary .mobile-facets__clear:hover, .mobile-facets__clear-wrapper.button--primary + button.button--primary:hover{
   background-color: rgba( var(--color-hover-button), var(--alpha-button-background) );
  color: rgba(var(--color-button-hover-text));
}
/* .mobile-facets__clear-wrapper.button--primary .mobile-facets__clear:hover , .mobile-facets__clear-wrapper.button--primary + button.button--primary:hover a{ color: rgba(var(--color-button-hover-text));} */

.horizontal .facets__wrapper details:nth-last-child(2) .facets__display {
    right: 0;
    left: unset;
}

.facet-filters__field > label {display: none;}
.facets__disclosure[open] .facets__display{ right: 0;  left: auto;  min-width: 200px; width: 100%;}
.facet-filters__field summary .icon-caret{right:20px;color: var(--gradient-background);}