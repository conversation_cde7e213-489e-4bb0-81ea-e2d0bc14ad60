<!doctype html>
<html class="no-js" lang="{{ request.locale.iso_code }}">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">
    <meta name="theme-color" content="">
    <link rel="canonical" href="{{ canonical_url }}">
    <link rel="preconnect" href="https://cdn.shopify.com" crossorigin>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    {%- if settings.favicon != blank -%}
      <link rel="icon" type="image/png" href="{{ settings.favicon | image_url: width: 32, height: 32 }}">
    {%- endif -%}

    {%- unless settings.type_header_font.system? and settings.type_body_font.system? -%}
      <link rel="preconnect" href="https://fonts.shopifycdn.com" crossorigin>
    {%- endunless -%}

    <title>
      {{ page_title }}
      {%- if current_tags %} &ndash; tagged "{{ current_tags | join: ', ' }}"{% endif -%}
      {%- if current_page != 1 %} &ndash; Page {{ current_page }}{% endif -%}
      {%- unless page_title contains shop.name %} &ndash; {{ shop.name }}{% endunless -%}
    </title>

    {% if page_description %}
      <meta name="description" content="{{ page_description | escape }}">
    {% endif %}

    {% render 'meta-tags' %}    
    <script src="{{ 'jquery.min.js' | asset_url }}"></script>        
    <script src="{{ 'global.js' | asset_url }}" defer="defer"></script>
    <script src="{{ 'swiper-bundle.min.js' | asset_url }}"></script>
    <script  src="{{ 'jquery-cookie-min.js' | asset_url }}"></script>
    <script src="{{ 'wow.min.js' | asset_url }}"></script>
    {{ content_for_header }}   
    {% style %}      
       {%- liquid
      if settings.custom_font_script_1 != blank
      echo settings.custom_font_script_1
      endif
      if settings.custom_font_script_2 != blank
      echo settings.custom_font_script_2
      endif
      if settings.custom_font_script_3 != blank
      echo settings.custom_font_script_3
      endif
        %}
      {{ settings.type_body_font | font_face: font_display: 'swap' }}                 
      {{ settings.type_header_font | font_face: font_display: 'swap' }}
    
      :root {
        {%- if settings.custom_font_family_1 != blank %}
        --font-heading-family: {{ settings.custom_font_family_1 }}
        {% else %}
        --font-heading-family: {{ settings.type_header_font.family }}, {{ settings.type_header_font.fallback_families }};
         {%  endif -%}
        --font-heading-style: {{ settings.type_header_font.style }};
        --font-heading-weight: {{ settings.type_header_font.weight }};
        --font-heading-scale: {{ settings.heading_scale | times: 1.0 | divided_by: settings.body_scale }};
        

        {%- if settings.custom_font_family_2 != blank %}
        --font-body-family: {{ settings.custom_font_family_2 }}
        {% else %}
        --font-body-family: {{ settings.type_body_font.family }}, {{ settings.type_body_font.fallback_families }};
        {% endif -%}
        --font-body-style: {{ settings.type_body_font.style }};
        --font-body-weight: {{ settings.type_body_font.weight }};
        --font-body-weight-bold: {{ settings.type_body_font.weight | plus: 300 | at_most: 1000 }};
        --font-body-scale: {{ settings.body_scale | divided_by: 100.0 }};


        {%- if settings.custom_font_family_3 != blank %}
        --font-additional-family: {{ settings.custom_font_family_3 }}
        {%  else %}
        --font-additional-family: {{ settings.type_additional_font.family }}, {{ settings.type_additional_font.fallback_families }};
        {%  endif -%}
        --font-additional-heading-style: {{ settings.type_additional_font.style }};
        --font-additional-heading-weight: {{ settings.type_additional_font.weight }};
         
        --color-base-text: {{ settings.colors_text.red }}, {{ settings.colors_text.green }}, {{ settings.colors_text.blue }};
     
        --color-shadow: {{ settings.colors_text.red }}, {{ settings.colors_text.green }}, {{ settings.colors_text.blue }};
        --color-base-background-1: {{ settings.colors_background_1.red }}, {{ settings.colors_background_1.green }}, {{ settings.colors_background_1.blue }};
        --color-base-background-2: {{ settings.colors_background_2.red }}, {{ settings.colors_background_2.green }}, {{ settings.colors_background_2.blue }};
        --color-base-background-3: {{ settings.colors_background_3.red }}, {{ settings.colors_background_3.green }}, {{ settings.colors_background_3.blue }};
        --color-base-solid-button-labels: {{ settings.colors_solid_button_labels.red }}, {{ settings.colors_solid_button_labels.green }}, {{ settings.colors_solid_button_labels.blue }};
        --color-base-outline-button-labels: {{ settings.colors_outline_button_labels.red }}, {{ settings.colors_outline_button_labels.green }}, {{ settings.colors_outline_button_labels.blue }};
        --color-base-accent-1: {{ settings.colors_accent_1.red }}, {{ settings.colors_accent_1.green }}, {{ settings.colors_accent_1.blue }};
        --color-base-accent-2: {{ settings.colors_accent_2.red }}, {{ settings.colors_accent_2.green }}, {{ settings.colors_accent_2.blue }};
        --color-base-accent-3: {{ settings.colors_accent_3.red }}, {{ settings.colors_accent_3.green }}, {{ settings.colors_accent_3.blue }};
        --color-overlay: {{ settings.overlay_color.red }}, {{ settings.overlay_color.green }}, {{ settings.overlay_color.blue }};
       --color-border: {{ settings.colors_border.red }}, {{ settings.colors_border.green }}, {{ settings.colors_border.blue }};
        --payment-terms-background-color: {{ settings.colors_background_1 }};
        --gradient-button-background-1: {% if settings.gradient_button_1 != blank %}{{ settings.gradient_button_1 }}{% else %}{{ settings.colors_solid_button_labels }}{% endif %};
        --gradient-button-hover:{% if settings.gradient_button_hover != blank %}{{ settings.gradient_button_hover }}{% endif %};

      
        --gradient-base-background-1: {% if settings.gradient_background_1 != blank %}{{ settings.gradient_background_1 }}{% else %}{{ settings.colors_background_1 }}{% endif %};
        --gradient-base-background-2: {% if settings.gradient_background_2 != blank %}{{ settings.gradient_background_2 }}{% else %}{{ settings.colors_background_2 }}{% endif %};
        --gradient-base-background-3: {% if settings.gradient_background_3 != blank %}{{ settings.gradient_background_3 }}{% else %}{{ settings.colors_background_3 }}{% endif %};
        --gradient-base-accent-1: {% if settings.gradient_accent_1 != blank %}{{ settings.gradient_accent_1 }}{% else %}{{ settings.colors_accent_1 }}{% endif %};
        --gradient-base-accent-2: {% if settings.gradient_accent_2 != blank %}{{ settings.gradient_accent_2 }}{% else %}{{ settings.colors_accent_2 }}{% endif %};
        --gradient-base-accent-3: {% if settings.gradient_accent_3 != blank %}{{ settings.gradient_accent_3 }}{% else %}{{ settings.colors_accent_3 }}{% endif %};
        
        --media-padding: {{ settings.media_padding }}px;
        --media-border-opacity: {{ settings.media_border_opacity | divided_by: 100.0 }};
        --media-border-width: {{ settings.media_border_thickness }}px;
        --media-radius: {{ settings.media_radius }}px;
        --media-shadow-opacity: {{ settings.media_shadow_opacity | divided_by: 100.0 }};
        --media-shadow-horizontal-offset: {{ settings.media_shadow_horizontal_offset }}px;
        --media-shadow-vertical-offset: {{ settings.media_shadow_vertical_offset }}px;
        --media-shadow-blur-radius: {{ settings.media_shadow_blur }}px;

        --page-width: {{ settings.page_width | divided_by: 10 }}rem;
        --page-width-laptop: {{ settings.page_width_laptop | divided_by: 10 }}rem;
        --page-width-tab: {{ settings.page_width_tab | divided_by: 10 }}rem;
      
        --page-full-width-spacing: {{ settings.page_full_width_spacing }}%;
        --page-width-margin: {% if settings.page_width == '1600' %}2{% else %}0{% endif %}rem;

        --card-image-padding: {{ settings.card_image_padding | divided_by: 10.0 }}rem;
        --card-corner-radius: {{ settings.card_corner_radius | divided_by: 10.0 }}rem;
        --card-text-alignment: {{ settings.card_text_alignment }};
        --card-border-width: {{ settings.card_border_thickness | divided_by: 10.0 }}rem;
        --card-border-opacity: {{ settings.card_border_opacity | divided_by: 100.0 }};
        --card-shadow-opacity: {{ settings.card_shadow_opacity | divided_by: 100.0 }};
        --card-shadow-horizontal-offset: {{ settings.card_shadow_horizontal_offset | divided_by: 10.0 }}rem;
        --card-shadow-vertical-offset: {{ settings.card_shadow_vertical_offset | divided_by: 10.0 }}rem;
        --card-shadow-blur-radius: {{ settings.card_shadow_blur | divided_by: 10.0 }}rem;

        --badge-corner-radius: {{ settings.badge_corner_radius | divided_by: 10.0 }}rem;

        --popup-border-width: {{ settings.popup_border_thickness }}px;
        --popup-border-opacity: {{ settings.popup_border_opacity | divided_by: 100.0 }};
        --popup-corner-radius: {{ settings.popup_corner_radius }}px;
        --popup-shadow-opacity: {{ settings.popup_shadow_opacity | divided_by: 100.0 }};
        --popup-shadow-horizontal-offset: {{ settings.popup_shadow_horizontal_offset }}px;
        --popup-shadow-vertical-offset: {{ settings.popup_shadow_vertical_offset }}px;
        --popup-shadow-blur-radius: {{ settings.popup_shadow_blur }}px;

        --drawer-border-width: {{ settings.drawer_border_thickness }}px;
        --drawer-border-opacity: {{ settings.drawer_border_opacity | divided_by: 100.0 }};
        --drawer-shadow-opacity: {{ settings.drawer_shadow_opacity | divided_by: 100.0 }};
        --drawer-shadow-horizontal-offset: {{ settings.drawer_shadow_horizontal_offset }}px;
        --drawer-shadow-vertical-offset: {{ settings.drawer_shadow_vertical_offset }}px;
        --drawer-shadow-blur-radius: {{ settings.drawer_shadow_blur }}px;

        --spacing-sections-desktop: {{ settings.spacing_sections }}px;
        --spacing-sections-mobile: {% if settings.spacing_sections < 24 %}{{ settings.spacing_sections }}{% else %}{{ settings.spacing_sections | times: 0.7 | round | at_least: 20 }}{% endif %}px;

        --grid-desktop-vertical-spacing: {{ settings.spacing_grid_vertical }}px;
        --grid-desktop-horizontal-spacing: {{ settings.spacing_grid_horizontal }}px;
        --grid-mobile-vertical-spacing: {{ settings.spacing_grid_vertical | divided_by: 2 }}px;
        --grid-mobile-horizontal-spacing: {{ settings.spacing_grid_horizontal | divided_by: 2 }}px;
        --sidebar-width:{{settings.sidebar_width}}px;

      
        --text-boxes-border-opacity: {{ settings.text_boxes_border_opacity | divided_by: 100.0 }};
        --text-boxes-border-width: {{ settings.text_boxes_border_thickness }}px;
        --text-boxes-radius: {{ settings.text_boxes_radius }}px;
        --text-boxes-shadow-opacity: {{ settings.text_boxes_shadow_opacity | divided_by: 100.0 }};
        --text-boxes-shadow-horizontal-offset: {{ settings.text_boxes_shadow_horizontal_offset }}px;
        --text-boxes-shadow-vertical-offset: {{ settings.text_boxes_shadow_vertical_offset }}px;
        --text-boxes-shadow-blur-radius: {{ settings.text_boxes_shadow_blur }}px;

        --buttons-radius: {{ settings.buttons_radius }}px;
        --buttons-radius-outset: {% if settings.buttons_radius > 0 %}{{ settings.buttons_radius | plus: settings.buttons_border_thickness }}{% else %}0{% endif %}px;
        --buttons-border-width: {% if settings.buttons_border_opacity > 0 %}{{ settings.buttons_border_thickness }}{% else %}0{% endif %}px;
        --buttons-border-opacity: {{ settings.buttons_border_opacity | divided_by: 100.0 }};
        --buttons-shadow-opacity: {{ settings.buttons_shadow_opacity | divided_by: 100.0 }};
        --buttons-shadow-horizontal-offset: {{ settings.buttons_shadow_horizontal_offset }}px;
        --buttons-shadow-vertical-offset: {{ settings.buttons_shadow_vertical_offset }}px;
        --buttons-shadow-blur-radius: {{ settings.buttons_shadow_blur }}px;
        --buttons-border-offset: {% if settings.buttons_radius > 0 or settings.buttons_shadow_opacity > 0 %}0.3{% else %}0{% endif %}px;

        --inputs-radius: {{ settings.inputs_radius }}px;
        --inputs-border-width: {{ settings.inputs_border_thickness }}px;
        --inputs-border-opacity: {{ settings.inputs_border_opacity | divided_by: 100.0 }};
        --inputs-shadow-opacity: {{ settings.inputs_shadow_opacity | divided_by: 100.0 }};
        --inputs-shadow-horizontal-offset: {{ settings.inputs_shadow_horizontal_offset }}px;
        --inputs-margin-offset: {% if settings.inputs_shadow_vertical_offset != 0 and settings.inputs_shadow_opacity > 0 %}{{ settings.inputs_shadow_vertical_offset | abs }}{% else %}0{% endif %}px;
        --inputs-shadow-vertical-offset: {{ settings.inputs_shadow_vertical_offset }}px;
        --inputs-shadow-blur-radius: {{ settings.inputs_shadow_blur }}px;
        --inputs-radius-outset: {% if settings.inputs_radius > 0 %}{{ settings.inputs_radius | plus: settings.inputs_border_thickness }}{% else %}0{% endif %}px;

        --variant-pills-radius: {{ settings.variant_pills_radius }}px;
        --variant-pills-border-width: {{ settings.variant_pills_border_thickness }}px;
        --variant-pills-border-opacity: {{ settings.variant_pills_border_opacity | divided_by: 100.0 }};
        --variant-pills-shadow-opacity: {{ settings.variant_pills_shadow_opacity | divided_by: 100.0 }};
        --variant-pills-shadow-horizontal-offset: {{ settings.variant_pills_shadow_horizontal_offset }}px;
        --variant-pills-shadow-vertical-offset: {{ settings.variant_pills_shadow_vertical_offset }}px;
        --variant-pills-shadow-blur-radius: {{ settings.variant_pills_shadow_blur }}px;
      }

      #preloader, .dT_loading {
      position: fixed;
      display:block;
      z-index: 2000;
      width: 100%;
      height: 100%;
      top:0;
      bottom:0;
      left:0;
      right: 0;
      margin: auto;
      {% if settings.preloader != blank %} 
      background-image:url('{{ settings.preloader | image_url: width: 1920 }}');
     {% endif %}
      background-repeat: no-repeat;
      background-position:center;
      background-color: rgb(var(--color-background));
      }
    .alert-overlay-wrapper {position:relative; width: 100%; height: 100%;}
      .alert-overlay{
      display: none; 
      position: fixed; 
      z-index: 1; 
      padding-top: 100px; 
      left: 0;
      top: 0;
      width: 100%; 
      height: 100%; 
      overflow: auto; 
      background-color: rgb(var(--color-base-accent-1)); 
      background-color:rgba(var(--color-base-accent-1), 0.4); 
      } 
      .alert-overlay .main-content {
      position: absolute;
      left: 50%;
      right: 0;
      top: 50%;
      background-color:rgb(var(--color-base-background-1));    
      bottom: 0;
      z-index: 99;
      width: calc(100% - 2rem);
      max-width: 500px;
      height: 200px;
      padding: 10px;
      transform: translate(-50%, -50%);
      align-items: center;
      display: flex;
      flex-direction: column;
      justify-content: center;
      }
    .closebtn {
    margin-left: 15px;
    background:rgb(var(--color-base-solid-button-labels));
    color: rgb(var(--color-base-background-1)); 
    font-weight: bold;
    float: right;
    font-size: 22px;
    line-height: 20px;
    cursor: pointer;
    transition: 0.3s;
    position:absolute;
    right:15px;
    top:15px;  
    width: 25px;
    height: 25px;
    justify-content: center;
    align-items: center;
    display: flex;  
    }
    .closebtn svg{width:1.4rem; height:1.4rem;}
    .closebtn:hover {
    background: rgb(var(--color-base-outline-button-labels)); color: rgb(var(--color-base-background-1)); 
    }
    .overflow-hidden.filter-clicked .shopify-section-header-sticky { z-index: 1;}
     .overflow-hidden-mobile .mobile-toolbar__icons, .overflow-hidden.filter-clicked .mobile-toolbar__icons { z-index: 0;}
 
      {% endstyle %}

    {{ 'base.css' | asset_url | stylesheet_tag }}
    {{ 'custom.css' | asset_url | stylesheet_tag }}
    {{ 'component-card.css' | asset_url | stylesheet_tag }}
    {{ 'swiper-bundle.min.css' | asset_url | stylesheet_tag }}    
    {{ 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css' | stylesheet_tag: preload: true }}
    {{ 'https://cdnjs.cloudflare.com/ajax/libs/flag-icon-css/4.1.5/css/flag-icons.min.css' | stylesheet_tag: preload: true }}
    {{ 'animate.min.css' | asset_url | stylesheet_tag }}    
    {{ 'placeholder.css' | asset_url | stylesheet_tag }}        
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
   

    {%- unless settings.type_body_font.system? -%}
      <link rel="preload" as="font" href="{{ settings.type_body_font | font_url }}" type="font/woff2" crossorigin>
    {%- endunless -%}
    {%- unless settings.type_header_font.system? -%}
      <link rel="preload" as="font" href="{{ settings.type_header_font | font_url }}" type="font/woff2" crossorigin>
    {%- endunless -%}
    
    {%- if settings.predictive_search_enabled -%}
      <link rel="stylesheet" href="{{ 'component-predictive-search.css' | asset_url }}" media="print" onload="this.media='all'">
    {%- endif -%}

     <script>
      document.documentElement.className = document.documentElement.className.replace('no-js', 'js');
      if (Shopify.designMode) {
        document.documentElement.classList.add('shopify-design-mode');
      }
    </script>
    
  </head>

  <body id="{{ page_title | handle }}" class="gradient {% if settings.preloader_enable %}preloader-overflow{% endif %}">
    <div class="dt-custom-overlay"></div>
    <a class="skip-to-content-link button visually-hidden" href="#MainContent">
      {{ "accessibility.skip_to_text" | t }}
    </a>
     {% if settings.preloader_enable %}
   <div id="preloader">
    <div class="spinner"></div> 
    </div>
    {% endif %}
    <div class="mobile-menu" data-menu="dt-main-menu"> </div>
    <div class="mobile-menu-overlay"></div> 
    {%- if settings.cart_type == 'drawer' -%}
    {%- render 'cart-drawer' -%}
    {%- endif -%}       
    {% section 'announcement-bar' %}   
    {% section 'top-bar' %}   
    {% section 'header' %}    
    {%- if shop.customer_accounts_enabled  -%}
       {%  render 'login-modal' %}
    {%- endif -%}
    <main id="MainContent" class="content-for-layout focus-none placeholder-shadow-blocks" role="main" tabindex="-1">      
      {{ content_for_layout }}      
    </main> 
      {%  if settings.footer_type == 'style1' %}
      {% section 'footer' %} 
      {% elsif settings.footer_type == 'style2' %}
      {% section 'footer_style1' %} 
      {% elsif settings.footer_type == 'style3' %}
      {% section 'footer_style2' %} 
      {% elsif settings.footer_type == 'style4' %}
      {% section 'footer_style3' %} 
      {% elsif settings.footer_type == 'style5' %}
      {% section 'footer_style4' %} 
      {% endif %}
    
    <ul hidden>
      <li id="a11y-refresh-page-message">{{ 'accessibility.refresh_page' | t }}</li>
    </ul>

    <script>
      window.shopUrl = '{{ request.origin }}';
      window.routes = {
        cart_add_url: '{{ routes.cart_add_url }}',
        cart_change_url: '{{ routes.cart_change_url }}',
        cart_update_url: '{{ routes.cart_update_url }}',
        cart_url: '{{ routes.cart_url }}',
        predictive_search_url: '{{ routes.predictive_search_url }}'
      };

      window.cartStrings = {
        error: `{{ 'sections.cart.cart_error' | t }}`,
        quantityError: `{{ 'sections.cart.cart_quantity_error_html' | t: quantity: '[quantity]' }}`
      }

      window.variantStrings = {
        addToCart: `{{ 'products.product.add_to_cart' | t }}`,
        soldOut: `{{ 'products.product.sold_out' | t }}`,
        unavailable: `{{ 'products.product.unavailable' | t }}`,
        unavailable_with_option: `{{ 'products.product.value_unavailable' | t: option_value: '[value]' }}`,
      }

      window.accessibilityStrings = {
        imageAvailable: `{{ 'products.product.media.image_available' | t: index: '[index]' }}`,
        shareSuccess: `{{ 'general.share.success_message' | t }}`,
        pauseSlideshow: `{{ 'sections.slideshow.pause_slideshow' | t }}`,
        playSlideshow: `{{ 'sections.slideshow.play_slideshow' | t }}`,
      }
      
      var DT_THEME = {
        strings: {
          addToWishList: {{ 'products.wishlist.addToWishList' | t | json }},
          viewMyWishList: {{ 'products.wishlist.viewMyWishList' | t | json }},             
          unavailable: {{ 'products.product.unavailable' | t | json }},
          addToCompareList: {{ 'products.compare.addToCompareList' | t | json }},
          viewMyCompareList: {{ 'products.compare.viewMyCompareList' | t | json }},
          minCompareProductNav: {{ 'products.compare.minCompareProductNav' | t | json }},
          minCompareProduct: {{ 'products.compare.minCompareProduct' | t | json }},
          inventoryStatus: {{ 'products.product.stock_status' | t | json }},
          in_stock: {{ 'products.product.in_stock' | t | json }},
          
        },
        moneyFormat: {{ shop.money_format | json }}        
      };      
        function debounce(func, timeout = 300){
        let timer;
        return (...args) => {
        clearTimeout(timer);
        timer = setTimeout(() => { func.apply(this, args); }, timeout);
      };
      }   
      new WOW().init();
    </script>
    {%- if settings.predictive_search_enabled -%}
      <script src="{{ 'predictive-search.js' | asset_url }}" defer="defer"></script>
    {%- endif -%}    
    <!-- Footer Scripts ================================ -->     
    {% if template contains 'product' %}
    <script src="{{ 'dT_bundle_base.js' | asset_url }}" defer="defer"></script>
    <script src="{{ 'dT_bundle.js' | asset_url }}" defer="defer"></script>
    {% endif %}            
    <script src="{{ 'dt_wishlist.js' | asset_url }}" defer="defer"></script>    
    <script src="{{ 'dt_compare.js' | asset_url }}" defer="defer"></script>  
    <script src="{{ 'dt-theme.js' | asset_url }}" defer="defer"></script>    
    {% section 'suggested-products' %}
    {% section 'cookie-banner' %}    
    {% render 'scroll-to-top' %}
    {% section 'newsletter-modal' %}  
    {% render 'compare-modal' %}
    {% render 'mobile-tool-bar' %}          
    <div class="video-popup">
      <a class="pop-up__video-close close">&times;</a>  
      <div class="video-wrapper">
        <div class="video-container">
          <iframe id="video-popup-iframe" width="860" height="615"  src="" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
        </div>
      </div>
    </div>
  </body>
</html>
