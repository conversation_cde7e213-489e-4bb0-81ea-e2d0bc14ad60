{%- if section.settings.search_style == 'search_icon' -%}
     <span class="icon-search" id="dT_TopStickySearchBtn">
<svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="currentcolor">
<path d="M15 24C21.0751 24 26 19.0751 26 13C26 6.92487 21.0751 2 15 2C8.92487 2 4 6.92487 4 13C4 19.0751 8.92487 24 15 24Z"  stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M3 25L7 21"  stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
               
  <svg class="modal__toggle-close icon icon-close" aria-hidden="true" focusable="false" role="presentation">
    <use href="#icon-close">
  </svg>
</span>
{%- endif -%}

{%- if section.settings.search_style == 'search_box' -%}  
       {%- if settings.predictive_search_enabled -%}
          <predictive-search class="search-modal__form" data-loading-text="{{ 'accessibility.loading' | t }}">
        {%- else -%}
          <search-form class="search-modal__form">
        {%- endif -%}
            <form action="{{ routes.search_url }}" method="get" role="search" class="search search-modal__form">
              <div class="field">
                <input class="search__input field__input"
                  id="{{ input_id }}"
                  type="search"
                  name="q"
                  value="{{ search.terms | escape }}"
                  placeholder="{{ 'general.search.search' | t }}"
                  {%- if settings.predictive_search_enabled -%}
                    role="combobox"
                    aria-expanded="false"
                    aria-owns="predictive-search-results"
                    aria-controls="predictive-search-results"
                    aria-haspopup="listbox"
                    aria-autocomplete="list"
                    autocorrect="off"
                    autocomplete="off"
                    autocapitalize="off"
                    spellcheck="false"
                  {%- endif -%}
                >
                <label class="field__label" for="{{ input_id }}">{{ 'general.search.search' | t }}</label>
                <input type="hidden" name="options[prefix]" value="last">
                <button type="reset" class="reset__button field__button{% if search.terms == blank %} hidden{% endif %}" aria-label="{{ 'general.search.reset' | t }}">
                  <svg class="icon icon-close" aria-hidden="true" focusable="false">
                    <use xlink:href="#icon-reset">
                  </svg>
                </button>
                <button class="search__button field__button" aria-label="{{ 'general.search.search' | t }}">
                  <svg class="icon icon-search" aria-hidden="true" focusable="false">
                    <use href="#icon-search">
                  </svg>
                </button>
              </div>

              {%- if settings.predictive_search_enabled -%}
                <div class="predictive-search predictive-search--header" tabindex="-1" data-predictive-search>
                  <div class="predictive-search__loading-state">
                    <svg aria-hidden="true" focusable="false" class="spinner" viewBox="0 0 66 66" xmlns="http://www.w3.org/2000/svg">
                      <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
                    </svg>
                  </div>
                </div>

                <span class="predictive-search-status visually-hidden" role="status" aria-hidden="true"></span>
              {%- endif -%}
            </form>
          {%- if settings.predictive_search_enabled -%}
            </predictive-search>
          {%- else -%}
            </search-form>
          {%- endif -%}
  {%  comment %}
<details-modal class="header__search">
  <details>
    <summary class="header__icon header__icon--search header__icon--summary link focus-inset modal__toggle" aria-haspopup="dialog" aria-label="{{ 'general.search.search' | t }}">
      <span>
        <svg class="modal__toggle-open icon icon-search" aria-hidden="true" focusable="false">
          <use href="#icon-search">
        </svg>
        <svg class="modal__toggle-close icon icon-close" aria-hidden="true" focusable="false">
          <use href="#icon-close">
        </svg>
      </span>
    </summary>
    <div class="search-modal modal__content gradient" role="dialog" aria-modal="true" aria-label="{{ 'general.search.search' | t }}">
      <div class="modal-overlay"></div>
      <div class="search-modal__content{% if settings.inputs_shadow_vertical_offset != 0 and settings.inputs_shadow_vertical_offset < 0 %} search-modal__content-top{% else %} search-modal__content-bottom{% endif %}" tabindex="-1">
       
        <button type="button" class="search-modal__close-button modal__close-button link link--text focus-inset" aria-label="{{ 'accessibility.close' | t }}">
          <svg class="icon icon-close" aria-hidden="true" focusable="false">
            <use href="#icon-close">
          </svg>
        </button>
      </div>
    </div>
  </details>
</details-modal>
   {%  endcomment %}      
{%- endif -%}
