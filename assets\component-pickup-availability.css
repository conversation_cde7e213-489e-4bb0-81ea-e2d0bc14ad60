pickup-availability {
  display: block;
}

pickup-availability[available] {
  min-height: 8rem;
}

.pickup-availability-preview {
  align-items: flex-start;
  display: flex;
  gap: 0.2rem;
  padding: 2.5rem;
  background: var(--gradient-base-background-2);
}

.pickup-availability-preview .icon {
  flex-shrink: 0;
  height: 2.4rem;
}

.pickup-availability-preview .icon-unavailable {
  height: 1.6rem;
  margin-top: 0.1rem;
}

.pickup-availability-button {
  background-color: transparent;
  color: rgba(var(--color-foreground), 0.75);
  letter-spacing: 0.06rem;
  padding: 0 0 0.2rem;
  text-align: left;
  text-decoration: underline;
}

/* .pickup-availability-button:hover {
  color: rgb(var(--color-foreground));
} */

.pickup-availability-info *:not(:last-child) {
  margin: 0 0 0.6rem;
}

pickup-availability-drawer .pickup-drawer{
  background-color: rgb(var(--color-background));
  height: 100%;
  opacity: 0;
  overflow-y: auto;
  padding: 3rem;
  position: fixed;
  top: 0;
  right: 0;
  z-index: 4;
  transition: opacity var(--duration-default) ease,
    transform var(--duration-default) ease;
  transform: translateX(100%);
  width: 100%;
  border-width: var(--drawer-border-width);
  border-color: transparent;
  border-style: solid;
  filter: drop-shadow(
    var(--drawer-shadow-horizontal-offset)
    var(--drawer-shadow-vertical-offset)
    var(--drawer-shadow-blur-radius)
    rgba(var(--color-shadow), var(--drawer-shadow-opacity))
  );
}

pickup-availability-drawer[open] .pickup-drawer{
  transform: translateX(0);
  opacity: 1;
}


  pickup-availability-drawer .pickup-drawer{
    transform: translateX(100%);
    width: 40rem;
  }

  pickup-availability-drawer[open] .pickup-drawer{
    opacity: 1;
    transform: translateX(0);
    animation: animateDrawerOpen var(--duration-default) ease;
  }
@media screen and (max-width: 450px) {
 pickup-availability-drawer .pickup-drawer{
    transform: translateX(100%);
    width: 30rem;
  }
}

.pickup-availability-header {
  align-items: flex-start;
  display: flex;
  justify-content: space-between;
  margin-bottom: 1.2rem;
}

.pickup-availability-drawer-title {
  margin: 0;
}

.pickup-availability-header .icon {
  width: 2rem;
}

.pickup-availability-drawer-button {
  background-color: transparent;
  border: none;
  color: rgb(var(--color-foreground));
  cursor: pointer;
  height: 1.5rem;
    padding: 0;
    width: 1.5rem;
    position: absolute;
    right: 20px;
    top: 20px;
    transition: all 0.3s linear;
}
.pickup-availability-drawer-button svg {width: 1.5rem;height: 1.5rem;fill: currentColor;}

.pickup-availability-drawer-button:hover {
  color: rgba(var(--color-base-outline-button-labels));
}

.pickup-availability-variant {
  font-size: 1.4rem;
  line-height: calc(1 + 0.2 / var(--font-body-scale));
  margin: 0 0 3rem;
  text-transform: capitalize;
  font-weight:500;
}

.pickup-availability-variant > * + strong {
  margin-left: 1rem;
}

.pickup-availability-list__item {
  /* border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.08); */
  padding: 3rem 0;
}

.pickup-availability-list__item:first-child {
  border-top: 0.1rem solid rgba(var(--color-foreground), 0.08);
}

.pickup-availability-list__item > * {
  margin: 0;
}

.pickup-availability-list__item > * + * {
  margin-top: 1rem;
}

.pickup-availability-address {
  font-style: normal;
  font-size: 1.6rem;
  line-height: calc(1 + 0.5 / var(--font-body-scale));
  padding: 0;
  background-color: transparent;
  /* border: 1px solid var(--gradient-base-background-2); */
}

.pickup-availability-address p {
  margin: 0;
}
.pickup-availability-list__item h3 {
    font-size: 18px;
    font-weight: 500;
}
.pickup-availability-list__item p.pickup-availability-preview.caption-large {
    background: transparent;
    padding: 0;
    font-size: 16px;
    margin-top: 20px;
}
@keyframes animateDrawerOpen {
  @media screen and (max-width: 749px) {
    0% {
      opacity: 0;
      transform: translateX(100%);
    }

    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @media screen and (min-width: 750px) {
    0% {
      opacity: 0;
      transform: translateX(100%);
    }

    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }
}

 pickup-availability-drawer[open]:after {
    content: "";
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    z-index: 3;
    background-color: rgba(0, 0, 0, 0.7);
    transition: all 0.5s linear;
}
