<!-- --VERSION-15-- -->

{% style %}
:root {
--vtl-neutral-70: #5e5e5e;
--vtl-neutral-01: #fff;
--vtl-size-1: 1px;
--vtl-size-2: 2px;
--vtl-size-4: 4px;
--vtl-size-14: 14px;
--vtl-size-16: 16px;
--vtl-size-20: 20px;
--vtl-size-24: 24px;
--vtl-size-32: 32px;
--vtl-size-40: 40px;
--vtl-font-weight-400: 400;
--vtl-font-weight-600: 600;
--vtl-line-height-140: 1.4;
--vtl-text-decoration-none: none;
--vtl-border-width-1: var(--vtl-size-1);
--vtl-border-width-2: var(--vtl-size-2);
--vtl-color-bg-fill-inverse-on-light: var(--vtl-neutral-01);
--vtl-color-text-disable-on-light: var(--vtl-neutral-70);
--vtl-space-4: var(--vtl-size-4);
--vtl-space-16: var(--vtl-size-16);
--vtl-space-20: var(--vtl-size-20);
--vtl-space-24: var(--vtl-size-24);
--vtl-space-32: var(--vtl-size-32);
--vtl-space-40: var(--vtl-size-40);
--vtl-font-size-14: var(--vtl-size-14);
}


{% endstyle %}

<!-- --UUID-3864dc2b-- -->

<section class="Vtls-{{ section.id | handle }}">
	<div class="VtlsCollListContainer">
		<ul class="VtlsCollList">
			{%- for block in section.blocks -%}
				{%- liquid
					assign collection_image = block.settings.collection.featured_image
					assign desktop_image = block.settings.image_desktop
					assign mobile_image = block.settings.image_mobile
					assign heading = block.settings.collection.title

					if mobile_image == blank and desktop_image != blank
						assign mobile_image = block.settings.image_desktop
					endif
					if mobile_image == blank
						assign mobile_image = block.settings.collection.featured_image
					endif
					if desktop_image != blank
						assign collection_image = desktop_image
					endif
					if block.settings.heading != blank
						assign heading = block.settings.heading
					endif
				-%}
				{%- capture block_css_variables -%}
					--block-banner-width: {{- block.settings.card_width -}};
					--block-banner-height: {{- block.settings.card_height -}};

					--block-subheading-size: {{- block.settings.subheading_size -}}px;
					--block-heading-size: {{- block.settings.heading_size -}}px;
					--block-description-size: {{- block.settings.description_size -}}px;
					--block-text-color: {{- block.settings.text_color -}};

					--block-primary-button-text-color: {{- block.settings.primary_text_color -}};
					--block-primary-button-background-color: {{- block.settings.primary_background_color -}};
					--block-primary-button-border-color: {{- block.settings.primary_border_color -}};

					--block-overlay-color: {{- block.settings.overlay_color -}};
					--block-overlay-opacity: {{- block.settings.overlay_opacity -}}%;
      			{%- endcapture -%}
				<li
					style="{{- block_css_variables | strip_newlines -}}"
					class="VtlsCollListCard"
					{{ block.shopify_attributes }}
				>
					<div class="VtlsCollListCard__Wrapper">
						<a
							href="{%- if block.settings.primary_button_link != blank -%}{{ block.settings.primary_button_link }}{% else %}{{- block.settings.collection.url -}}{%- endif -%}"
							class="
								VtlsCollListCard__Media
								{% if block.settings.primary_button_link == blank and block.settings.collection == blank %}
								 	VtlsCollListCard__Media--inactiveLink
								{% endif %}
							"
						>
							{%- if collection_image -%}
								{{-
									collection_image
									| image_url: width: collection_image.width
									| image_tag:
										sizes: "100vw",
										widths: "400, 600, 800, 1200, 1600, 1800, 2400, 2800, 3200",
										fetchpriority: "low",
										loading: "lazy",
										class: "VtlsCollListCard__Image VtlsCollListCard__Image--media-desktop"
								-}}
							{%- else -%}
								<img
									src="data:image/webp;base64,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"
									alt="Collection image"
									width="580"
									height="580"
									loading="lazy"
									sizes="100vw"
									fetchpriority="low"
									class="VtlsCollListCard__Image VtlsCollListCard__Image--media-desktop"
								>
							{%- endif -%}
							{%- if mobile_image -%}
								{{-
									mobile_image
									| image_url: width: mobile_image.width
									| image_tag:
										sizes: "100vw",
										widths: "400, 600, 800, 1200, 1600, 1800, 2400, 2800, 3200",
										fetchpriority: "low",
										loading: "lazy",
										class: "VtlsCollListCard__Image VtlsCollListCard__Image--media-mobile"
								-}}
							{%- else -%}
								<img
									src="data:image/webp;base64,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"
									alt="Collection image"
									width="580"
									height="580"
									loading="lazy"
									sizes="100vw"
									fetchpriority="low"
									class="VtlsCollListCard__Image VtlsCollListCard__Image--media-mobile"
								>
							{%- endif -%}
						</a>
						<div class="VtlsCollListCard__Content {% if block.settings.inner_border == true %}VtlsCollListCard__Content--hasBorder{% endif %}">
							<div class="VtlsCollListCard__Information VtlsCollListCard__Information--desktop-position--{{ section.settings.desktop_content_position }} VtlsCollListCard__Information--mobile-position--{{ section.settings.mobile_content_position }}">
								{%- if block.settings.subheading != blank -%}
									<p class="VtlsCollListCard__Caption">
										{{- block.settings.subheading -}}
									</p>
								{%- endif -%}
								{%- if heading -%}
									<h2>
										<a
											class="VtlsCollListCard__Heading"
											href="{{- block.settings.collection.url -}}"
										>
											{{- heading | escape -}}
										</a>
									</h2>
								{%- else -%}
									<h2>
										<span class="VtlsCollListCard__Heading"> Collection title </span>
									</h2>
								{%- endif -%}
								{%- if block.settings.description != blank -%}
									<p class="VtlsCollListCard__Description">
										{{- block.settings.description | strip_html | truncatewords: 12 -}}
									</p>
								{%- endif -%}
								{%- if block.settings.primary_button_label != blank -%}
									<a
										{% if block.settings.primary_new_tab == true %}
											target="_blank"
										{% endif %}
										href="{%- if block.settings.primary_button_link != blank -%} {{ block.settings.primary_button_link }} {% else %} {{- block.settings.collection.url -}} {%- endif -%}"
										class="
											VtlsCollListCard__Button VtlsCollListCard__Button--{{ block.settings.primary_button_style }}
											{% if block.settings.primary_button_link == blank and block.settings.collection == blank %}
											  VtlsCollListCard__Button--inactiveLink
											{% endif %}
										"
									>
										{{- block.settings.primary_button_label | escape -}}
									</a>
								{%- endif -%}
							</div>
						</div>
					</div>
				</li>
			{%- endfor -%}
		</ul>
	</div>
</section>

{% schema %}
{
	"name": "❤️ Collection Banners",
	"disabled_on": {
		"groups": ["header", "footer"]
	},
	"settings": [
		{
			"type": "header",
			"content": "Design"
		},
		{
			"type": "color",
			"id": "section_background",
			"label": "Section background"
		},
		{
			"type": "select",
			"id": "desktop_content_position",
			"label": "Desktop content position",
			"options": [
				{
					"value": "top-left",
					"label": "Top left"
				},
				{
					"value": "top-center",
					"label": "Top center"
				},
				{
					"value": "top-right",
					"label": "Top right"
				},
				{
					"value": "center-left",
					"label": "Center left"
				},
				{
					"value": "center-center",
					"label": "Center center"
				},
				{
					"value": "center-right",
					"label": "Center right"
				},
				{
					"value": "bottom-left",
					"label": "Bottom left"
				},
				{
					"value": "bottom-center",
					"label": "Bottom center"
				},
				{
					"value": "bottom-right",
					"label": "Bottom right"
				}
			],
			"default": "center-center"
		},
		{
			"type": "select",
			"id": "mobile_content_position",
			"label": "Mobile content position",
			"options": [
				{
					"value": "top-left",
					"label": "Top left"
				},
				{
					"value": "top-center",
					"label": "Top center"
				},
				{
					"value": "top-right",
					"label": "Top right"
				},
				{
					"value": "center-left",
					"label": "Center left"
				},
				{
					"value": "center-center",
					"label": "Center center"
				},
				{
					"value": "center-right",
					"label": "Center right"
				},
				{
					"value": "bottom-left",
					"label": "Bottom left"
				},
				{
					"value": "bottom-center",
					"label": "Bottom center"
				},
				{
					"value": "bottom-right",
					"label": "Bottom right"
				}
			],
			"default": "center-center"
		},
		{
			"type": "range",
			"id": "buttons_corner",
			"min": 0,
			"max": 100,
			"step": 2,
			"unit": "px",
			"label": "Buttons corner radius",
			"default": 4
		},
		{
			"type": "header",
			"content": "Layout"
		},
		{
			"type": "range",
			"id": "section_width",
			"min": 70,
			"max": 100,
			"step": 1,
			"unit": "%",
			"label": "Section max width",
			"default": 90
		},
		{
			"type": "text",
			"id": "section_max_width",
			"label": "Custom max width (optional)",
			"info": "Section max width in pixels"
		},
		{
			"type": "range",
			"id": "items_gap",
			"min": 0,
			"max": 50,
			"step": 2,
			"unit": "px",
			"label": "Space between banners",
			"default": 20
		},
		{
			"type": "range",
			"id": "vertical_padding",
			"min": 0,
			"max": 100,
			"step": 2,
			"unit": "px",
			"label": "Top - bottom padding (desktop)",
			"default": 40
		},
		{
			"type": "range",
			"id": "vertical_margin",
			"min": 0,
			"max": 100,
			"step": 2,
			"unit": "px",
			"label": "Top - bottom margin (desktop)",
			"default": 0
		},
		{
			"type": "range",
			"id": "vertical_padding_mobile",
			"min": 0,
			"max": 100,
			"step": 2,
			"unit": "px",
			"label": "Top - bottom padding (mobile)",
			"default": 20
		},
		{
			"type": "range",
			"id": "vertical_margin_mobile",
			"min": 0,
			"max": 100,
			"step": 2,
			"unit": "px",
			"label": "Top - bottom margin (mobile)",
			"default": 0
		}
	],
	"blocks": [
		{
			"type": "collection",
			"limit": 12,
			"name": "Collection",
			"settings": [
				{
					"type": "collection",
					"id": "collection",
					"label": "Collection"
				},
				{
					"type": "header",
					"content": "Image (optional)",
					"info": "Select another image for the collection banner."
				},
				{
					"type": "image_picker",
					"id": "image_desktop",
					"label": "Image on desktop"
				},
				{
					"type": "image_picker",
					"id": "image_mobile",
					"label": "Image on mobile"
				},
				{
					"type": "header",
					"content": "Banner settings",
					"info": "When using a 1/4 width and small height banner, we recommend centering the content."
				},
				{
					"type": "select",
					"id": "card_width",
					"label": "Banner width on desktop",
					"options": [
						{
							"value": "1",
							"label": "1/4 section"
						},
						{
							"value": "2",
							"label": "1/2 section"
						},
						{
							"value": "3",
							"label": "3/4 section"
						},
						{
							"value": "4",
							"label": "Full section"
						}
					],
					"default": "2"
				},
				{
					"type": "select",
					"id": "card_height",
					"label": "Banner height on desktop",
					"options": [
						{
							"value": "1",
							"label": "Small"
						},
						{
							"value": "2",
							"label": "Medium"
						},
						{
							"value": "3",
							"label": "Large"
						}
					],
					"default": "1"
				},
				{
					"type": "color",
					"id": "overlay_color",
					"label": "Overlay color",
					"default": "#222222"
				},
				{
					"type": "range",
					"id": "overlay_opacity",
					"min": 0,
					"max": 100,
					"step": 2,
					"unit": "%",
					"label": "Overlay opacity",
					"default": 20
				},
				{
					"type": "checkbox",
					"id": "inner_border",
					"label": "Add inner border",
					"default": false
				},
				{
					"type": "header",
					"content": "Text settings (optional)"
				},
				{
					"type": "text",
					"id": "heading",
					"label": "Heading"
				},
				{
					"type": "select",
					"id": "heading_size",
					"label": "Heading size",
					"options": [
						{
							"value": "24",
							"label": "Small"
						},
						{
							"value": "32",
							"label": "Medium"
						},
						{
							"value": "48",
							"label": "Large"
						}
					],
					"default": "32"
				},
				{
					"type": "text",
					"id": "subheading",
					"label": "Subheading"
				},
				{
					"type": "select",
					"id": "subheading_size",
					"label": "Subheading size",
					"options": [
						{
							"value": "12",
							"label": "Small"
						},
						{
							"value": "14",
							"label": "Medium"
						},
						{
							"value": "18",
							"label": "Large"
						}
					],
					"default": "14"
				},
				{
					"type": "text",
					"id": "description",
					"label": "Description"
				},
				{
					"type": "select",
					"id": "description_size",
					"label": "Description size",
					"options": [
						{
							"value": "14",
							"label": "Small"
						},
						{
							"value": "16",
							"label": "Medium"
						},
						{
							"value": "20",
							"label": "Large"
						}
					],
					"default": "16"
				},
				{
					"type": "color",
					"id": "text_color",
					"label": "Text color",
					"default": "#ffffff"
				},
				{
					"type": "header",
					"content": "Button settings"
				},
				{
					"type": "text",
					"id": "primary_button_label",
					"label": "Button label"
				},
				{
					"type": "url",
					"id": "primary_button_link",
					"label": "Button link (optional)"
				},
				{
					"type": "select",
					"id": "primary_button_style",
					"label": "Button style",
					"options": [
						{
							"value": "solid",
							"label": "Filled"
						},
						{
							"value": "outline",
							"label": "Outline"
						},
						{
							"value": "link",
							"label": "Link"
						}
					],
					"default": "link"
				},
				{
					"type": "color",
					"id": "primary_text_color",
					"label": "Button text",
					"default": "#ffffff"
				},
				{
					"type": "color",
					"id": "primary_background_color",
					"label": "Button background",
					"default": "#ffffff",
					"visible_if": "{{ block.settings.primary_button_style == 'solid' }}"
				},
				{
					"type": "color",
					"id": "primary_border_color",
					"label": "Button border",
					"default": "#ffffff",
					"visible_if": "{{ block.settings.primary_button_style == 'outline' }}"
				}
			]
		}
	],
	"presets": [
		{
			"name": "❤️ Square: Collection Banners",
			"category": "Square sections",
			"blocks": [
				{
					"type": "collection",
					"settings": {
						"card_width": "2",
						"card_height": "2",
						"primary_button_style": "solid",
						"primary_text_color": "#222222",
						"primary_button_label": "Shop now",
						"subheading": "Subheading"
					}
				},
				{
					"type": "collection"
				},
				{
					"type": "collection"
				}
			]
		}
	]
}
{% endschema %}

{% style %}
	.Vtls-{{ section.id | handle }} {
		--section-vertical-margin: {{- section.settings.vertical_margin -}}px;
		--section-vertical-margin-mobile: {{- section.settings.vertical_margin_mobile -}}px;
		--section-banners-space: {{- section.settings.items_gap -}}px;
		--section-background-color: {{- section.settings.section_background -}};
		--section-vertical-padding: {{- section.settings.vertical_padding -}}px;
		--section-vertical-padding-mobile: {{- section.settings.vertical_padding_mobile -}}px;
		--section-max-width: {{- section.settings.section_max_width -}}px;
		--section-width: {{- section.settings.section_width -}}%;
		--section-buttons-corner-radius: {{- section.settings.buttons_corner -}}px;
	}

	

.Vtls-{{ section.id | handle }} .VtlsCollListContainer{margin:var(--section-vertical-margin-mobile) 0;background-color:var(--section-background-color)}@media(min-width: 992px){.Vtls-{{ section.id | handle }} .VtlsCollListContainer{margin:var(--section-vertical-margin) 0}}.Vtls-{{ section.id | handle }} .VtlsCollList{padding:var(--section-vertical-padding-mobile) 0;display:grid;grid:auto-flow dense 200px/repeat(1, minmax(0, 1fr));max-width:var(--section-max-width);gap:var(--section-banners-space);width:var(--section-width);margin:0 auto;list-style-type:none}@media(min-width: 992px){.Vtls-{{ section.id | handle }} .VtlsCollList{padding:var(--section-vertical-padding) 0;display:grid;grid:auto-flow dense 290px/repeat(4, minmax(0, 1fr));max-width:var(--section-max-width);gap:var(--section-banners-space);width:var(--section-width);margin:0 auto}}.Vtls-{{ section.id | handle }} .VtlsCollListCard{grid-area:span min(2, var(--block-banner-height))/span min(1,var(--block-banner-width))}@media(min-width: 992px){.Vtls-{{ section.id | handle }} .VtlsCollListCard{grid-area:span var(--block-banner-height)/span var(--block-banner-width)}}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Wrapper{height:100%;position:relative}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Media{overflow:hidden;height:100%;display:block}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Media img{width:100%;height:100%;object-fit:cover}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Media::before{position:absolute;top:0;left:0;display:block;width:100%;height:100%;content:"";z-index:2;background-color:var(--block-overlay-color);opacity:var(--block-overlay-opacity)}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Media--inactiveLink{cursor:default;pointer-events:none}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Image--media-desktop{display:none}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Image--media-mobile{display:block}@media(min-width: 992px){.Vtls-{{ section.id | handle }} .VtlsCollListCard__Image--media-desktop{display:block}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Image--media-mobile{display:none}}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Content--hasBorder::after{content:"";border:var(--vtl-border-width-2) solid var(--block-text-color);position:absolute;top:50%;left:50%;z-index:2;pointer-events:none;transform:translate(-50%, -50%);width:calc(100% - var(--vtl-space-20));height:calc(100% - var(--vtl-space-20))}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Information{position:absolute;color:var(--block-text-color);width:100%;z-index:2;pointer-events:none;height:fit-content;display:flex;flex-direction:column;padding:var(--vtl-space-20)}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Information h2,.Vtls-{{ section.id | handle }} .VtlsCollListCard__Information p{margin:0}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Information--mobile-position--top-left{top:0;left:0;text-align:left;align-items:flex-start}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Information--mobile-position--top-center{top:0;left:50%;transform:translate(-50%, 0);text-align:center;align-items:center}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Information--mobile-position--top-right{top:0;right:0;text-align:right;align-items:flex-end}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Information--mobile-position--center-left{top:50%;left:0;transform:translate(0, -50%);text-align:left;align-items:flex-start}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Information--mobile-position--center-center{top:50%;left:50%;transform:translate(-50%, -50%);text-align:center;align-items:center}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Information--mobile-position--center-right{top:50%;right:0;transform:translate(0, -50%);text-align:right;align-items:flex-end}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Information--mobile-position--bottom-left{bottom:0;left:0;text-align:left;align-items:flex-start}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Information--mobile-position--bottom-center{top:unset;bottom:0;left:50%;transform:translate(-50%, 0);text-align:center;align-items:center}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Information--mobile-position--bottom-right{bottom:0;right:0;text-align:right;align-items:flex-end}@media(min-width: 992px){.Vtls-{{ section.id | handle }} .VtlsCollListCard__Information{padding:var(--vtl-space-32)}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Information--desktop-position--top-left{top:0;left:0;text-align:left;transform:unset;align-items:flex-start}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Information--desktop-position--top-center{top:0;left:50%;transform:translate(-50%, 0);text-align:center;align-items:center}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Information--desktop-position--top-right{top:0;right:0;left:0;text-align:right;transform:unset;align-items:flex-end}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Information--desktop-position--center-left{top:50%;left:0;transform:translate(0, -50%);text-align:left;align-items:flex-start}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Information--desktop-position--center-center{top:50%;left:50%;transform:translate(-50%, -50%);text-align:center;align-items:center}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Information--desktop-position--center-right{top:50%;right:0;left:unset;transform:translate(0, -50%);text-align:right;align-items:flex-end}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Information--desktop-position--bottom-left{bottom:0;left:0;text-align:left;top:unset;transform:unset;align-items:flex-start}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Information--desktop-position--bottom-center{top:unset;bottom:0;left:50%;transform:translate(-50%, 0);text-align:center;align-items:center}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Information--desktop-position--bottom-right{bottom:0;right:0;top:unset;left:unset;transform:unset;text-align:right;align-items:flex-end}}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Caption{margin:0;font-size:var(--block-subheading-size);font-weight:var(--vtl-font-weight-600)}@media(min-width: 992px){.Vtls-{{ section.id | handle }} .VtlsCollListCard__Caption{font-size:var(--block-subheading-size)}}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Heading{padding-bottom:var(--vtl-space-16);pointer-events:auto;margin:0;font-size:calc(var(--block-heading-size)*.75);text-decoration:none;color:var(--block-text-color);display:block;width:100%;font-weight:var(--vtl-font-weight-600);pointer-events:auto}@media(min-width: 992px){.Vtls-{{ section.id | handle }} .VtlsCollListCard__Heading{font-size:var(--block-heading-size)}}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Description{max-width:400px;padding-bottom:var(--vtl-space-16);font-size:calc(var(--block-description-size)*.88)}@media(min-width: 992px){.Vtls-{{ section.id | handle }} .VtlsCollListCard__Description{font-size:var(--block-description-size)}}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Button{display:inline-flex;justify-content:center;align-items:center;cursor:pointer;font:inherit;font-size:var(--vtl-font-size-14);text-decoration:var(--vtl-text-decoration-none);-webkit-appearance:none;appearance:none;width:fit-content;padding:var(--vtl-space-16) var(--vtl-space-20);transition:all .3s ease-in;line-height:1}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Button:hover{opacity:.85}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Button--solid{color:var(--block-primary-button-text-color);background:var(--block-primary-button-background-color);border-radius:var(--section-buttons-corner-radius)}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Button--outline{color:var(--block-primary-button-text-color);background:rgba(0,0,0,0);border:var(--vtl-border-width-1) solid var(--block-primary-button-border-color);border-radius:var(--section-buttons-corner-radius)}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Button--link{color:var(--block-primary-button-text-color);background:rgba(0,0,0,0);border:0;border-radius:0;padding:0;text-decoration:underline;text-underline-offset:var(--vtl-space-4)}.Vtls-{{ section.id | handle }} .VtlsCollListCard__Button--inactiveLink{cursor:default;pointer-events:none}

{% endstyle %}

<script>
	(function () {
		const uninstallMessage = 'Displaying this section requires an active Square: Theme Sections & AI Pages subscription. Reinstall the <a href="https://apps.shopify.com/theme-sections-ai-page-builder" target="_blank">Square: Theme Sections & AI Pages app</a> to activate it or consider removing the section.';
		const downgradeMessage = 'You downgraded to a free plan. You need to upgrade your <a href="https://apps.shopify.com/theme-sections-ai-page-builder" target="_blank">Square: Theme Sections & AI Pages app</a> plan to activate it or consider removing the section.';
		const projectId = '5';
		const sectionsSelector = '.shopify-section';
		const apps = {
			'1': {
				prefix: 'vitals',
				hostname: 'https://appsolve.io'
			},
			'5': {
				prefix: 'tsapb',
				hostname: 'https://theme-sections.square-apps.com'
			}
		};

		const cacheKeysMetaFields = '{{ shop.metafields.vitals.tsck }}';
		const shopId = '{{ shop.id }}';

		const getHostname = () => {
			if (apps[projectId].hostname) {
				return apps[projectId].hostname;
			}

			console.error(`hostname for appId ${projectId} cannot be determined.`);
			return undefined;
		}

		const disableSection = (message, selector) => {
			const mainContainer = document.querySelector(selector);
			const contentContainer = document.querySelector(`${selector} > section`);

			if ('{{ request.design_mode }}' === 'true') {
				contentContainer.innerHTML = `<h2 class="VtlsUninstallBG"><div class="VtlsUninstallBG__Text">${message}</div></h2>`;
			} else {
				mainContainer.style.display = 'none';
				mainContainer.remove();
			}
		};

		const getThemeSectionIdsByUUID = (installedThemeSections, uuid, appId) => {
			const listIds = [];

			installedThemeSections.forEach((installedThemeSection) => {
				if (installedThemeSection.id.includes(`${apps[appId].prefix}_${uuid}`)) {
					listIds.push(installedThemeSection.id);
				}
			})

			return listIds;
		}

		const getThemeSectionsStatusList = (url) => fetch(url, {
			method: 'GET',
			headers: { 'Content-Type': 'application/json' },
		}).then((response) => response.json());

		const evaluateThemeSections = (url) => {
			getThemeSectionsStatusList(url).then(response => {
				const { data, f, i } = response;
				const installedThemeSections = document.querySelectorAll(sectionsSelector);

				data.forEach((themeSection) => {
					const { e, u, a } = themeSection;

					if (!e) {
						const themeSectionsWithUUID = getThemeSectionIdsByUUID(installedThemeSections, u, a);
						let message = '';

						if (f) {
							message = downgradeMessage;
						}

						if (!i) {
							message = uninstallMessage;
						}

						themeSectionsWithUUID.forEach((themeSectionSelector) => {
							disableSection(message, `#${themeSectionSelector}`);
						})
					}
				});
			})
		}

		if (shopId) {
			if (window.vtlsSecurityCheckStarted === undefined) {
				window.vtlsSecurityCheckStarted = true;

				let url = '';
				const hostname = getHostname();

				if (!hostname) {
					return;
				}

				const baseUrl = `${hostname}/bundle/api/v2/sf/ts`;

				if (cacheKeysMetaFields) {
					url = `${baseUrl}/${shopId}/${cacheKeysMetaFields}.json`;
				} else {
					const timestamp = Math.round(new Date().getTime() / 1000 / 10000) * 10000;
					url = `${baseUrl}/${shopId}/${timestamp}.json`;
				}

				evaluateThemeSections(url);
			}
		}
	})();
</script>

{% style %}
	

.VtlsUninstallBG{background-color:var(--vtl-color-bg-fill-inverse-on-light);background-image:url("data:image/avif;base64,iVBORw0KGgoAAAANSUhEUgAABOcAAABkCAQAAABnAJxfAAAAIGNIUk0AAHomAACAhAAA+gAAAIDoAAB1MAAA6mAAADqYAAAXcJy6UTwAAAACYktHRAD/h4/MvwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAAd0SU1FB+gLFQgmAgUXFkMAAARaSURBVHja7daxbSRBEATB8ZzvKw0h1dcIlJS4jl25Azlavffn9/3v++e//+vvCwqFQqFQKBRKSklEUCgUCoVCoVAMOgqFQqFQKJSzSiKCQqFQKBQKhWLQUSgUCoVCoZxVEhEUCoVCoVAoFIOOQqFQKBQK5VOURASFQqFQKBQKZVUSERQKhUKhUCiUVUlEUCgUCoVCoVBWZbtOPoVCoVAoFArlovIKERQKhUKhUCiUVXmFCAqFQqFQKBTKqrxCBIVCoVAoFAplVV4hgkKhUCgUCoUyK4kICoVCoVAoFMqqJCIoFAqFQqFQKKuSiKBQKBQKhUKhrEoigkKhUCgUCoVi0FEoFAqFQqGcVRIRFAqFQqFQKBSDjkKhUCgUCuWskoigUCgUCoVCoRh0FAqFQqFQKGeVRASFQqFQKBQKxaCjUCgUCoVCOaskIigUCoVCoVAoBh2FQqFQKBTKWSURQaFQKBQKhUIx6CgUCoVCoVDOKokICoVCoVAoFIpBR6FQKBQKhXJWSURQKBQKhUKhUAw6CoVCoVAolLNKIoJCoVAoFAqFYtBRKBQKhUKhnFUSERQKhUKhUCgUg45CoVAoFArlrJKIoFAoFAqFQqEYdBQKhUKhUChnlUQEhUKhUCgUCsWgo1AoFAqFQvkUJRFBoVAoFAqFQlmVRASFQqFQKBQKZVUSERQKhUKhUCiUVdmuk0+hUCgUCoVCuai8QgSFQqFQKBQKZVVeIYJCoVAoFAqFsiqvEEGhUCgUCoVCWZVXiKBQKBQKhUKhzEoigkKhUCgUCoWyKokICoVCoVAoFMqqJCIoFAqFQqFQKKuSiKBQKBQKhUKhGHQUCoVCoVAoZ5VEBIVCoVAoFArFoKNQKBQKhUI5qyQiKBQKhUKhUCgGHYVCoVAoFMpZJRFBoVAoFAqFQjHoKBQKhUKhUM4qiQgKhUKhUCgUikFHoVAoFAqFclZJRFAoFAqFQqFQDDoKhUKhUCiUs0oigkKhUCgUCoVi0FEoFAqFQqGcVRIRFAqFQqFQKBSDjkKhUCgUCuWskoigUCgUCoVCoRh0FAqFQqFQKGeVRASFQqFQKBQKxaCjUCgUCoVCOaskIigUCoVCoVAoBh2FQqFQKBTKWSURQaFQKBQKhUIx6CgUCoVCoVA+RUlEUCgUCoVCoVBWJRFBoVAoFAqFQlmVRASFQqFQKBQKZVW26+RTKBQKhUKhUC4qrxBBoVAoFAqFQlmVV4igUCgUCoVCoazKK0RQKBQKhUKhUFblFSIoFAqFQqFQKLOSiKBQKBQKhUKhrEoigkKhUCgUCoWyKokICoVCoVAoFMqqJCIoFAqFQqFQKAYdhUKhUCgUylklEUGhUCgUCoVCMegoFAqFQqFQziqJCAqFQqFQKBSKQUehUCgUCoVyVklEUCgUCoVCoVAMOgqFQqFQKJSzSiKCQqFQKBQKhWLQUSgUCoVCoZxVEhEUCoVCoVAoFIOOQqFQKBQK5dOVX5nMB52iezhxAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDI0LTExLTIxVDA4OjM4OjAyKzAwOjAw30WrCwAAACV0RVh0ZGF0ZTptb2RpZnkAMjAyNC0xMS0yMVQwODozODowMiswMDowMK4YE7cAAAAodEVYdGRhdGU6dGltZXN0YW1wADIwMjQtMTEtMjFUMDg6Mzg6MDIrMDA6MDD5DTJoAAAAAElFTkSuQmCC");background-position:center;background-size:cover;text-align:center;padding:var(--vtl-space-32);margin:0}.VtlsUninstallBG__Text{color:var(--vtl-color-text-disable-on-light);font-family:Inter,sans-serif;font-size:var(--vtl-font-size-14);font-weight:var(--vtl-font-weight-400);line-height:var(--vtl-line-height-140);padding:var(--vtl-space-16) var(--vtl-space-16) var(--vtl-space-16) var(--vtl-space-40);background-color:var(--vtl-color-bg-fill-inverse-on-light);display:inline-block}.VtlsUninstallBG__Text::before{content:url("data:image/svg+xml;utf8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2220%22%20height%3D%2220%22%20viewBox%3D%220%200%2020%2020%22%20fill%3D%22none%22%3E%0A%09%09%09%3Cpath%20d%3D%22M9.99994%206.75C10.4142%206.75%2010.7499%207.08579%2010.7499%207.5V11C10.7499%2011.4142%2010.4142%2011.75%209.99994%2011.75C9.58573%2011.75%209.24994%2011.4142%209.24994%2011V7.5C9.24994%207.08579%209.58573%206.75%209.99994%206.75Z%22%20fill%3D%22%23B28400%22%2F%3E%0A%09%09%09%3Cpath%20d%3D%22M11%2013.5C11%2014.0523%2010.5523%2014.5%2010%2014.5C9.44772%2014.5%209%2014.0523%209%2013.5C9%2012.9477%209.44772%2012.5%2010%2012.5C10.5523%2012.5%2011%2012.9477%2011%2013.5Z%22%20fill%3D%22%23B28400%22%2F%3E%0A%09%09%09%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M10%203.5C8.95471%203.5%208.21616%204.20232%207.84762%204.94672C7.65565%205.33448%206.75984%207.0441%205.84305%208.79353L5.81531%208.84646C4.9229%2010.5493%204.01714%2012.2777%203.80716%2012.702C3.4351%2013.4537%203.32931%2014.4519%203.89953%2015.316C4.46911%2016.1791%205.44222%2016.5%206.36356%2016.5L13.6364%2016.5C14.5577%2016.5%2015.5308%2016.1791%2016.1004%2015.316C16.6707%2014.4519%2016.5649%2013.4537%2016.1928%2012.702C15.9832%2012.2784%2015.08%2010.555%2014.1889%208.85476L14.1569%208.79352C13.24%207.044%2012.3443%205.33454%2012.1524%204.94693C11.784%204.20242%2011.0454%203.5%2010%203.5ZM9.1919%205.61225C9.59605%204.79592%2010.4041%204.79592%2010.8081%205.61225C11.0102%206.02061%2011.9201%207.75686%2012.8296%209.49243C13.7383%2011.2262%2014.6466%2012.9594%2014.8485%2013.3673C15.2525%2014.1837%2014.8485%2015%2013.6364%2015L6.36356%2015C5.1515%2015%204.74746%2014.1837%205.1515%2013.3673C5.35376%2012.9587%206.26468%2011.2205%207.1748%209.4838C8.08283%207.75111%208.99005%206.01994%209.1919%205.61225Z%22%20fill%3D%22%23B28400%22%2F%3E%0A%09%09%3C%2Fsvg%3E");position:absolute;width:var(--vtl-space-20);height:var(--vtl-space-20);margin-left:calc(-1*var(--vtl-space-24))}.VtlsUninstallBG__Text a{color:var(--vtl-color-text-disable-on-light)}.VtlsUninstallBG__Text a:hover{text-decoration:none}

{% endstyle %}

