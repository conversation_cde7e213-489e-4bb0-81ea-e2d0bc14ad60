<!-- Image Gallery Mega Menu -->
{%- assign image_count = 0 -%}

{%- for i in (1..9) -%}
  {%- assign image_key = 'gallery_image_' | append: i -%}
  {%- if block.settings[image_key] != blank -%}
    {%- assign image_count = image_count | plus: 1 -%}
  {%- endif -%}
{%- endfor -%}

{%- if image_count > 0 -%}
<div class="mega-menu__image-gallery">
  <div class="image-gallery-container" 
       data-image-count="{{ image_count }}" 
       data-border-radius="{{ block.settings.border_radius | default: 'medium' }}"
       style="--image-size: {{ block.settings.image_height | default: 200 }}px; --image-size-mobile: {{ block.settings.image_height_mobile | default: 150 }}px; --text-size: {{ block.settings.text_size | default: 14 }}px; --text-size-mobile: {{ block.settings.text_size_mobile | default: 12 }}px;">
    
    {%- for i in (1..9) -%}
      {%- assign image_key = 'gallery_image_' | append: i -%}
      {%- assign title_key = 'gallery_title_' | append: i -%}
      {%- assign link_key = 'gallery_link_' | append: i -%}
      {%- assign hide_key = 'hide_image_' | append: i -%}
      
      {%- assign current_image = block.settings[image_key] -%}
      {%- assign current_title = block.settings[title_key] -%}
      {%- assign current_link = block.settings[link_key] -%}
      {%- assign hide_image = block.settings[hide_key] -%}
      
      {%- if current_image != blank and hide_image != true -%}
        <a href="{{ current_link | default: '#' }}" class="image-gallery-item">
          <div class="image-wrapper">
            <img src="{{ current_image | image_url: width: 400 }}" 
                 alt="{{ current_title | escape | default: 'Gallery Image' }}"
                 loading="lazy"
                 width="400"
                 height="400">
          </div>
          
          {%- if current_title != blank -%}
            <span class="image-title">{{ current_title | escape }}</span>
          {%- endif -%}
        </a>
      {%- endif -%}
    {%- endfor -%}
  </div>
</div>
{%- endif -%}