{{ 'section-instagram-gallery.css' | asset_url | stylesheet_tag }}

{%- liquid

 assign columns_mobile_int = section.settings.columns_mobile | plus: 0
  assign show_mobile_slider = false
  if section.settings.swipe_on_mobile and section.blocks.size > columns_mobile_int
    assign show_mobile_slider = true
  endif
  if section.settings.swiper_enable
  assign enable_slider = true  
  endif
 %}
{%- style -%}
    .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.50 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.50 | round: 0 }}px;
  }
   @media screen and (min-width: 576px) and (max-width: 749px){
   .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }
   }
  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }


  .insta-gallery.section-{{ section.id }} .insta-gallery-section.overlay .insta-gallery-wrapper{height:{{ section.settings.overlay_height }}px;}
  @media screen and (max-width: 1540px) {
  .insta-gallery.section-{{ section.id }} .insta-gallery-section.overlay .insta-gallery-wrapper{height:{{ section.settings.overlay_height_laptop }}px;}
  }
  @media screen and (max-width: 1024px) {
  .insta-gallery.section-{{ section.id }} .insta-gallery-section.overlay .insta-gallery-wrapper{height:{{ section.settings.overlay_height_tab }}px;}
  }
  @media screen and (max-width: 767px) {
  .insta-gallery.section-{{ section.id }} .insta-gallery-section.overlay .insta-gallery-wrapper{height:{{ section.settings.overlay_height_mobile }}px;}
  }
      
{%- endstyle -%}

<div class="insta-gallery color-{{ section.settings.color_scheme }} gradient{% if section.settings.title == blank %} no-heading{% endif %} {{ section.settings.custom_class_name }} section-{{ section.id }}">
 <div class="{% if section.settings.page_full_width %} page-full-width {% else %} page-width {% endif %} {% if section.settings.page_full_width_spacing %} page-full-width_spacing {% endif %}section-{{ section.id }}-padding isolate">
<div class="row"> 
  <div class="insta-gallery-wrapper">
      {%- unless section.settings.title == blank -%}
        <div class="title-wrapper-with-link title-wrapper--self-padded-mobile title-wrapper--no-top-margin content-align--{{ section.settings.column_alignment }}">
         {%- if section.settings.sub_heading != blank -%}
         <h6 class="sub-heading">{{ section.settings.sub_heading | escape }}</h6>
         {%- endif -%}  
         {%- if section.settings.title != blank -%}
         <h2 class="title {{ section.settings.heading_size }}">{{ section.settings.title | escape }} </h2>
         {%- endif -%} 
         {%- if section.settings.description != blank -%} 
         <p class="description"> {{ section.settings.description | escape }}</p> 
          {%- endif -%} 
          {%- if section.settings.button_label != blank -%}
            <a href="{{ section.settings.button_link }}" class="banner-button button button--primary">{{ section.settings.button_label | escape }}</a>
          {%- endif -%}
          </div>
        {%- endunless -%}
    {% unless enable_slider %}
       <slider-component class="slider-mobile-gutter{% if show_mobile_slider == false and section.settings.page_full_width == false %} {% endif %}{% if show_desktop_slider == false and section.settings.full_width == false %} page-width-desktop{% endif %}{% if show_desktop_slider %} slider-component-desktop{% endif %}">
    {% else %}
    <insta-slider>
      <div data-slider-options='{"loop": "{%- if section.settings.centered_slide -%}1{%- else -%}2{%- endif -%}","desktop": "{{ section.settings.desktop_column }}","laptop": "{{ section.settings.laptop_column }}","tablet": "{{ section.settings.tablet_column }}","mobile": "{{ section.settings.mobile_column }}","auto_play": "{{ section.settings.auto_play }}"}'>
      <div class="swiper" data-insta-slider>
      {%- endunless -%}
     <div class="{% if enable_slider %}swiper-wrapper{% endif %} insta-gallery-section  overlay {% unless section.settings.background_style == 'none' and settings.text_boxes_border_thickness > 0 or settings.text_boxes_shadow_opacity > 0 %} background-{{ section.settings.background_style }}{% endunless %} {% unless enable_slider %} grid grid--{{ section.settings.columns_mobile }}-col-tablet-down grid--{{ section.settings.desktop_column }}-col-desktop{% if show_mobile_slider %} slider slider--mobile grid--peek{% endif %}{%- endunless -%}"
        id="Slider-{{ section.id }}"
        role="list">
       {%- for block in section.blocks -%} 
       <div id="Slide-{{ section.id }}-{{ forloop.index }}" class="insta-gallery-wrapper {% if enable_slider %}swiper-slide{% else %}grid__item slider__slide{% endif %}">   
         <div class="insta-gallery-block-image">
          {%- if block.settings.enable_title_link %} <a href="{{ block.settings.block_button_link }}" class="insta-gallery-image">{% endif %}
          {% if block.settings.block_image != blank %}
            <img
                      class="insta-gallery-image"
                      srcset="{%- if block.settings.block_image.width >= 375 -%}{{ block.settings.block_image | image_url: width: 375 }} 375w,{%- endif -%}
                      {%- if block.settings.block_image.width >= 550 -%}{{ block.settings.block_image | image_url: width: 550 }} 550w,{%- endif -%}
                      {%- if block.settings.block_image.width >= 750 -%}{{ block.settings.block_image | image_url: width: 750 }} 750w,{%- endif -%}
                      {%- if block.settings.block_image.width >= 1100 -%}{{ block.settings.block_image | image_url: width: 1100 }} 1100w,{%- endif -%}
                      {%- if block.settings.block_image.width >= 1500 -%}{{ block.settings.block_image | image_url: width: 1500 }} 1500w,{%- endif -%}
                      {%- if block.settings.block_image.width >= 1780 -%}{{ block.settings.block_image | image_url: width: 1780 }} 1780w,{%- endif -%}
                      {%- if block.settings.block_image.width >= 2000 -%}{{ block.settings.block_image | image_url: width: 2000 }} 2000w,{%- endif -%}
                      {%- if block.settings.block_image.width >= 3000 -%}{{ block.settings.block_image | image_url: width: 3000 }} 3000w,{%- endif -%}
                      {%- if block.settings.block_image.width >= 3840 -%}{{ block.settings.block_image | image_url: width: 3840 }} 3840w,{%- endif -%}
                      {{ block.settings.block_image | image_url: width: 1500 }} {{ block.settings.block_image.width }}w"
                      sizes="100vw"
                      src="{{ block.settings.block_image | image_url: width: 1500 }}"
                      loading="lazy"              
                      alt="{{ block.settings.block_image.alt | escape }}"
                      width="{{ block.settings.block_image.width }}"
                      height="{{ block.settings.block_image.width | divided_by: block.settings.block_image.aspect_ratio | round }}"
                    >
            {% else %}
            {{ 'image' | placeholder_svg_tag: 'placeholder_svg' }}
            {% endif %}
          </a>
         </div>    
           <a class="icon-svg color-{{ section.settings.color_scheme }} gradient"  {% if block.settings.block_button_link == blank %} role="link" aria-disabled="true"{% else %} href="{{ block.settings.block_button_link }}"{% endif %}>  
            <div class="insta-gallery-content ">
              <div class="insta-gallery-inner banner--content-align-{{ section.settings.desktop_content_alignment }} ">
                      {% if block.settings.block_description != blank %}
                     <p class="description {% unless block.settings.show_content %}hidden {% endunless %}">{{ block.settings.block_description }}</p>
                    {% endif %}
                    {% if block.settings.block_sub_title != blank %}
                    <h6 class="sub-title {% unless block.settings.show_content %}hidden {% endunless %}">{{ block.settings.block_sub_title }}</h6>
                    {% endif %}  
                    {%- if block.settings.icon_class -%}
                     <div class="insta-icon"> {% render 'icon-instagram' %}</div>
                  {%- endif -%}   
              </div> 
             </div>  
            </a>  
          </div>  
         {% endfor %}   
     </div> 
        {% unless enable_slider %}
      {%- if show_mobile_slider -%}
        {% if section.settings.arrow_on_mobile %}
        <div class="slider-buttons no-js-hidden large-up-hide">
          <button type="button" class="slider-button slider-button--prev" name="previous" aria-label="{{ 'general.slider.previous_slide' | t }}">{% render 'icon-caret' %}</button>
          <div class="slider-counter caption">
          </div>
          <button type="button" class="slider-button slider-button--next" name="next" aria-label="{{ 'general.slider.next_slide' | t }}">{% render 'icon-caret' %}</button>
        </div>
      {% endif %}
        {%- endif -%}
      </slider-component>
      {% else %}
        {% if section.settings.swiper_navigation != blank %}
        <div class="swiper-button-next"><span></span></div>
        <div class="swiper-button-prev"><span></span></div>
        {% endif %}
    </div>
    {% if section.settings.swiper_pagination != blank %}
    <div class="swiper-pagination"></div>
    {% endif %}
    </div>
    </insta-slider>
    {%- endunless -%}
      
  </div>
  
</div>
</div>
</div>  
<!-- Script-Start -->

<script type="text/javascript">
  $(document).ready(function(){
    $( ".gridPlay" ).each(function( index ) {
      $(this).on('click', function(){
      $(this).css('display','none');
      $(this).next('.gridPause').css('display','flex');
      $(this).closest('.dt-sc-insta-gallery').find('video').trigger('play');
      });
    });
    $( ".gridPause" ).each(function( index ) {
      $(this).on('click', function(){
      $(this).css('display','none');
      $(this).prev('.gridPlay').css('display','flex');
      $(this).closest('.dt-sc-insta-gallery').find('video').trigger('pause');
      });
    });
  });
</script>

<!-- Script-End -->
{% schema %}
{
  "name": "t:sections.insta-gallery.name",
  "class": "section",
  "tag": "section",
  "settings": [
    {
      "type":"checkbox",
      "id":"page_full_width",
       "default": false,
      "label": "t:sections.all.page_full_width.label"
    },
    {
      "type":"checkbox",
      "id":"page_full_width_spacing",
       "default": false,
      "label": "t:sections.all.page_full_width_spacing.label"
    },
    {
      "type": "text",
      "id": "title",
      "default": "insta-gallery",
      "label": "t:sections.all.title.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__3.label"
        }
      ],
      "default": "h1",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "text",
      "id": "sub_heading",
      "default": "Sub Heading", 
      "label": "t:sections.all.sub_heading.label"
    },
     {
      "type": "text",
      "id": "description",
      "default": "Use This Text To Share The Information Which You Like!.",    
      "label": "t:sections.all.description.label"
    },
    {
      "type": "select",
      "id": "column_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.insta-gallery.settings.column_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.insta-gallery.settings.column_alignment.options__2.label"
        }
      ],
      "default": "center",
      "label": "t:sections.insta-gallery.settings.column_alignment.label"
    },    
    {
      "type": "text",
      "id": "button_label",
      "default": "Button label",
      "label": "t:sections.insta-gallery.settings.button_label.label"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "t:sections.insta-gallery.settings.button_link.label"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "t:sections.all.colors.accent_1.label"
        },
        {
          "value": "accent-2",
          "label": "t:sections.all.colors.accent_2.label"
        },
        {
          "value": "background-1",
          "label": "t:sections.all.colors.background_1.label"
        },
        {
          "value": "background-2",
          "label": "t:sections.all.colors.background_2.label"
        },
        {
          "value": "inverse",
          "label": "t:sections.all.colors.inverse.label"
        }
      ],
      "default": "background-1",
      "label": "t:sections.all.colors.label"
    },
      {
      "type": "header",
      "content": "t:sections.insta-gallery.settings.overlay_image_height_settings.content"
    },
    {
      "type": "range",
      "id": "overlay_height",
      "min": 0,
      "max": 1000,
      "step": 100,
      "unit": "px",
      "default": 400,
      "label": "t:sections.insta-gallery.settings.overlay_height.label"
   },
     {
      "type": "range",
      "id": "overlay_height_laptop",
      "min": 0,
      "max": 1000,
      "step": 100,
      "unit": "px",
      "default": 300,
      "label": "t:sections.insta-gallery.settings.overlay_height_laptop.label"
    },
     {
      "type": "range",
      "id": "overlay_height_tab",
       "min": 0,
      "max": 1000,
      "step": 100,
      "unit": "px",
      "default": 300,
      "label": "t:sections.insta-gallery.settings.overlay_height_tab.label"
    },
     {
      "type": "range",
      "id": "overlay_height_mobile",
      "min": 0,
      "max": 1000,
      "step": 100,
      "unit": "px",
      "default": 200,
      "label": "t:sections.insta-gallery.settings.overlay_height_mobile.label"
    },
    {
      "type": "select",
      "id": "desktop_content_alignment",
      "options": [
       {
          "value": "left",
          "label": "t:sections.insta-gallery.settings.desktop_content_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.insta-gallery.settings.desktop_content_alignment.options__2.label"
        },
        {
          "value": "right",
          "label": "t:sections.insta-gallery.settings.desktop_content_alignment.options__3.label"
        }
      ],
      "default": "center",
      "label": "t:sections.insta-gallery.settings.desktop_content_alignment.label"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
        {
      "type": "header",
      "content": "t:sections.all.swiper.swiper_slider_title"
    },
    {
      "type": "checkbox",
      "id": "swiper_enable",
      "default": false,
      "label": "t:sections.all.swiper.swiper_slider_enable"
    },
    {
      "type": "range",
      "id": "desktop_column",
      "min": 1,
      "max": 10,
      "step": 1,
      "label": "t:sections.all.swiper.desktop_column",
      "default": 4
    },
    {
      "type": "range",
      "id": "laptop_column",
      "min": 1,
      "max": 10,
      "step": 1,
      "label": "t:sections.all.swiper.laptop_column",
      "default": 4
    },
    {
      "type": "range",
      "id": "tablet_column",
      "min": 1,
      "max": 5,
      "step": 1,
      "label": "t:sections.all.swiper.tablet_column",
      "default": 3
    },
    {
      "type": "range",
      "id": "mobile_column",
      "min": 1,
      "max": 3,
      "step": 1,
      "label": "t:sections.all.swiper.mobile_column",
      "default": 1
    },
    {
      "type": "checkbox",
      "id": "centered_slide",
      "default": false,
      "label": "t:sections.all.swiper.centered_slide"
    },
    {
      "type": "checkbox",
      "id": "swiper_pagination",
      "default": false,
      "label": "t:sections.all.swiper.swiper_pagination"
    },
    {
      "type": "checkbox",
      "id": "swiper_navigation",
      "default": false,
      "label": "t:sections.all.swiper.swiper_navigation"
    },
    {
      "type": "range",
      "id": "auto_play",
      "min": 0,
      "max": 5,
      "step": 1,
      "label": "t:sections.all.swiper.auto_play",
      "default": 0
    },
    {
      "type": "checkbox",
      "id": "swipe_on_mobile",
      "default": false,
      "label": "t:sections.multicolumn.settings.swipe_on_mobile.label"
    },
    {
      "type": "checkbox",
      "id": "arrow_on_mobile",
      "default": false,
      "label": "t:sections.multicolumn.settings.swipe_on_mobile.controls"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "options": [
        {
          "value": "1",
          "label": "t:sections.multicolumn.settings.columns_mobile.options__1.label"
        },
        {
          "value": "2",
          "label": "t:sections.multicolumn.settings.columns_mobile.options__2.label"
        }
      ],
      "default": "1",
      "label": "t:sections.multicolumn.settings.columns_mobile.label"
    },
    {
    "type": "header",
    "content": "t:sections.all.custom_class_heading.content"
    },
    {
    "type": "text",
    "id": "custom_class_name",
    "label": "t:sections.all.custom_class_name.label"
    }
  ],
  "blocks": [
    {
    "type": "text",
    "name": "t:sections.insta-gallery.blocks.text.name",
    "settings": [
    {
    "type": "image_picker",
    "id": "block_image",
    "label": "t:sections.insta-gallery.blocks.text.settings.block_image.label",
    "info": "Size: 1280x820 [Act as poster image for video type]"
    },
    {
    "type": "checkbox",
    "id": "show_content",
    "label": "t:sections.insta-gallery.blocks.text.settings.show_content.label",
    "default": true
    },
    {
    "type": "checkbox",
    "id": "icon_class",
    "default": true,
    "label": "t:sections.insta-gallery.blocks.text.settings.icon_class.label"
    },  
    {
    "type": "text",
    "id": "block_sub_title",
    "label": "t:sections.insta-gallery.blocks.text.settings.block_sub_title.label",
    "default": "Sub title"
    },
    {
    "type": "text",
    "id": "block_description",
    "label": "t:sections.insta-gallery.blocks.text.settings.block_description.label",
    "default": "Short description"
    },
    {
    "type": "url",
    "id": "block_button_link",
    "label": "t:sections.insta-gallery.blocks.text.settings.block_button_link.label"
    }
    ]
    }
    ],
    "presets": [
    {
      "name": "t:sections.insta-gallery.presets.name",
        "blocks": [
        {
          "type": "text"
        },
        {
          "type": "text"
        },
        {
          "type": "text"
        },
        {
          "type": "text"
        }  
      ]
    }
  ]
}

{% endschema %}        