{%- style -%}
.section-{{ section.id }}-padding {
  padding-top: calc({{ section.settings.padding_top }}px * 0.75);
  padding-bottom: calc({{ section.settings.padding_bottom }}px  * 0.75);
}

@media screen and (min-width: 750px) {
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top }}px;
    padding-bottom: {{ section.settings.padding_bottom }}px;
  }
}
.contact-layout .layout-contact-right{
  height:600px;
}
{%- endstyle -%}

<div class="color-{{ section.settings.color_scheme }} gradient">
 <div class="{% if section.settings.page_full_width %} page-full-width {% else %} page-width {% endif %} {% if section.settings.page_full_width_spacing %} page-full-width_spacing {% endif %}section-{{ section.id }}-padding isolate">
    <div class="row">
  <div class="section-{{ section.id }}">
  <div class="contact-layout">
     <div class="layout-contact-left">
          <div class="collapsible_address-block">
            {% if section.settings.address_heading != blank %}
            <h3 class="address-block-heading h4">{{ section.settings.address_heading}}</h3>
            {% endif %}  
            {% if section.settings.address_desc != blank %}
            <p class="address-block-desc">{{ section.settings.address_desc}}</p>
            {% endif %}  
            <ul class=" list-unstyled">
            {% if section.settings.collapsible_address != blank %}
            <li class="address"> 
            {%- render 'icon-location' -%}
            <address>{{ section.settings.collapsible_address }}</address>
            </li>
            {% endif %}  
            {% if section.settings.collapsible_contact_id != blank %}
            <li class="office-mail">
            <a href="mailto:{{ section.settings.collapsible_contact_id}}" class="link">
            {%- render 'icon-mail' -%}<span>{{ section.settings.collapsible_contact_id }}</span></a>     
            </li>
            {% endif %}
            {% if section.settings.collapsible_contact_no != blank %}
            <li class="contact-phone">    
            <a href="tel:{{ section.settings.collapsible_contact_no }}" class="link">{%- render 'icon-phone' -%}{{ section.settings.collapsible_contact_no }}</a>    
            </li>
            {% endif %}
            </ul>
          </div>
     </div>
        <div class="layout-contact-right">
        {{ section.settings.custom_liquid }}
        </div>
  </div>
 </div>
</div>
</div>
</div>
{% schema %}
{
  "name": "Custom liquid",
  "tag": "section",
  "class": "section section-custom-liquid",
  "settings": [
    {
      "type":"checkbox",
      "id":"page_full_width",
       "default": false,
      "label": "t:sections.all.page_full_width.label"
    },
    {
      "type":"checkbox",
      "id":"page_full_width_spacing",
       "default": false,
      "label": "t:sections.all.page_full_width_spacing.label"
    },
    {
      "type": "liquid",
      "id": "custom_liquid",
      "label": "t:sections.custom-liquid.settings.custom_liquid.label",
      "info": "t:sections.custom-liquid.settings.custom_liquid.info"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "t:sections.all.colors.accent_1.label"
        },
        {
          "value": "accent-2",
          "label": "t:sections.all.colors.accent_2.label"
        },
        {
          "value": "background-1",
          "label": "t:sections.all.colors.background_1.label"
        },
        {
          "value": "background-2",
          "label": "t:sections.all.colors.background_2.label"
        },
        {
          "value": "inverse",
          "label": "t:sections.all.colors.inverse.label"
        }
      ],
      "default": "background-1",
      "label": "t:sections.all.colors.label"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 40
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 52
    },
    {
          "type": "text",
          "id": "address_heading",
          "default": "Heading",
          "label": "t:sections.custom-liquid.settings.address_heading.label"
        },
      {
          "type": "text",
          "id": "address_desc",
          "default": "Description",
          "label": "t:sections.custom-liquid.settings.address_desc.label"
        },
    
        {
        "type": "textarea",
        "id": "collapsible_address",
        "label": "t:sections.custom-liquid.settings.collapsible_address.label"
        },
        {
        "type": "text",
        "id": "collapsible_contact_no",
        "label": "t:sections.custom-liquid.settings.collapsible_contact_no.label"
        },
         {
        "type": "text",
        "id": "collapsible_contact_id",
        "label": "t:sections.custom-liquid.settings.collapsible_contact_id.label"
        }


  ],
  "presets": [
    {
      "name": "Custom liquid"
    }
  ]
}
{% endschema %}
