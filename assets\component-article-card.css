/* @media screen and (max-width: 749px) {
  .articles-wrapper .article {
    width: 100%;
  }
} */
/* .card__content .circle-divider::before {
    content: '|';
    margin: 0 1.3rem 0 1.5rem;
} */
/* .article {
  display: flex;
  align-items: center;
} */

.article.grid__item {
  padding: 0;
}

.grid--peek .article-card {
  box-sizing: border-box;
}

.article-card__image-wrapper > a {
  display: block;
}

.article-card__title {
  text-decoration: none;
  word-break: break-word;
}

.article-card__title a:after {
  bottom: 0;
  content: "";
  height: 100%;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  width: 100%;
  z-index: 1;
}

.article-card__link.link {
  padding: 0;
}

.article-card__link {
  text-underline-offset: 0.3rem;
}

.article-card .card__heading {
 
    font-size: 3rem;
}
.article-card a.blog__button.button{ margin-bottom:0;min-height: 30px;
    width: 30px;
    font-size: 1.2rem;
}
.blog .card .card__content{padding:20px 0 0;}
.blog .blog-list-style .card.article-card .card__content{padding:20px;}
/* .article-card  a.blog__button.button{margin:0;} */
.blog-articles .article-card .card__information,
.blog__posts .article-card .card__information {
  padding-left: 0;
  padding-right: 0;
  text-align:left;
}

.article-card__info {
  padding-top: 0rem;
  font-size: 1.2rem;
  letter-spacing: 0;
  line-height: 25px;
  text-transform: capitalize;
  font-weight: 500;
  margin-bottom: 0;
  color: rgb(var(--color-foreground));
  transition: all 0.3s linear;
/*   white-space: nowrap; */
}

.article-card__footer {
  letter-spacing: 0.1rem;
  font-size: 1.4rem;
}

.article-card__footer:not(:last-child) {
  margin-bottom: 1rem;
}

.article-card__footer:last-child {
  margin-top: auto;
}

.article-card__excerpt {
  width: 100%;
  font-size: 1.8rem;
  letter-spacing: 0;
  transition:all 0.3s linear;
}

.article-card__link:not(:only-child) {
  margin-right: 3rem;
}

@media screen and (min-width: 990px) {
  .article-card__link:not(:only-child) {
    margin-right: 4rem;
  }
}

.article-card__image--small .ratio::before {
  padding-bottom: 11rem;
}

.article-card__image--medium .ratio::before {
  padding-bottom: 22rem;
}

.article-card__image--large .ratio::before {
  padding-bottom: 33rem;
}

@media screen and (min-width: 750px) {
  .article-card__image--small .ratio::before {
    padding-bottom: 14.3rem;
  }

  .article-card__image--medium .ratio::before {
    padding-bottom: 21.9rem;
  }

  .article-card__image--large .ratio::before {
    padding-bottom: 27.5rem;
  }
}

@media screen and (min-width: 990px) {
  .article-card__image--small .ratio::before {
    padding-bottom: 17.7rem;
  }

  .article-card__image--medium .ratio::before {
    padding-bottom: 30.7rem;
  }

  .article-card__image--large .ratio::before {
    padding-bottom: 40.7rem;
  }
}
.article-card .card__inner  a{width:100%}
.article-card .card__information h3.card__heading {font-size: 2.2rem;margin-bottom: 10px;}
.article-card__footer span{display: flex;align-items: center;}
.article-card__footer span svg{margin-right:5px;}
.article__image{height:100%;}
.comment-icon svg { width: 1.6rem; height: 1.6rem;}
.article-author svg { width: 1.6rem; height: 1.6rem;}

@media screen and (max-width: 749px) {
  .article-card .card__information h3.card__heading { font-size: 1.8rem;}
  .article-card__excerpt{font-size:1.6rem;}
}
.article-card .card__information> *:not(:last-child){margin-bottom:15px;}
/* check for flexbox gap in older Safari versions */
@supports not (inset: 10px) {
  .articles-wrapper.grid {
    margin: 0 0 5rem 0;
  }

  @media screen and (min-width: 750px) {
    .articles-wrapper.grid {
      margin-bottom: 7rem;
    }
  }
}
/*list*/
.blog__post.article.grid__item.blog-list-style .card__content {padding:20px; align-items: center; justify-content: center; display: flex;}
/*overlay*/
.blog__posts.articles-wrapper .article.blog-overlay-style .card__information{
      left: 20px;
    right: 20px;
    top: 20px;
    bottom: 20px;
    margin: auto;
    position: absolute;
    display: flex;
    flex-direction: column;
    justify-content: center;
}
.blog__posts.articles-wrapper .article.blog-overlay-style  .blog__button.button{width:fit-content;}
.blog__post.article.grid__item.blog-overlay-style .card__content{padding:0;}
.article-card .article-card__image{transition:all 0.3s linear;}
.article-card:hover .article-card__image {
    transform: scale(1.12);
    -webkit-transform: scale(1.12);
}
