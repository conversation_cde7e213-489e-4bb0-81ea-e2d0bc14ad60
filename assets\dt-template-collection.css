@media screen and (max-width: 749px) {
.lush-product-tab .collection .grid__item:only-child {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

@media screen and (max-width: 989px) {
.lush-product-tab .collection .slider.slider--tablet {
    margin-bottom: 1.5rem;
  }
}

.lush-product-tab .collection .loading-overlay {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: none;
  width: 100%;
  padding: 0 1.5rem;
  opacity: 0.7;
}

@media screen and (min-width: 750px) {
.lush-product-tab .collection .loading-overlay {
    padding-left: 5rem;
    padding-right: 5rem;
  }

}

.lush-product-tab .collection--empty .title-wrapper .title--primary {
  font-size: calc(var(--font-heading-scale) * 2rem);
  line-height: normal;
}

.lush-product-tab .collection.loading .loading-overlay {
  display: block;
}

.lush-product-tab .collection--empty .title-wrapper {
  margin-top: 10rem;
  margin-bottom: 15rem;
}

@media screen and (max-width: 989px) {
.lush-product-tab .collection .slider--tablet.product-grid {
    scroll-padding-left: 1.5rem;
  }
}

.lush-product-tab .collection__description>* {
  margin: 0;
}

.lush-product-tab .collection__title.title-wrapper {
  margin-bottom: 2.5rem;
  text-align: center;
}

.lush-product-tab .collection__title .title:not(:only-child) {
  margin-bottom: 5rem;
}

.lush-product-tab .collection__title.title-wrapper h2.title {
  color: var(--color-icon);
}

@media screen and (min-width: 990px) {
.lush-product-tab .collection__title--desktop-slider .title {
    margin-bottom: 2.5rem;
  }

.lush-product-tab .collection__title.title-wrapper--self-padded-tablet-down {
    padding: 0 5rem;
  }

.lush-product-tab .collection slider-component:not(.page-width-desktop) {
    padding: 0;
  }

.lush-product-tab .collection--full-width slider-component:not(.slider-component-desktop) {
    padding: 0 1.5rem;
    max-width: none;
  }
}

.lush-product-tab .collection__view-all a:not(.link) {
  margin-top: 1rem;
}

.lush-product-tab .facets-vertical.sidebar-right {
  flex-direction: row-reverse;
}

.lush-product-tab .facets-vertical.no-sidebar aside.facets-wrapper {
  display: none;
}

.lush-product-tab facet-filters-form.facets.facets-vertical-sort {
  margin-bottom: 70px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  border-radius: 0px;
  padding: 0 50px;
  background: var(--gradient-base-accent-2);
}

.lush-product-tab .filter-style .filter-buttons {
  padding: 0;
  margin: 15px 0;
  display: flex;
  background: var(--gradient-base-background-1);
  border: 1px solid var(--gradient-base-background-2);
  overflow: hidden;
  border-radius: 0px;
  cursor: pointer;
}

.lush-product-tab .filter-style .filter-buttons .grid-view-button {
  padding: 10px;
  position: relative;
  align-items: center;
  justify-content: center;
  display: flex;
}

.lush-product-tab .filter-style .filter-buttons .list-view-button {
  padding: 10px;
  position: relative;
  align-items: center;
  justify-content: center;
  display: flex;
}
}

.lush-product-tab .filter-style .filter-buttons .layout-mode:after {
  content: "";
  position: absolute;
  width: 0;
  top: 0;
  bottom: 0;
  z-index: 0;
  background-color: var(--gradient-base-accent-2);
  transition: all 0.3s linear;
}

.lush-product-tab .filter-style .filter-buttons .layout-mode.active,
.lush-product-tab .filter-style .filter-buttons .layout-mode.active:hover {
  color: var(--gradient-base-background-2);
  background: var(--gradient-base-accent-3);
}

.lush-product-tab filter-style .filter-buttons .layout-mode.active:after {
  width: 100%;
}

ul.grid.product-grid.view-mode.grid--2-col-desktop.grid--2-col-tablet-down.list-view-filter {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
}

.lush-product-tab .list-view-filter .card.card--standard.card--media {
  display: grid;
  grid-template-columns: 2fr 2.5fr;
}

.lush-product-tab .list-view-filter .card.card--standard.card--media .card__inner {
  float: left;
  width: 100%;
  border-radius: var(--DTRadius);
  position: relative;
  overflow: hidden;
}

.lush-product-tab .list-view-filter .card.card--standard.card--media .card__content {
  align-items: flex-start;
  padding: 20px;
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  justify-content: center;
}

.lush-product-tab .list-view-filter .card.card--standard.card--media .card__inner .card__content {
  padding: 0;
}

.lush-product-tab .list-view-filter .card.card--standard.card--media .card__content .variant-option-color {
  display: flex;
  justify-content: flex-start;
  padding: 0;
  margin-top: 0.7rem;
}

.lush-product-tab .list-view-filter .card--standard>.card__content .card__information h3.card__heading {
  text-align: left;
}

.lush-product-tab .list-view-filter .card.card--standard.card--media .card__content .card-information {
  text-align: left;
}

.lush-product-tab .list-view-filter .card.card--standard.card--media .card__information {
  padding-bottom: 0;
  padding-top: 0;
}

.lush-product-tab .list-view-filter .card.card--standard.card--media .card__inner .card__content .product-icons {
  flex-direction: row;
  align-items: center;
  right: 0;
  left: 0;
  margin: auto;
  top: unset;
}

.lush-product-tab ul.grid.product-grid.view-mode.grid--2-col-desktop.grid--2-col-tablet-down.list-view-filter .grid__item {
  width: 100%;
  max-width: 100%;
}

@media screen and (min-width: 750px) and (max-width: 989px) {
  .lush-product-tab ul.grid.product-grid.view-mode.grid--2-col-desktop.grid--2-col-tablet-down.grid-view-filter .grid__item {
    width: 100%;
    max-width: 100%;
  }

  .lush-product-tab ul.grid.product-grid.view-mode.grid--2-col-desktop.grid--2-col-tablet-down.list-view-filter .grid__item {
    width: 100%;
    max-width: 100%;
  }

.lush-product-tab .sidebar-left .grid--2-col-tablet-down .grid__item,
.lush-product-tab .sidebar-right .grid--2-col-tablet-down .grid__item {
    width: 100%;
    max-width: 100%;
  }
}

@media screen and (min-width: 990px) and (max-width: 1199px) {

.lush-product-tab .sidebar-left .grid--2-col-tablet-down .grid__item,
.lush-product-tab .sidebar-right .grid--2-col-tablet-down .grid__item {
    width: calc(50% - var(--grid-desktop-horizontal-spacing) * 2 / 4);
    max-width: calc(50% - var(--grid-desktop-horizontal-spacing) * 2 / 4);
  }
}

@media screen and (max-width: 1199px) {
.lush-product-tab .grid--4-col-desktop .grid__item {
    width: calc(33.33% - var(--grid-desktop-horizontal-spacing) * 3 / 4);
    max-width: calc(33.33% - var(--grid-desktop-horizontal-spacing) * 2 / 4);
  }
}

@media screen and (max-width: 768px) {
.lush-product-tab .grid--4-col-desktop .grid__item {
    width: calc(50% - var(--grid-desktop-horizontal-spacing) * 3 / 4);
    max-width: calc(50% - var(--grid-desktop-horizontal-spacing) * 1 / 4);
  }
}

@media screen and (min-width: 750px) and (max-width: 1199px) {

.lush-product-tab .grid--3-col-desktop.list-view-filter .grid__item {
    width: calc(100% - var(--grid-desktop-horizontal-spacing) * 2 / 2);
    max-width: calc(100% - var(--grid-desktop-horizontal-spacing) * 2 / 2);
  }

}

@media screen and (min-width: 1200px) and (max-width: 1440px) {
.lush-product-tab .grid--3-col-desktop.list-view-filter .grid__item {
    width: calc(50% - var(--grid-desktop-horizontal-spacing) * 2 / 2);
    max-width: calc(50% - var(--grid-desktop-horizontal-spacing) * 2 / 2);
  }
}

@media screen and (max-width: 1400px) and (min-width: 1201px) {
  .lush-product-tab facet-filters-form.facets.facets-vertical-sort.vertical {
    justify-content: center;
  }

.lush-product-tab .custom-product-grid {
    margin: 0 0 0rem;
  }
}

@media screen and (max-width: 1200px) {
.lush-product-tab .custom-product-grid {
    display: none !important;
  }

  .lush-product-tab facet-filters-form.facets.facets-vertical-sort.vertical {
    padding: 1rem;
    justify-content: center;
  }

}


/*List view*/
.lush-product-tab .list-view-filter .card-wrapper .card {
  display: grid;
  grid-template-columns: 1fr 2fr;
}

.lush-product-tab .list-view-filter .card-wrapper .card .card__content {
  align-items: center;
  padding: 30px;
  display: flex;
  flex-wrap: wrap;
  text-align: left;
  flex-direction: column;
  justify-content: center;
}

.lush-product-tab .list-view-filter .card-wrapper .card__content [class*=variant-option] {
  padding: 0;
}

.lush-product-tab .list-view-filter .card-wrapper .card .product-icons li {
  margin: 3px;
}

.lush-product-tab .list-view-filter .card-wrapper .card .card__content .card__information {
  padding: 0;
  text-align: left;
}

.lush-product-tab .list-view-filter .card-wrapper .card .card__content .card__information .card-information {
  text-align: left;
}

.lush-product-tab .list-view-filter .card-wrapper .card span.price-item.price-item--sale.price-item--last {
  margin: 0 1rem 0 0;
}

.lush-product-tab .list-view-filter .card__information .card__heading {
  text-align: left;
  margin-bottom: 1rem;
}

.lush-product-tab .list-view-filter .card-information.new--tag {
  text-align: left;
  margin-bottom: 1rem;
}

.lush-product-tab .list-view-filter .card__content .variant-option-size {
  justify-content: left;
  margin-bottom: 1rem;
}

.lush-product-tab .list-view-filter .card__content .variant-option-color {
  margin-bottom: 1rem;
  justify-content: flex-start;
}

.lush-product-tab .list-view-filter .caption-with-letter-spacing.light {
  margin-bottom: 1rem;
}

.lush-product-tab .list-view-filter li.grid__item {
  width: 100%;
  max-width: 100%;
}

@media screen and (max-width: 1540px) {
  .lush-product-tab ul.grid.product-grid.view-mode.grid--3-col-desktop.grid--2-col-tablet-down.list-view-filter .grid__item {
    width: 100%;
    max-width: 100%;
  }

.lush-product-tab .home-featured-collection-1 .product-grid .card .card__content {
    text-align: center;
    justify-content: center;
  }
}

@media screen and (max-width: 1439px) and (min-width: 1200px) {
.lush-product-tab .product-grid-container .product-grid li.grid__item {
    width: calc(33.3% - var(--grid-desktop-horizontal-spacing) * 2 / 3);
    max-width: calc(33.3% - var(--grid-desktop-horizontal-spacing) * 2 / 3);
  }
}

@media screen and (max-width: 1640px) and (min-width: 1201px) {

.lush-product-tab .list-view-filter .card-wrapper .card span.price-item.price-item--sale.price-item--last {
    margin: 0 1rem 0 0;
  }
}

@media screen and (max-width: 1200px) and (min-width: 991px) {
.lush-product-tab .list-view-filter .card-wrapper .card span.price-item.price-item--sale.price-item--last {
    margin: 0 1rem 0 0;
  }
}

@media screen and (max-width: 990px) and (min-width: 750px) {
.lush-product-tab .list-view-filter .card-wrapper .card {
    grid-template-columns: 1fr 1fr;
  }

.lush-product-tab .list-view-filter .card-wrapper .card .card__content {
    padding: 20px;
  }
}

@media screen and (max-width: 767px) {
.lush-product-tab .list-view-filter .product-icons.right-aligned li:hover tooltip.tooltip {
    opacity: 0;
  }
}

@media screen and (max-width: 480px) {
.lush-product-tab .list-view-filter .card-wrapper .card {
    grid-template-columns: 1.5fr 2fr;
  }

.lush-product-tab .list-view-filter .card-wrapper .quick-add__submit.button {
    letter-spacing: 0;
    padding: 0;
    font-size: 1.2rem;
    display: block;
    min-width: 100%;
  }

.lush-product-tab .list-view-filter .card-wrapper .card__inner .quick-add.button-quick-add {
    left: 0;
    width: calc(100% - 20px);
    right: 0;
    margin: 0 auto;
  }

.lush-product-tab .list-view-filter .card-wrapper .card .card__content {
    padding: 20px;
  }
}

.lush-product-tab .progress-bar {
  width: 100%;
  height: 10px;
  border-radius: 10px;
  max-width: 300px;
  margin: 15px auto;
  background-color: var(--gradient-base-accent-2);
  position: relative;
  overflow: hidden;
}

.lush-product-tab .custom-page-progress-bar .progress-bar .active-bar {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  background-color: var(--gradient-base-background-2);
}

.lush-product-tab .custom-page-progress-bar .progress-bar .active-bar:after {
  content: '';
  position: absolute;
}

.lush-product-tab .custom-page-progress-bar {
  text-align: center;
}

.lush-product-tab .load_more_btn {
  display: flex;
  justify-content: center;
  margin: 1rem 0;
}

.lush-product-tab .pagination-method-loadmore_btn {
  display: flex;
  flex-direction: column;
}

.lush-product-tab .progress-bar div:empty {
  display: block;
}

.lush-product-tab .mobile-facets__inner .optional-sidebar {
  width: 100%;
}

@media screen and (max-width: 576px) {
.lush-product-tab .grid--1-col-mobile .grid__item {
    width: 100%;
    max-width: 100%;
  }
}

.lush-product-tab .mobile-facets__inner #accordian a {
  margin-left: 30px;
}


.lush-product-tab facet-filters-form .optional-sidebar.small-hide {
  display: block !important;
  padding: 0 25px;

}


/* custom css */
.lush-product-tab .home-featured-collection-1 .product-grid .card .card__content {
  text-align: left;
  justify-content: left;
}

.lush-product-tab .home-featured-collection-1 .product-grid .card .card__content .card__information .caption-with-letter-spacing.light {
  font-size: 1.4rem;
  letter-spacing: 0;
  font-weight: 400;
}

.lush-product-tab .card .card__content .product-icons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  justify-items: center;
  align-items: center;
}

@media screen and (min-width:1540px) {
.lush-product-tab .home-featured-collection-1 .product-grid .grid__item {
    width: 100%;
    max-width: 100%;
  }

.lush-product-tab .home-featured-collection-1 .product-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
  }

.lush-product-tab .home-featured-collection-1 .product-grid .grid__item:not(:nth-child(1)) .card {
    flex-direction: row;
    align-items: center;
  }

.lush-product-tab .home-featured-collection-1 .product-grid .grid__item:nth-child(1) {
    grid-column: 3/5;
    grid-row: 4/-1;
  }

.lush-product-tab .home-featured-collection-1 .product-grid .grid__item:nth-child(3) {
    order: -1;
  }

.lush-product-tab .home-featured-collection-1 .product-grid .grid__item:nth-child(2) {
    order: -1;
  }

.lush-product-tab .home-featured-collection-1 .product-grid .grid__item:nth-child(1) .card-wrapper .card .card__inner {
    height: 850px;
  }

.lush-product-tab .home-featured-collection-1 .product-grid .grid__item:nth-child(1) .card-wrapper .card .card__content {
    justify-content: left;
    padding: 30px;
    border-top: 1px solid var(--gradient-base-accent-2);
  }

}

@media screen and (max-width:750px) {
.lush-product-tab .collection .home-featured-collection-1 .slider-mobile-gutter .product-grid .grid__item {
    width: calc(100% - var(--grid-desktop-horizontal-spacing) * 0 / 1);
    max-width: calc(100% - var(--grid-desktop-horizontal-spacing) * 0 / 1);
  }
}