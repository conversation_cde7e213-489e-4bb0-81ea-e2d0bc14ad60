{%- capture styles -%}
  {%- style -%}
  {{ section.settings.type_header_font  | font_face: font_display: 'swap' }}
  
    #shopify-section-{{ section.id }} .button:after {
      display: none;
    }
    #shopify-section-{{ section.id }} .otsb-btn__sliced.otsb-button-text-link .otsb-button-text {
      transform: translate(0px);
    }
    #shopify-section-{{ section.id }} .highlight.hl-font
    {
      color: var(--color-highlight);
    }
    #shopify-section-{{ section.id }} .hl-underline.highlight-anm-start {
      transition: color .3scubic-bezier(0.06, 0.14, 0.8, 0.91);
      transition-delay: 0.4s;
      color: var(--color-highlight);
    }
    #shopify-section-{{ section.id }} .highlight-anm-start .svg-underline path, 
    #shopify-section-{{ section.id }} .highlight-anm-start .svg-circle path {
      stroke-dashoffset: 0;
    }
    #shopify-section-{{ section.id }} .svg-underline path {
      stroke-dasharray: 1000;
      stroke-dashoffset: 1000;
      transition: stroke-dashoffset 1.5s ease-in-out;
  }
    #shopify-section-{{ section.id }} .highlight {
      {% if section.settings.color_heading_highlight != blank and section.settings.color_heading_highlight.alpha != 0.0 %}
        --color-highlight: {{ section.settings.color_heading_highlight }};
      {% elsif color_text.alpha != 0.0 and color_text != blank  %}
        --color-highlight: {{ section.settings.text }};
      {% else %}
      --color-highlight: rgba(var(--color-foreground));
      {% endif %}
      color: var(--color-highlight);
    }
    #shopify-section-{{ section.id }} .divider {
      border-color: rgba(var(--color-foreground), 0.75);
    }
    #shopify-section-{{ section.id }} .otsb-button-link {
      text-decoration: underline;
      {% if section.settings.text_link.alpha != 0.0 %}
        color: {{ section.settings.text_link }};
      {% endif %}
    }
    #shopify-section-{{ section.id }} {
      {% if section.settings.background_color.alpha != 0.0 %}
        background: {{ section.settings.background_color }};
        --background-color: {{ section.settings.background_color.red }}, {{ section.settings.background_color.green }}, {{ section.settings.background_color.blue }};
      {% else %}
        --background-color: var(--color-background);
        background: rgb(var(--color-background));
      {% endif %}
      {% if section.settings.text_link.alpha != 0.0 %}
        --colors-text-link: {{ section.settings.text_link.red }},{{ section.settings.text_link.green }},{{ section.settings.text_link.blue }};
        {% else %}
        --colors-text-link: var(--color-link);
      {% endif %}
      {% if section.settings.text.alpha != 0.0 %}
      --image-treatment-text: {{ section.settings.text.red }},{{ section.settings.text.green }},{{ section.settings.text.blue }};
      {% else %}
      --image-treatment-text: var(--color-foreground);
      {% endif %}
    }
    #shopify-section-{{ section.id }} .button-link {
      color: rgba(var(--colors-text-link));
    }
    #shopify-section-{{ section.id }} .image-treatment-text {
      color: rgba(var(--image-treatment-text));
    }
    
    #shopify-section-{{ section.id }} {
      {% if section.settings.text.alpha != 0.0 %}
        --colors-text: {{ section.settings.text.red }},{{ section.settings.text.green }},{{ section.settings.text.blue }};
      {% endif %}
      {% if section.settings.heading.alpha != 0.0 %}
        --colors-heading: {{ section.settings.heading.red }},{{ section.settings.heading.green }},{{ section.settings.heading.blue }};
      {% else %}
        --colors-heading: var(--color-foreground);
      {% endif %}
    }
    #shopify-section-{{ section.id }} .otsb-button.otsb-button-solid,
    #shopify-section-{{ section.id }} .otsb-button.otsb-button-solid {
      color: rgb(var(--colors-button-text));
      background-color: rgba(var(--colors-button));
      line-height: revert;
    }
    @media (max-width: 1024px) {
      #shopify-section-{{ section.id }} .otsb-btn__sliced .otsb-button-text {
        transform: translate(0px);
      }
      #shopify-section-{{ section.id }} .otsb-btn__sliced .otsb-button-icon {
        display: none;
      }
    }
  
  {%- endstyle -%}
  {%- endcapture -%}
  {% comment %} {%- assign before =  styles.size -%} {% endcomment %}
  {%- assign styles =  styles | strip_newlines | split: " " | join: " " | split: "*/" -%}
  {%- assign minified_style = "" -%}
  {%- for word in styles -%}
    {%- assign new_word = word | split: "/*" | first | strip | replace: "; ", ";" | replace: "} ", "}" | replace: "{ ", "{" | replace: " {", "{" -%}
      {%- assign minified_style = minified_style | append: new_word -%}
  {%- endfor -%}
  {% comment %} /* CSS minifed: {{ before }} --> {{ minified_style.size }} */ {% endcomment %}
  {{- minified_style  }}
  {% if request.design_mode %}
    <style>
      .otsb_nope {
        display: none !important;
        height: 0 !important;
        overflow: hidden !important;
        visibility: hidden !important;
        width: 0 !important;
        opacity: 0 !important;
      }
      ._otsb_warning {
        position: relative;
        box-shadow: 0rem 0.1875rem 0.0625rem -0.0625rem rgba(26, 26, 26, 0.07);
        border-radius: 1rem;
      }
      ._otsb_warning::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 0;
        box-shadow: 0.0625rem 0rem 0rem 0rem rgba(0, 0, 0, 0.13) inset, -0.0625rem 0rem 0rem 0rem rgba(0, 0, 0, 0.13) inset, 0rem -0.0625rem 0rem 0rem rgba(0, 0, 0, 0.17) inset, 0rem 0.0625rem 0rem 0rem rgba(204, 204, 204, 0.5) inset;
        border-radius: 1rem;
        pointer-events: none;
        mix-blend-mode: luminosity;
      }
      .otsb_warning_root {
        margin-top:36px;
        margin-bottom:36px;
      }
      .otsb_warning_root ._otsb_warning_1 {border-top-left-radius:1rem;border-top-right-radius:1rem;border:1px solid #fcaf0a;background:#fcb827;padding:1rem}
      .otsb_warning_root ._otsb_warning_2 {align-items:center;gap:8px;display:flex;flex-direction:row;flex-wrap:nowrap;justify-content:space-between}
      .otsb_warning_root ._otsb_warning_3 {display:flex;gap:4px;flex-direction:row;flex-wrap:nowrap;justify-content:space-between}
      .otsb_warning_root ._otsb_warning ._otsb_warning__icon {display:block;height:20px;width:20px;max-height:100%;max-width:100%;margin:auto}
      .otsb_warning_root h2 {overflow-wrap:anywhere;word-break:normal;font-size:100%;font-weight:650;line-height:1.25;color:rgb(37,26,0)}
      .otsb_warning_root * {
        margin: 0;
        padding: 0;
        font-family: var(--font-body-family);
        line-height: 1.375;
      }
      .otsb_warning_root ul {
        list-style-type: disc;
      }
      .otsb_warning_root a {
        color: rgb(0, 0, 238);
        text-decoration: underline;
      }
      .otsb_warning_root .otsb_warning_message_container {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        padding:1rem;
        color:rgb(37,26,0);
      }
      .otsb_warning_root .otsb_warning_message_container ul {
        padding-inline-start:3rem;
      }
    </style>
    <div x-data="otsb_script_require" class="page-width otsb_warning_root">
      <div class="_otsb_warning">
        <div class="_otsb_warning_1">
          <div class="_otsb_warning_2">
            <div class="_otsb_warning_3">
              <span class="_otsb_warning__icon">
                <svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path d="M10 6.75a.75.75 0 0 1 .75.75v3.5a.75.75 0 1 1-1.5 0v-3.5a.75.75 0 0 1 .75-.75Z"></path><path d="M11 13.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"></path><path fill-rule="evenodd" d="M10 3.5c-1.045 0-1.784.702-2.152 1.447a449.26 449.26 0 0 1-2.005 3.847l-.028.052a403.426 403.426 0 0 0-2.008 3.856c-.372.752-.478 1.75.093 2.614.57.863 1.542 1.184 2.464 1.184h7.272c.922 0 1.895-.32 2.464-1.184.57-.864.465-1.862.093-2.614-.21-.424-1.113-2.147-2.004-3.847l-.032-.061a429.497 429.497 0 0 1-2.005-3.847c-.368-.745-1.107-1.447-2.152-1.447Zm-.808 2.112c.404-.816 1.212-.816 1.616 0 .202.409 1.112 2.145 2.022 3.88a418.904 418.904 0 0 1 2.018 3.875c.404.817 0 1.633-1.212 1.633h-7.272c-1.212 0-1.617-.816-1.212-1.633.202-.408 1.113-2.147 2.023-3.883a421.932 421.932 0 0 0 2.017-3.872Z"></path></svg>
              </span>
              <h2>App Embeds Are Disabled</h2>
            </div>
          </div>
        </div>
        <div class="otsb_warning_message_container">
          <p>To use this section, the app embeds of OT: Theme Sections must be enabled in the theme editor. Please follow these steps to activate them:</p>
          <ul>
            <li>In the left panel, click the last icon that says <b>“App embeds”</b>.</li>
            <li>Enter <b>“OT”</b> on the search bar to quickly find and embed the apps from OT: Theme Sections.</li>
            <li>Turn on the Toggle buttons of "Section Builder Script" and "Section Builder Style", then click <b>Save</b>.</li>
          </ul>
          <p>Please refer to the User Guide <a href="https://support.omnithemes.com/blogs/ot-theme-sections-get-started/1-embed-app-to-shopify-theme" target="_blank">here</a></p>
          <p>For further support: feel free to contact us at <a href="mailto:<EMAIL>"><EMAIL></a>!</p>
        </div>
      </div>
    </div>
  {% endif %}
  <div class="otsb_nope" x-data="otsb_script_1">
  {% render 'otsb-section-divider' %}
  
  <div class="section--{{ section.id }} relative page-width mx-auto pt-[{{ section.settings.padding_top_mobile }}px] md:pt-[{{ section.settings.padding_top }}px] pb-[{{ section.settings.padding_bottom_mobile }}px] md:pb-[{{ section.settings.padding_bottom }}px]">
    <div class="md:flex justify-{{ section.settings.content_alignment }}">
      <div class="w-full text-{{ section.settings.content_alignment_mobile }} md:text-{{ section.settings.content_alignment }}{% unless section.settings.make_full_page_width %} max-w-[896px]{% endunless %}">
        {%- for block in section.blocks -%}
          {%- case block.type -%}
            {%- when 'heading' -%}
              {%- if block.settings.heading != blank -%}
                {%- liquid
                  if settings.heading_base_size != blank
                    assign heading_size = settings.heading_base_size | times: block.settings.heading_size | times: 0.000225
                  else
                    assign heading_size = block.settings.heading_size | times: 100 | times: 0.0004
                  endif
                if settings.heading_scale
                  assign heading_size = heading_size | times: settings.heading_scale | times: 0.01
                endif
                -%}
                {%- style -%} 
                  .otsb__root.otsb-v2 .heading--{{ block.id }}--{{ section.id }} {
                    {% if section.settings.change_heading_font %}
                    font-family: {{ section.settings.type_header_font.family }}, {{ section.settings.type_header_font.fallback_families }};
                    font-weight: {{ section.settings.type_header_font.weight }};
                    font-style: {{ section.settings.type_header_font.style }};
                    {% endif %}
                    font-size: {{ heading_size | times: 0.6 }}rem; 
                  }
                  @media (min-width: 768px){
                    .otsb__root.otsb-v2 .heading--{{ block.id }}--{{ section.id }} {
                      font-size: {{ heading_size }}rem; 
                    }
                  }
                {%- endstyle -%}
                <{{ block.settings.heading_tag }} class="heading-{{ block.id }} {% if block.settings.heading_text_transform == 'capitalize' %} capitalize {% elsif block.settings.heading_text_transform == 'lowercase' %}lowercase {% elsif block.settings.heading_text_transform == 'uppercase' %} uppercase {% else %} normal-case {% endif %}p-break-words heading--{{ block.id }}--{{ section.id }} anm-fade-element h2 block pt-2 pb-2 leading-tight" x-intersect.once="$el.classList.add('is-visible')" {{ block.shopify_attributes }}>
                  {% render 'otsb-heading-highlight',
                    headingId: block.id,
                    heading: block.settings.heading,
                    highlight_type: block.settings.highlight_type,
                    color_heading_highlight_light: section.settings.color_heading_highlight,
                    color_text: section.settings.text
                  %}
                </{{ block.settings.heading_tag }}>
              {%- endif -%}
            {%- when 'caption' -%}
              {%- if block.settings.caption != blank -%}
                {%- liquid
                  if settings.text_base_size != blank
                    assign text_size =  block.settings.text_size | times: settings.text_base_size | times: 0.0000875
                  else
                    assign text_size =  block.settings.text_size | times: 180 | times: 0.0000875
                  endif
                -%}
                {%- style -%}  
                  .description--{{ block.id }} {
                    font-size: {{ text_size | times: 0.9 }}rem;
                  }
                  @media (min-width: 768px){
                    .description--{{ block.id }} {
                      font-size: {{ text_size }}rem; 
                    }
                  }
                {%- endstyle -%}
                <p 
                  x-intersect.once="$el.classList.add('is-visible')"
                  class="{{ block.settings.text_style }} text-[rgb(var(--colors-text))] anm-fade-element pt-2 pb-2 p-break-words description--{{ block.id }} tracking-widest{% if forloop.index != 1 %} mt-1{% endif %} leading-tight" 
                  {{ block.shopify_attributes }}
                >
                  {{ block.settings.caption | escape }}
                </p>
              {% endif %}
            {%- when 'text' -%}
              <div 
                x-intersect.once="$el.classList.add('is-visible')"
                class="p-break-words rte pt-2 pb-2 leading-tight text-[rgb(var(--colors-text))] anm-fade-element" 
                {{ block.shopify_attributes }}
              >
                {%- render 'otsb-truncate-text', 
                  number_of_lines_shown: block.settings.number_of_lines_shown 
                  blockID: block.id,
                  content: block.settings.text,
                  read_more_label: block.settings.read_more_label,
                  see_less_label: block.settings.see_less_label,
                  text_alignment_mobile: section.settings.content_alignment_mobile,
                  text_alignment: section.settings.content_alignment
                -%}
              </div>
            {%- when 'button' -%}
              {% liquid
                assign main_button_classes = ''
                  case block.settings.button_type
                    when 'square'
                      assign main_button_classes = main_button_classes | append: ' otsb-btn-square'
                    when 'rounded'
                      assign main_button_classes = main_button_classes | append: ' otsb-btn-rounded'
                            when 'rounded_corners'
                              assign main_button_classes = main_button_classes | append: ' otsb-btn-rounded-corners'
                            when 'mixed'
                              assign main_button_classes = main_button_classes | append: ' otsb-btn-mixed'
                          endcase
                          case block.settings.button_animation
                            when 'slide'
                              assign main_button_classes = main_button_classes | append: ' otsb-btn__slide'
                            when 'fill_up'
                              assign main_button_classes = main_button_classes | append: ' otsb-btn__fill_up'
                            when 'underline'
                              assign main_button_classes = main_button_classes | append: ' otsb-btn__underline'
                            when 'sliced'
                              assign main_button_classes = main_button_classes | append: ' otsb-btn__sliced'
                          endcase
                          if section.settings.content_position == 'top-left'
                            assign main_button_classes = main_button_classes | append: ' md:w-full'
                          endif
                        %}
              {% style %}
                #shopify-section-{{ section.id }} .otsb-button.button--{{ block.id }} {
                  {% if block.settings.button_color_mobile != "hover" %}
                    --color-button-mobile: var(--color-button);
                    --color-button-text-mobile: rgb(var(--color-button-text));
                  {% else %}
                    --color-button-mobile: var(--colors-button-hover);
                    --color-button-text-mobile: rgb(var(--colors-button-text-hover));
                  {% endif %}
                }
                .otsb__root .otsb-button.otsb-btn-square {
                  border-radius: 0px;
                }
                .otsb__root#shopify-section-{{ section.id }} .otsb-btn__solid:hover {
                  color: rgb(var(--colors-button-text-hover, var(--color-button-text)));
                }
                .otsb__root#shopify-section-{{ section.id }} .otsb-btn__solid {
                  color: var(--color-button-text-mobile);
                }
                .button--{{ block.id }} { 
                  {%- unless block.settings.color_button.alpha == 0.0 -%}
                    --color-line-and-border: {{ block.settings.color_button.red }}, {{ block.settings.color_button.green }}, {{ block.settings.color_button.blue }};
                    --color-button: {{ block.settings.color_button.red }}, {{ block.settings.color_button.green }}, {{ block.settings.color_button.blue }};
                  {%- else -%}
                    --color-line-and-border: var(--colors-button);
                  {%- endunless -%}
                  {%- unless block.settings.color_button_hover.alpha == 0.0 -%}
                    --colors-button-hover: {{ block.settings.color_button_hover.red }}, {{ block.settings.color_button_hover.green }}, {{ block.settings.color_button_hover.blue }};
                  {%- endunless -%}
                  {%- unless block.settings.color_text_button.alpha == 0.0 -%}
                    --color-button-text: {{ block.settings.color_text_button.red }}, {{ block.settings.color_text_button.green }}, {{ block.settings.color_text_button.blue }};
                  {%- endunless -%}
                  {%- unless block.settings.color_text_button_hover.alpha == 0.0 -%}
                    --colors-button-text-hover: {{ block.settings.color_text_button_hover.red }}, {{ block.settings.color_text_button_hover.green }}, {{ block.settings.color_text_button_hover.blue }};
                  {%- endunless -%}
                }
                .button--{{ block.id }}.otsb-button-outline {
                  {%- if block.settings.secondary_button_text.alpha != 0.0 -%} 
                    --colors-secondary-button: {{ block.settings.secondary_button_text.red }}, {{ block.settings.secondary_button_text.green }}, {{ block.settings.secondary_button_text.blue }}; 
                    --colors-line-secondary-button: {{ block.settings.secondary_button_text.red }}, {{ block.settings.secondary_button_text.green }}, {{ block.settings.secondary_button_text.blue }}; 
                    --background-secondary-button: transparent;
                  {% endif %}
                  {%- if block.settings.color_button_secondary.alpha != 0.0 -%} 
                    --background-secondary-button: {{ block.settings.color_button_secondary.red }}, {{ block.settings.color_button_secondary.green }}, {{ block.settings.color_button_secondary.blue }}; 
                    --colors-line-secondary-button: {{ block.settings.color_button_secondary.red }}, {{ block.settings.color_button_secondary.green }}, {{ block.settings.color_button_secondary.blue }}; 
                  {% endif %}
                }
                {% if block.settings.colors_text_link.alpha != 0.0 %}
                  .button--{{ block.id }}.otsb-button-text-link, .button--{{ block.id }}.otsb-button-text-link::after, .button--{{ block.id }}.otsb-button-text-link::before {
                    --colors-text-link: {{ block.settings.colors_text_link.red }}, {{ block.settings.colors_text_link.green }}, {{ block.settings.colors_text_link.blue }};
                  }
                {% endif %}
                {% if block.settings.button_animation == 'sliced' %}
                  .button--{{ block.id }}.otsb-button.otsb-button-outline:not(.not-icon), .button--{{ block.id }}.otsb-button.otsb-btn__solid:not(.not-icon) {
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    padding-left: 1.5rem;
                    padding-right: 1.5rem;
                  }
                  .button--{{ block.id }}.otsb-button.otsb-button-outline:not(.not-icon) .otsb-button-text {
                    transform: translateX(0px);
                  }
                  .button--{{ block.id }}.otsb-btn__solid .otsb-button-icon, .button--{{ block.id }}.otsb-btn__solid .otsb-button-text {
                    transition-timing-function: cubic-bezier(0,.71,.4,1);
                  }
                  .button--{{ block.id }}.otsb-btn__solid .otsb-button-icon {
                    transition: opacity .25s,transform .5s;
                  }
                  .button--{{ block.id }}.otsb-btn__solid .otsb-button-text {
                    transition: transform .5s;
                    transform: translateX(0.625rem);
                  }
                  .button--{{ block.id }}.otsb-btn__solid:hover .otsb-button-text {
                    opacity: 1;
                    transform: translateX(0px);
                  }
                  .button--{{ block.id }}.otsb-btn__solid:hover .otsb-button-icon {
                    opacity: 1;
                    transform: translateX(0.3125rem);
                  }
                {% endif %}
                @media (min-width: 768px){
                  .otsb__root#shopify-section-{{ section.id }} .otsb-btn__solid {
                    color: rgb(var(--color-button-text, 255,255,255));
                  }
                }
              {% endstyle %}
              {% if block.settings.button_label != blank or block.settings.button_label_2 != blank %}
                <div 
                   x-intersect.once="$el.classList.add('is-visible')"
                  class="w-full anm-fade-element{% if forloop.index != 1 %} mt-2 md:mt-3{% endif %} mb-3{% if block.settings.button_label != blank and block.settings.button_label_2 != blank %} inline-flex rtl:flex-row-reverse justify-{{ section.settings.content_alignment_mobile }} md:justify-{{ section.settings.content_alignment }} flex-wrap gap-2{% endif %} p-break-words" 
                  {{ block.shopify_attributes }}
                >
                {% style %}
                .button-radius--{{ block.id  }} {
                  --border-radius: 54px;
                }
              {% endstyle %}
                  {% if block.settings.button_label != blank %}
                    <a
                      {% if block.settings.button_link %} href="{{ block.settings.button_link }}"{% if block.settings.open_new_window_button %} target="_blank"{% endif %}{% else %} role="link" aria-disabled="true"{% endif %}
                      class="otsb-button button-radius--{{ block.id  }} rounded-[54px] button--{{ block.id }} py-2.5 px-7 md:px-9 md:py-3{% if block.settings.show_button_style_1 == 'secondary' %} otsb-button-outline{% elsif block.settings.show_button_style_1 == 'text-link' %} otsb-button-text-link {% else %} otsb-btn__solid{% endif %} text-center inline-block border empty:hidden {% unless block.settings.button_link %} hover:cursor-not-allowed opacity-70{% endunless %} {{ main_button_classes }}"
                    >
                      {% render 'otsb-button-label', button_animation: block.settings.button_animation, custom_icon_button: block.settings.custom_icon_button, button_label: block.settings.button_label, show_button_style: block.settings.show_button_style_1 %}
                    </a>
                  {% endif %}
                  {% if block.settings.button_label_2 != blank %}
                    <a
                      {% if block.settings.button_link_2 %} href="{{ block.settings.button_link_2 }}"{% if block.settings.open_new_window_button_2 %} target="_blank"{% endif %}{% else %} role="link" aria-disabled="true"{% endif %}
                      class="otsb-button button-radius--{{ block.id  }} rounded-[54px] button--{{ block.id }} py-2.5 px-7 md:px-9 md:py-3{% if block.settings.show_button_style_2 == 'secondary' %} otsb-button-outline{% elsif block.settings.show_button_style_2 == 'text-link' %} otsb-button-text-link {% else %} otsb-btn__solid{% endif %} text-center inline-block border empty:hidden {% unless block.settings.button_link_2 %} hover:cursor-not-allowed opacity-70{% endunless %} {{ main_button_classes }}"
                    >
                      {% render 'otsb-button-label', button_animation: block.settings.button_animation, custom_icon_button: block.settings.custom_icon_button, button_label: block.settings.button_label_2, show_button_style: block.settings.show_button_style_2 %}
                    </a>
                  {% endif %}
                </div>
              {% endif %}
            {% when 'media' %}
              {% style %}
                .otsb__root#shopify-section-{{ section.id }} {
                {%- if block.settings.video_icon_color.alpha != 0.0 -%}
                  --image-treatment-text: {{ block.settings.video_icon_color.red }}, {{ block.settings.video_icon_color.green }}, {{ block.settings.video_icon_color.blue }};
                {% else %}
                  --image-treatment-text: 255, 255, 255;
                {%- endif -%}
                }
                @media (min-width: 768px) {
                  .otsb__root .width-{{ block.id }} {
                    width: {{ block.settings.media_size }}%;
                  }
                }
              {% endstyle %}
              {% liquid
                assign ratio_video = ''
                assign video_type = false
                assign video_alt = block.settings.video_alt_text
                if block.settings.video_url.type == 'youtube'
                  assign video_type = 'youtube'
                  assign video_id = block.settings.video_url.id 
                  assign ratio_video = ' pb-[56.25%]'
                endif
                if block.settings.video_url.type == 'vimeo'
                  assign video_type = 'vimeo'
                  assign video_id = block.settings.video_url.id
                  assign ratio_video = ' pb-[56.25%]'
                endif
              
                if block.settings.video != null 
                  assign video_type = 'video_select'
                endif
              %}
              
              <div class="flex mt-5 mb-5 empty:hidden justify-{{ section.settings.content_alignment }}">
                {%- if block.settings.image != blank and video_type == false -%}
                  {%- capture sizes -%}
                    (min-width: {{ settings.page_width }}px) {{ block.settings.media_size | times: settings.page_width | divided_by: 100 | times: 1.1 | ceil }}px ,(min-width: 768px) {{ block.settings.media_size }}vw, 100vw
                  {%- endcapture -%}
                  <div x-show="loading">
                    <div class="h-[300px] block md:hidden"></div>
                  </div>
                  <div class=" w-full h-full  width-{{ block.id }} overflow-hidden{% if settings.edges_type == 'rounded_corners' %} rounded-[10px]{% endif %}">
                    {% liquid
                      assign image_width = block.settings.media_size | times: settings.page_width | divided_by: 100 | times: 1.1 | ceil
                      assign width_tag = '375, 450, 750, 900, 1100, 1500, 1780, ' | append:  image_width
                    %}
                    {{ block.settings.image | image_url: width: 1780 | image_tag: loading: "lazy", sizes: sizes, widths: width_tag, class: 'w-full image-hover' }}
                  </div>
                {%- elsif video_type -%}
                  <div class="flex items-center w-full z-0 width-{{ block.id }}">
                    <div class="relative external-video otsb-external-video w-full overflow-hidden {% unless video_type == 'video_select' %}{{ ratio_video }}{% endunless %}{% if block.settings.enable_video_autoplay %} otsb-video-hero video-hero{% endif %}{% if settings.edges_type == 'rounded_corners' %} rounded-[10px]{% else %} rounded-none{% endif %}" 
                      x-intersect:leave="$store.xVideo.pause($el)"
                      {% if block.settings.enable_video_autoplay %}
                        @click.stop="$store.xVideo.togglePlay($el)"
                        {% if video_type == 'video_select' %}x-intersect="$store.xVideo.play($el)"{% endif %}
                        {% if video_type == 'youtube' or video_type == 'vimeo' %}
                          x-intersect.once="$store.xVideo.externalLoad($el, '{{ video_type }}', '{{ video_id }}', false, '{{ video_alt }}')"
                          x-intersect="$store.xVideo.play($el)"
                        {% endif %}
                      {% else %}
                        {% if video_type == 'video_select' %}@click.prevent="$store.xVideo.mp4Thumbnail($el)"{% endif %}
                        {% if video_type == 'youtube' or video_type == 'vimeo' %}@click.prevent="$store.xVideo.externalLoad($el, '{{ video_type }}', '{{ video_id }}', false, '{{ video_alt }}')"{% endif %}
                      {% endif %}
                    {% if video_type == 'video_select' %}
                        style="padding-bottom: {{ 1 | divided_by: block.settings.video.aspect_ratio | times: 100 }}%;"
                      {% endif %}
                    >
                      {%- render 'otsb-media-video', 
                        enable_video_autoplay: block.settings.enable_video_autoplay,
                        video_type: video_type,
                        video_id: video_id,
                        video_alt: video_alt,
                        video: block.settings.video,
                        cover_image: block.settings.image,
                        show_sound_control: block.settings.show_sound_control
                      -%}
                    </div>
                  </div>
                {%- endif -%}
              </div>
          {% endcase %}
        {% endfor %}
      </div>
    </div>
  </div>
  </div>
  