/* Category Showcase - Revolutionary Design System */

:root {
  --enhanced-card-radius: 20px;
  --enhanced-card-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  --enhanced-card-shadow-hover: 0 16px 48px rgba(0, 0, 0, 0.18);
  --enhanced-transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  --enhanced-spring: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  --enhanced-glass-bg: rgba(255, 255, 255, 0.95);
  --enhanced-glass-border: rgba(255, 255, 255, 0.2);
  --enhanced-backdrop-blur: blur(20px);
  --premium-gradient: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  --premium-border: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
}

/* Prevent horizontal scroll */
.category-showcase-wrapper {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* Ensure all containers stay within viewport */
.category-showcase-wrapper *,
.category-showcase-wrapper *::before,
.category-showcase-wrapper *::after {
  box-sizing: border-box;
  max-width: 100%;
}

/* Additional safety for page width container */
.category-showcase .page-width {
  max-width: 100%;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
}

/* Force all flex containers to respect viewport */
.category-showcase .category-grid {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  margin: 0;
  /* padding: ; */
}

/* Override any theme styles that might cause overflow */
.category-showcase-wrapper .page-width {
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Section Header */
.category-showcase {
  position: relative;
  overflow: hidden;
  width: 100%;
  max-width: 100%;
}

.category-showcase .section-header {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

.category-showcase .section-subheading {
  font-size: 1.2rem;
  font-weight: 500;
  color: rgb(var(--color-foreground) / 0.6);
  margin-bottom: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.08em;
}

.category-showcase .section-description {
  font-size: 1.6rem;
  line-height: 1.5;
  color: rgb(var(--color-foreground) / 0.7);
  max-width: 55rem;
  margin: 0 auto;
}

/* Grid Layouts */
.category-grid {
  display: grid;
  row-gap: var(--custom-gap-vertical, 2.5rem);
  column-gap: var(--custom-gap-horizontal, 2.5rem);
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
  padding:100px;
}

/* High Priority Flex Layout - Desktop Only - Cannot be overwritten */
@media screen and (min-width: 750px) {
  .category-showcase .category-grid,
  .category-showcase .category-grid.category-grid--grid,
  .category-showcase .category-grid.category-grid--hexagonal,
  .category-showcase .category-grid.category-grid--masonry,
  .category-showcase .category-grid.category-grid--floating,
  .category-showcase .category-grid.category-grid--custom-rows,
  .category-showcase .category-grid[data-custom-pattern] {
    display: flex !important;
    flex-wrap: wrap !important;
    align-items: flex-start !important;
    justify-content: flex-start !important;
    max-width: 1099px !important;
    margin: 0 auto !important;
  }
}

/* Classic Grid */
.category-grid--grid {
  grid-template-columns: repeat(var(--columns-desktop, 3), 1fr);
  justify-items: center;
}

/* Use flexbox for better control when custom widths are used */
.category-showcase .category-grid--grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--custom-gap-vertical, 2.5rem) var(--custom-gap-horizontal, 2.5rem);
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
}

/* Hexagonal Grid */
.category-grid--hexagonal {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--custom-gap-vertical, 1.5rem) var(--custom-gap-horizontal, 1.5rem);
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
}

.category-grid--hexagonal .category-card {
  flex: 0 0 var(--custom-category-width, calc(33.333% - var(--custom-gap-horizontal, 1.5rem) * 2 / 3));
  max-width: var(--custom-category-width, 32rem);
}

.category-grid--hexagonal .category-card:nth-child(even) {
  transform: translateY(1.5rem);
}

/* Masonry Grid */
.category-grid--masonry {
  columns: var(--columns-desktop, 3);
  column-gap: var(--custom-gap-horizontal, 2.5rem);
}

.category-grid--masonry .category-card {
  break-inside: avoid;
  margin-bottom: var(--custom-gap-vertical, 2.5rem);
}

/* Floating Cards */
.category-grid--floating {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--custom-gap-vertical, 1.5rem) var(--custom-gap-horizontal, 1.5rem);
  perspective: 1200px;
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  box-sizing: border-box;
}

.category-grid--floating .category-card {
  flex: 0 0 var(--custom-category-width, calc(33.333% - var(--custom-gap-horizontal, 1.5rem) * 2 / 3));
  max-width: var(--custom-category-width, 32rem);
  transform-style: preserve-3d;
}

.category-grid--floating .category-card:nth-child(odd) {
  transform: translateY(-0.8rem) rotateY(1.5deg);
}

.category-grid--floating .category-card:nth-child(even) {
  transform: translateY(0.8rem) rotateY(-1.5deg);
}

/* Custom Row Layout - Works with ALL grid styles */
.category-grid[data-custom-pattern] {
  display: flex !important;
  flex-wrap: wrap !important;
  align-items: flex-start !important;
  justify-content: flex-start !important;
  gap: var(--custom-gap-vertical, 2rem) var(--custom-gap-horizontal, 2rem) !important;
  width: 100% !important;
  max-width: 1099px !important;
  overflow-x: hidden !important;
  box-sizing: border-box !important;
}

.category-grid[data-custom-pattern] .category-card {
  flex: 0 0 auto !important;
  transform-style: preserve-3d;
  transition: var(--enhanced-transition);
  box-sizing: border-box !important;
  margin: 0 !important; /* Clear any default margins */
}

/* Clear any conflicting styles for custom patterns */
@media screen and (min-width: 750px) {
  .category-showcase .category-grid[data-custom-pattern] .category-card {
    /* Reset any individual sizing that might conflict */
    --custom-category-width: auto !important;
    --custom-category-height: auto !important;
  }
  
  .category-showcase .category-grid[data-custom-pattern] .category-card[style] {
    /* Override inline styles that might come from individual category settings */
    flex: inherit !important;
    width: inherit !important;
    max-width: inherit !important;
  }
}

/* Ensure custom pattern cards maintain premium styling */
.category-grid[data-custom-pattern] .category-card {
  background: var(--glass-background);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: var(--enhanced-card-shadow);
}

/* Original Custom Row Layout (kept for backwards compatibility) */
.category-grid--custom-rows {
  display: flex !important;
  flex-wrap: wrap !important;
  align-items: flex-start !important;
  justify-content: flex-start !important;
  gap: var(--custom-gap-vertical, 2rem) var(--custom-gap-horizontal, 2rem) !important;
  width: 100% !important;
  max-width: 1099px !important;
  overflow-x: hidden !important;
  box-sizing: border-box !important;
}

.category-grid--custom-rows .category-card {
  flex: 0 0 auto !important;
  transform-style: preserve-3d;
  transition: var(--enhanced-transition);
  box-sizing: border-box !important;
  margin: 0 !important; /* Clear any default margins */
}

/* Clear any conflicting styles for custom rows */
@media screen and (min-width: 750px) {
  .category-showcase .category-grid--custom-rows .category-card {
    /* Reset any individual sizing that might conflict */
    --custom-category-width: auto !important;
    --custom-category-height: auto !important;
  }
  
  .category-showcase .category-grid--custom-rows .category-card[style] {
    /* Override inline styles that might come from individual category settings */
    flex: inherit !important;
    width: inherit !important;
    max-width: inherit !important;
  }
}

/* Ensure custom row cards maintain premium styling */
.category-grid--custom-rows .category-card {
  background: var(--glass-background);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: var(--enhanced-card-shadow);
}

/* Custom row layout responsive behavior */
@media screen and (max-width: 749px) {
  .category-grid--custom-rows {
    flex-direction: column;
    gap: 15px;
    justify-content: space-between;
  }
  
  .category-grid--custom-rows .category-card {
    width: 100% !important;
    max-width: 100% !important;
    flex: 0 0 100% !important;
  }
  
  /* Mobile: First card full width, rest 48% */
  .category-grid--custom-rows .category-card:nth-child(1) {
    width: 100% !important;
    max-width: 100% !important;
    height: 320px !important;
    flex: 0 0 100% !important;
    --custom-category-width: 100% !important;
    --custom-category-height: 320px !important;
  }
  
  .category-grid--custom-rows .category-card:nth-child(n+2) {
    width: 48% !important;
    max-width: 48% !important;
    height: 280px !important;
    flex: 0 0 48% !important;
    --custom-category-width: 48% !important;
    --custom-category-height: 280px !important;
  }
  
  /* Override any inline styles or custom properties on mobile */
  .category-showcase .category-grid--custom-rows .category-card[style] {
    width: inherit !important;
    max-width: inherit !important;
    height: inherit !important;
    flex: inherit !important;
  }
  
  .category-showcase .category-grid--custom-rows .category-card:nth-child(1)[style] {
    width: 100% !important;
    max-width: 100% !important;
    height: 320px !important;
    flex: 0 0 100% !important;
  }
  
  .category-showcase .category-grid--custom-rows .category-card:nth-child(n+2)[style] {
    width: 48% !important;
    max-width: 48% !important;
    height: 280px !important;
    flex: 0 0 48% !important;
  }
}

/* Desktop: Ensure custom row cards don't break on large screens */
@media screen and (min-width: 1200px) {
  .category-grid--custom-rows .category-card {
    min-width: 200px; /* Prevent cards from becoming too narrow */
  }
}

/* Category Cards */
.category-card {
  position: relative;
  border-radius: var(--enhanced-card-radius);
  overflow: hidden;
  background: var(--enhanced-glass-bg);
  backdrop-filter: var(--enhanced-backdrop-blur);
  border: 1px solid transparent;
  background-clip: padding-box;
  box-shadow: var(--enhanced-card-shadow);
  transition: var(--enhanced-spring);
  cursor: pointer;
  transform-origin: center center;
  flex: 0 0 auto;
  height: var(--custom-category-height, auto);
  min-height: 200px;
  width: 100%;
  max-width: calc(100vw - 30px);
  box-sizing: border-box;
}

.category-card::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 1px;
  background: var(--premium-border);
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: subtract;
  pointer-events: none;
}

/* Default card width for all layouts */
.category-grid--grid .category-card {
  flex: 0 0 calc(33.333% - var(--custom-gap-horizontal, 2.5rem) * 2 / 3);
  max-width: min(calc(33.333% - var(--custom-gap-horizontal, 2.5rem) * 2 / 3), 90vw);
  width: min(calc(33.333% - var(--custom-gap-horizontal, 2.5rem) * 2 / 3), 90vw);
  overflow: hidden;
  box-sizing: border-box;
}

.category-card::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 1px;
  background: var(--premium-border);
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: subtract;
  pointer-events: none;
}

.category-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--premium-gradient);
  opacity: 0;
  transition: var(--enhanced-transition);
  z-index: 1;
  pointer-events: none;
}

.category-card:hover::after {
  opacity: 1;
}

/* Card Sizes */
.category-card--small {
  max-width: 28rem;
}

.category-card--medium {
  max-width: 32rem;
}

.category-card--large {
  max-width: 36rem;
}

/* Featured Cards */
.category-card--featured {
  grid-column: span 2;
  grid-row: span 2;
  background: linear-gradient(135deg, var(--enhanced-glass-bg), rgba(255, 255, 255, 0.1));
  border: 1px solid var(--enhanced-glass-border);
  box-shadow: var(--enhanced-card-shadow-hover);
}

.category-card--featured .category-card__title {
  font-size: 2rem;
}

.category-card--featured .category-card__description {
  font-size: 1.4rem;
}

/* Card Hover Effects */
.category-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--enhanced-card-shadow-hover);
  border-color: var(--card-accent, rgba(255, 255, 255, 0.3));
}

.category-grid--floating .category-card:hover {
  transform: translateY(-12px) rotateY(0deg) scale(1.04);
}

/* Card Inner */
.category-card__inner {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  z-index: 2;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border-radius: var(--enhanced-card-radius);
}

/* Media */
.category-card__media {
  position: relative;
  aspect-ratio: 1.3 / 1;
  overflow: hidden;
  border-radius: var(--enhanced-card-radius) var(--enhanced-card-radius) 0 0;
  flex: 1;
  min-height: 200px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  transition: var(--enhanced-spring);
}

/* Image element styling */
.category-card__image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
}

/* Placeholder styling */
.category-card__media--placeholder {
  background-color: rgb(var(--color-foreground) / 0.08);
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-card__media--placeholder .placeholder-svg {
  width: 60px;
  height: 60px;
  opacity: 0.3;
}

/* Adjust media for custom heights - make image take more space */
.category-card[style*="height"] .category-card__media {
  height: 85%;
  aspect-ratio: unset;
}

.category-card:hover .category-card__media {
  transform: scale(1.05);
}

/* Overlay */
.category-card__overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(0, 0, 0, 0.05) 0%, 
    rgba(0, 0, 0, 0.3) 50%, 
    var(--card-accent, rgba(0, 0, 0, 0.5)) 100%);
  opacity: 0;
  transition: var(--enhanced-transition);
  z-index: 1;
}

.category-card:hover .category-card__overlay {
  opacity: 1;
}

/* Badge */
.category-card__badge {
  position: absolute;
  top: 1.2rem;
  right: 1.2rem;
  z-index: 3;
}

.badge {
  display: inline-flex;
  align-items: center;
  gap: 0.4rem;
  padding: 0.8rem 1.2rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85));
  color: rgb(var(--color-foreground));
  border-radius: 2rem;
  font-size: 1rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  backdrop-filter: blur(16px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.5);
  animation: pulse 3s infinite;
  white-space: nowrap;
  flex-wrap: nowrap;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.badge--trending {
  background: linear-gradient(135deg, #ff6b6b, #feca57, #ff9ff3);
  color: white;
  box-shadow: 0 4px 20px rgba(255, 107, 107, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* Content */
.category-card__content {
  padding: 0.5rem;
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  position: relative;
  z-index: 2;
  min-height: 60px;
}

/* Adjust content for custom heights - reduce to 15% for better image visibility */
.category-card[style*="height"] .category-card__content {
  height: 15%;
  flex: none;
  padding: 0.5rem;
}

.category-card__text {
  margin-bottom: 0.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.category-card__arrow {
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  width: 20px;
  height: 20px;
  opacity: 0.7;
  transition: all 0.3s ease;
  fill: currentColor;
}

.category-card:hover .category-card__arrow {
  opacity: 1;
  transform: translateX(3px);
}

.category-card__title {
  font-size: 1.2rem;
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 0.2rem;
  color: rgb(var(--color-foreground));
  transition: var(--enhanced-transition);
}

.category-card__link {
  text-decoration: none;
  color: inherit;
  position: relative;
}

.category-card__link::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 0;
  height: 1px;
  background: var(--card-accent, rgb(var(--color-foreground)));
  transition: var(--enhanced-transition);
}

.category-card:hover .category-card__link::after {
  width: 100%;
}

.category-card__description {
  font-size: 1rem;
  line-height: 1.3;
  color: rgb(var(--color-foreground) / 0.7);
  margin-bottom: 0.2rem;
}

.category-card__meta {
  margin-bottom: 0.8rem;
}

.category-card__count {
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--card-accent, rgb(var(--color-foreground) / 0.5));
  text-transform: uppercase;
  letter-spacing: 0.04em;
}

/* Actions */
.category-card__actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.8rem;
}

.category-card__button {
  display: inline-flex;
  align-items: center;
  gap: 0.6rem;
  padding: 1rem 2rem;
  border-radius: 2.5rem;
  font-weight: 600;
  text-decoration: none;
  transition: var(--enhanced-spring);
  position: relative;
  overflow: hidden;
  transform-origin: center center;
}

.category-card__button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: var(--enhanced-transition);
}

.category-card__button:hover::before {
  width: 250%;
  height: 250%;
}

.category-card__button:hover {
  transform: translateY(-1px) scale(1.03);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.category-card__button-icon {
  transition: var(--enhanced-transition);
}

.category-card__button:hover .category-card__button-icon {
  transform: translateX(3px);
}

/* Quick Actions */
.category-card__quick-actions {
  display: flex;
  gap: 0.6rem;
}

.quick-action {
  width: 3.5rem;
  height: 3.5rem;
  border: none;
  background: var(--enhanced-glass-bg);
  backdrop-filter: var(--enhanced-backdrop-blur);
  border: 1px solid var(--enhanced-glass-border);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--enhanced-transition);
  color: rgb(var(--color-foreground));
}

.quick-action:hover {
  background: var(--card-accent, rgb(var(--color-foreground)));
  color: rgb(var(--color-background));
  transform: scale(1.08) rotate(4deg);
}

/* 3D Effect */
.category-card__shine {
  position: absolute;
  top: 0;
  left: -150%;
  width: 150%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(255, 255, 255, 0.2) 30%,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0.2) 70%,
    transparent 100%);
  transform: skewX(-25deg);
  transition: var(--enhanced-transition);
  z-index: 3;
  pointer-events: none;
}

.category-card:hover .category-card__shine {
  left: 150%;
}

/* Section Footer */
.section-footer {
  text-align: center;
  margin-top: 4rem;
}

/* Animations */
@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.03); }
}

.animate-fade-up {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeUp 0.7s ease-out forwards;
}

.animate-slide-up {
  opacity: 0;
  transform: translateY(40px);
  animation: slideUp 0.7s ease-out forwards;
}

@keyframes fadeUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animation Delays */
[data-delay] {
  animation-delay: calc(var(--delay, 0) * 1ms);
}

/* Responsive Design */
@media screen and (max-width: 1199px) {
  .category-grid--hexagonal .category-card,
  .category-grid--floating .category-card {
    flex: 0 0 var(--custom-category-width, calc(50% - var(--custom-gap-horizontal, 1rem) / 2));
  }
  
  .category-card--featured {
    grid-column: span 1;
    grid-row: span 1;
  }
}

@media screen and (max-width: 749px) {
  .category-showcase .section-header {
    margin-bottom: 2rem;
  }
  
  .category-grid {
    row-gap: 1.5rem;
    column-gap: 0;
  }
  
  /* Mobile: Single column layout */
  .category-grid--grid {
    grid-template-columns: 1fr;
  }
  
  .category-showcase .category-grid--grid {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: flex-start;
    gap: 15px;
    width: 100%;
    padding: 0 15px;
    box-sizing: border-box;
  }
  
  .category-grid--hexagonal,
  .category-grid--floating {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: flex-start;
    gap: 15px;
    width: 100%;
    padding: 0 15px;
    box-sizing: border-box;
  }
  
  /* Mobile card styling - First card full width, rest half width */
  .category-card {
    flex: none !important;
    min-width: 0 !important;
    margin: 0 !important;
    box-sizing: border-box !important;
    height: 280px !important;
    border-radius: 20px !important;
    overflow: hidden !important;
    display: flex !important;
    flex-direction: column !important;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%) !important;
    backdrop-filter: blur(20px) !important;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15), 0 4px 16px rgba(0, 0, 0, 0.1) !important;
    position: relative !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
  }
  
  /* First card - Full width */
  .category-card:nth-child(1) {
    width: 100% !important;
    max-width: 100% !important;
    height: 320px !important;
    flex: 0 0 100% !important;
    --custom-category-width: 100% !important;
    --custom-category-height: 320px !important;
  }
  
  /* Cards 2-5 - 48% width for better gap */
  .category-card:nth-child(n+2) {
    width: 48% !important;
    max-width: 48% !important;
    height: 280px !important;
    flex: 0 0 48% !important;
    --custom-category-width: 48% !important;
    --custom-category-height: 280px !important;
  }
  
  /* Override any inline styles or custom properties on mobile */
  .category-showcase .category-card[style] {
    width: inherit !important;
    max-width: inherit !important;
    height: inherit !important;
    flex: inherit !important;
  }
  
  .category-showcase .category-card:nth-child(1)[style] {
    width: 100% !important;
    max-width: 100% !important;
    height: 320px !important;
    flex: 0 0 100% !important;
  }
  
  .category-showcase .category-card:nth-child(n+2)[style] {
    width: 48% !important;
    max-width: 48% !important;
    height: 280px !important;
    flex: 0 0 48% !important;
  }
  
  .category-grid--hexagonal .category-card,
  .category-grid--floating .category-card {
    flex: none !important;
  }
  
  .category-grid--hexagonal .category-card:nth-child(1),
  .category-grid--floating .category-card:nth-child(1) {
    width: 100% !important;
    max-width: 100% !important;
    height: 320px !important;
  }
  
  .category-grid--hexagonal .category-card:nth-child(n+2),
  .category-grid--floating .category-card:nth-child(n+2) {
    width: calc(50% - 7.5px) !important;
    max-width: calc(50% - 7.5px) !important;
    height: 280px !important;
  }
  
  /* Reset custom widths on mobile */
  .category-card[style*="width"] {
    flex: none !important;
  }
  
  .category-card[style*="width"]:nth-child(1) {
    width: 100% !important;
    max-width: 100% !important;
  }
 
  
  .category-card[style*="width"]:nth-child(n+2) {
    width: calc(50% - 7.5px) !important;
    max-width: calc(50% - 7.5px) !important;
  }
  
  /* Override custom heights on mobile */
  .category-card[style*="height"]:nth-child(1) {
    height: 320px !important;
  }
  
  .category-card[style*="height"]:nth-child(n+2) {
    height: 280px !important;
  }
  
  /* Force card inner structure */
  .category-card__inner {
    display: flex !important;
    flex-direction: column !important;
    height: 100% !important;
    width: 100% !important;
  }
  
  /* Mobile card media - Different heights for first vs rest */
  .category-card__media {
    width: 100% !important;
    overflow: hidden !important;
    aspect-ratio: unset !important;
    position: relative !important;
    background-size: cover !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
    display: block !important;
  }
  
  /* First card - larger image area */
  .category-card:nth-child(1) .category-card__media {
    height: 75% !important;
    border-radius: 20px 20px 0 0 !important;
  }
  
  /* Cards 2-5 - smaller image area for compact design */
  .category-card:nth-child(n+2) .category-card__media {
    height: 70% !important;
    border-radius: 20px 20px 0 0 !important;
  }
  
  .category-card[style*="height"] .category-card__media {
    aspect-ratio: unset !important;
  }
  
  .category-card[style*="height"]:nth-child(1) .category-card__media {
    height: 75% !important;
  }
  
  .category-card[style*="height"]:nth-child(n+2) .category-card__media {
    height: 70% !important;
  }
  
  .category-card__media .category-card__image {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    object-position: center !important;
    display: block !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    filter: brightness(1.05) contrast(1.1) saturate(1.1) !important;
  }
  
  /* Ensure placeholder images also follow mobile rules */
  .category-card__media--placeholder {
    width: 100% !important;
  }
  
  .category-card:nth-child(1) .category-card__media--placeholder {
    height: 75% !important;
    border-radius: 20px 20px 0 0 !important;
  }
  
  .category-card:nth-child(n+2) .category-card__media--placeholder {
    height: 70% !important;
    border-radius: 20px 20px 0 0 !important;
  }
  
  /* Mobile card content - Different heights for first vs rest */
  .category-card__content {
    display: flex !important;
    flex-direction: row !important;
    justify-content: center !important;
    align-items: flex-start !important;
    text-align: left !important;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(255, 255, 255, 0.95) 100%) !important;
    border-radius: 0 0 20px 20px !important;
    box-sizing: border-box !important;
    position: relative !important;
    backdrop-filter: blur(10px) !important;
    border-top: 1px solid rgba(255, 255, 255, 0.2) !important;
  }
  
  /* First card - larger content area */
  .category-card:nth-child(1) .category-card__content {
    height: 25% !important;
    padding: 18px 20px !important;
  }
  
  /* Cards 2-5 - smaller content area */
  .category-card:nth-child(n+2) .category-card__content {
    height: 15% !important;
    padding: 14px 16px !important;
  }
  
  .category-card[style*="height"] .category-card__content {
    flex: none !important;
  }
  
  .category-card[style*="height"]:nth-child(1) .category-card__content {
    height: 25% !important;
    padding: 18px 20px !important;
  }
  
  .category-card[style*="height"]:nth-child(n+2) .category-card__content {
    height: 30% !important;
    padding: 14px 16px !important;
  }
  
  .category-card__text {
    width: 100% !important;
    display: flex !important;
    flex-direction: row !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin: 0 !important;
    flex: 1 !important;
  }
  
  .category-card__title {
    color: rgb(var(--color-foreground)) !important;
    margin: 0 !important;
    flex: 1 !important;
    line-height: 1.3 !important;
    text-align: left !important;
    letter-spacing: -0.02em !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
  }
  
  /* First card - larger title */
  .category-card:nth-child(1) .category-card__title {
    font-size: 20px !important;
    font-weight: 700 !important;
  }
  
  /* Cards 2-5 - smaller title for compact design */
  .category-card:nth-child(n+2) .category-card__title {
    font-size: 15px !important;
    font-weight: 600 !important;
  }
  
  .category-card--featured .category-card__title {
    background: linear-gradient(135deg, rgb(var(--color-foreground)), rgba(var(--color-foreground), 0.8)) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
  }
  
  /* First card featured */
  .category-card:nth-child(1).category-card--featured .category-card__title {
    font-size: 22px !important;
  }
  
  /* Cards 2-5 featured */
  .category-card:nth-child(n+2).category-card--featured .category-card__title {
    font-size: 17px !important;
  }
  
  .category-card__arrow {
    width: 20px !important;
    height: 20px !important;
    color: rgb(var(--color-foreground)) !important;
    opacity: 0.8 !important;
    flex-shrink: 0 !important;
    margin-left: 12px !important;
    display: block !important;
    position: static !important;
    bottom: unset !important;
    right: unset !important;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1)) !important;
    transition: all 0.3s ease !important;
  }
  
  .category-card:hover .category-card__arrow {
    opacity: 1 !important;
    transform: translateX(3px) !important;
  }
  
  /* Hide additional elements on mobile */
  .category-card__actions {
    display: none;
  }
  
  .category-card__quick-actions {
    display: none;
  }
  
  .category-card__description {
    display: none;
  }
  
  .category-card__button {
    display: none;
  }
  
  /* Badge positioning for mobile */
  .category-card__badge {
    top: 18px;
    right: 18px;
    z-index: 3;
  }
  
  .badge {
    font-size: 11px;
    padding: 8px 14px;
    border-radius: 20px;
    font-weight: 700;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.5);
  }
  
  /* Reset transforms on mobile */
  .category-grid--hexagonal .category-card:nth-child(even),
  .category-grid--floating .category-card:nth-child(odd),
  .category-grid--floating .category-card:nth-child(even) {
    transform: none;
  }
  
  /* Add subtle hover effect on mobile */
  .category-card:active {
    transform: scale(0.98) !important;
    transition: transform 0.1s ease !important;
  }
}

@media screen and (max-width: 549px) {
  .category-showcase .section-subheading {
    font-size: 1.1rem;
  }
  
  .category-showcase .section-description {
    font-size: 1.4rem;
  }
  
  /* Maintain the mobile card layout */
  .category-card {
    height: 320px !important;
    box-shadow: 0 10px 35px rgba(0, 0, 0, 0.12), 0 3px 14px rgba(0, 0, 0, 0.08) !important;
  }
  
  .category-card__media {
    height: 80% !important;
    aspect-ratio: unset !important;
  }
  
  .category-card__content {
    height: 20% !important;
    padding: 14px 18px !important;
  }
  
  .category-card__title {
    font-size: 16px !important;
  }
  
  .category-card--featured .category-card__title {
    font-size: 18px !important;
  }
  
  .category-card__arrow {
    width: 18px !important;
    height: 18px !important;
    margin-left: 10px !important;
  }
  
  .badge {
    font-size: 10px;
    padding: 6px 12px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .category-card {
    border: 1px solid rgb(var(--color-foreground));
    background: rgb(var(--color-background));
  }
  
  .category-card__overlay {
    background: rgba(0, 0, 0, 0.7);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .category-card,
  .category-card__image,
  .category-card__button,
  .quick-action {
    transition: none;
    animation: none;
  }
  
  .category-card:hover {
    transform: none;
  }
  
  .animate-fade-up,
  .animate-slide-up {
    opacity: 1;
    transform: none;
    animation: none;
  }
}

/* View All Button Section Header Styles */
.section-header__top {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 2rem;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  padding: 0 15px;
}

.section-header__text {
  flex: 1;
  min-width: 0;
  max-width: calc(100% - 120px);
  overflow: hidden;
}

.section-header__action {
  flex-shrink: 0;
  flex-basis: auto;
  white-space: nowrap;
  max-width: 120px;
  overflow: hidden;
}

/* When there's no header content, align button to the right */
.section-header__top.button-only {
  justify-content: flex-end;
}

.view-all-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0;
  background: transparent;
  border: none;
  color: rgb(var(--color-foreground));
  text-decoration: underline;
  text-underline-offset: 4px;
  text-decoration-thickness: 1px;
  font-weight: 400;
  font-size: 16px;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
  margin-right: 20px;
  transition: none;
}
@media screen and (max-width: 750px) {
    .view-all-button{
        margin-right:20px !important;
    }
}


.view-all-button:hover {
  color: rgb(var(--color-foreground));
  text-decoration: underline;
  text-underline-offset: 4px;
  text-decoration-thickness: 1px;
  transform: none;
  transition: none;
}

/* Mobile responsive for view all button */
@media screen and (max-width: 750px) {
  .section-header__top {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
    width: 100%;
    padding: 0 10px;
  }

  .section-header__text {
    max-width: 100%;
  }

  .section-header__action {
    align-self: flex-end;
    width: auto;
    max-width: 100%;
  }

  .view-all-button {
    padding: 0.4rem 0;
    font-size: 14px;
    display: inline-flex;
    width: auto;
    margin-right: 3px;
    transition: none;
    transform: none;
  }

  /* Mobile: when no header content, center the button */
  .section-header__top.button-only {
    align-items: center;
    justify-content: center;
    padding: 0 10px;
  }
}

@media screen and (max-width: 549px) {
  .section-header__top {
    gap: 0.8rem;
  }
  
  .view-all-button {
    font-size: 14px;
    padding: 0.3rem 0;
    margin-right: 3px;
    transition: none;
    transform: none;
  }
}
