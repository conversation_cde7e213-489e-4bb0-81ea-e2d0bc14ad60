{% comment %}
    Renders cart drawer

    Usage:
    {% render 'cart-drawer' %}
{% endcomment %}
<script src="{{ 'shipping.js' | asset_url }}" defer="defer"></script>
<script src="{{ 'cart.js' | asset_url }}" defer="defer"></script>

<style>
  .drawer {
    visibility: hidden;
  }
.cart-group{ display: flex;
    align-items: center;
    flex-direction: row;
    justify-content: space-between;}
  .cart-drawer .cart-item ul.discounts.list-unstyled .discounts__discount{display:none;}
  
{% comment %} .cart-drawer .cart-item td.cart-item__details { display: none;}  {% endcomment %}

</style>

<cart-drawer class="drawer{% if cart == empty %} is-empty{% endif %}">
  <div id="CartDrawer" class="cart-drawer">
    <div id="CartDrawer-Overlay"class="cart-drawer__overlay"></div>
    <div class="drawer__inner" role="dialog" aria-modal="true" aria-label="{{ 'sections.cart.title' | t }}" tabindex="-1">
       {%- if cart == empty -%}
        <div class="drawer__inner-empty">
          <div class="cart-drawer__warnings center{% if settings.cart_drawer_collection != blank %} cart-drawer__warnings--has-collection{% endif %}">
            <div class="cart-drawer__empty-content">
              <h2 class="cart__empty-text">{{ 'sections.cart.empty' | t }}</h2>
              <button class="drawer__close close_icon_button" type="button" onclick="this.closest('cart-drawer').close()" aria-label="{{ 'accessibility.close' | t }}">{% render 'icon-close' %}</button>
              <a href="{{ routes.all_products_collection_url }}" class="button">
                {{ 'general.continue_shopping' | t }}
              </a>

              {%- if shop.customer_accounts_enabled and customer == nil -%}
                <p class="cart__login-title h3">{{ 'sections.cart.login.title' | t }}</p>
                <p class="cart__login-paragraph">
                  {{ 'sections.cart.login.paragraph_html' | t: link: routes.account_login_url }}
                </p>
              {%- endif -%}
            </div>
          </div>          
        </div>
      {%- endif -%}
      <div class="drawer__header">
        <h2 class="drawer__heading">{{ 'sections.cart.title' | t }}</h2>
        <button class="drawer__close close_icon_button"  type="button" onclick="this.closest('cart-drawer').close()" aria-label="{{ 'accessibility.close' | t }}">{% render 'icon-close' %}</button>
      </div>     
       <div class="cart__items-widget">
      <cart-drawer-items {% if cart == empty %}class=" is-empty"{% endif %}>
        <form action="{{ routes.cart_url }}" id="CartDrawer-Form" class="cart__contents cart-drawer__form" method="post">
          <div id="CartDrawer-CartItems" class="drawer__contents js-contents">
            {%- if cart != empty -%}
          {%  render 'progress-bar' %}
              <div class="drawer__cart-items-wrapper">
                <table class="cart-items" role="table">
                  <thead role="rowgroup">
                    <tr role="row">
                      <th id="CartDrawer-ColumnProductImage" role="columnheader"><span class="visually-hidden">{{ 'sections.cart.headings.image' | t }}</span></th>
                      <th id="CartDrawer-ColumnProduct" class="caption-with-letter-spacing" scope="col" role="columnheader">{{ 'sections.cart.headings.product' | t }}</th>
                      <th id="CartDrawer-ColumnTotal" class="right caption-with-letter-spacing" scope="col" role="columnheader">{{ 'sections.cart.headings.total' | t }}</th>
                      <th id="CartDrawer-ColumnQuantity" role="columnheader"><span class="visually-hidden">{{ 'sections.cart.headings.quantity' | t }}</span></th>
                    </tr>
                  </thead>

                  <tbody role="rowgroup">
                    {%- for item in cart.items -%}
                      <tr id="CartDrawer-Item-{{ item.index | plus: 1 }}" class="cart-item" role="row">
                        <td class="cart-item__media" role="cell" headers="CartDrawer-ColumnProductImage">
                          {% if item.image %}
                            {% comment %} Leave empty space due to a:empty CSS display: none rule {% endcomment %}
                            <a href="{{ item.url }}" class="cart-item__link" tabindex="-1" aria-hidden="true"> </a>
                            <img class="cart-item__image"
                              src="{{ item.image | image_url: width: 300 }}"
                              alt="{{ item.image.alt | escape }}"
                              loading="lazy"
                              width="150"
                              height="{{ 150 | divided_by: item.image.aspect_ratio | ceil }}"
                            >
                          {% endif %}
                        </td>

                        <td class="cart-item__details" role="cell" headers="CartDrawer-ColumnProduct">
                          
                           <a href="{{ item.url }}" class="cart-item__name h4 break">{{ item.product.title | escape }}</a>

                          <div class="cart-item__totals right" role="cell" headers="CartDrawer-ColumnTotal">
                         

                          <div class="cart-item__price-wrapper">
                            {%- if item.original_line_price != item.final_line_price -%}
                              <div class="cart-item__discounted-prices">
                                <span class="visually-hidden">
                                  {{ 'products.product.price.regular_price' | t }}
                                </span>
                                <s class="cart-item__old-price price price--end">
                                  {{ item.original_line_price | money }}
                                </s>
                                <span class="visually-hidden">
                                  {{ 'products.product.price.sale_price' | t }}
                                </span>
                                <span class="price price--end">
                                  {{ item.final_line_price | money }}
                                </span>
                              </div>
                            {%- else -%}
                              <span class="price price--end">
                                {{ item.original_line_price | money }}
                              </span>
                            {%- endif -%}

                            {%- if item.variant.available and item.unit_price_measurement -%}
                              <div class="unit-price caption">
                                <span class="visually-hidden">{{ 'products.product.price.unit_price' | t }}</span>
                                {{ item.variant.unit_price | money }}
                                <span aria-hidden="true">/</span>
                                <span class="visually-hidden">&nbsp;{{ 'accessibility.unit_price_separator' | t }}&nbsp;</span>
                                {%- if item.variant.unit_price_measurement.reference_value != 1 -%}
                                  {{- item.variant.unit_price_measurement.reference_value -}}
                                {%- endif -%}
                                {{ item.variant.unit_price_measurement.reference_unit }}
                              </div>
                            {%- endif -%}
                          </div>
                        </div>

                          {%- if item.product.has_only_default_variant == false or item.properties.size != 0 or item.selling_plan_allocation != nil -%}
                            <dl>
                              {%- if item.product.has_only_default_variant == false -%}
                                {%- for option in item.options_with_values -%}
                                  <div class="product-option {{ option.name }}">
                                    <dt>{{ option.name }}: </dt>
                                    <dd>{{ option.value }}{% unless forloop.last %} {% endunless %}</dd>
                                  </div>
                                {%- endfor -%}
                              {%- endif -%}

                              {%- for property in item.properties -%}
                                {%- assign property_first_char = property.first | slice: 0 -%}
                                {%- if property.last != blank and property_first_char != '_' -%}
                                  <div class="product-option">
                                    <dt>{{ property.first }}: </dt>
                                    <dd>
                                      {%- if property.last contains '/uploads/' -%}
                                        <a href="{{ property.last }}" class="link" target="_blank" aria-describedby="a11y-new-window-message">
                                          {{ property.last | split: '/' | last }}
                                        </a>
                                      {%- else -%}
                                        {{ property.last }}
                                      {%- endif -%}
                                    </dd>
                                  </div>
                                {%- endif -%}
                              {%- endfor -%}
                            </dl>

                            <p class="product-option">{{ item.selling_plan_allocation.selling_plan.name }}</p>
                          {%- endif -%}

                          <ul class="discounts list-unstyled" role="list" aria-label="{{ 'customer.order.discount' | t }}">
                            {%- for discount in item.discounts -%}
                              <li class="discounts__discount">
                                {%- render 'icon-discount' -%}
                                {{ discount.title }}
                              </li>
                            {%- endfor -%}
                          </ul>
                         <div class="cart-item__quantity" role="cell" headers="CartDrawer-ColumnQuantity">
                          <div class="cart-item__quantity-wrapper">
                            <quantity-input class="quantity">
                              <button class="quantity__button no-js-hidden" name="minus" type="button">
                                <span class="visually-hidden">{{ 'products.product.quantity.decrease' | t: product: item.product.title | escape }}</span>
                                {% render 'icon-minus' %}
                              </button>
                              <input class="quantity__input"
                                type="number"
                                name="updates[]"
                                value="{{ item.quantity }}"
                                min="0"
                                aria-label="{{ 'products.product.quantity.input_label' | t: product: item.product.title | escape }}"
                                id="Drawer-quantity-{{ item.index | plus: 1 }}"
                                data-index="{{ item.index | plus: 1 }}"
                              >
                              <button class="quantity__button no-js-hidden" name="plus" type="button">
                                <span class="visually-hidden">{{ 'products.product.quantity.increase' | t: product: item.product.title | escape }}</span>
                                {% render 'icon-plus' %}
                              </button>
                            </quantity-input>

                            
                          </div>

                          <div id="CartDrawer-LineItemError-{{ item.index | plus: 1 }}" class="cart-item__error" role="alert">
                            <small class="cart-item__error-text"></small>
                            <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-error" viewBox="0 0 13 13">
                              <circle cx="6.5" cy="6.50049" r="5.5" stroke="white" stroke-width="2"/>
                              <circle cx="6.5" cy="6.5" r="5.5" fill="#EB001B" stroke="#EB001B" stroke-width="0.7"/>
                              <path d="M5.87413 3.52832L5.97439 7.57216H7.02713L7.12739 3.52832H5.87413ZM6.50076 9.66091C6.88091 9.66091 7.18169 9.37267 7.18169 9.00504C7.18169 8.63742 6.88091 8.34917 6.50076 8.34917C6.12061 8.34917 5.81982 8.63742 5.81982 9.00504C5.81982 9.37267 6.12061 9.66091 6.50076 9.66091Z" fill="white"/>
                              <path d="M5.87413 3.17832H5.51535L5.52424 3.537L5.6245 7.58083L5.63296 7.92216H5.97439H7.02713H7.36856L7.37702 7.58083L7.47728 3.537L7.48617 3.17832H7.12739H5.87413ZM6.50076 10.0109C7.06121 10.0109 7.5317 9.57872 7.5317 9.00504C7.5317 8.43137 7.06121 7.99918 6.50076 7.99918C5.94031 7.99918 5.46982 8.43137 5.46982 9.00504C5.46982 9.57872 5.94031 10.0109 6.50076 10.0109Z" fill="white" stroke="#EB001B" stroke-width="0.7">
                            </svg>
                          </div>
                           <cart-remove-button id="CartDrawer-Remove-{{ item.index | plus: 1 }}" data-index="{{ item.index | plus: 1 }}">
                              <button class="button button--tertiary" aria-label="{{ 'sections.cart.remove_title' | t: title: item.title }}">
                                {% render 'icon-close' %}
                              </button>
                          </cart-remove-button>
                         </div>         
                        </td>
                      </tr>
                    {%- endfor -%}
                  </tbody>
                </table>
              </div>
            {%- endif -%}
            <p id="CartDrawer-LiveRegionText" class="visually-hidden" role="status"></p>
            <p id="CartDrawer-LineItemStatus" class="visually-hidden" aria-hidden="true" role="status">{{ 'accessibility.loading' | t }}</p>
          </div>
          <div id="CartDrawer-CartErrors" role="alert"></div>
        </form>
      </cart-drawer-items>
      {%  render 'cart-drawer-collection' %}   
  </div>
     
                <div class="drawer__footer {% if cart == empty %}is-empty{% endif %}">
    <div class="cart-group">
        {%- if settings.show_cart_note -%}         
        <details id="Details-CartDrawer">
        <summary>
        {% render 'icon-note' %}   
        <span class="summary__title">
        {{ 'sections.cart.note' | t }}
        </span>        
        </summary>  
        <div class="cart-drawer-detail">                          
        <cart-note class="cart__note field drawer-details">
        <button type="button" class="close link" onclick="this.closest('details').querySelector('summary').click()">
        {%  render 'icon-close' %}
        </button>
        <label class="visually-hidden" for="CartDrawer-Note">{{ 'sections.cart.note' | t }}</label>
        <textarea id="CartDrawer-Note" class="text-area text-area--resize-vertical field__input" name="note" placeholder="{{ 'sections.cart.note_text' | t }}">{{ cart.note }}</textarea>
        </cart-note>  
          </div>
        </details>
        {%- endif -%}
        
        {%- if settings.show_gift_wrap and linklists.gift-wrapping.links.size > 0 -%}         
        <details id="Gift-Wrapper">
        <summary>
        {% render 'icon-gift-wrap' %}    
        <span class="summary__title">{{ 'sections.cart.gift_wrap' | t }}</span>
        </summary>
        {% render 'gift-wrapping' %} 
        </details>
        {%- endif -%}
        
        {%- if settings.show_shipping_info -%}           
        <details id="Shipping-Wrapper">
        <summary>
        {% render 'icon-free-shipping' %}
        <span class="summary__title">{{ 'sections.cart.shipping' | t }} </span>
        </summary>
        {% render 'shipping-calculator' %}
        </details>
        {%- endif -%}
        
        {%- if settings.show_discount_option -%}         
        <details id="Discount-Wrapper">            
        <summary>
        {% render 'icon-coupon' %}
        <span class="summary__title">{{ 'sections.cart.discount_option' | t }}</span>
        </summary>
         <div class="cart-drawer-detail">                         
        <div class="drawer-details">                      
        <button type="button" class="close link" onclick="this.closest('details').querySelector('summary').click()">
        {%  render 'icon-close' %}
        </button>
        <input type="text" id="drawer-discount" name="discount" placeholder="Enter discount code..."  class="discount-code" value="">   
        <button type="button" class="save button" onclick="saveOption();">SAVE</button>
        </div>
        </div>                          
        </details>
        <script>
        function saveOption() { 
        const element = document.getElementById("drawer-discount");
        const name = $("input[name=discount]").val();  
        if(name != ""){               
        localStorage.setItem("coupon", name);    
        var savedCode = localStorage.getItem('coupon')        
        }
        }
        </script>  
        {%- endif -%}
          </div>
        <!-- Start blocks-->
        <!-- Subtotals-->

        <div class="cart-drawer__footer" {{ block.shopify_attributes }}>
          <div class="totals" role="status">
            <h2 class="totals__subtotal">{{ 'sections.cart.subtotal' | t }}</h2>
            <p class="totals__subtotal-value">{{ cart.total_price | money }}</p>
          </div>

          <div>
            {%- if cart.cart_level_discount_applications.size > 0 -%}
              <ul class="discounts list-unstyled" role="list" aria-label="{{ 'customer.order.discount' | t }}">
                {%- for discount in cart.cart_level_discount_applications -%}
                  <li class="discounts__discount discounts__discount--end">
                    {%- render 'icon-discount' -%}
                    {{ discount.title }}
                    (-{{ discount.total_allocated_amount | money }})
                  </li>
                {%- endfor -%}
              </ul>
            {%- endif -%}
          </div>

          <small class="tax-note caption-large rte">
            {%- if cart.taxes_included and shop.shipping_policy.body != blank -%}
              {{ 'sections.cart.taxes_included_and_shipping_policy_html' | t: link: shop.shipping_policy.url }}
            {%- elsif cart.taxes_included -%}
              {{ 'sections.cart.taxes_included_but_shipping_at_checkout' | t }}
            {%- elsif shop.shipping_policy.body != blank -%}
              {{ 'sections.cart.taxes_and_shipping_policy_at_checkout_html' | t: link: shop.shipping_policy.url }}
            {%- else -%}
              {{ 'sections.cart.taxes_and_shipping_at_checkout' | t }}
            {%- endif -%}
          </small>
        </div>

        <!-- CTAs -->

        <div class="cart__ctas" {{ block.shopify_attributes }}>
          <noscript>
            <button type="submit" class="cart__update-button button button--secondary" form="CartDrawer-Form">
              {{ 'sections.cart.update' | t }}
            </button>
          </noscript>
           <button type="submit" id="CartDrawer-Checkout" class="cart__checkout-button button" name="checkout" form="CartDrawer-Form"{% if cart == empty %} disabled{% endif %}>
            {{ 'sections.cart.checkout' | t }}
          </button>                      
           <a href="{{ routes.cart_url }}" type="submit" id="CartDrawer-Cart" class="cart__view_cart-button link" name="viewcart" form="CartDrawer-Form"{% if cart == empty %} disabled{% endif %}>
                                  {{'sections.cart.view_cart' | t}}
          </a>                    
        </div>
        </div>     
    </div>
  </div>
</cart-drawer>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    function isIE() {
      const ua = window.navigator.userAgent;
      const msie = ua.indexOf('MSIE ');
      const trident = ua.indexOf('Trident/');

      return (msie > 0 || trident > 0);
    }

    if (!isIE()) return;
    const cartSubmitInput = document.createElement('input');
    cartSubmitInput.setAttribute('name', 'checkout');
    cartSubmitInput.setAttribute('type', 'hidden');
    document.querySelector('#cart').appendChild(cartSubmitInput);
    document.querySelector('#checkout').addEventListener('click', function(event) {
      document.querySelector('#cart').submit();
    });
  });
  
</script>

