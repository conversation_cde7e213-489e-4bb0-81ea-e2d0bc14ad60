window?.otsb?.loadedScript||(window.otsb={loadedScript:[]}),window.otsb.loadedScript.includes("otsb-product-cart.min.js")||(window.otsb.loadedScript.push("otsb-product-cart.min.js"),requestAnimationFrame((()=>{document.addEventListener("alpine:init",(()=>{Alpine.data("xProductCart",(()=>({loading:!1,errorMessage:!1,buttonSubmit:"",addToCart(e,t){e.preventDefault();let r=new FormData(this.$refs.product_form);this.loading=!0,r.append("sections_url",window.location.pathname),fetch("/cart/add",{method:"POST",headers:{Accept:"application/javascript","X-Requested-With":"XMLHttpRequest"},body:r}).then((e=>e.json())).then((e=>{if("422"==e.status)return this.errorMessage=!0,void(this.$refs.error_message&&(this.$refs.error_message.textContent=e.description));{const r=t.parentNode.querySelector(".mess_success");r.innerHTML="You added ["+e.product_title+"] to your shopping cart",setTimeout((()=>{r.innerHTML=""}),1e4),this.Updateheader(),Alpine.store("xQuickView").show&&(Alpine.store("xQuickView").show=!1)}})).catch((e=>{console.error("Error:",e)})).finally((()=>{this.loading=!1}))},Updateheader(){const e=document.querySelector(".header");var t=`${Shopify.routes.root}`;fetch(t).then((e=>{if(!e.ok){var t=new Error(e.status);throw console.log(t),t}return e.text()})).then((t=>{const r=(new DOMParser).parseFromString(t,"text/html").querySelector(".header").innerHTML;e.innerHTML=r})).catch((e=>{throw e}))}})))}))})));