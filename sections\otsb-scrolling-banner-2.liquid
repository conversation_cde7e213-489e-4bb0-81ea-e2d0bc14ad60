{%  render 'otsb-scrolling-banner-base' %}

{% schema %}
{
  "name": "OT: Scrolling Promo #2",
  "class": "otsb__root section section-scroll-banner x-section otsb-v1 otsb-v2 otsb-v3",
  "tag": "section",
  "disabled_on": {
    "groups": [
      "header",
      "footer",
      "aside"
    ]
  },
  "settings": [
    {
      "type": "checkbox",
      "id": "show_border",
      "default": true,
      "label": "Show section borders"
    },
    {
      "type": "header",
      "content": "Scrolling"
    },
    {
      "type": "range",
      "id": "speed",
      "min": 6,
      "max": 200,
      "step": 2,
      "unit": "s",
      "label": "Speed",
      "default": 10
    },
    {
      "type": "select",
      "id": "direction",
      "options": [
        {
          "value": "left",
          "label": "Left to right"
        },
        {
          "value": "right",
          "label": "Right to left"
        }
      ],
      "default": "right",
      "label": "Direction"
    },
    {
      "type": "checkbox",
      "id": "pause_hover",
      "default": true,
      "label": "Pause on hover"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "bg_light",
      "label": "Background",
      "default": "#f2f2f2"
    },
    {
      "type": "color",
      "id": "color_text_light",
      "label": "Primary",
      "default": "#f96f96"
    },
    {
      "type": "color",
      "id": "color_text_light_2",
      "label": "Secondary",
      "default": "#32355d"
    },
    {
      "type": "color",
      "id": "color_line",
      "label": "Line and borders",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "header",
      "content": "Desktop layout"
    },
    {
      "type": "checkbox",
      "id": "full_width",
      "default": true,
      "label": "Content full width"
    },
    {
      "type": "range",
      "id": "content_max_width",
      "min": 1000,
      "max": 1600,
      "step": 100,
      "unit": "px",
      "label": "Content max width",
      "default": 1200
    },
    {
      "type": "range",
      "id": "space_block",
      "min": 10,
      "max": 150,
      "step": 5,
      "unit": "px",
      "label": "Block spacing",
      "default": 45
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 12
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 12
    },
    {
      "type": "header",
      "content": "Mobile layout"
    },
    {
      "type": "range",
      "id": "space_block_mobile",
      "min": 10,
      "max": 150,
      "step": 5,
      "unit": "px",
      "label": "Block spacing",
      "default": 45
    },
    {
      "type": "range",
      "id": "padding_top_mobile",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 12
    },
    {
      "type": "range",
      "id": "padding_bottom_mobile",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 12
    }
  ],
  "max_blocks": 16,
  "blocks": [
    {
      "type": "icon",
      "name": "Icon",
      "settings": [
        {
          "type": "select",
          "id": "icon",
          "options": [
            {
              "value": "none",
              "label": "none"
            },
            {
              "value": "another_icon",
              "label": "Another icon"
            },
            {
              "value": "activity",
              "label": "Activity"
            },
            {
              "value": "archive",
              "label": "Archive"
            },
            {
              "value": "arrow-down-cricle",
              "label": "Arrow down cricle"
            },
            {
              "value": "arrow-left",
              "label": "Arrow left"
            },
            {
              "value": "arrow-left-circle",
              "label": "Arrow left circle"
            },
            {
              "value": "arrow-right",
              "label": "Arrow right"
            },
            {
              "value": "arrow-right-circle",
              "label": "Arrow right circle"
            },
            {
              "value": "arrow-up-circle",
              "label": "Arrow up circle"
            },
            {
              "value": "chevron-left",
              "label": "Chevron left"
            },
            {
              "value": "trending-down",
              "label": "Trending down"
            },
            {
              "value": "tv",
              "label": "Tv"
            },
            {
              "value": "trending-up",
              "label": "Trending up"
            },
            {
              "value": "zap",
              "label": "Zap"
            },
            {
              "value": "1st-medal",
              "label": "1st medal"
            },
            {
              "value": "award",
              "label": "Award"
            },
            {
              "value": "bicycle",
              "label": "Bicycle"
            },
            {
              "value": "box",
              "label": "Box"
            },
            {
              "value": "briefcase",
              "label": "Briefcase"
            },
            {
              "value": "blink",
              "label": "Blink"
            },
            {
              "value": "calendar",
              "label": "Calendar"
            },
            {
              "value": "camera",
              "label": "Camera"
            },
            {
              "value": "chat-bubble",
              "label": "Chat bubble"
            },
            {
              "value": "check-mark",
              "label": "Check mark"
            },
            {
              "value": "clock",
              "label": "Clock"
            },
            {
              "value": "cloud-rain",
              "label": "Cloud rain"
            },
            {
              "value": "coffee",
              "label": "Coffee"
            },
            {
              "value": "coin",
              "label": "Coin"
            },
            {
              "value": "credit-card",
              "label": "Credit card"
            },
            {
              "value": "delivery-truck",
              "label": "Delivery truck"
            },
            {
              "value": "dollar-sign",
              "label": "Dollar sign"
            },
            {
              "value": "dna",
              "label": "Dna"
            },
            {
              "value": "dark-mode",
              "label": "Dark mode"
            },
            {
              "value": "earth",
              "label": "Earth"
            },
            {
              "value": "eye",
              "label": "Eye"
            },
            {
              "value": "feather",
              "label": "Feather"
            },
            {
              "value": "fire",
              "label": "Fire"
            },
            {
              "value": "flower",
              "label": "Flower"
            },
            {
              "value": "free-delivery",
              "label": "Free delivery"
            },
            {
              "value": "gift",
              "label": "Gift"
            },
            {
              "value": "globe",
              "label": "Globe"
            },
            {
              "value": "heart",
              "label": "Heart"
            },
            {
              "value": "help",
              "label": "Help"
            },
            {
              "value": "hot-sale",
              "label": "Hot sale"
            },
            {
              "value": "iron",
              "label": "Iron"
            },
            {
              "value": "information",
              "label": "Infomation"
            },
            {
              "value": "leaf",
              "label": "Leaf"
            },
            {
              "value": "lock",
              "label": "Lock"
            },
            {
              "value": "light-mode",
              "label": "Light mode"
            },
            {
              "value": "map-pin",
              "label": "Map pin"
            },
            {
              "value": "megaphone",
              "label": "Megaphone"
            },
            {
              "value": "message-text",
              "label": "Message text"
            },
            {
              "value": "music",
              "label": "Music"
            },
            {
              "value": "moon",
              "label": "Moon"
            },
            {
              "value": "packages",
              "label": "Packages"
            },
            {
              "value": "pants",
              "label": "Pants"
            },
            {
              "value": "percent",
              "label": "Percent"
            },
            {
              "value": "piggy-bank",
              "label": "Piggy bank"
            },
            {
              "value": "plane",
              "label": "Plane"
            },
            {
              "value": "planet",
              "label": "Planet"
            },
            {
              "value": "question-mark",
              "label": "Question mark"
            },
            {
              "value": "rocket",
              "label": "Rocket"
            },
            {
              "value": "rulers",
              "label": "Rulers"
            },
            {
              "value": "scissors",
              "label": "Scissors"
            },
            {
              "value": "settings",
              "label": "Settings"
            },
            {
              "value": "shirt",
              "label": "Shirt"
            },
            {
              "value": "shop-alt",
              "label": "Shop alt"
            },
            {
              "value": "shopping-bag",
              "label": "Shopping bag"
            },
            {
              "value": "shopping-cart",
              "label": "Shopping cart"
            },
            {
              "value": "smile",
              "label": "Smile"
            },
            {
              "value": "star",
              "label": "Star"
            },
            {
              "value": "sun",
              "label": "Sun"
            },
            {
              "value": "support",
              "label": "Support"
            },
            {
              "value": "tag",
              "label": "Tag"
            },
            {
              "value": "telephone",
              "label": "Telephone"
            },
            {
              "value": "truck",
              "label": "Truck"
            },
            {
              "value": "wallet",
              "label": "Wallet"
            },
            {
              "value": "washing",
              "label": "Washing"
            },
            {
              "value": "water",
              "label": "Water"
            },
            {
              "value": "yoga",
              "label": "Yoga"
          },
        ],
          "default": "delivery-truck",
          "label": "Icon"
        },
        {
          "type": "text",
          "id": "another_icon",
          "label": "Use another icon",
          "info": "If you want to use different icons from the options above, look for an icon from our [icon list](https://support.omnithemes.com/blogs/user-guide/theme-icons) and fill the icon name here (E.g: price tag)."
        },
        {
          "type": "html",
          "id": "custom_icon",
          "label": "Custom icon (SVG code)",
          "info": "For assistance with custom SVG code or fixing issues that arise from custom SVG code, please [contact our support](https://support.bsscommerce.com/support/tickets/new)."
        },
        {
          "type": "range",
          "id": "height_icon",
          "min": 20,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Icon height",
          "default": 20
        },
        {
          "type": "radio",
          "id": "icon_color",
          "label": "Icon color",
          "default": "primary",
          "options": [
            {
              "value": "primary",
              "label": "Primary color"
            },
            {
              "value": "secondary",
              "label": "Secondary color"
            }
          ]
        },
        {
          "type": "header",
          "content": "Desktop layout"
        },
        {
          "type": "range",
          "id": "spacing_left",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "px",
          "label": "Left spacing",
          "default": 0
        },
        {
          "type": "range",
          "id": "spacing_right",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "px",
          "label": "Right spacing",
          "default": 0
        },
        {
          "type": "header",
          "content": "Mobile layout"
        },
        {
          "type": "range",
          "id": "spacing_left_mobile",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "px",
          "label": "Left spacing",
          "default": 0
        },
        {
          "type": "range",
          "id": "spacing_right_mobile",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "px",
          "label": "Right spacing",
          "default": 0
        }
      ]
    },
    {
      "type": "text",
      "name": "Text",
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "default": "<p>Free Shipping</p>",
          "label": "Text"
        },
        {
          "type": "checkbox",
          "id": "use_custom_font",
          "label": "Use custom font",
          "default": false
        },
        {
          "type": "font_picker",
          "id": "text_custom_font",
          "label": "Custom font",
          "default": "instrument_sans_n4"
        },
        {
          "type": "range",
          "id": "text_size",
          "min": 50,
          "max": 200,
          "unit": "%",
          "step": 10,
          "default": 100,
          "label": "Text size"
        },
        {
          "type": "radio",
          "id": "text_color",
          "label": "Text color",
          "default": "secondary",
          "options": [
            {
              "value": "primary",
              "label": "Primary color"
            },
            {
              "value": "secondary",
              "label": "Secondary color"
            }
          ]
        },
        {
          "type": "color",
          "id": "color_link",
          "label": "Text link",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "header",
          "content": "Desktop layout"
        },
        {
          "type": "range",
          "id": "spacing_left",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "px",
          "label": "Left spacing",
          "default": 0
        },
        {
          "type": "range",
          "id": "spacing_right",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "px",
          "label": "Right spacing",
          "default": 0
        },
        {
          "type": "header",
          "content": "Mobile layout"
        },
        {
          "type": "range",
          "id": "spacing_left_mobile",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "px",
          "label": "Left spacing",
          "default": 0
        },
        {
          "type": "range",
          "id": "spacing_right_mobile",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "px",
          "label": "Right spacing",
          "default": 0
        }
      ]
    },
    {
      "type": "image",
      "name": "Image",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "select",
          "id": "image_ratio",
          "options": [
            {
              "value": "natural",
              "label": "Natural"
            },
            {
              "value": "100",
              "label": "Square (1:1)"
            },
            {
              "value": "75",
              "label": "Landscape (4:3)"
            },
            {
              "value": "56",
              "label": "Wide (16:9)"
            },
            {
              "value": "133",
              "label": "Standard (3:4)"
            }
          ],
          "default": "natural",
          "label": "Image ratio"
        },
        {
          "type": "range",
          "id": "height_image",
          "min": 20,
          "max": 150,
          "step": 5,
          "unit": "px",
          "label": "Image height",
          "default": 80
        },
        {
          "type": "select",
          "id": "edges_type",
          "label": "Edges",
          "options": [
            {
              "value": "square",
              "label": "Square"
            },
            {
              "value": "rounded_corners",
              "label": "Rounded corners"
            }
          ],
          "default": "rounded_corners"
        },
        {
          "type": "header",
          "content": "Desktop layout"
        },
        {
          "type": "range",
          "id": "spacing_left",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "px",
          "label": "Left spacing",
          "default": 0
        },
        {
          "type": "range",
          "id": "spacing_right",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "px",
          "label": "Right spacing",
          "default": 0
        },
        {
          "type": "header",
          "content": "Mobile layout"
        },
        {
          "type": "range",
          "id": "spacing_left_mobile",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "px",
          "label": "Left spacing",
          "default": 0
        },
        {
          "type": "range",
          "id": "spacing_right_mobile",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "px",
          "label": "Right spacing",
          "default": 0
        }
      ]
    },
    {
      "type": "button",
      "name": "Button",
      "settings": [
        {
          "type": "text",
          "id": "button_label",
          "default": "Button label",
          "label": "Button label"
        },
        {
          "type": "url",
          "id": "button_link",
          "label": "Button link"
        },
        {
          "type": "checkbox",
          "id": "open_new_window",
          "default": false,
          "label": "Open this link in a new window"
        },
        {
          "type": "checkbox",
          "id": "primary_button",
          "default": true,
          "label": "Show as primary button"
        },
        {
          "type": "header",
          "content": "Colors"
        },
        {
          "type": "color",
          "id": "color_button",
          "label": "Button",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "color_button_hover",
          "label": "Button hover",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "color_text_button",
          "label": "Button text",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "color_text_button_hover",
          "label": "Button text hover",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "color_button_secondary",
          "label": "Secondary button",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "secondary_button_text",
          "default": "rgba(0,0,0,0)",
          "label": "Secondary button text"
        },
        {
          "type": "header",
          "content": "Button Design"
        },
        {
          "type": "select",
          "id": "button_type",
          "options": [
            {
              "value": "square",
              "label": "Square"
            },
            {
              "value": "rounded",
              "label": "Rounded"
            },
            {
              "value": "rounded_corners",
              "label": "Rounded Corners"
            },
            {
              "value": "mixed",
              "label": "Mixed"
            }
          ],
          "label": "Button style",
          "default": "square"
        },
        {
          "type": "select",
          "id": "button_color_mobile",
          "options": [
            {
              "value": "hover",
              "label": "Use button hover color"
            },
            {
              "value": "color",
              "label": "Use button color"
            }
          ],
          "label": "Mobile primary button style",
          "default": "color"
        },
        {
          "type": "select",
          "id": "button_animation",
          "options": [
            {
              "value": "slide",
              "label": "Slide"
            },
            {
              "value": "fill_up",
              "label": "Fill up"
            },
            {
              "value": "sliced",
              "label": "Sliced with icon"
            },
            {
              "value": "underline",
              "label": "Underline"
            }
          ],
          "label": "Primary button hover animation",
          "default": "fill_up"
        },
        {
          "type": "html",
          "id": "custom_icon_button",
          "label": "Custom icon (SVG code)",
          "info": "Applies to 'Sliced with icon' animation type. For assistance with custom SVG code or fixing issues that arise from custom SVG code, please [contact our support](https://support.bsscommerce.com/support/tickets/new)."
        },
        {
          "type": "header",
          "content": "Desktop layout"
        },
        {
          "type": "range",
          "id": "spacing_left",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "px",
          "label": "Left spacing",
          "default": 0
        },
        {
          "type": "range",
          "id": "spacing_right",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "px",
          "label": "Right spacing",
          "default": 0
        },
        {
          "type": "header",
          "content": "Mobile layout"
        },
        {
          "type": "range",
          "id": "spacing_left_mobile",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "px",
          "label": "Left spacing",
          "default": 0
        },
        {
          "type": "range",
          "id": "spacing_right_mobile",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "px",
          "label": "Right spacing",
          "default": 0
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "OT: Scrolling Promo #2",
      "blocks": [
        {
          "type": "text"
        },
        {
          "type": "icon"
        }
      ]
    }
  ]
}
{% endschema %}
