.list-social {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
 margin-top: 3.5rem;
  column-gap:10px;
}

@media only screen and (max-width: 749px) {
  .list-social {
    justify-content: center;
  }
}

.list-social__item .icon {
  height: 1.8rem;
  width: 1.8rem;
  transition: all var(--duration-default) linear;
}

.list-social__link {
    align-items: center;
    display: flex;
    padding: 1.3rem 0;
    color: var(--gradient-base-accent-1);
    font-weight: 600;
    text-decoration: none;
    width: 30px;
    height: 30px;
    background: var(--gradient-base-background-1);
    border-radius: 50%;
    justify-content: center;
}

.list-social__link:hover .icon {
  /* transform: scale(1.07); */
  color: rgb(var(--color-base-outline-button-labels));
}
