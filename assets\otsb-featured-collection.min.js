(()=>{var r=(i,d,a)=>({sectionId:i,pageParam:d,currentTab:1,loading:!0,loaded:[],showCollection:!1,select(e){this.currentTab=e},loadData(e){let t=e-1;if(!this.loaded.includes(t)){this.loading=!0;let n=`${window.location.pathname}?section_id=${this.sectionId}&${this.pageParam}=${e}`;fetch(n,{method:"GET"}).then(l=>l.text()).then(l=>{let c=new DOMParser().parseFromString(l,"text/html"),o=`x-fc-${this.sectionId}-${e}`;Shopify.designMode&&document.getElementById(o)&&document.getElementById(o).remove();let s=c.getElementById(o);s&&!document.getElementById(o)&&(a.appendChild(s),this.loaded.push(t)),this.loading=!1})}},scrollIntoView(e){let t=e.closest(".overflow-auto"),n=e.offsetLeft;t.scroll({left:n,behavior:"smooth"})}});window.otsb.loadedScript.includes("otsb-featured-collection.js")||(window.otsb.loadedScript.push("otsb-featured-collection.js"),document.addEventListener("alpine:init",()=>Alpine.data("xFeaturedCollection",r)));})();
