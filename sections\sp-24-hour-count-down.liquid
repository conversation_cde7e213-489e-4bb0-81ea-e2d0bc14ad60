{% comment %}
  © Sections Pro. You are free to use this section in your store. You may not redistribute this section in another Shopify app.
{% endcomment %}
<style>

  

  {%- capture sp_content -%} 

  {% if section.settings.override_fonts %}
      {{ section.settings.text_font | font_face }}
      {{ section.settings.headline_font | font_face }}
  {% endif %}

  #spro-{{ section.id }} p {
      {% if section.settings.override_text_sizes %}
      font-size: {{ section.settings.text_size }}px;
      {% endif %}
      {% if section.settings.override_fonts %}
      font-family: {{ section.settings.text_font.family }}, {{ section.settings.text_font.fallback_families }};
      font-weight: {{ section.settings.text_font.weight }};
      {% endif %}
      {% if section.settings.text_line_height != 'inherit' %}line-height: {{ section.settings.text_line_height }};{% endif %}
      margin: 0 0 {{ section.settings.element_spacing }}px 0;
      padding: 0;
      {% if section.settings.override_text_colors %}
      color: {{ section.settings.text_color }};
      {% endif %}
  }

  #spro-{{ section.id }} div.spro-richtext {
    margin: 0 0 {{ section.settings.element_spacing }}px 0;
  }

  #spro-{{ section.id }} ul, #spro-{{ section.id }} ol {
    margin: 0 0 {{ section.settings.element_spacing }}px 0;
  }

  #spro-{{ section.id }} li {
    {% if section.settings.override_text_sizes %}
    font-size: {{ section.settings.text_size }}px;
    {% endif %}
    {% if section.settings.override_fonts %}
    font-family: {{ section.settings.text_font.family }}, {{ section.settings.text_font.fallback_families }};
    font-weight: {{ section.settings.text_font.weight }};
    {% endif %}
    {% if section.settings.text_line_height != 'inherit' %}line-height: {{ section.settings.text_line_height }};{% endif %}
    margin: 0 0 5px 0;
    padding: 0;
    {% if section.settings.override_text_colors %}
    color: {{ section.settings.text_color }};
    {% endif %}
  }

  #spro-{{ section.id }} li:last-child {
    margin: 0;
  }

  #spro-{{ section.id }} p a,
  #spro-{{ section.id }} p a:visited
  #spro-{{ section.id }} li a,
  #spro-{{ section.id }} li a:visited {
    {% if section.settings.override_text_colors %}
    color: {{ section.settings.link_color }};
    {% endif %}
  }

  @media (max-width: 767px) {
      #spro-{{ section.id }} p,
      #spro-{{ section.id }} li {
        {% if section.settings.override_text_sizes %}
        font-size: {{ section.settings.mobile_text_size }}px;
        {% endif %}
      }
  }

  #spro-{{ section.id }} h1,
  #spro-{{ section.id }} h2,
  #spro-{{ section.id }} h3,
  #spro-{{ section.id }} h4,
  #spro-{{ section.id }} h5 {
      {% if section.settings.override_text_sizes %}
      font-size: {{ section.settings.headline_size }}px;
      {% endif %}
      {% if section.settings.override_fonts %}
      font-family: {{ section.settings.headline_font.family }}, {{ section.settings.headline_font.fallback_families }};
      font-weight: {{ section.settings.headline_font.weight }};
      {% endif %}
      {% if section.settings.headline_line_height != 'inherit' %}line-height: {{ section.settings.headline_line_height }};{% endif %}
      margin: 0 0 {{ section.settings.element_spacing }}px 0;
      padding: 0;
      {% if section.settings.override_text_colors %}
      color: {{ section.settings.text_color }};
      {% endif %}
  }

  @media (max-width: 767px) {
      #spro-{{ section.id }} h1,
      #spro-{{ section.id }} h2,
      #spro-{{ section.id }} h3,
      #spro-{{ section.id }} h4,
      #spro-{{ section.id }} h5 {
        {% if section.settings.override_text_sizes %}
          font-size: {{ section.settings.mobile_headline_size }}px;
        {% endif %}
      }
  }

  #spro-{{ section.id }} h2 {
    {% if section.settings.override_text_sizes %}
      font-size: {{ section.settings.headline_size | minus:5  | at_least:13 }}px;
    {% endif %}
  }

  #spro-{{ section.id }} h3 {
    {% if section.settings.override_text_sizes %}
      font-size: {{ section.settings.headline_size | minus:10 | at_least:13 }}px;
    {% endif %}
  }

  #spro-{{ section.id }} h4 {
    {% if section.settings.override_text_sizes %}
      font-size: {{ section.settings.headline_size | minus:15 | at_least:13 }}px;
    {% endif %}
  }

  #spro-{{ section.id }} h5 {
    {% if section.settings.override_text_sizes %}
      font-size: {{ section.settings.headline_size | minus:20 | at_least:13 }}px;
    {% endif %}
  }

  @media (max-width: 767px) {
      #spro-{{ section.id }} h2 {
        {% if section.settings.override_text_sizes %}
          font-size: {{ section.settings.mobile_headline_size | minus:5 | at_least:13 }}px;
        {% endif %}
      }

      #spro-{{ section.id }} h3 {
        {% if section.settings.override_text_sizes %}
          font-size: {{ section.settings.mobile_headline_size | minus:10 | at_least:13 }}px;
        {% endif %}
      }

      #spro-{{ section.id }} h4 {
        {% if section.settings.override_text_sizes %}
        font-size: {{ section.settings.mobile_headline_size | minus:15 | at_least:13 }}px;
      {% endif %}
      }

      #spro-{{ section.id }} h5 {
        {% if section.settings.override_text_sizes %}
          font-size: {{ section.settings.mobile_headline_size | minus:20 | at_least:13 }}px;
        {% endif %}
      }
  }

  #spro-{{ section.id }} {
      background-image: {{ section.settings.section_background_color }};
      {% if section.settings.section_background_image %}
          background: {% if section.settings.section_background_image_color %}{{ section.settings.section_background_image_color }}{%endif%} url({{ section.settings.section_background_image | image_url }});
          background-repeat: no-repeat;
          background-position: center;
          width: 100%;
          background-size: {{ section.settings.section_background_size }};
      {% endif %}
      width: 100%;
      box-sizing: border-box;
      padding: {{ section.settings.section_padding_top_bottom }}px {{ section.settings.section_padding_left_right }}px;
      overflow: hidden;
  }

  {% if section.settings.mobile_section_background_image %}
  @media (max-width: 767px) {
    #spro-{{ section.id }} {
          background: {% if section.settings.mobile_section_background_image_color %}{{ section.settings.mobile_section_background_image_color }}{%endif%} url({{ section.settings.mobile_section_background_image | image_url }});
          background-repeat: no-repeat;
          background-position: center;
          width: 100%;
          background-size: {{ section.settings.mobile_section_background_size }};
    }
  }
  {% endif %}

  @media (max-width: 767px) {
      #spro-{{ section.id }} {
        padding: {{ section.settings.mobile_section_padding_top_bottom }}px {{ section.settings.mobile_section_padding_left_right }}px;
      }
  }

  {% if section.settings.show_on_device == 'mobile' %}
    @media (min-width: 768px) {
        #spro-{{ section.id }} {
            display: none;
        }
    }
  {% endif %}

  {% if section.settings.show_on_device == 'desktop' %}
    @media (max-width: 767px) {
        #spro-{{ section.id }} {
            display: none;
        }
    }
  {% endif %}

  #spro-{{ section.id }} .spro-container {
      position: relative;
      margin: 0 auto;
      background-image: {{ section.settings.container_background_color }};
      border-radius: {{ section.settings.container_radius }}px;
      {% if section.settings.container_shadow %}box-shadow: 0 0 5px 0 rgba(0,0,0,0.20);{% endif %}
      border: {{ section.settings.container_border_size }}px solid {{ section.settings.container_border_color }};
      max-width: {{ section.settings.container_max_width }}px;
      padding: {{ section.settings.container_padding_top_bottom }}px {{ section.settings.container_padding_left_right }}px;
  }

  @media (max-width: 767px) {
      #spro-{{ section.id }} .spro-container {
      padding: {{ section.settings.mobile_container_padding_top_bottom }}px {{ section.settings.mobile_container_padding_left_right }}px;
      }
  }


  #spro-{{ section.id }} .spro-headline p {
    font-size: {{ section.settings.text_size }}px;
    {% if section.settings.override_fonts %}
    font-family: {{ section.settings.text_font.family }}, {{ section.settings.text_font.fallback_families }};
    font-weight: {{ section.settings.text_font.weight }};
    {% endif %}
    {% if section.settings.override_text_colors %}
      color: {{ section.settings.text_color }};
    {% endif %}
    line-height: 1.15;
    margin: 0;
    padding: 0 0 25px 0;
    text-align: {{ section.settings.text_alignment }};
  }

  ##spro-{{ section.id }} .spro-headline {
    margin: 0;
    padding: 0 0 {{ section.settings.headline_spacing }}px 0;
  }

  #spro-{{ section.id }} .spro-headline * {
    text-align: {{ section.settings.text_alignment }};
  }

  @media only screen and (max-width: 800px) {
    #spro-{{ section.id }} .spro-headline * {
      text-align: {{ section.settings.mobile_text_alignment }};
    }
  }

  #spro-{{ section.id }} .spro-grid {
    display: grid;
    margin: 0 auto;
    grid-auto-columns: {{ section.settings.item_width }}px;
    grid-auto-flow: column;
    gap: {{ section.settings.grid_gap }}px;

    {% if section.settings.timer_alignment == 'left' %}
      justify-content: start;
    {% endif %}
    {% if section.settings.timer_alignment == 'center' %}
      justify-content: center;
    {% endif %}
    {% if section.settings.timer_alignment == 'right' %}
      justify-content: end;
    {% endif %}

    
  }

  @media only screen and (max-width: 800px) {
    #spro-{{ section.id }} .spro-grid {
      display: grid;
      grid-auto-columns: 1fr;
    }
  }

  #spro-{{ section.id }} .spro-grid-item {
    background-image: {{ section.settings.countdown_background_color }};
    padding: {{ section.settings.countdown_padding }}px;
    text-align: center;
    border-radius: {{ section.settings.media_radius }}px;
    {% if section.settings.media_shadow %}box-shadow: 0 0 5px 0 rgba(0,0,0,0.20);{% endif %}
  }

  #spro-{{ section.id }} .spro-timer {
    {% if section.settings.override_fonts %}
    font-family: {{ section.settings.text_font.family }}, {{ section.settings.text_font.fallback_families }};
    font-weight: {{ section.settings.text_font.weight }};
    {% endif %}
    font-size: {{ section.settings.timer_size }}px;
    font-weight: bold;
    color: {{ section.settings.timer_color }};
    line-height: 1;
    text-align: center;
    display: block;
  }

  #spro-{{ section.id }} .spro-label {
    {% if section.settings.override_fonts %}
      font-family: {{ section.settings.text_font.family }}, {{ section.settings.text_font.fallback_families }};
      font-weight: {{ section.settings.text_font.weight }};
    {% endif %}
    font-size: {{ section.settings.text_size }}px;
    font-weight: bold;
    color: {{ section.settings.timer_color }};
    display: block;
    line-height: 1;
    text-align: center;
  }

  #spro-{{ section.id }} .spro-section {
    position: relative;
    padding: 10px;
    margin: 0;
  }

  #spro-{{ section.id }} .spro-section * {
    box-sizing: border-box;
  }
  
  {%- endcapture -%} 

  {%- liquid
    assign chunks = sp_content | strip_newlines | split: ' ' | join: ' ' | split: '*/'
    for chunk in chunks
      assign mini = chunk | split: '/*' | first | strip | replace: ': ', ':' | replace: '; ', ';' | replace: '} ', '}' | replace: '{ ', '{' | replace: ' {', '{' | replace: ';}', '}'
      echo mini
    endfor
  %}
</style>

<!-- start content -->
<section id="spro-{{ section.id }}" class="spro-section" spro-section>

    <section class="spro-container" spro-container>

      <div class="spro-headline">
        {% if section.settings.headline %}<h2>{{ section.settings.headline }}</h2>{% endif %}
        {% if section.settings.text %}<p>{{ section.settings.text }}</p>{% endif %}
      </div>

      <div class="spro-grid">
        <div class="spro-grid-item">
          <div class="spro-countdown">
            <span id="spro-hours" class="spro-timer">{{ section.settings.start_hours }}</span>
            <span class="spro-label">{{ section.settings.hours_text }}</span>
          </div>
        </div>
        <div class="spro-grid-item">
          <div class="spro-countdown">
            <span id="spro-minutes" class="spro-timer">00</span>
            <span class="spro-label">{{ section.settings.minutes_text }}</span>
          </div>
        </div>
        
        <div class="spro-grid-item" {% if section.settings.show_seconds == false %}style="display:none;"{% endif %}>
          <div class="spro-countdown">
            <span id="spro-seconds" class="spro-timer">00</span>
            <span class="spro-label">{{ section.settings.seconds_text }}</span>
          </div>
        </div>
        
      </div>
    </section>
  
  </section>
  <!-- end Sections.AI content -->

  <script>
    function startCountdown() {
      var hoursElement = document.getElementById('spro-hours');
      var minutesElement = document.getElementById('spro-minutes');
      var secondsElement = document.getElementById('spro-seconds');

      var countdownInterval = setInterval(function () {
        var hours = parseInt(hoursElement.innerText);
        var minutes = parseInt(minutesElement.innerText);
        var seconds = parseInt(secondsElement.innerText);

        if (hours === 0 && minutes === 0 && seconds === 0) {
          clearInterval(countdownInterval);
          return;
        }

        if (seconds === 0) {
          seconds = 59;

          if (minutes === 0) {
            minutes = 59;
            hours--;
          } else {
            minutes--;
          }
        } else {
          seconds--;
        }

        hoursElement.innerText = hours.toString().padStart(2, '0');
        minutesElement.innerText = minutes.toString().padStart(2, '0');
        if(secondsElement)secondsElement.innerText = seconds.toString().padStart(2, '0');
      }, 1000);
    }

    startCountdown();
  </script>
  <!-- end snippet code -->

  <!-- start schema -->
  {% schema %}
  {
    "name": "🚀 24 Hour Countdown",
    "settings": [
      
    {
        "type": "header",
        "content": "Font",
        "info": "Set the fonts for your section. If overriding, the theme fonts will be ignored."
    },
    {
        "type": "checkbox",
        "id": "override_fonts",
        "label": "Override theme fonts",
        "default": false
    },
    {
        "type": "font_picker",
        "id": "headline_font",
        "label": "Headline Font",
        "default": "sans-serif",
        "visible_if": "{{ section.settings.override_fonts == true }}"
    },
    {
        "type": "font_picker",
        "id": "text_font",
        "label": "Text Font",
        "default": "sans-serif",
        "visible_if": "{{ section.settings.override_fonts == true }}"
    },
    {
        "type": "header",
        "content": "Text",
        "info": "Set the text for your section. If overriding, the theme text styles will be ignored."
    },
    {
        "type": "checkbox",
        "id": "override_text_sizes",
        "label": "Override text sizes",
        "default": false
    },
    {
        "type": "range",
        "id": "text_size",
        "min": 10,
        "max": 50,
        "step": 1,
        "unit": "px",
        "label": "Text Size",
        "default": 15,
        "visible_if": "{{ section.settings.override_text_sizes == true }}"
    },
    {
        "type": "range",
        "id": "mobile_text_size",
        "min": 10,
        "max": 50,
        "step": 1,
        "unit": "px",
        "label": "Mobile Text Size",
        "default": 15,
        "visible_if": "{{ section.settings.override_text_sizes == true }}"
    },
    {
        "type": "select",
        "id": "text_line_height",
        "label": "Text Line Height",
        "options": [
            {
                "value": "0.75",
                "label": "0.75"
            },
            {
                "value": "1.0",
                "label": "1.0"
            },
            {
                "value": "1.1",
                "label": "1.1"
            },
            {
                "value": "1.2",
                "label": "1.2"
            },
            {
                "value": "1.3",
                "label": "1.3"
            },
            {
                "value": "1.4",
                "label": "1.4"
            },
            {
                "value": "1.5",
                "label": "1.5"
            },
            {
                "value": "1.6",
                "label": "1.6"
            },
            {
                "value": "1.7",
                "label": "1.7"
            },
            {
                "value": "1.8",
                "label": "1.8"
            },
            {
                "value": "1.9",
                "label": "1.9"
            },
            {
                "value": "2.0",
                "label": "2.0"
            },
            {
                "value": "inherit",
                "label": "Inherit"
            }
        ],
        "default": "inherit",
        "visible_if": "{{ section.settings.override_text_sizes == true }}"
    },
    {
        "type": "range",
        "id": "headline_size",
        "min": 10,
        "max": 75,
        "step": 1,
        "unit": "px",
        "label": "Headline Size",
        "default": 40,
        "visible_if": "{{ section.settings.override_text_sizes == true }}"
    },
    {
        "type": "range",
        "id": "mobile_headline_size",
        "min": 10,
        "max": 75,
        "step": 1,
        "unit": "px",
        "label": "Mobile Headline Size",
        "default": 40,
        "visible_if": "{{ section.settings.override_text_sizes == true }}"
    },
    {
        "type": "select",
        "id": "headline_line_height",
        "label": "Headline Line Height",
        "options": [
            {
                "value": "0.75",
                "label": "0.75"
            },
            {
                "value": "1.0",
                "label": "1.0"
            },
            {
                "value": "1.1",
                "label": "1.1"
            },
            {
                "value": "1.2",
                "label": "1.2"
            },
            {
                "value": "1.3",
                "label": "1.3"
            },
            {
                "value": "1.4",
                "label": "1.4"
            },
            {
                "value": "1.5",
                "label": "1.5"
            },
            {
                "value": "1.6",
                "label": "1.6"
            },
            {
                "value": "1.7",
                "label": "1.7"
            },
            {
                "value": "1.8",
                "label": "1.8"
            },
            {
                "value": "1.9",
                "label": "1.9"
            },
            {
                "value": "2.0",
                "label": "2.0"
            },
            {
                "value": "inherit",
                "label": "Inherit"
            }
        ],
        "default": "inherit",
        "visible_if": "{{ section.settings.override_text_sizes == true }}"
    },
    {
        "type": "checkbox",
        "id": "override_text_colors",
        "label": "Override text colors",
        "default": false
    },
    {
        "type": "color",
        "id": "text_color",
        "default": "#111",
        "label": "Text Color",
        "visible_if": "{{ section.settings.override_text_colors == true }}"
    },
    {
        "type": "color",
        "id": "link_color",
        "default": "#005bd3",
        "label": "Link Color",
        "visible_if": "{{ section.settings.override_text_colors == true }}"
    },
    {
        "type": "header",
        "content": "Section Design",
        "info": "Set the design for the section"
    },
    {
        "type": "select",
        "id": "show_on_device",
        "label": "Show Section",
        "options": [
            {
                "value": "all",
                "label": "All Devices"
            },
            {
                "value": "mobile",
                "label": "Mobile Only"
            },
            {
                "value": "desktop",
                "label": "Desktop Only"
            }
        ],
        "default": "all"
    },
    {
        "type": "color_background",
        "id": "section_background_color",
        "default": "linear-gradient(127deg, rgba(241, 246, 251, 1) 11%, rgba(241, 246, 251, 1) 81%)",
        "label": "Background Color"
    },
    {
        "type": "paragraph",
        "content": "Set the Background Image (optional)"
    },
    {
        "type": "image_picker",
        "id": "section_background_image",
        "label": "Background Image"
    },
    {
        "type": "color",
        "id": "section_background_image_color",
        "label": "Background Image Color"
    },
    {
        "type": "select",
        "id": "section_background_size",
        "default": "cover",
        "label": "Background Image Size",
        "options": [
          {
            "value": "auto",
            "label": "Auto"
          },
          {
            "value": "cover",
            "label": "Cover"
          },
          {
            "value": "contain",
            "label": "Contain"
          }
        ]
    },
    {
        "type": "paragraph",
        "content": "Set the Mobile Background Image (optional)"
    },
    {
        "type": "image_picker",
        "id": "mobile_section_background_image",
        "label": "Mobile Background Image"
    },
    {
        "type": "color",
        "id": "mobile_section_background_image_color",
        "label": "Mobile Background Image Color"
    },
    {
        "type": "select",
        "id": "mobile_section_background_size",
        "default": "cover",
        "label": "Mobile Background Image Size",
        "options": [
          {
            "value": "auto",
            "label": "Auto"
          },
          {
            "value": "cover",
            "label": "Cover"
          },
          {
            "value": "contain",
            "label": "Contain"
          }
        ]
    },
    {
        "type": "paragraph",
        "content": "Set the padding for the section"
    },
    {
        "type": "number",
        "id": "section_padding_top_bottom",
        "default": 25,
        "label": "Padding Top/Bottom"
    },
    {
        "type": "number",
        "id": "mobile_section_padding_top_bottom",
        "default": 25,
        "label": "Mobile Padding Top/Bottom"
    },
    {
        "type": "number",
        "id": "section_padding_left_right",
        "default": 25,
        "label": "Padding Left/Right"
    },
    {
        "type": "number",
        "id": "mobile_section_padding_left_right",
        "default": 25,
        "label": "Mobile Padding Left/Right"
    },
    {
        "type": "header",
        "content": "Container Design",
        "info": "Set the design for your inner container"
    },
    {
        "type": "color_background",
        "id": "container_background_color",
        "label": "Background Color"
    },
    {
        "type": "number",
        "id": "container_max_width",
        "default": 1000,
        "label": "Max Width"
    },
    {
        "type": "number",
        "id": "container_padding_top_bottom",
        "default": 10,
        "label": "Padding Top/Bottom"
    },
    {
        "type": "number",
        "id": "mobile_container_padding_top_bottom",
        "default": 10,
        "label": "Mobile Padding Top/Bottom"
    },
    {
        "type": "number",
        "id": "container_padding_left_right",
        "default": 10,
        "label": "Padding Left/Right"
    },
    {
        "type": "number",
        "id": "mobile_container_padding_left_right",
        "default": 10,
        "label": "Mobile Padding Left/Right"
    },
    {
        "type": "number",
        "id": "element_spacing",
        "default": 15,
        "label": "Spacing Between Elements"
    },
    {
        "type": "range",
        "id": "container_radius",
        "min": 0,
        "max": 50,
        "step": 1,
        "unit": "px",
        "label": "Round Borders on Container",
        "default": 0
    },
    {
        "type": "checkbox",
        "id": "container_shadow",
        "default": false,
        "label": "Subtle Shadow on Container"
    },
    {
        "type": "range",
        "id": "container_border_size",
        "min": 0,
        "max": 50,
        "step": 1,
        "unit": "px",
        "label": "Border Size on Container",
        "default": 0
    },
    {
        "type": "color",
        "id": "container_border_color",
        "default": "#888",
        "label": "Border Color on Container"
    }
,
      {
        "type": "header",
        "content": "Grid Display",
        "info": "Set the design of the grid"
      },
      {
        "type": "number",
        "id": "item_width",
        "default": 150,
        "label": "Column Width"
      },
      {
        "type": "number",
        "id": "grid_gap",
        "default": 15,
        "label": "Space between Columns"
      },
      {
        "type": "header",
        "content": "Timer Display",
        "info": "Customize the display of the timer"
      },
      {
        "type": "text_alignment",
        "id": "timer_alignment",
        "label": "Timer Alignment",
        "default": "center"
      },
      {
        "type": "range",
        "id": "timer_size",
        "min": 10,
        "max": 100,
        "step": 1,
        "unit": "px",
        "label": "Timer Size",
        "default": 60
      },
      {
        "type": "number",
        "id": "countdown_padding",
        "label": "Padding",
        "default": 20
      },
      {
        "type": "color_background",
        "id": "countdown_background_color",
        "default": "linear-gradient(164deg, #111 0%, #555 100%)",
        "label": "Countdown Background Color"
      },
      {
        "type": "color",
        "id": "timer_color",
        "default": "#FFF",
        "label": "Timer Color"
      },
      {
        "type": "number",
        "id": "start_hours",
        "label": "Start Hours Left",
        "default": 14
      },
      {
        "type": "inline_richtext",
        "id": "hours_text",
        "label": "Hours Text",
        "default": "hours"
      },
      {
        "type": "inline_richtext",
        "id": "minutes_text",
        "label": "Minutes Text",
        "default": "minutes"
      },
      {
        "type": "inline_richtext",
        "id": "seconds_text",
        "label": "Seconds Text",
        "default": "seconds"
      },
      {
        "type": "checkbox",
        "id": "show_seconds",
        "label": "Show Seconds Column",
        "default": true
      },
      {
        "type": "range",
        "id": "media_radius",
        "min": 0,
        "max": 50,
        "step": 1,
        "unit": "px",
        "label": "Round Borders on Countdown",
        "default": 5
      },
      {
        "type": "checkbox",
        "id": "media_shadow",
        "default": true,
        "label": "Subtle Shadow on Countdown"
      },
      {
        "type": "header",
        "content": "Headline",
        "info": "Set text for the headline"
      },
      {
          "type": "text_alignment",
          "id": "text_alignment",
          "label": "Text Alignment",
          "default": "center"
      },
      {
          "type": "text_alignment",
          "id": "mobile_text_alignment",
          "label": "Mobile Text Alignment",
          "default": "center"
      },
      {
        "type": "inline_richtext",
        "id": "headline",
        "label": "Headline",
        "default": "<b>Sample Headline</b>"
      },
      {
        "type": "inline_richtext",
        "id": "text",
        "label": "Text",
        "default": "Use this block to add a description. Leave blank to remove."
      },
      {
        "type": "number",
        "id": "headline_spacing",
        "default": 15,
        "label": "Spacing between Headline and Features"
      }
    ],
    "blocks": [],
    "presets": [
      {
        "name": "🚀 24 Hour Countdown"
      }
    ]
  }
  {% endschema %}
  <!-- end schema -->
  