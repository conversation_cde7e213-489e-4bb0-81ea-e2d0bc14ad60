{%- liquid
    assign enable_slider = false
    if section.blocks.size > 1
        assign enable_slider = true
    endif
    assign block_first = section.blocks | first
    assign desktop_height = section.settings.desktop_height | append: "px"
    assign mobile_height = section.settings.mobile_height | append: "px"
    if section.settings.desktop_height == "fullscreen"
        assign desktop_height = "95vh"
    endif
    if section.settings.mobile_height == "fullscreen"
        assign mobile_height = "94vh"
    endif

-%}
{%- capture styles -%}
{%- style -%}
  :root,
    *:before {
    --image-treatment-text: 255, 255, 255
    }
    .otsb__root .mobile-{{ section.id }}-natural{
    {%- if block_first and block_first.settings.image_mobile != blank -%}
        height:0;
        padding-bottom: {{ 1 | divided_by: block_first.settings.image_mobile.aspect_ratio | times: 100 }}%;
    {%- else -%}
        height: 550px;
    {%- endif -%}
    }
    #shopify-section-{{ section.id }} .highlight
    {
      color: var(--color-highlight);
    }
    #shopify-section-{{ section.id }} .hl-underline.highlight-anm-start {
        color: var(--color-highlight);
        transition: color 0.3s cubic-bezier(0.06, 0.14, 0.8, 0.91) 0.4s;
    }
    #shopify-section-{{ section.id }} .highlight-anm-start .svg-underline path, 
    #shopify-section-{{ section.id }} .highlight-anm-start .svg-circle path {
      stroke-dashoffset: 0;
    }
    #shopify-section-{{ section.id }} .otsb-video-hero iframe {
      width: 100%;
      height: 300%;
    }
    #shopify-section-{{ section.id }} .svg-underline path {
      stroke-dasharray: 1000;
      stroke-dashoffset: 1000;
      transition: stroke-dashoffset 1.5s ease-in-out;
  }
  #shopify-section-{{ section.id }} .animate_transition_slide__box-right {
    opacity: 1;
    transform: translate(0);
  }
  #shopify-section-{{ section.id }} .otsb-button-icon svg,
  #shopify-section-{{ section.id }} .otsb-button-icon {
    width: 19px;
    height: 19px;
  }
  #shopify-section-{{ section.id }} .image-treatment-text {
    color: rgba(var(--image-treatment-text));
  }
    @media screen and (min-width: 768px){
    .otsb__root .desktop-{{ section.id }}-natural { 
    {%- if block_first and block_first.settings.image != blank -%}
        height:0;
        padding-bottom: {{ 1 | divided_by: block_first.settings.image.aspect_ratio | times: 100 }}%;
    {%- else -%}
        height: 650px;
    {%- endif -%}
    }
    }

    #shopify-section-{{ section.id }} button.otsb-button-arrow {
    background-color: rgba({{ section.settings.slider_button_color.red }}, {{ section.settings.slider_button_color.green }}, {{ section.settings.slider_button_color.blue }}, 0.3);
    color: {{ section.settings.slider_button_text_color }};
    box-shadow: none;
    border-radius: 50px;
    }

    #shopify-section-{{ section.id }} button.otsb-button-arrow:hover {
    color: {{ section.settings.slider_button_hover_text_color }};
    background: {{ section.settings.slider_button_hover_color }};
    }
{%- endstyle -%}
{%- endcapture -%}
{% comment %} {%- assign before =  styles.size -%} {% endcomment %}
{%- assign styles =  styles | strip_newlines | split: " " | join: " " | split: "*/" -%}
{%- assign minified_style = "" -%}
{%- for word in styles -%}
	{%- assign new_word = word | split: "/*" | first | strip | replace: "; ", ";" | replace: "} ", "}" | replace: "{ ", "{" | replace: " {", "{" -%}
    {%- assign minified_style = minified_style | append: new_word -%}
{%- endfor -%}
{% comment %} /* CSS minifed: {{ before }} --> {{ minified_style.size }} */ {% endcomment %}
{{- minified_style  }}
{% if request.design_mode %}
  <style>
    .otsb_nope {
      display: none !important;
      height: 0 !important;
      overflow: hidden !important;
      visibility: hidden !important;
      width: 0 !important;
      opacity: 0 !important;
    }
    ._otsb_warning {
      position: relative;
      box-shadow: 0rem 0.1875rem 0.0625rem -0.0625rem rgba(26, 26, 26, 0.07);
      border-radius: 1rem;
    }
    ._otsb_warning::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 0;
      box-shadow: 0.0625rem 0rem 0rem 0rem rgba(0, 0, 0, 0.13) inset, -0.0625rem 0rem 0rem 0rem rgba(0, 0, 0, 0.13) inset, 0rem -0.0625rem 0rem 0rem rgba(0, 0, 0, 0.17) inset, 0rem 0.0625rem 0rem 0rem rgba(204, 204, 204, 0.5) inset;
      border-radius: 1rem;
      pointer-events: none;
      mix-blend-mode: luminosity;
    }
  </style>
  <div x-data="otsb_script_require" class="page-width" style="margin-top:36px;margin-bottom:36px">
    <div class="_otsb_warning">
      <div style="border-top-left-radius:1rem;border-top-right-radius:1rem;border:1px solid #fcaf0a;background:#fcb827;padding:1rem">
        <div style="align-items:center;gap:8px;display:flex;flex-direction:row;flex-wrap:nowrap;justify-content:space-between">
          <div style="display:flex;gap:4px;flex-direction:row;flex-wrap:nowrap;justify-content:space-between">
            <span style="display:block;height:20px;width:20px;max-height:100%;max-width:100%;margin:auto">
              <svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path d="M10 6.75a.75.75 0 0 1 .75.75v3.5a.75.75 0 1 1-1.5 0v-3.5a.75.75 0 0 1 .75-.75Z"></path><path d="M11 13.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"></path><path fill-rule="evenodd" d="M10 3.5c-1.045 0-1.784.702-2.152 1.447a449.26 449.26 0 0 1-2.005 3.847l-.028.052a403.426 403.426 0 0 0-2.008 3.856c-.372.752-.478 1.75.093 2.614.57.863 1.542 1.184 2.464 1.184h7.272c.922 0 1.895-.32 2.464-1.184.57-.864.465-1.862.093-2.614-.21-.424-1.113-2.147-2.004-3.847l-.032-.061a429.497 429.497 0 0 1-2.005-3.847c-.368-.745-1.107-1.447-2.152-1.447Zm-.808 2.112c.404-.816 1.212-.816 1.616 0 .202.409 1.112 2.145 2.022 3.88a418.904 418.904 0 0 1 2.018 3.875c.404.817 0 1.633-1.212 1.633h-7.272c-1.212 0-1.617-.816-1.212-1.633.202-.408 1.113-2.147 2.023-3.883a421.932 421.932 0 0 0 2.017-3.872Z"></path></svg>
            </span>
            <h2 style="overflow-wrap:anywhere;word-break:normal;font-size:100%;font-weight:650;line-height:1.25;color:rgb(37,26,0)">App Embeds Are Disabled</h2>
          </div>
        </div>
      </div>
      <div style="padding:1rem;color:rgb(37,26,0)">
        <p>To use this section, the app embeds of OT: Theme Sections must be enabled in the theme editor. Please follow these steps to activate them:</p>
        <ul>
          <li>In the left panel, click the last icon that says <b>“App embeds”</b>.</li>
          <li>Enter <b>“OT”</b> on the search bar to quickly find and embed the apps from OT: Theme Sections.</li>
          <li>Turn on the Toggle buttons of "Section Builder Script" and "Section Builder Style", then click <b>Save</b>.</li>
        </ul>
        <p>Please refer to the User Guide <a href="https://support.omnithemes.com/blogs/ot-theme-sections-get-started/1-embed-app-to-shopify-theme" target="_blank">here</a></p>
        <p>For further support: feel free to contact us at <a href="mailto:<EMAIL>"><EMAIL></a>!</p>
      </div>
    </div>
  </div>
{% endif %}

<div class="otsb__root otsb_nope" x-data="otsb_script_1">
    <div
        class="pt-[{{ section.settings.padding_top_mobile }}px] md:pt-[{{ section.settings.padding_top }}px] pb-[{{ section.settings.padding_bottom_mobile }}px] md:pb-[{{ section.settings.padding_bottom }}px]">
        <div
            id="x-slideshow-{{ section.id }}"
            class="{% if settings.lang_direction contains request.locale.iso_code and section.settings.transition_style == "fade" %}otsb-ltr {% endif %}splide-slideshow visible mx-auto{% if enable_slider %} x-splide splide group{% endif %}{% if section.settings.full_width %}{% if section.settings.padding_full_width %} md:pl-5 md:pr-5{% else %} md:pl-0 md:pr-0{% endif %}{% else %} otsb-page-width{% endif %}{% if section.settings.full_width_mobile %} otsb-full-width-mobile{% else %} pl-5 pr-5{% endif %}"
            {% if enable_slider %}
                x-data
                x-intersect.once.margin.200px='$store.otsb_xSplide.load($el, {
        "speed": 1000,
        "pauseOnHover": false,
        "pauseOnFocus": false,
        {%- if section.settings.transition_style == "fade" %}
          "rewind": true,
          "type": "fade",
        {%- else %}
          "type": "loop",
        {%- endif %}
        {%- if section.settings.auto_play -%}
          "autoplay": true,
          "interval": {{ section.settings.change_slides_speed | times: 1000 }},
        {%- endif %}
        {%- if settings.lang_direction contains request.locale.iso_code %}
          "direction": "rtl",
        {%- endif %}
        "classes": {
          {%- if section.settings.show_arrow -%}
            "arrows": "splide__arrows block",
          {%- else -%}
            "arrows": "splide__arrows otsb-hidden",
          {%- endif -%}
          {%- if section.settings.slider_visual != "none" %}
            "pagination": "{% if settings.lang_direction contains request.locale.iso_code %}rtl {% endif %}splide__pagination flex-nowrap absolute bottom-0 left-1/2 -translate-x-1/2 pagination-{{ section.settings.slider_visual }}{% if section.settings.slider_visual == 'dots' %} mb-2{% else %} mb-4{% endif %}",
            {% if section.settings.slider_visual == "dots" -%}
              "page": "otsb-button none_border flex items-center pt-3 pb-3 pl-3 pr-3"
            {%- else -%}
              "page": "{% if settings.lang_direction contains request.locale.iso_code %}pt-3 -rotate-180  {% endif %}otsb-button md:w-20 h-2.5 none_border w-14 relative pagination-bars:rounded-none ml-0.5 mr-0.5 bg-none before:absolute before:top-0 before:right-0 before:bottom-0 before:h-1/2 before:left-0 before:opacity-40 before:bg-image-treatment{% if section.settings.auto_play %} pagination-bars:after:absolute after:bg-white pagination-bars:after:top-0 after:right-0 pagination-bars:overflow-hidden pagination-bars:after:bottom-0 pagination-bars:after:left-0 pagination-bars:after:-translate-x-full pagination-bars:relative pagination-bars:is-active-pagination:after:h-1/2 pagination-bars:is-active-pagination pagination-bars:is-active-pagination:after:animate-shimmerX_{{ section.settings.change_slides_speed | append: 's' }}{% else %} pagination-bars:is-active-pagination:before:opacity-[99%]{% endif %}"
            {%- endif %}
          {% else %}
            "pagination": "otsb-hidden"
          {%- endif %}
        }
      })'
      {%- endif -%}
        >
        {%- liquid
          assign parent = " md:slideshow:h-[" | append: desktop_height | append: "] md:slideshow:pb-0"
          assign child = " md:slideshow:relative"
          if section.settings.desktop_height == "natural"
            assign parent = " desktop-" | append: section.id | append: "-natural"
            assign child = " md:absolute md:top-0 md:left-0 md:bottom-0"
          endif
            assign parent_mobile = " h-[" | append: mobile_height | append: "]"
            assign child_mobile = " relative"
          if section.settings.mobile_height == "natural"
            assign parent_mobile = " mobile-" | append: section.id | append: "-natural"
            assign child_mobile = " absolute top-0 left-0 bottom-0"
            endif
          -%}
            <div
                class="relative bg-[#c9c9c9]{% if enable_slider %} splide__track{% endif %} overflow-hidden{{ parent }}{{ parent_mobile }}{% if section.settings.rounded_corner_image %} md:rounded-[10px]{% else %} md:rounded-none{% endif %}{% if section.settings.rounded_corner_image_mobile %} rounded-[10px]{% endif %}">
                <div
                    class="w-full h-full{% if enable_slider %} splide__list flex{% endif %}{{ child }}{{ child_mobile }}">
                    {%- for block in section.blocks -%}
                    {% case block.type %}
                    {% when 'slide' %}
                    {%- liquid
                        assign heading_size = block.settings.heading_size | times: 100 | times: 0.000225
                        assign imageUrlMobile = block.settings.image
                        if settings.colors_image_treatment_text != blank
                            assign colors_image_treatment_text = settings.colors_image_treatment_text
                        endif
                        if block.settings.image_mobile != blank
                            assign imageUrlMobile = block.settings.image_mobile
                        endif
                        assign check_video = false
                        if block.settings.video_url.type == 'youtube'
                        assign video_type = 'youtube'
                        assign check_video = true
                        assign video_id = block.settings.video_url.id
                        endif
                        if block.settings.video_url.type == 'vimeo'
                        assign video_type = 'vimeo'
                        assign check_video = true
                        assign video_id = block.settings.video_url.id
                        endif
                    
                        if block.settings.video != null or block.settings.video_mobile != null
                        assign video_type = 'video_select'
                        assign check_video = true
                        endif 
                    -%}
                    {%- style -%}
                        #shopify-section-{{ section.id }} .x-slideshow-{{ block.id }} {
                        --colors-text-link: var(--image-treatment-text);
                        }
                        #shopify-section-{{ section.id }} .x-slideshow-{{ block.id }} {
                        {%- if block.settings.color_text.alpha != 0.0 -%}
                            --image-treatment-text: {{ block.settings.color_text.red }}, {{ block.settings.color_text.green }}, {{ block.settings.color_text.blue }};
                            --colors-text-link: {{ block.settings.color_text.red }}, {{ block.settings.color_text.green }}, {{ block.settings.color_text.blue }};
                        {% elsif colors_image_treatment_text != blank %}
                            --image-treatment-text: colors_image_treatment_text
                        {% else %}
                            --image-treatment-text: var(--color-foreground);
                        {% endif %}
                        }

                        #shopify-section-{{ section.id }} .x-slideshow-{{ block.id }} {
                        {%- if block.settings.color_text_link.alpha != 0.0 -%}
                            --colors-text-link: {{ block.settings.color_text_link.red }}, {{ block.settings.color_text_link.green }}, {{ block.settings.color_text_link.blue }};
                            {% else %}
                            --colors-text-link: var(--color-link);
                        {%- endif -%}
                        }
                        #shopify-section-{{ section.id }} .heading--{{ block.id }},#shopify-section-{{ section.id }} .content-text-{{ block.id }} {
                        {% if block.settings.heading_color.alpha != 0.0 %}
                            --colors-heading: {{ block.settings.heading_color.red }}, {{ block.settings.heading_color.green }}, {{ block.settings.heading_color.blue }};
                        {% elsif colors_image_treatment_text != blank %}
                            --colors-heading: colors_image_treatment_text
                        {% elsif settings.colors-heading != blank %}
                            --colors-heading: settings.colors-heading;
                        {% else %}
                            --colors-heading: var(--color-foreground);
                        {% endif %}
                        }

                        .button--{{ block.id }}.otsb-button-solid,
                        .button--{{ block.id }}.otsb-button-solid:before {
                        {%- if block.settings.color_button.alpha != 0.0 -%}
                            --colors-line-and-border: {{ block.settings.color_button.red }}, {{ block.settings.color_button.green }}, {{ block.settings.color_button.blue }};
                            --colors-button: {{ block.settings.color_button.red }}, {{ block.settings.color_button.green }}, {{ block.settings.color_button.blue }};
                        {% elsif settings.colors_button != blank %}
                            --colors-button: settings.colors_button;
                        {%- else -%}
                            --colors-line-and-border: var(--colors-button);
                            --colors-button: var(--color-button);
                        {%- endif -%}
                        {%- if block.settings.color_button_hover.alpha != 0.0 -%}
                            --colors-button-hover: rgb({{ block.settings.color_button_hover.red }}, {{ block.settings.color_button_hover.green }}, {{ block.settings.color_button_hover.blue }});
                        {% elsif settings.color_button_hover != blank %}
                            --colors-button-hover: settings.color_button_hover;
                        {% else %}
                            --colors-button-hover: rgba(var(--color-button), 0.5);
                        {%- endif -%}
                        {%- if block.settings.color_text_button.alpha != 0.0 -%}
                            --colors-button-text: {{ block.settings.color_text_button.red }}, {{ block.settings.color_text_button.green }}, {{ block.settings.color_text_button.blue }};
                        {% elsif settings.colors-button-text != blank %}
                            --colors-button-text: settings.colors-button-text;
                        {% else %}
                            --colors-button-text: var(--color-button-text);
                        {%- endif -%}
                        {%- if block.settings.color_text_button_hover.alpha != 0.0 -%}
                            --colors-button-text-hover: {{ block.settings.color_text_button_hover.red }}, {{ block.settings.color_text_button_hover.green }}, {{ block.settings.color_text_button_hover.blue }};
                        {% elsif settings.colors_text_button_hover != blank %}
                            --colors-button-text-hover: settings.colors_text_button_hover;
                        {% else %}
                            --colors-button-text-hover: var(--color-button-text);
                        {%- endif -%}
                        }
                        .button--{{ block.id }}.otsb-button-outline {
                        {%- if block.settings.secondary_button_text.alpha != 0.0 -%}
                            --colors-secondary-button: {{ block.settings.secondary_button_text.red }}, {{ block.settings.secondary_button_text.green }}, {{ block.settings.secondary_button_text.blue }};
                            --colors-line-secondary-button: {{ block.settings.secondary_button_text.red }}, {{ block.settings.secondary_button_text.green }}, {{ block.settings.secondary_button_text.blue }};
                        {% elsif settings.secondary_button_text != blank %}
                            --colors-secondary-button: settings.secondary_button_text;
                            --colors-line-secondary-button: settings.secondary_button_text;
                        {% else %}
                            --colors-secondary-button: var(--color-secondary-button-text);
                        {% endif %}
                        {%- if block.settings.color_button_secondary.alpha != 0.0 -%}
                            --background-secondary-button: {{ block.settings.color_button_secondary.red }}, {{ block.settings.color_button_secondary.green }}, {{ block.settings.color_button_secondary.blue }};
                            --colors-line-secondary-button: {{ block.settings.color_button_secondary.red }}, {{ block.settings.color_button_secondary.green }}, {{ block.settings.color_button_secondary.blue }};
                        {% elsif settings.background-secondary-button != blank %}
                            --background-secondary-button: settings.background-secondary-button;
                            --colors-line-secondary-button: settings.background-secondary-button;
                        {% else %}
                            --colors-line-secondary-button: var(--color-secondary-button-text);
                        {% endif %}
                        }
                        {%- if block.settings.content_alignment == "custom" -%}
                            .content--{{ block.id }}{
                            left: {{ block.settings.custom_horizontal }}%;
                            top: {{ block.settings.custom_vertical }}%;
                            transform: translate(-{{ block.settings.custom_horizontal }}%,-{{ block.settings.custom_vertical }}%);
                            }
                        {%- endif -%}
                        .bg-content-{{ block.id }} {
                        {%- if block.settings.content_background_color.alpha != 0.0 -%}
                            background-color: {{ block.settings.content_background_color }};
                        {% else %}
                            background-color: rgb(var(--colors-background-secondary));
                        {%- endif -%}
                        }
                        @media (max-width: 1024px) {
                        {%- if block.settings.mobile_custom_position -%}
                            .otsb__root .content-mobile--{{ block.id }}{
                            min-width: 270px;
                            position: absolute;
                            left: {{ block.settings.custom_horizontal_mobile }}%;
                            top: {{ block.settings.custom_vertical_mobile }}%;
                            transform: translate(-{{ block.settings.custom_horizontal_mobile }}%,-{{ block.settings.custom_vertical_mobile }}%);
                            }
                            .otsb__root .content-mobile--{{ block.id }} .animate_transition_slide__box-{{ section.id }} {
                                {%  unless block.settings.alignment_mobile == "center" %} 
                                  float: {{ block.settings.alignment_mobile }};
                                {%  else %}
                                  float: right;
                                {% endunless %}
                                padding-top: 1.25rem;
                            }
                        {%- endif -%}
                        }
                        .otsb__root h2.heading--{{ block.id }},.otsb__root .otsb-h2.heading--{{ block.id }} {
                        font-size: {{ heading_size }}rem;
                        }
                        .sub-heading-{{ block.id }} {
                        font-size: {{ heading_size | times: 0.7 }}rem;
                        }
                        .image-{{ block.id }} {
                        object-position:{{ imageUrlMobile.presentation.focal_point }};
                        }
                        @media screen and (min-width: 768px) {
                        .otsb__root .otsb-h2.heading--{{ block.id }} {
                        font-size: {{ heading_size }}rem;
                        }
                        .sub-heading-{{ block.id }} {
                        font-size: {{ heading_size | times: 0.58 }}rem;
                        }
                        .image-{{ block.id }} {
                        object-position:{{ block.settings.image.presentation.focal_point }};
                        }
                        }
                        .content-text-{{ block.id }} {
                        --h1-font-size: calc(var(--font-heading-scale) * 4rem);
                        --h2-font-size: calc(var(--font-heading-scale) * 2.4rem);
                        --h3-font-size: calc(var(--font-heading-scale) * 1.8rem);
                        --h4-font-size: calc(var(--font-heading-scale) * 1.5rem);
                        --h5-font-size: calc(var(--font-heading-scale) * 1.3rem);
                        --h6-font-size: calc(var(--font-heading-scale) * 1.1rem);
                        --h1-font-size-mobile: calc(var(--font-heading-scale) * 3rem);
                        --h2-font-size-mobile: calc(var(--font-heading-scale) * 2rem);
                        --h3-font-size-mobile: calc(var(--font-heading-scale) * 1.7rem);
                        --h4-font-size-mobile: calc(var(--font-heading-scale) * 1.5rem);
                        --h5-font-size-mobile: calc(var(--font-heading-scale) * 1.2rem);
                        --h6-font-size-mobile: calc(var(--font-heading-scale) * 1.1rem);
                        }
                    {%- endstyle -%}
                    {% if section.settings.show_hero and forloop.first %}
                        <div class="otsb-hidden">
                            {%- if block.settings.image_mobile != blank -%}
                                {{ imageUrlMobile | image_url: width: 1500 | image_tag: widths: '375, 450, 750, 900, 1100, 1500', preload: true, loading: 'lazy' }}
                            {%- endif -%}
                            {{ block.settings.image | image_url: width: 3840 | image_tag: widths: '750, 900, 1100, 1500, 1780, 2000, 3000, 3840', preload: true, loading: 'lazy' }}
                        </div>
                    {% endif %}
                    <div x-slide-index="{{ forloop.index | minus: 1 }}"
                         class="{% if block.settings.slide_link and block.settings.image != blank %} cursor-pointer {% else %} cursor-grab {% endif %} x-slideshow-{{ block.id }}{% if settings.lang_direction contains request.locale.iso_code %} otsb-rtl{% endif %} h-full relative transition-opacity flex-shrink-0 w-full{% if enable_slider %} splide__slide x-splide-slide{% endif %}{% if section.settings.show_hero %} w-full{% endif %}"
                         x-data="{effect: false}"
                        {% if section.settings.transition_style != "fade" or enable_slider == false %}
                            {%- if section.settings.full_width %} x-intersect:leave.margin.-40px="effect = false"{% else %} x-intersect:leave.margin.-20px.half.-20px.haft="effect = false"{% endif %}
                            x-intersect.half="effect = true"
                        {% endif %}
                        {{ block.shopify_attributes }}
                    >
                        {% if section.settings.transition_style == "fade" and enable_slider %}
                            <div x-data x-intersect.half="effect = true" x-intersect:leave.margin.-20px="effect = false"
                                 class="absolute top-0 left-0 w-full h-full otsb-hidden active-slide-fade{% if forloop.first %} active-slide-fade-first{% endif %}">
                            </div>
                        {% endif %}
                        <div class="h-full relative overflow-hidden">
                            {% if check_video %}
                                {%- capture button_play -%}
                                  <div class="button-play z-20 absolute top-1/2 left-1/2">
                                    <div class="cursor-pointer absolute z-20 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[60px] h-[60px] md:w-[60px] md:h-[60px] rounded-full p-5 bg-[rgba(var(--image-treatment-text),0.06)] hover:bg-[rgba(var(--image-treatment-text),0.1)] bg-opacity-30 disabled:cursor-not-allowed">
                                      <span class="pointer-events-none duration-200 bg-button-play absolute w-4 h-5 md:w-4 md:h-5 top-1/2 left-1/2 -translate-y-1/2 -translate-x-1/2 text-[rgba(var(--image-treatment-text))]">
                                        {% render 'otsb-icon-media', icon: 'icon-play', class: 'w-full h-full' %}
                                      </span>
                                    </div>
                                  </div>
                                {%- endcapture -%}
                                <div 
                                  class="otsb-video-hero video-hero"
                                >
                                  {% if block.settings.enable_video_autoplay or block.settings.enable_bakground %}
                                    <div 
                                      class="otsb-video-slideshow video-slideshow otsb-external-video external-video absolute top-1/2 left-1/2 w-full h-full object-cover -translate-x-1/2 -translate-y-1/2"
                                      x-intersect:leave="$store.xVideo.pause($el)"
                                      @click.stop="$store.xVideo.togglePlay($el)"
                                      {% if video_type == 'video_select' %}x-intersect="$store.xVideo.play($el)"{% endif %}
                                      {% if video_type == 'youtube' or video_type == 'vimeo' %}
                                        x-intersect.once="$store.xVideo.externalLoad($el, '{{ video_type }}', '{{ video_id }}', true, '{{ block.settings.video_alt_text }}', 1)"
                                        x-intersect="$store.xVideo.play($el)"
                                      {% endif %}  
                                      {% if block.settings.video != blank %}
                                        style="padding-bottom: {{ 1 | divided_by: block.settings.video.aspect_ratio | times: 100 }}%;"
                                      {% endif %}
                                    >
                                      <span class="absolute top-0 left-0 bottom-0 right-0 z-10 image-treatment-overlay opacity-{{ block.settings.overlay_opacity }}"></span>
                                      {%- if video_type == 'youtube' or video_type == 'vimeo' -%}
                                        <div
                                          class="w-full h-full absolute"
                                        >
                                        </div>
                                      {%- endif -%}
                                      {% if video_type == 'video_select' %}
                                        {{ block.settings.video
                                        | video_tag:
                                          image_size: "1100x",
                                          loop: true,
                                          controls: false,
                                          muted: true,
                                          class: "w-full h-full absolute top-0 left-0 otsb-video video object-cover",
                                          alt: block.settings.video.alt
                                        }}
                                        {{ button_play }}
                                      {% endif %}
                                    </div>
                                    {% if block.settings.show_sound_control and video_type == 'video_select' %}
                                      <button x-data="{ muted: true }" class="z-20 button-sound-control bg-[rgba(var(--image-treatment-text),0.06)] hover:bg-[rgba(var(--image-treatment-text),0.1)] bg-opacity-30 {% if settings.edges_type == 'rounded_corners' %}rounded-[10px] {% endif %}flex items-center justify-center absolute right-2 rtl:left-2 rtl:right-auto cursor-pointer w-[35px] h-[35px] md:w-[34px] md:h-[34px]{% if section.settings.slider_visual == 'image' %} top-2 {% else %} bottom-2 {% endif %}" @click.stop="$store.xVideo.toggleMute($el); muted=!muted" aria-label="button sound control">
                                        <span class="w-[18px] h-[18px] lg:w-[18px] lg:h-[18px] text-[rgba(var(--image-treatment-text))]" x-show="muted">
                                          {% render 'otsb-icon-media', icon: 'icon-unmute' %}
                                        </span>
                                        <span class="w-[18px] h-[18px] lg:w-[18px] lg:h-[18px] text-[rgba(var(--image-treatment-text))]" x-show="!muted" x-cloak>
                                          {% render 'otsb-icon-media', icon: 'icon-mute' %}
                                        </span>
                                      </button>
                                    {% endif %}
                                  {% else %}
                                    {% liquid
                                      assign image_cover = blank
                                      if video_type == 'video_select'
                                        assign image_cover = block.settings.video.preview_image
                                      endif
                                      assign video_alt = block.settings.video_alt_text              
                                    %}
                                    {%- capture sizes -%}
                                      {%- if section.settings.full_width -%}
                                        (min-width: 1024px) 100vw,
                                      {%- else -%}
                                        (min-width: {{ settings.page_width }}px) {{ settings.page_width }}px,
                                      {%- endif -%}
                                      (min-width: 768px) calc(100vw / 3),
                                    {%- endcapture -%}            
                                    <div
                                      class="video-slideshow otsb-external-video external-video absolute top-1/2 left-1/2 w-full h-full object-cover -translate-x-1/2 -translate-y-1/2"
                                      x-intersect:leave="$store.xVideo.pause($el)"
                                      x-data="{ isHovered: false }"
                                      {% if video_type == 'video_select' %}@click.prevent="$store.xVideo.mp4Thumbnail($el)"{% endif %}
                                      {% if video_type == 'youtube' or video_type == 'vimeo' %}@click.prevent="$store.xVideo.externalLoad($el, '{{ video_type }}', '{{ video_id }}', false, '{{ video_alt }}')"{% endif %}
                                      >
                                        <span class="absolute top-0 left-0 bottom-0 right-0 z-20 image-treatment-overlay opacity-{{ block.settings.overlay_opacity }}"></span>
                                      {% if image_cover != blank %}
                                        <img
                                          srcset="{{ image_cover | image_url: width: 375 }} 375w,
                                          {{ image_cover | image_url: width: 450 }} 450w,
                                          {{ image_cover | image_url: width: 750 }} 750w,
                                          {{ image_cover | image_url: width: 900 }} 900w,
                                          {{ image_cover | image_url: width: 1100 }} 1100w,
                                          {{ image_cover | image_url: width: 1500 }} 1500w,
                                          {{ image_cover | image_url: width: 1780 }} 1780w"
                                          sizes="{{ sizes }}"
                                          src="{{ image_cover | image_url: width: 1780 }}"
                                          alt="{{ image_cover.alt | escape }}"
                                          class="object-cover z-20 absolute top-0 left-0 h-full w-full img-thumbnail animate-Xpulse skeleton-image{% if settings.edges_type == 'rounded_corners' %} rounded-[10px]{% endif %}"
                                          loading="lazy"
                                          onload="this.closest('.skeleton-image')?.classList.remove('animate-Xpulse', 'skeleton-image');"
                                          width="{{ image_cover.width }}"
                                          height="{{ image_cover.height }}"
                                          style="object-position:{{ image_cover.presentation.focal_point }};"
                                        >
                                        {% if video_type == 'video_select' %}
                                          {{ block.settings.video
                                            | video_tag:
                                              image_size: "1100x",
                                              loop: false,
                                              muted: false,
                                              class: "w-full h-full absolute top-0 left-0 otsb-video video object-cover",
                                              alt: block.settings.video.alt
                                              | replace: '<video ', '<video x-on:mouseover="isHovered = true" x-on:mouseleave="isHovered = false" :controls="isHovered" '
                                          }}
                                          {{ button_play }}
                                        {% endif %}
                                      {% else %}
                                        {% unless check_video %}
                                          <div class="w-full h-full flex justify-center items-center absolute bg-[#c9c9c9]">
                                            <span class="w-20 md:w-40 h-20 md:h-40 flex items-center">
                                              {% render 'otsb-icon-media', icon: 'icon-video', class: 'text-[#acacac] w-full h-full' %}
                                            </span>
                                          </div>
                                        {% endunless %}
                                        {% if video_type == 'youtube' %}
                                          {% comment %}theme-check-disable RemoteAsset{% endcomment %}
                                          <div class="absolute h-full w-full otsb-video video">
                                            <picture>
                                              <source type="image/webp" srcset="{{ video_id | prepend: 'https://i.ytimg.com/vi_webp/' | append: '/maxresdefault.webp' }}">
                                              <source type="image/jpeg" srcset="{{ video_id | prepend: 'https://i.ytimg.com/vi/' | append: '/maxresdefault.jpg' }}">
                                              <img 
                                                src="{{ video_id | prepend: 'https://i.ytimg.com/vi_webp/' | append: '/maxresdefault.webp' }}" 
                                                class="w-full h-full object-cover animate-Xpulse skeleton-image" alt="{{ video_alt }}" width="1280" height="890"
                                                loading="lazy"
                                                onload="this.closest('.skeleton-image')?.classList.remove('animate-Xpulse', 'skeleton-image');"
                                              />
                                            </picture>
                                          </div>
                                          {% comment %}theme-check-enable RemoteAsset{% endcomment %}
                                          {{ button_play }}
                                        {% elsif video_type == 'vimeo' %}
                                          {%- capture options -%}
                                            {
                                              'alt': '{{ video_alt }}',
                                              'width': 1280
                                            }
                                          {%- endcapture -%}
                                          <div class="external-video otsb-external-video w-full h-full">
                                            <div class="h-full w-full" x-init="$store.xVideo.renderVimeoFacade($el, '{{ video_id }}', {{ options }})"></div>
                                            {{ button_play }}
                                          </div>
                                        {% elsif video_type == 'video_select' %}
                                          {{ block.settings.video
                                            | video_tag:
                                              image_size: "1100x",
                                              loop: false,
                                              controls: true,
                                              muted: false,
                                              class: "w-full h-full absolute otsb-video video",
                                              alt: block.settings.video.alt
                                              | replace: '<video ', '<video x-on:pause="paused = true" x-on:play="paused = false" x-on:mouseover="isHovered = true" x-on:mouseleave="isHovered = false" :controls="isHovered" '
                                          }}
                                          {{ button_play }}
                                        {% endif %}
                                      {% endif %}
                                    </div>
                                  {% endif %}
                                </div>
                              {% else %}
                            {% style %}
                                .x-slideshow-{{ block.id }} .otsb-image-treatment-overlay {
                                background: rgb({{ block.settings.image_overlay_color.red }}, {{ block.settings.image_overlay_color.green }}, {{ block.settings.image_overlay_color.blue }})
                                }
                            {% endstyle %}
                            {% if block.settings.image != blank %}
                            <span {% if block.settings.slide_link %}onclick="{% if block.settings.open_new_window_slide %} {% if request.design_mode %} location.href='{{ block.settings.slide_link }}'{% else %} window.open('{{ block.settings.slide_link }}', '_blank'){% endif %} {% else %}location.href='{{ block.settings.slide_link }}' {% endif %}" {% endif %}
                               class="absolute top-0 left-0 bottom-0 right-0 otsb-image-treatment-overlay opacity-{{ block.settings.overlay_opacity }} z-[1]"></span>
                            {% endif %}
                        <div :class="effect && 'active'"
                                 class="h-full w-full absolute top-0 left-0 animate_transition_slide__image">
                                {%- if block.settings.image == blank -%}
                                    <div
                                        class="w-full h-full text-[#acacac]{% if block.settings.image_mobile != blank %} otsb-hidden{% endif %} md:block mx-auto">
                                        {{ 'lifestyle-2' | placeholder_svg_tag: 'w-full h-full' }}
                                    </div>
                                {%- else -%}
                                    {% comment %}theme-check-disable ImgLazyLoading{% endcomment %}
                                    <parallax-image
                                        class="{% if block.settings.image_mobile != blank and request.design_mode %} otsb-hidden md:block{% endif %} h-full w-full">
                                        <parallax-movement class="no_parallax_effect">
                                            <picture>
                                                <source
                                                    srcset="{{ imageUrlMobile | image_url: width: 375 }} 375w,
                                                    {{ imageUrlMobile | image_url: width: 450 }} 450w,
                                                    {{ imageUrlMobile | image_url: width: 750 }} 750w,
                                                    {{ imageUrlMobile | image_url: width: 900 }} 900w,
                                                    {{ imageUrlMobile | image_url: width: 1100 }} 1100w,
                                                    {{ imageUrlMobile | image_url: width: 1500 }} 1500w"
                                                    media="(max-width: 767px)"
                                                    width="{{ imageUrlMobile.width }}"
                                                    height="{{ imageUrlMobile.height }}"
                                                >
                                                <img
                                                    srcset="
                                                    {{ imageUrlMobile | image_url: width: 375 }} 375w,
                                                    {{ imageUrlMobile | image_url: width: 450 }} 450w,
                                                    {{ imageUrlMobile | image_url: width: 750 }} 750w,
                                                    {{ block.settings.image | image_url: width: 900 }} 900w,
                                                    {{ block.settings.image | image_url: width: 1100 }} 1100w,
                                                    {{ block.settings.image | image_url: width: 1500 }} 1500w,
                                                    {{ block.settings.image | image_url: width: 1780 }} 1780w,
                                                    {{ block.settings.image | image_url: width: 2000 }} 2000w,
                                                    {{ block.settings.image | image_url: width: 3000 }} 3000w,
                                                    {{ block.settings.image | image_url: width: 3840 }} 3840w"
                                                    {% unless section.settings.show_hero and forloop.first %}
                                                        loading="eager"
                                                    {% else %}
                                                        loading="eager"
                                                        fetchpriority="high"
                                                        decoding="sync"
                                                    {% endunless %}
                                                    sizes="100vw"
                                                    src="{{ block.settings.image | image_url: width: 3840 }}"
                                                    {% if block.settings.image_mobile != blank %}
                                                        :alt="(screen.width < 768) ? '{{ block.settings.image_mobile.alt }}' : '{{ block.settings.image.alt }}'"
                                                    {% else %}
                                                        alt="{{ block.settings.image.alt | escape }}"
                                                    {% endif %}
                                                    class="object-cover h-full w-full image-{{ block.id }}"
                                                    width="{{ block.settings.image.width }}"
                                                    height="{{ block.settings.image.height }}"
                                                >
                                            </picture>
                                        </parallax-movement>
                                    </parallax-image>
                                    {% comment %}theme-check-enable ImgLazyLoading{% endcomment %}
                                {%- endif %}
                                {%- if block.settings.image_mobile != blank and request.design_mode or block.settings.image == blank -%}
                                    {% comment %}theme-check-disable ImgLazyLoading{% endcomment %}
                                    <img
                                        srcset="{{ block.settings.image_mobile | image_url: width: 375 }} 375w,
                    {{ block.settings.image_mobile | image_url: width: 450 }} 450w,
                    {{ block.settings.image_mobile | image_url: width: 750 }} 750w,
                    {{ block.settings.image_mobile | image_url: width: 900 }} 900w,
                    {{ block.settings.image_mobile | image_url: width: 1100 }} 1100w,
                    {{ block.settings.image_mobile | image_url: width: 1500 }} 1500w"
                                        sizes="100vw"
                                        src="{{ block.settings.image_mobile | image_url: width: 750 }}"
                                        alt="{{ block.settings.image_mobile.alt | escape }}"
                                        class="object-cover h-full w-full{% if block.settings.image_mobile != blank %} md:otsb-hidden{% endif %}"
                                        {% unless section.settings.show_hero and forloop.first %}
                                            loading="eager"
                                        {% else %}
                                            loading="eager"
                                            fetchpriority="high"
                                            decoding="sync"
                                        {% endunless %}
                                        width="{{ block.settings.image_mobile.width }}"
                                        height="{{ block.settings.image_mobile.height }}"
                                        style="object-position: {{ block.settings.image_mobile.presentation.focal_point }}"
                                    >
                                    {% comment %}theme-check-enable ImgLazyLoading{% endcomment %}
                                {%- endif %}
                            </div>
                        {% endif %}
                        </div>
                        {%- assign alignment_desktop = block.settings.content_alignment | split: '-' -%}
                        <div 
                            class="hero-text pointer-events-none transition{% unless block.settings.content_alignment == "custom" %} otsb-page-width{% endunless %} absolute left-1/2 -translate-x-1/2 top-0 w-full h-full table z-[2]">
                        {% if block.settings.content_alignment == 'custom' %}
                            {% if block.settings.image != blank %}
                                <div class="pointer-events-none absolute top-0 left-0 block w-full h-full" {% if block.settings.slide_link %}onclick="{% if block.settings.open_new_window_slide %} {% if request.design_mode %} location.href='{{ block.settings.slide_link }}'{% else %} window.open('{{ block.settings.slide_link }}', '_blank'){% endif %} {% else %}location.href='{{ block.settings.slide_link }}' {% endif %}" {% endif %}></div>
                            {% endif %}
                        {% endif %}
                            <div
                                class="table-cell table-cell-{{ block.id }} max-w-2xl md:min-w-[30rem] lg:w-3/5{% if block.settings.mobile_custom_position %} content-mobile--{{ block.id }}{% endif %}{% if block.settings.content_alignment == "custom" %} min-w-[75%] lg:min-w-[30rem] absolute content--{{ block.id }} text-{{block.settings.alignment_mobile}} md:text-{{ block.settings.alignment }} pl-5 pr-5{% else %} {% if block.settings.mobile_custom_position %} lg:align-{{ alignment_desktop[0] }} md:text-{{ block.settings.alignment }}{%  else %} align-{{ alignment_desktop[0] }} md:text-{{ block.settings.alignment }}{% endif %}{% endif %} {% if block.settings.mobile_custom_position %} text-{{block.settings.alignment_mobile}} justify-{{block.settings.alignment_mobile}} {% else %} text-{{block.settings.alignment_mobile}} md:text-{{block.settings.alignment}} justify-{{block.settings.alignment}} {% endif %}">
                            
                                <div :class="effect && 'active'"
                                     class="{% if block.settings.enable_bakground %} bg-content-{{ block.id }} lg:w-max max-w-full lg:pt-10 lg:pb-10 xl:pt-[60px] xl:pb-[60px] {% else %} mb-5 md:mb-6 {% endif %} animate_transition_slide__box-{{ section.id }} {% if block.settings.content_alignment contains 'center' %} xl:pl-5 xl:pr-5 lg:mx-auto{% endif %}{% if block.settings.content_alignment != 'custom' %} max-w-2xl pt-5 pr-5 pl-5 float-{{ alignment_desktop[1] }} {% endif -%} pb-10">
                                    {%- if block.settings.subheading != blank -%}
                                        <p class="sub-heading-{{ block.id }} leading-tight italic p-break-words otsb-image-treatment-text">{{ block.settings.subheading | escape }}</p>
                                    {%- endif -%}
                                    {%- if block.settings.heading != blank -%}
                                    <{{ block.settings.heading_tag }} class="p-break-words heading--{{ block.id }} heading-{{ block.id }}
                                    otsb-h2 block mt-1 leading-tight">
                                    {% render 'otsb-heading-highlight',
                                        headingId: block.id,
                                        heading: block.settings.heading,
                                        highlight_type: block.settings.highlight_type,
                                        color_heading_highlight_light: block.settings.color_heading_highlight,
                                        color_text: section.settings.text
                                    %}
                                </{{ block.settings.heading_tag }}>
                                {%- endif -%}
                                {%- if block.settings.text != blank -%}
                                    <div
                                        class="lg:mt-1 p-break-words content-text-{{ block.id }} otsb-rte otsb-image-treatment-text text-medium">
                                        {{ block.settings.text }}
                                    </div>
                                {%- endif -%}
                                {%- if block.settings.button_label_1 != blank or block.settings.button_label_2 != blank -%}
                                  {%- liquid
                                      case block.settings.button_type
                                      when 'rounded'
                                      assign borderRadius = '100px'
                                      when 'rounded_corners'
                                      assign borderRadius = '6px'
                                      when 'mixed'
                                      assign borderRadius = '6px 0 6px 0'
                                      else
                                      assign borderRadius = '0'
                                      endcase
                                    %}
                                    {% style %}
                                    .x-button-{{ block.id }} .button--{{ block.id }} {
                                      --border-radius: {{ borderRadius }};
                                      {% if block.settings.button_animation == 'slide' %}
                                      --button-width: 102%;
                                      --button-height: 500%;
                                      --button-transform: rotate3d(0,0,1,-10deg) translate3d(-130%,-10em,0);
                                      --button-transform-origin: 100% 0%;
                                      {% elsif block.settings.button_animation == 'fill_up' %}
                                      --button-width: 120%;
                                      --button-height: 100%;
                                      --button-transform: rotate3d(0,0,1,10deg) translate3d(-1.2em,110%,0);
                                      --button-transform-origin: 0% 100%;
                                      {% endif %}
                                      }
                                        {% if block.settings.button_color_mobile == "hover" %}
                                            .x-button-{{ block.id }} [role="button"], .x-button-{{ block.id }} [type="button"], .x-button-{{ block.id }} .otsb-button {
                                            color: rgb(var(--colors-button-text-hover));
                                            }
                                            .x-button-{{ block.id }} button.otsb-button-solid, .x-button-{{ block.id }} .otsb-button.otsb-button-solid {
                                            border: none;
                                            background-color: var(--colors-button-hover);
                                            }
                                            .x-button-{{ block.id }} button.otsb-button-disable-effect, .x-button-{{ block.id }} .otsb-button.otsb-button-disable-effect {
                                            color: rgba(var(--colors-button-text-hover));
                                            background-color: var(--colors-button-hover);
                                            }
                                            .x-button-{{ block.id }} .otsb-button-action {
                                            border: none;
                                            color: rgba(var(--colors-button-text-hover));
                                            background-color: var(--colors-button-hover);
                                            }
                                        {% else %}
                                            .x-button-{{ block.id }} [role="button"], .x-button-{{ block.id }} [type="button"], .x-button-{{ block.id }} .otsb-button {
                                            color: rgb(var(--colors-button-text));
                                            }
                                            .x-button-{{ block.id }} button.otsb-button-solid, .x-button-{{ block.id }} .otsb-button.otsb-button-solid {
                                            border: none;
                                            background-color: rgba(var(--colors-button));
                                            }
                                            .x-button-{{ block.id }} button.otsb-button-disable-effect, .x-button-{{ block.id }} .otsb-button.otsb-button-disable-effect {
                                            color: rgb(var(--colors-button-text));
                                            background-color: rgba(var(--colors-button));
                                            }
                                            .x-button-{{ block.id }} .otsb-button-action {
                                            border: none;
                                            color: rgb(var(--colors-button-text));
                                            background-color: rgba(var(--colors-button));
                                            }
                                        {% endif %}
                                        .x-button-{{ block.id }} [role="button"], .x-button-{{ block.id }} [type="button"], .x-button-{{ block.id }} .otsb-button {
                                        direction: ltr;
                                        }

                                        {% if block.settings.button_animation == 'sliced' %}
                                            .x-button-{{ block.id }} .otsb-button.otsb-button-outline:not(.not-icon), .x-button-{{ block.id }} .otsb-button.otsb-button-solid:not(.not-icon) {
                                            display: inline-flex;
                                            align-items: center;
                                            justify-content: center;
                                            padding-left: 1.5rem;
                                            padding-right: 1.5rem;
                                            }
                                            .x-button-{{ block.id }} .otsb-button-solid .otsb-button-icon, .x-button-{{ block.id }} .otsb-button-solid .otsb-button-text {
                                            transition-timing-function: cubic-bezier(0,.71,.4,1);
                                            }
                                            .x-button-{{ block.id }} .otsb-button-solid .otsb-button-icon {
                                            transition: opacity .25s,transform .5s;
                                            }
                                            .x-button-{{ block.id }} .otsb-button-solid .otsb-button-text {
                                            transition: transform .5s;
                                            transform: translateX(0.9rem);
                                            }
                                            .x-button-{{ block.id }} .otsb-button-solid:hover .otsb-button-text {
                                            opacity: 1;
                                            transform: translateX(0px);
                                            }
                                            .x-button-{{ block.id }} .otsb-button-solid:hover .otsb-button-icon {
                                            opacity: 1;
                                            transform: translateX(0.3125rem);
                                            }
                                        {% endif %}
                                        {% if block.settings.button_animation == 'underline' %}
                                            .x-button-{{ block.id }} .otsb-button-solid .otsb-button-text {
                                            position: relative;
                                            display: block;
                                            }
                                            .x-button-{{ block.id }} .otsb-button-solid .otsb-button-text::after {
                                            content: "";
                                            pointer-events: none;
                                            bottom: 1px;
                                            left: 50%;
                                            position: absolute;
                                            width: 0%;
                                            height: 1px;
                                            background-color: rgba(var(--colors-button-text));
                                            transition-timing-function: cubic-bezier(0.25, 0.8, 0.25, 1);
                                            transition-duration: 400ms;
                                            transition-property: width, left;
                                            }
                                            .x-button-{{ block.id }} .otsb-button-solid:hover .otsb-button-text::after {
                                            {% if block.settings.button_color_mobile == "hover" %}
                                                background-color: rgba(var(--colors-button-text-hover));
                                            {% else %}
                                                background-color: rgba(var(--colors-button-text));
                                            {% endif %}
                                            width: 100%;
                                            left: 0%;
                                            }
                                        {% endif %}

                                        @media (min-width: 1024px){
                                        .x-button-{{ block.id }} [role="button"], .x-button-{{ block.id }} [type="button"], .x-button-{{ block.id }} .otsb-button {
                                        color: rgba(var(--colors-button-text));
                                        }
                                        .x-button-{{ block.id }} button.otsb-button-solid, .x-button-{{ block.id }} .otsb-button.otsb-button-solid {
                                        border: none;
                                        box-shadow: none;
                                        color: rgb(var(--colors-button-text));
                                        background-color: rgba(var(--colors-button));
                                        overflow: hidden;
                                        background-origin: border-box;
                                        }
                                        .x-button-{{ block.id }} button.otsb-button-solid:hover, .x-button-{{ block.id }} .otsb-button.otsb-button-solid:hover {
                                        {% if block.settings.button_animation == 'sliced' or block.settings.button_animation == 'underline' %}
                                            transition-duration: 0.2s;
                                        {% else %}
                                            transition-delay: 0.5s;
                                        {% endif %}
                                        transition-property: background-color;
                                        background-color: var(--colors-button-hover);
                                        color: rgba(var(--colors-button-text-hover));
                                        background-origin: border-box;
                                        }
                                        .x-button-{{ block.id }} .otsb-button-action {
                                        border: none;
                                        color: rgba(var(--colors-button-text-hover));
                                        background-color: var(--colors-button-hover);
                                        }
                                        .x-button-{{ block.id }} button.otsb-button-disable-effect, .x-button-{{ block.id }} .otsb-button.otsb-button-disable-effect {
                                        color: rgb(var(--colors-button-text));
                                        background-color: rgba(var(--colors-button));
                                        }
                                        .x-button-{{ block.id }} button.otsb-button-disable-effect:hover, .x-button-{{ block.id }} .otsb-button.otsb-button-disable-effect:hover {
                                        color: rgba(var(--colors-button-text-hover));
                                        background-color: var(--colors-button-hover);
                                        }
                                        {% if block.settings.button_animation == 'slide' or block.settings.button_animation == 'fill_up' %}
                                            .x-button-{{ block.id }} button.otsb-button-solid:before, .x-button-{{ block.id }} .otsb-button.otsb-button-solid:before {
                                            content: "";
                                            z-index: -1;
                                            position: absolute;
                                            top: 0;
                                            right: 0;
                                            bottom: 0;
                                            left: 0;
                                            width: var(--button-width);
                                            height: var(--button-height);
                                            background-color: var(--colors-button-hover);
                                            backface-visibility: hidden;
                                            will-change: transform;
                                            transform: var(--button-transform);
                                            transform-origin: var(--button-transform-origin);
                                            transition: transform 0.5s ease;
                                            }
                                            .x-button-{{ block.id }} button.otsb-button-solid:hover:before, .x-button-{{ block.id }} .otsb-button.otsb-button-solid:hover:before {
                                            transform: rotate3d(0,0,1,0) translateZ(0);
                                            }
                                        {% endif %}
                                        {% if block.settings.button_animation == 'underline' %}
                                            .x-button-{{ block.id }} .otsb-button-solid:hover .otsb-button-text::after {
                                            background-color: rgba(var(--colors-button-text-hover));
                                            }
                                        {% endif %}
                                        }
                                        @media (min-width: 768px){
                                            .otsb__root .x-button-{{ block.id }} {
                                                justify-content: {{ block.settings.alignment }};
                                            }
                                        }
                                        .otsb__root .x-button-{{ block.id }} .otsb-button.otsb-button-outline {
                                        {% if block.settings.color_button_secondary.alpha != 0.0 %}
                                            --background-secondary-button: {{ block.settings.color_button_secondary.red }}, {{ block.settings.color_button_secondary.green }}, {{ block.settings.color_button_secondary.blue }};
                                            --colors-line-and-border: {{ block.settings.color_button_secondary.red }}, {{ block.settings.color_button_secondary.green }}, {{ block.settings.color_button_secondary.blue }};
                                            --colors-line-secondary-button: {{ block.settings.color_button_secondary.red }}, {{ block.settings.color_button_secondary.green }}, {{ block.settings.color_button_secondary.blue }};
                                        {% endif %}
                                        {% if block.settings.secondary_button_text.alpha != 0.0 %}
                                            --color-secondary-button-text: {{ block.settings.secondary_button_text.red }}, {{ block.settings.secondary_button_text.green }}, {{ block.settings.secondary_button_text.blue }}
                                        {% endif %}
                                        }
                                        .otsb__root .x-button-{{ block.id }} .otsb-button.otsb-button-outline {
                                        background: rgba(var(--background-secondary-button), 0.7);
                                        color: rgba(var(--color-secondary-button-text));
                                        }

                                    {% endstyle %}
                                    {% comment %} End button design {% endcomment %}
                                    <div
                                        class="pointer-events-auto x-button-{{ block.id }} x-button-{{ section.id }} mt-4 lg:mt-9 inline-flex flex-wrap gap-x-1.5 gap-y-2 p-break-words justify-{{block.settings.alignment_mobile}}"  {{ block.shopify_attributes }}>
                                        {%- if block.settings.button_label_1 != blank -%}
                                            <a {% if block.settings.button_link_1 %} href="{{ block.settings.button_link_1 }}" onlick="location.href({{ block.settings.button_link_1 }})" {% if block.settings.open_new_window_button %} target="_blank"{% endif %}{% else %} role="link" aria-disabled="true"{% endif %}
                                                class="otsb-button button--{{ block.id }} button--{{ section.id }} {% if block.settings.button_primary_1 %} otsb-button-solid{% else %} otsb-button-outline{% endif %} border ml-0.5 mr-0.5 mt-0.5 mb-0.5 empty:otsb-hidden pl-6 pr-6 lg:pl-9 lg:pr-9 pt-2.5 pb-2.5 md:pt-3 md:pb-3 leading-normal{% unless block.settings.button_link_1 %} hover:cursor-not-allowed opacity-70{% endunless %}">
                                                {% render 'otsb-button-label', button_animation: block.settings.button_animation, custom_icon_button: block.settings.custom_icon_button, button_label: block.settings.button_label_1, show_button_primary: block.settings.button_primary_1 %}
                                            </a>
                                        {%- endif -%}
                                        {%- if block.settings.button_label_2 != blank -%}
                                            <a {% if block.settings.button_link_2 %} href="{{ block.settings.button_link_2 }}" onlick="location.href({{ block.settings.button_link_2 }})" {% if block.settings.open_new_window_button_2 %} target="_blank"{% endif %}{% else %} role="link" aria-disabled="true"{% endif %}
                                                class="otsb-button button--{{ block.id }} button--{{ section.id }} {% if block.settings.button_primary_2 %} otsb-button-solid{% else %} otsb-button-outline{% endif %} border ml-0.5 mr-0.5 mt-0.5 mb-0.5 empty:otsb-hidden pl-6 pr-6 lg:pl-9 lg:pr-9 pt-2.5 pb-2.5 md:pt-3 md:pb-3 leading-normal{% unless block.settings.button_link_2 %} hover:cursor-not-allowed opacity-70{% endunless %}">
                                                {% render 'otsb-button-label', button_animation: block.settings.button_animation, custom_icon_button: block.settings.custom_icon_button, button_label: block.settings.button_label_2, show_button_primary: block.settings.button_primary_2 %}
                                            </a>
                                        {%- endif -%}
                                    </div>
                                {%- endif -%}
                            </div>
                        </div>
                    </div>
                </div>
                {% when 'slide_text' %}
                    {%- liquid
                      assign heading_size = block.settings.heading_size | times: 100 | times: 0.000225
                      assign imageUrlMobile = block.settings.image
                      if block.settings.image_mobile != blank
                        assign imageUrlMobile = block.settings.image_mobile
                      endif
                    -%}
                    {%- style -%}
                      {%- unless block.settings.color_text.alpha == 0.0 -%}
                        #shopify-section-{{ section.id }} .x-slideshow-{{ block.id }} {
                          --image-treatment-text: {{ block.settings.color_text.red }}, {{ block.settings.color_text.green }}, {{ block.settings.color_text.blue }};
                        }
                      {%- endunless -%}
                      .block-text--{{ block.id }} {
                        --h1-font-size: calc(var(--font-heading-scale) * 4rem);
                        --h2-font-size: calc(var(--font-heading-scale) * 2.4rem);
                        --h3-font-size: calc(var(--font-heading-scale) * 1.8rem);
                        --h4-font-size: calc(var(--font-heading-scale) * 1.5rem);
                        --h5-font-size: calc(var(--font-heading-scale) * 1.3rem);
                        --h6-font-size: calc(var(--font-heading-scale) * 1.1rem);
                        --h1-font-size-mobile: calc(var(--font-heading-scale) * 3rem);
                        --h2-font-size-mobile: calc(var(--font-heading-scale) * 2rem);
                        --h3-font-size-mobile: calc(var(--font-heading-scale) * 1.7rem);
                        --h4-font-size-mobile: calc(var(--font-heading-scale) * 1.5rem);
                        --h5-font-size-mobile: calc(var(--font-heading-scale) * 1.2rem);
                        --h6-font-size-mobile: calc(var(--font-heading-scale) * 1.1rem);
                        }
                      #shopify-section-{{ section.id }} .block-text--{{ block.id }} {
                        {% if block.settings.content_background_color.alpha != 0.0 %}
                          background: {{ block.settings.content_background_color }};
                        {% endif %}
                        {% if block.settings.color_text.alpha != 0.0 %}
                          color: {{ block.settings.color_text }};
                        {% endif %}
                        }
                        #shopify-section-{{ section.id }} .button--{{block.id}}.otsb-btn__text-link {
                        border:0;
                        padding:.75rem 1.25rem;
                      }
                      .button--{{ block.id }}.otsb-button-solid,
                      .button--{{ block.id }}.otsb-button-solid:before,
                      .button--{{ block.id }}.otsb-btn__solid:hover {
                        {%- unless block.settings.color_button.alpha == 0.0 -%}
                          --colors-line-and-border: {{ block.settings.color_button.red }}, {{ block.settings.color_button.green }}, {{ block.settings.color_button.blue }};
                          --color-button: {{ block.settings.color_button.red }}, {{ block.settings.color_button.green }}, {{ block.settings.color_button.blue }};
                          --color-button-mobile: {{ block.settings.color_button.red }}, {{ block.settings.color_button.green }}, {{ block.settings.color_button.blue }};
                        {%- else -%}
                          --color-button: var(--colors-button);
                          --colors-line-and-border: var(--color-button);
                        {%- endunless -%}
                        {%- if block.settings.color_button_hover.alpha != 0.0 -%}
                          --colors-button-hover: rgb({{ block.settings.color_button_hover.red }}, {{ block.settings.color_button_hover.green }}, {{ block.settings.color_button_hover.blue }});
                        {%- else -%}
                         --colors-button-hover: rgba(var(--color-button));
                        {% endif %}
                        {%- unless block.settings.color_text_button.alpha == 0.0 -%}
                          --color-button-text: {{ block.settings.color_text_button.red }}, {{ block.settings.color_text_button.green }}, {{ block.settings.color_text_button.blue }};
                          --color-button-text-mobile: rgb({{ block.settings.color_text_button.red }}, {{ block.settings.color_text_button.green }}, {{ block.settings.color_text_button.blue }});
                        {%- endunless -%}
                        {%- if block.settings.color_text_button_hover.alpha != 0.0 -%}
                          --colors-button-text-hover: {{ block.settings.color_text_button_hover.red }}, {{ block.settings.color_text_button_hover.green }}, {{ block.settings.color_text_button_hover.blue }};
                        {%- else -%}
                          --colors-button-text-hover: var(--color-button-text);
                        {% endif %}
                      }
                      .button--{{ block.id }}.otsb-button-outline {
                        {%- if block.settings.secondary_button_text.alpha != 0.0 -%} 
                          --colors-secondary-button: {{ block.settings.secondary_button_text.red }}, {{ block.settings.secondary_button_text.green }}, {{ block.settings.secondary_button_text.blue }}; 
                          --colors-line-secondary-button: {{ block.settings.secondary_button_text.red }}, {{ block.settings.secondary_button_text.green }}, {{ block.settings.secondary_button_text.blue }}; 
                          --background-secondary-button: transparent;
                        {% endif %}
                        {%- if block.settings.color_button_secondary.alpha != 0.0 -%} 
                          --background-secondary-button: {{ block.settings.color_button_secondary.red }}, {{ block.settings.color_button_secondary.green }}, {{ block.settings.color_button_secondary.blue }}; 
                          --colors-line-secondary-button: {{ block.settings.color_button_secondary.red }}, {{ block.settings.color_button_secondary.green }}, {{ block.settings.color_button_secondary.blue }}; 
                        {% endif %}
                      }
                      .button--{{ block.id }}.otsb-button-text-link, .button--{{ block.id }}.otsb-button-text-link::after, .button--{{ block.id }}.otsb-button-text-link::before, a {
                        {% if block.settings.colors_text_link.alpha != 0.0 %}
                        --colors-text-link: {{ block.settings.colors_text_link.red }}, {{ block.settings.colors_text_link.green }}, {{ block.settings.colors_text_link.blue }};
                        {% else %}
                        --colors-text-link: var(--color-link);
                        {% endif %}
                      }
                      
                      {%- if block.settings.content_alignment == "custom" -%}
                        .content--{{ block.id }}{
                          left: {{ block.settings.custom_horizontal }}%;
                          top: {{ block.settings.custom_vertical }}%;
                          transform: translate(-{{ block.settings.custom_horizontal }}%,-{{ block.settings.custom_vertical }}%);
                        }
                      {%- endif -%}
                      #shopify-section-{{ section.id }} .heading-{{ block.id }} {
                        font-size: {{ heading_size | times: 0.5 }}rem;
                      }
                      #shopify-section-{{ section.id }} .sub-heading-{{ block.id }} {
                        font-size: {{ heading_size | times: 0.3 }}rem;
                      }
                    
                      .image-{{ block.id }} {
                        object-position:{{ imageUrlMobile.presentation.focal_point }};
                      }
                      #shopify-section-{{ section.id }} .heading--{{ block.id }},#shopify-section-{{ section.id }} .content-text-{{ block.id }} {
                        {% if block.settings.heading_color.alpha != 0.0 %}
                            --colors-heading: {{ block.settings.heading_color.red }}, {{ block.settings.heading_color.green }}, {{ block.settings.heading_color.blue }};
                        {% elsif colors_image_treatment_text != blank %}
                            --colors-heading: colors_image_treatment_text
                        {% elsif settings.colors-heading != blank %}
                            --colors-heading: settings.colors-heading;
                        {% else %}
                            --colors-heading: var(--color-foreground); 
                        {% endif %}
                        }
                    

                      @media screen and (min-width: 768px) {
                        #shopify-section-{{ section.id }} .heading-{{ block.id }} {
                          font-size: {{ heading_size }}rem;
                        }
                        #shopify-section-{{ section.id }} .sub-heading-{{ block.id }} {
                          font-size: {{ heading_size | times: 0.58 }}rem;
                        }
                        .image-{{ block.id }} {
                          object-position:{{ block.settings.image.presentation.focal_point }};
                        }
                        #image-paginate--{{ section.id }}[image-next-active="{{ forloop.index | minus: 1 }}"] {
                          {% if block.settings.image != blank %}
                            background-image: url({{ block.settings.image | image_url: width: 300 }});
                          {% elsif check_video %}
                            {% if video_type == 'video_select' %}
                              background-image: url({{ block.settings.video.preview_image | image_url: width: 300 }});
                            {% elsif video_type == 'youtube' %}
                              background-image: url({{ video_id | prepend: 'https://i.ytimg.com/vi_webp/' | append: '/maxresdefault.webp' }});
                            {% endif %}
                          {% else %}
                            background-color: #c9c9c9;
                          {% endif %}
                          background-size: cover;
                          background-repeat: no-repeat;
                          background-position: center;
                        }
                      }
                    {%- endstyle -%}
                    {% if section.settings.show_hero and forloop.first %}
                      <div class="hidden">
                        {%- if block.settings.image_mobile != blank -%}
                          {{ imageUrlMobile | image_url: width: 1500 | image_tag: widths: '375, 450, 750, 900, 1100, 1500', preload: true, loading: 'lazy' }}
                        {%- endif -%}
                        {{ block.settings.image | image_url: width: 3840 | image_tag: widths: '750, 450, 550, 750, 890, 1000, 1500, 1920', preload: true, loading: 'lazy' }}
                      </div>
                    {% endif %}
                    <div x-slide-index="{{ forloop.index | minus: 1 }}" 
                      class="x-slideshow-{{ block.id }}{% if section.settings.show_hero and forloop.first %} disable-animation-slideshow {% endif %} ltr h-full flex flex-col md:flex-row {% if block.settings.mobile_layout == 'bottom' %} flex-col-reverse{% endif %}{% if block.settings.desktop_layout == 'right' %} md:flex-row-reverse{% endif %} relative transition-opacity flex-shrink-0 w-full{% if enable_slider %} splide__slide x-splide-slide{% endif %}{% if section.settings.show_hero %} w-full{% endif %}"
                      x-data="{effect: false}" 
                      {% if section.settings.transition_style != "fade" or enable_slider == false %} 
                        {%- if section.settings.full_width %} x-intersect:leave.margin.-40px="effect = false"{% else %} x-intersect:leave.margin.-20px.half.-20px.haft="effect = false"{% endif %}
                        x-intersect.half="effect = true" 
                      {% endif %} 
                      {{ block.shopify_attributes }}
                    >
                      {% if section.settings.transition_style == "fade" and enable_slider %}
                        <div x-intersect.half="effect = true" x-intersect:leave.margin.-20px="effect = false" class="absolute top-0 left-0 w-full h-full hidden active-slide-fade{% if forloop.first %} active-slide-fade-first{% endif %}">
                        </div>
                      {% endif %}
                      <div class="h-full md:w-3/5 relative overflow-hidden">
                        <div :class="effect && 'active'" class="h-full w-full absolute top-0 left-0 z-0 animate_transition_slide__image">
                          {%- if block.settings.image == blank -%}
                            <div class="image-svg w-full h-full bg-[#c9c9c9] text-[#acacac]{% if block.settings.image_mobile != blank %} otsb-hidden{% endif %} md:block mx-auto">
                              {{ 'lifestyle-2' | placeholder_svg_tag: 'w-full h-full' }}
                            </div>
                          {%- else -%}
                            {% comment %}theme-check-disable ImgLazyLoading{% endcomment %}
                            <parallax-image class="{% if block.settings.image_mobile != blank and request.design_mode %} otsb-hidden md:block{% endif %} h-full w-full">
                              <parallax-movement x-data="xParallax" x-intersect.once.margin.200px="load({{ settings.disable_parallax_effect }})">
                                <picture>
                                  <source
                                    srcset="{{ imageUrlMobile | image_url: width: 375 }} 375w,
                                      {{ imageUrlMobile | image_url: width: 450 }} 450w,
                                      {{ imageUrlMobile | image_url: width: 750 }} 750w,
                                      {{ imageUrlMobile | image_url: width: 900 }} 900w,
                                      {{ imageUrlMobile | image_url: width: 1100 }} 1100w,
                                      {{ imageUrlMobile | image_url: width: 1500 }} 1500w"
                                    media="(max-width: 767px)"
                                    width="{{ imageUrlMobile.width }}"
                                    height="{{ imageUrlMobile.height }}"
                                  >
                                  <img
                                    srcset="{{ block.settings.image | image_url: width: 750 }} 750w,
                                      {{ block.settings.image | image_url: width: 450 }} 900w,
                                      {{ block.settings.image | image_url: width: 550 }} 1100w,
                                      {{ block.settings.image | image_url: width: 750 }} 1500w,
                                      {{ block.settings.image | image_url: width: 890 }} 1780w,
                                      {{ block.settings.image | image_url: width: 1000 }} 2000w,
                                      {{ block.settings.image | image_url: width: 1500 }} 3000w,
                                      {{ block.settings.image | image_url: width: 1920 }} 3840w"
                                    {% unless section.settings.show_hero and forloop.first %}
                                      loading="lazy"
                                    {% else %}
                                      loading="eager"
                                      fetchpriority="high"
                                      decoding="sync"
                                    {% endunless %}
                                    sizes="100vw"
                                    src="{{ block.settings.image | image_url: width: 3840 }}"
                                    {% if block.settings.image_mobile != blank %}
                                      :alt="(screen.width < 768) ? '{{ block.settings.image_mobile.alt }}' : '{{ block.settings.image.alt }}'"
                                    {% else %}
                                      alt="{{ block.settings.image.alt | escape }}"
                                    {% endif %}
                                    class="object-cover h-full w-full image-{{ block.id }}"
                                    width="{{ block.settings.image.width }}"
                                    height="{{ block.settings.image.height }}"
                                  >
                                </picture>
                              </parallax-movement>
                            </parallax-image>
                            {% comment %}theme-check-enable ImgLazyLoading{% endcomment %}
                          {%- endif %}
                          {%- if block.settings.image_mobile != blank and request.design_mode or block.settings.image == blank -%}
                            {% comment %}theme-check-disable ImgLazyLoading{% endcomment %}
                            <img
                              srcset="{{ block.settings.image_mobile | image_url: width: 375 }} 375w,
                              {{ block.settings.image_mobile | image_url: width: 450 }} 450w,
                              {{ block.settings.image_mobile | image_url: width: 750 }} 750w,
                              {{ block.settings.image_mobile | image_url: width: 900 }} 900w,
                              {{ block.settings.image_mobile | image_url: width: 1100 }} 1100w,
                              {{ block.settings.image_mobile | image_url: width: 1500 }} 1500w"
                              sizes="100vw"
                              src="{{ block.settings.image_mobile | image_url: width: 750 }}"
                              alt="{{ block.settings.image_mobile.alt | escape }}"
                              class="object-cover h-full w-full{% if block.settings.image_mobile != blank %} md:hidden{% endif %}"
                              {% unless section.settings.show_hero and forloop.first %}
                                loading="lazy"
                              {% else %}
                                loading="eager"
                                fetchpriority="high"
                                decoding="sync"
                              {% endunless %}
                              width="{{ block.settings.image_mobile.width }}"
                              height="{{ block.settings.image_mobile.height }}"
                              style="object-position: {{ block.settings.image_mobile.presentation.focal_point }}"
                            >  
                            {% comment %}theme-check-enable ImgLazyLoading{% endcomment %}
                          {%- endif %}
                        </div>
                        {% if block.settings.image_link != blank %}
                          <a href="{{ block.settings.image_link }}"{% if block.settings.open_new_window_image %} target="_blank"{% endif %} class="absolute top-0 left-0 bottom-0 right-0 z-[19] opacity-0">{{ block.settings.image_link }}</a>
                        {% endif %}
                      </div>
                      {%- assign alignment = block.settings.content_alignment | split: '-' -%}
                      <div class="hero-text block-text--{{ block.id }} bg-[#c9c9c9] w-full md:w-2/5 pointer-events-none relative transition{% if settings.lang_direction contains request.locale.iso_code %} rtl{% endif %}{% unless block.settings.content_alignment == "custom" %} x-page-width page-width{% endunless %} z-20 h-fit md:h-full table">
                        <div class="table-cell max-w-2xl lg:w-3/5{% if block.settings.content_alignment == "custom" %} min-w-[90%] lg:min-w-[30rem] absolute content--{{ block.id }} text-{{ block.settings.alignment }} pl-5 pr-5{% else %} align-{{ alignment[0] }} text-{{ block.settings.alignment }}{% endif %}">
                          <div :class="effect && 'active'" class="xl:pl-10 xl:pr-10 mx-auto{% if block.settings.content_alignment != 'custom' %} max-w-2xl pt-5 pr-3 pl-3 pb-5 float-{{ alignment[1] }}{% endif %}{% if section.settings.slider_visual != "none" %} pb-10 lg:pb-12{% endif %}">
                            {%- if block.settings.subheading != blank -%}
                              <p class="sub-heading-{{ block.id }} leading-tight italic p-break-words image-treatment-text">{{ block.settings.subheading | escape }}</p>
                            {%- endif -%}
                            {%- if block.settings.heading != blank -%}
                              <{{ block.settings.heading_tag }} class="p-break-words heading-{{ block.id }} h2 block mt-1 leading-tight image-treatment-text ">
                                {% render 'otsb-heading-highlight',
                                  headingId: block.id,
                                  heading: block.settings.heading,
                                  highlight_type: block.settings.highlight_type,
                                  color_heading_highlight_light: block.settings.color_heading_highlight,
                                  color_text: section.settings.text
                                %}  
                              </{{ block.settings.heading_tag }}>
                            {%- endif -%}
                            {%- if block.settings.text != blank -%}
                              <div class="lg:mt-1 p-break-words pointer-events-auto rte image-treatment-text text-medium rte otsb-rte">
                                {{ block.settings.text }}
                              </div>
                            {%- endif -%}
                            {%- if block.settings.button_label_1 != blank or block.settings.button_label_2 != blank -%}
                              {%- liquid
                                case block.settings.button_type
                                when 'rounded'
                                assign borderRadius = '100px'
                                when 'rounded_corners'
                                assign borderRadius = '6px'
                                when 'mixed'
                                assign borderRadius = '6px 0 6px 0'
                                else
                                assign borderRadius = '0'
                                endcase
                              %}
                              {% style %}
                              .x-button-{{ block.id }} .button--{{ block.id }} {
                                --border-radius: {{ borderRadius }};
                                {% if block.settings.button_animation == 'slide' %}
                                --button-width: 102%;
                                --button-height: 500%;
                                --button-transform: rotate3d(0,0,1,-10deg) translate3d(-130%,-10em,0);
                                --button-transform-origin: 100% 0%;
                                {% elsif block.settings.button_animation == 'fill_up' %}
                                --button-width: 120%;
                                --button-height: 100%;
                                --button-transform: rotate3d(0,0,1,10deg) translate3d(-1.2em,110%,0);
                                --button-transform-origin: 0% 100%;
                                {% endif %}
                                }
                              {% if block.settings.button_color_mobile == "hover" %}
                              .x-button-{{ block.id }} [role="button"], .x-button-{{ block.id }} [type="button"], .x-button-{{ block.id }}
                              .otsb-button {
                              color: rgb(var(--colors-button-text-hover));
                              }
                              .x-button-{{ block.id }} button.otsb-button-solid, .x-button-{{ block.id }} .otsb-button.otsb-button-solid {
                              border: none;
                              background-color: var(--colors-button-hover);
                              }
                              .x-button-{{ block.id }} button.otsb-button-disable-effect, .x-button-{{ block.id }}
                              .otsb-button.otsb-button-disable-effect {
                              color: rgba(var(--colors-button-text-hover));
                              background-color: var(--colors-button-hover);
                              }
                              .x-button-{{ block.id }} .otsb-button-action {
                              border: none;
                              color: rgba(var(--colors-button-text-hover));
                              background-color: var(--colors-button-hover);
                              }
                              {% else %}
                              .x-button-{{ block.id }} [role="button"], .x-button-{{ block.id }} [type="button"], .x-button-{{ block.id }}
                              .otsb-button {
                              color: rgb(var(--color-button-text));
                              }
                              .x-button-{{ block.id }} button.otsb-button-solid, .x-button-{{ block.id }} .otsb-button.otsb-button-solid {
                              border: none;
                              background-color: rgba(var(--color-button));
                              }
                              .x-button-{{ block.id }} button.otsb-button-disable-effect, .x-button-{{ block.id }}
                              .otsb-button.otsb-button-disable-effect {
                              color: rgb(var(--color-button-text));
                              background-color: rgba(var(--colors-button));
                              }
                              .x-button-{{ block.id }} .otsb-button-action {
                              border: none;
                              color: rgb(var(--color-button-text));
                              background-color: rgba(var(--colors-button));
                              }
                              {% endif %}
                              .x-button-{{ block.id }} [role="button"], .x-button-{{ block.id }} [type="button"], .x-button-{{ block.id }}
                              .otsb-button {
                              direction: ltr;
                              }
                              
                              {% if block.settings.button_animation == 'sliced' %}
                              .x-button-{{ block.id }} .otsb-button.otsb-button-outline:not(.not-icon), .x-button-{{ block.id }}
                              .otsb-button.otsb-button-solid:not(.not-icon) {
                              display: inline-flex;
                              align-items: center;
                              justify-content: center;
                              padding-left: 1.5rem;
                              padding-right: 1.5rem;
                              }
                              .x-button-{{ block.id }} .otsb-button-solid .otsb-button-icon, .x-button-{{ block.id }} .otsb-button-solid
                              .otsb-button-text {
                              transition-timing-function: cubic-bezier(0,.71,.4,1);
                              }
                              .x-button-{{ block.id }} .otsb-button-solid .otsb-button-icon {
                              transition: opacity .25s,transform .5s;
                              }
                              .x-button-{{ block.id }} .otsb-button-solid .otsb-button-text {
                              transition: transform .5s;
                              transform: translateX(0.9rem);
                              }
                              .x-button-{{ block.id }} .otsb-button-solid:hover .otsb-button-text {
                              opacity: 1;
                              transform: translateX(0px);
                              }
                              .x-button-{{ block.id }} .otsb-button-solid:hover .otsb-button-icon {
                              opacity: 1;
                              transform: translateX(0.3125rem);
                              }
                              {% endif %}
                              {% if block.settings.button_animation == 'underline' %}
                              .x-button-{{ block.id }} .otsb-button-solid .otsb-button-text {
                              position: relative;
                              display: block;
                              }
                              .x-button-{{ block.id }} .otsb-button-solid .otsb-button-text::after {
                              content: "";
                              pointer-events: none;
                              bottom: 1px;
                              left: 50%;
                              position: absolute;
                              width: 0%;
                              height: 1px;
                              background-color: rgba(var(--colors-button-text));
                              transition-timing-function: cubic-bezier(0.25, 0.8, 0.25, 1);
                              transition-duration: 400ms;
                              transition-property: width, left;
                              }
                              .x-button-{{ block.id }} .otsb-button-solid:hover .otsb-button-text::after {
                              {% if block.settings.button_color_mobile == "hover" %}
                              background-color: rgba(var(--colors-button-text-hover));
                              {% else %}
                              background-color: rgba(var(--colors-button-text));
                              {% endif %}
                              width: 100%;
                              left: 0%;
                              }
                              {% endif %}
                              
                              @media (min-width: 1024px){
                              .x-button-{{ block.id }} [role="button"], .x-button-{{ block.id }} [type="button"], .x-button-{{ block.id }}
                              .otsb-button {
                              color: rgba(var(--color-button-text));
                              }
                              .x-button-{{ block.id }} button.otsb-button-solid, .x-button-{{ block.id }} .otsb-button.otsb-button-solid {
                              border: none;
                              box-shadow: none;
                              color: rgb(var(--color-button-text));
                              background-color: rgba(var(--color-button));
                              overflow: hidden;
                              background-origin: border-box;
                              }
                              .x-button-{{ block.id }} button.otsb-button-solid:hover, .x-button-{{ block.id }} .otsb-button.otsb-button-solid:hover {
                              {% if block.settings.button_animation == 'sliced' or block.settings.button_animation == 'underline' %}
                              transition-duration: 0.2s;
                              {% else %}
                              transition-delay: 0.5s;
                              {% endif %}
                              transition-property: background-color;
                              background-color: var(--colors-button-hover);
                              color: rgba(var(--colors-button-text-hover));
                              background-origin: border-box;
                              }
                              .x-button-{{ block.id }} .otsb-button-action {
                              border: none;
                              color: rgba(var(--colors-button-text-hover));
                              background-color: var(--colors-button-hover);
                              }
                              .x-button-{{ block.id }} button.otsb-button-disable-effect, .x-button-{{ block.id }}
                              .otsb-button.otsb-button-disable-effect {
                              color: rgb(var(--color-button-text));
                              background-color: rgba(var(--colors-button));
                              }
                              .x-button-{{ block.id }} button.otsb-button-disable-effect:hover, .x-button-{{ block.id }}
                              .otsb-button.otsb-button-disable-effect:hover {
                              color: rgba(var(--colors-button-text-hover));
                              background-color: var(--colors-button-hover);
                              }
                              {% if block.settings.button_animation == 'slide' or block.settings.button_animation == 'fill_up' %}
                              .x-button-{{ block.id }} button.otsb-button-solid:before, .x-button-{{ block.id }} .otsb-button.otsb-button-solid:before
                              {
                              content: "";
                              z-index: -1;
                              position: absolute;
                              top: 0;
                              right: 0;
                              bottom: 0;
                              left: 0;
                              width: var(--button-width);
                              height: var(--button-height);
                              background-color: var(--colors-button-hover);
                              backface-visibility: hidden;
                              will-change: transform;
                              transform: var(--button-transform);
                              transform-origin: var(--button-transform-origin);
                              transition: transform 0.5s ease;
                              }
                              .x-button-{{ block.id }} button.otsb-button-solid:hover:before, .x-button-{{ block.id }}
                              .otsb-button.otsb-button-solid:hover:before {
                              transform: rotate3d(0,0,1,0) translateZ(0);
                              }
                              {% endif %}
                              {% if block.settings.button_animation == 'underline' %}
                              .x-button-{{ block.id }} .otsb-button-solid:hover .otsb-button-text::after {
                              background-color: rgba(var(--colors-button-text-hover));
                              }
                              {% endif %}
                              }
                              @media (min-width: 768px){
                              .otsb__root .x-button-{{ block.id }} {
                              justify-content: {{ block.settings.alignment }};
                              }
                              }
                              .otsb__root .x-button-{{ block.id }} .otsb-button.otsb-button-outline {
                              {% if block.settings.color_button_secondary.alpha != 0.0 %}
                              --background-secondary-button: {{ block.settings.color_button_secondary.red }}, {{
                              block.settings.color_button_secondary.green }}, {{ block.settings.color_button_secondary.blue }};
                              --colors-line-and-border: {{ block.settings.color_button_secondary.red }}, {{
                              block.settings.color_button_secondary.green }}, {{ block.settings.color_button_secondary.blue }};
                              --colors-line-secondary-button: {{ block.settings.color_button_secondary.red }}, {{
                              block.settings.color_button_secondary.green }}, {{ block.settings.color_button_secondary.blue }};
                              {% endif %}
                              {% if block.settings.secondary_button_text.alpha != 0.0 %}
                              --color-secondary-button-text: {{ block.settings.secondary_button_text.red }}, {{
                              block.settings.secondary_button_text.green }}, {{ block.settings.secondary_button_text.blue }}
                              {% endif %}
                              }
                              .otsb__root .x-button-{{ block.id }} .otsb-button.otsb-button-outline {
                              background: rgba(var(--background-secondary-button), 0.7);
                              color: rgba(var(--color-secondary-button-text));
                              }
                              
                              {% endstyle %}
                              <div class="x-button-{{ block.id }} x-button-{{ section.id }} mt-4 pointer-events-auto lg:mt-9 inline-flex flex-wrap gap-y-2 p-break-words gap-x-1{% if block.settings.content_alignment == 'custom' or block.settings.content_alignment contains 'center' %} justify-center{% else %} justify-{{ alignment[1] }}{% endif %}" {{ block.shopify_attributes }}>
                                {%- if block.settings.button_label_1 != blank -%}
                                  <a{% if block.settings.button_link_1 %} href="{{ block.settings.button_link_1 }}"{% if block.settings.open_new_window_button %} target="_blank"{% endif %}{% else %} role="link" aria-disabled="true"{% endif %}   class="otsb-button button--{{ block.id }} button--{{ section.id }} {% if block.settings.show_button_style_1 == 'primary' %} otsb-btn__solid otsb-button-solid{% elsif block.settings.show_button_style_1 == 'secondary' %} otsb-button-outline {% elsif block.settings.show_button_style_1 == 'text-link' %} otsb-btn__text-link otsb-button-text-link{% endif %} border ml-0.5 mr-0.5 mt-0.5 mb-0.5 empty:otsb-hidden pl-6 pr-6 lg:pl-9 lg:pr-9 pt-2.5 pb-2.5 md:pt-3 md:pb-3 leading-normal{% unless block.settings.button_link_1 %} hover:cursor-not-allowed opacity-70{% endunless %}">
                                    {% render 'otsb-button-label', button_animation: block.settings.button_animation, custom_icon_button: block.settings.custom_icon_button, button_label: block.settings.button_label_1, show_button_style: block.settings.show_button_style_1 %}
                                  </a> 
                                {%- endif -%}
                                {%- if block.settings.button_label_2 != blank -%}
                                  <a{% if block.settings.button_link_2 %} href="{{ block.settings.button_link_2 }}"{% if block.settings.open_new_window_button_2 %} target="_blank"{% endif %}{% else %} role="link" aria-disabled="true"{% endif %}   class="otsb-button button--{{ block.id }} button--{{ section.id }} {% if block.settings.show_button_style_2 == 'primary' %} otsb-btn__solid otsb-button-solid{% elsif block.settings.show_button_style_2 == 'secondary' %} otsb-button-outline {% elsif block.settings.show_button_style_2 == 'text-link' %} otsb-btn__text-link otsb-button-text-link {% endif %} border ml-0.5 mr-0.5 mt-0.5 mb-0.5 empty:otsb-hidden pl-6 pr-6 lg:pl-9 lg:pr-9 pt-2.5 pb-2.5 md:pt-3 md:pb-3 leading-normal{% unless block.settings.button_link_2 %} hover:cursor-not-allowed opacity-70{% endunless %}">
                                    {% render 'otsb-button-label', button_animation: block.settings.button_animation, custom_icon_button: block.settings.custom_icon_button, button_label: block.settings.button_label_2, show_button_style: block.settings.show_button_style_2 %}
                                  </a> 
                                {%- endif -%}
                              </div>
                            {%- endif -%}
                          </div>
                        </div>
                      </div>
                    </div>
                {% endcase %}
                {%- endfor -%}
            </div>
        </div>
        {%- if enable_slider and section.settings.show_arrow -%}
            <div class="splide__arrows">
                <button
                    class="splide__arrow splide__arrow--prev otsb-button-arrow otsb-hidden group-hover:lg:block absolute none_border z-2 w-8 h-8 lg:w-[48px] lg:h-[48px] rounded-full after:text-[20px] pt-2 pb-2 pl-2 pr-2 lg:p-[16px] top-1/2 left-0 -translate-y-1/2 rotate-90 lg:left-8 duration-100 opacity-60 hover:opacity-100 disabled:opacity-40 disabled:cursor-not-allowed"
                    aria-label="previous slide">
                    {% render 'otsb-icon-alls', icon: 'icon-caret' %}
                </button>
                <button
                    class="splide__arrow splide__arrow--next otsb-button-arrow otsb-hidden group-hover:lg:block absolute none_border z-2 w-8 h-8 lg:w-[48px] lg:h-[48px] rounded-full after:text-[20px] pt-2 pb-2 pl-2 pr-2 lg:p-[16px] top-1/2 right-0 -translate-y-1/2 -rotate-90 lg:right-8 duration-100 opacity-60 hover:opacity-100 disabled:opacity-40 disabled:cursor-not-allowed"
                    aria-label="next slide">
                    {% render 'otsb-icon-alls', icon: 'icon-caret' %}
                </button>
            </div>
        {%- endif -%}
    </div>
</div>
</div>
