.predictive-search {
  display: none;
  position: absolute;
  top: calc(100% + 0.1rem);
  left: 0rem;
  border-width: var(--popup-border-width);
  border-style: solid;
  border-color: rgba(var(--color-foreground), var(--popup-border-opacity));
  background-color: rgb(var(--color-background));
  z-index: 3;
  border-bottom-right-radius: var(--popup-corner-radius);
  border-bottom-left-radius: var(--popup-corner-radius);
  transition:all 0.3s linear;
  box-shadow: var(--popup-shadow-horizontal-offset) var(--popup-shadow-vertical-offset) var(--popup-shadow-blur-radius) rgba(var(--color-shadow),var(--popup-shadow-opacity));
}
.header__search.search-show .predictive-search { top:0;left:0;height: 100%!important;max-height: 100%!important;box-shadow:none;position:relative;  }
.header .search-modal__form .predictive-search #predictive-search-results{padding:50px;}
.header .search-modal__form .predictive-search {max-height: fit-content!important; }

/* .header .search-modal__form  .predictive-search .predictive-search__results-groups-wrapper{    flex-direction: column;} */
.predictive-search--search-template {
  z-index: 3;
}

@media screen and (max-width: 749px) {
  .predictive-search--header {
    right: 0;
    left: 0;
    top: 100%;
  }
}

@media screen and (max-width: 989px) {
  .predictive-search {
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    width:100%;
  }
}

@media screen and (min-width: 750px) {
  .predictive-search {
    border-top: none;
    width: calc(100% + 0rem);
  }
}
/* .header predictive-search {max-width:200px} */
.header .search-box .search-modal__content {
  justify-content: flex-start;
}
predictive-search[open] .predictive-search,
predictive-search[loading] .predictive-search {
  display: block;
}

.predictive-search__heading {
  border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.08);
  padding: 1.5rem 0 0.75rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: calc(100% - 4rem);
  color: rgba(var(--color-foreground), 0.7);
  transition: all var(--duration-default) linear;
  font-size: 1.4rem;
  font-weight: 500;
}

predictive-search .spinner {
  width: 1.5rem;
  height: 1.5rem;
  line-height: 0;
}

.predictive-search__heading .spinner {
  margin: 0 0.2rem 0 2rem;
}

predictive-search:not([loading]) .predictive-search__heading .spinner,
predictive-search:not([loading]) .predictive-search__loading-state,
predictive-search:not([loading]) .predictive-search-status__loading {
  display: none;
}

predictive-search[loading] .predictive-search__loading-state {
  display: flex;
  justify-content: center;
  padding: 1rem;
}

predictive-search[loading]
  .predictive-search__heading
  ~ .predictive-search__loading-state,
predictive-search[loading] .predictive-search__results-list:first-child {
  display: none;
}

/* .predictive-search__list-item:nth-last-child(2) {
  border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.08);
} */

.predictive-search__list-item[aria-selected="true"] > *,
.predictive-search__list-item:hover > * {
  color: rgb(var(--color-foreground));
  transition: all var(--duration-default) linear;
}

.predictive-search__list-item[aria-selected="true"]
  .predictive-search__item-heading,
.predictive-search__list-item:hover .predictive-search__item-heading {
  /*   text-decoration: underline;
  text-underline-offset: 0.3rem; */
  color: rgba(var(--color-base-outline-button-labels));
   transition: all var(--duration-default) linear;
}
.predictive-search .predictive-search__search-for-button{margin-top:20px;}
.predictive-search__item {
  display: flex;
  padding: 1rem 0;
  text-align: left;
  text-decoration: none;
  width: 100%;
}

.predictive-search__item--link {
  display: grid;
  grid-template-columns: 5rem 1fr;
  grid-column-gap: 2rem;
  grid-template-areas: "product-image product-content";
}

.predictive-search__item-content {
  grid-area: product-content;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.predictive-search__item-content--centered {
  justify-content: center;
}

.predictive-search__item-vendor {
  font-size: 0.9rem;
}

.predictive-search__item-heading {
  margin: 0;
  font-size: 1.6rem;
  font-weight: 500;
  transition: all var(--duration-default) linear;
}

.predictive-search__item .price {
  color: rgba(var(--color-foreground), 0.7);
  font-size: 1.2rem;
}
.predictive-search__item .price--on-sale .price-item--regular {
  font-size: 1rem;
}
.predictive-search__item-vendor + .predictive-search__item-heading,
.predictive-search .price {
  margin-top: 0.5rem;
}

.predictive-search__item--term {
  justify-content: flex-start;
  align-items: center;
  padding: 1.3rem 2rem;
  word-break: break-all;
  line-height: calc(1 + 0.4 / var(--font-body-scale));
}

@media screen and (min-width: 750px) {
  .predictive-search__item--term {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }
}

.predictive-search__item--term .icon-arrow {
  width: calc(var(--font-heading-scale) * 1.4rem);
  height: calc(var(--font-heading-scale) * 1.4rem);
  flex-shrink: 0;
  margin-left: calc(var(--font-heading-scale) * 2rem);
  color: rgb(var(--color-link));
  margin-top: .1rem;
}

.predictive-search__image {
  grid-area: product-image;
  object-fit: contain;
  font-family: "object-fit: contain";
}

.search-modal__content.search-modal__content-bottom {
    background-color: var(--gradient-background);
    padding: 50px;
    overflow: auto;
    height: calc(78vh - 100px);
    margin: auto;
    width: calc(70vw - 100px);
    position: relative;
    display: inline-block;
    max-width: 250px;
    max-width: 100%;
}
.search-modal  form.search.search-modal__form {
    max-width: 100%;
    position: relative;
    width: 100%;
    margin: auto;
    background: transparent;
}
.search-modal  form.search.search-modal__form .field{width:50%;margin:auto;}
.search-modal  .modal__close-button.link {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0rem;
    height: 4.4rem;
    width: 4.4rem;
    background-color: var(--gradient-base-accent-3);
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 99;
    fill: var(--gradient-background);
}
.search-modal  .search-tags {
    display: flex;
    flex-wrap: wrap;
    margin: 15px 0 0;
    width: 100%;
    align-items: center;
    justify-content: center;
  padding:0;
}
.search-modal  .search-tags li {
    list-style: none;
    margin: 0;font-weight:500;
}
.search-modal  h3.recently_purchased {
    text-align: center;
}
.search-modal .search-tags li a {
    display: block;
    padding: 0 10px;
    text-transform: capitalize;
    position: relative;
    background: transparent;
    color: var(--gradient-base-accent-1);
    transition: all 0.3s linear;
   font-size:1.4rem;font-weight:400;
}
.search-modal .search-tags li a:hover {
    color: rgba(var(--color-base-outline-button-labels));
}
.search-modal .modal__close-button.link svg{width:15px;height:15px;}
.search-modal .modal__close-button.link {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0rem;
    height: 3rem;
    width: 3rem;
    background-color:transparent; 
    position: absolute;
    right: 6px;
    top: 6px;
    z-index: 99;
    color: rgb(var(--color-base-solid-button-labels));
}
.search-modal .modal__close-button.link:hover{
  color:rgb(var(--color-base-outline-button-labels));
}
.search-modal svg.icon.icon-close {
    transform: scale(1);
}
#dT_top-sticky.search-show {
    padding: 30px;
    top: 0;
    left: 0;
    right: 0;
    display: none;
    z-index: 9999;
    width: 100%;
    height: 100%;
    position: absolute;
    background-color: rgba(var(--color-base-accent-1), .8);
}
.search-modal .modal-overlay {
    display: block !important;
    z-index: -1;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    height:100vh;
    background-color: rgba(var(--color-base-accent-2), .8);
}
body.search-overlay-open {
    overflow: hidden;
}
.search-modal button.search__button.field__button{background:transparent;}
.search-modal button.search__button.field__button:hover {
    fill: var(--gradient-base-accent-2);
}

@media screen and (max-width: 1200px) {
  .search-modal form.search.search-modal__form{width:100%;}   
  }
  @media screen and (max-width: 750px) {

.search-modal form.search.search-modal__form{width:100%;}  
.search-modal .search-tags{padding:0;}   
 .search-modal .search-tags > li:not(.tag-item) {
    width: 100%;
    margin-right: 0rem;
    margin-bottom: 0.5rem;
    text-align: center;
}   
 }
  @media screen and (max-width: 576px) {
.search-modal__content.search-modal__content-bottom{padding: 50px 20px; width: calc(100vw - 20px);}
  }


/*new-search*/
.predictive-search .predictive-search__results-groups-wrapper{
      display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
}
.predictive-search .predictive-search__results-groups-wrapper .predictive-search__result-group:first-child{
      flex: 100% 0 0;
}
.predictive-search .predictive-search__results-groups-wrapper .predictive-search__result-group:last-child{
      flex: 100% 0 0;
}
.predictive-search__item--term{    justify-content: center;}  
.predictive-search__results-list{margin-top:15px;}

@media screen and (min-width: 780px) {
.predictive-search .predictive-search__results-groups-wrapper{  display: flex; flex-direction: row; flex-wrap: nowrap;}
.predictive-search .predictive-search__results-groups-wrapper .predictive-search__result-group:first-child{ flex: 50% 0 0;}
.predictive-search .predictive-search__results-groups-wrapper .predictive-search__result-group:last-child{ flex: 50% 0 0;}
.predictive-search__item--term{ justify-content: flex-start;}  
}
button.predictive-search__item.predictive-search__item--term.link:hover {
    color: rgba(var(--color-base-outline-button-labels));
    background: rgba(var(--color-foreground),.04);
}
.predictive-search .predictive-search__results-groups-wrapper .predictive-search__item-content{margin-left:10px}
predictive-search button.reset__button.field__button{background:transparent;}
.header__search .search-modal{height:100vh;}

@media screen and (max-width: 1540px) {
.search-modal__content.search-modal__content-bottom{width: calc(80vw - 100px);}
}
@media screen and (max-width: 1199px) {
.search-modal__content.search-modal__content-bottom{width: calc(90vw - 100px);}
.search-modal form.search.search-modal__form .field{width:80%;}  
}
@media screen and (max-width: 767px) {
.search-collection-tags-with-middle-left{padding:30px 0;}
.search-modal__content.search-modal__content-bottom{width: calc(100vw - 100px);}
.search-modal form.search.search-modal__form .field{width:90%;}    
}
@media screen and (max-width: 576px) {
  .search-modal__content.search-modal__content-bottom{height: calc(90vh - 100px);width: calc(100vw - 50px);}
  .header .search-modal__form .predictive-search #predictive-search-results{padding:30px;}
}
