{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.50 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.50 | round: 0 }}px;
  }
   @media screen and (min-width: 576px) and (max-width: 749px){
   .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }
   }

@media screen and (min-width: 750px) {
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top }}px;
    padding-bottom: {{ section.settings.padding_bottom }}px;
  }
}
{%- endstyle -%}
<div class="color-{{ section.settings.color_scheme }} gradient">
 <div class="{% if section.settings.page_full_width %} page-full-width {% else %} page-width {% endif %} {% if section.settings.page_full_width_spacing %} page-full-width_spacing {% endif %}section-{{ section.id }}-padding isolate">
    <div class="row">
     {%- unless section.settings.title == blank -%}
      <div class="title-wrapper-with-link title-wrapper--self-padded-mobile title-wrapper--no-top-margin content-align--{{ section.settings.column_alignment }}">
         {%- if section.settings.sub_heading != blank -%}  
         <h6 class="sub-heading">{{ section.settings.sub_heading | escape }}</h6>
         {%- endif -%} 
         {%- if section.settings.title != blank -%}  
          <h2 class="title {{ section.settings.heading_size }}">
            {{ section.settings.title | escape }}
          </h2>
          {%- endif -%} 
          {%- if section.settings.description != blank -%}  
          <p class="description">{{ section.settings.description }}</p>
          {%- endif -%}   
          {%- if section.settings.button_label != blank -%}
            <a {% if section.settings.button_link == blank %} role="link" aria-disabled="true"{% else %} href="{{ section.settings.button_link }}"{% endif %} class="button{% if section.settings.button_style_secondary %} button--secondary{% else %} button--primary{% endif %}">{{ section.settings.button_label | escape }}</a>
          {%- endif -%}
          </div>
    {%- endunless -%}  
  <div class="section-{{ section.id }}">
      <div class="contact-layout">
    {% if section.settings.address_heading != blank or section.settings.address_desc != blank or section.settings.collapsible_address != blank or  section.settings.collapsible_contact_id != blank  or section.settings.collapsible_contact_no != blank %}
      <div class="layout-contact-left">
          <div class="collapsible_address-block">
            {% if section.settings.address_heading != blank %}
            <h3 class="address-block-heading h4">{{ section.settings.address_heading}}</h3>
            {% endif %}  
            {% if section.settings.address_desc != blank %}
            <p class="address-block-desc">{{ section.settings.address_desc}}</p>
            {% endif %}  
            <ul class=" list-unstyled">
            {% if section.settings.collapsible_address != blank %}
            <li class="address"> 
            <span>{%- render 'icon-location' -%}</span>
            <address>{{ section.settings.collapsible_address }}</address>
            </li>
            {% endif %}  
            {% if section.settings.collapsible_contact_id != blank %}
            <li class="office-mail">
            <a href="mailto:{{ section.settings.collapsible_contact_id}}" class="link">
            {%- render 'icon-mail' -%}<span>{{ section.settings.collapsible_contact_id }}</span></a>     
            </li>
            {% endif %}
            {% if section.settings.collapsible_contact_no != blank %}
            <li class="contact-phone">    
            <a href="tel:{{ section.settings.collapsible_contact_no }}" class="link">{%- render 'icon-phone' -%}{{ section.settings.collapsible_contact_no }}</a>    
            </li>
            {% endif %}
            </ul>
          </div>
     </div>
        {% endif %}
    {% if section.settings.custom_liquid %}
        <div class="layout-contact-right">
        {{ section.settings.custom_liquid }}
        </div>
      {% endif %}
  </div>
 </div>
</div>
</div>
</div>
{% schema %}
{
  "name": "t:sections.custom-liquid.name",
  "tag": "section",
  "class": "section section-custom-liquid",
  "settings": [
    {
      "type":"checkbox",
      "id":"page_full_width",
       "default": false,
      "label": "t:sections.all.page_full_width.label"
    },
    {
      "type":"checkbox",
      "id":"page_full_width_spacing",
       "default": false,
      "label": "t:sections.all.page_full_width_spacing.label"
    },
    {
      "type": "text",
      "id": "title",
      "default": "Custom Liquid",
      "label": "t:sections.all.title.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__3.label"
        }
      ],
      "default": "h1",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "text",
      "id": "sub_heading",
      "default": "Sub Heading", 
      "label": "t:sections.all.sub_heading.label"
    },
     {
      "type": "text",
      "id": "description",
      "default": "Use This Text To Share The Information Which You Like!.",    
      "label": "t:sections.all.description.label"
    },
    {
      "type": "text",
      "id": "button_label",
      "default": "Button label",
      "label": "t:sections.all.button_label.label"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "t:sections.all.button_link.label"
    },
    {
      "type": "checkbox",
      "id": "button_style_secondary",
      "default": false,
      "label": "t:sections.all.button_style_secondary.label"
      },
    {
      "type": "select",
      "id": "column_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.contact-section.settings.column_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.contact-section.settings.column_alignment.options__2.label"
        }
      ],
      "default": "center",
      "label": "t:sections.contact-section.settings.column_alignment.label"
    },
     {
      "type": "header",
      "content": "t:sections.contact-section.settings.contact_section_settings.content"
    },
    {
      "type": "liquid",
      "id": "custom_liquid",
      "label": "t:sections.custom-liquid.settings.custom_liquid.label",
      "info": "t:sections.custom-liquid.settings.custom_liquid.info"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "t:sections.all.colors.accent_1.label"
        },
        {
          "value": "accent-2",
          "label": "t:sections.all.colors.accent_2.label"
        },
        {
          "value": "background-1",
          "label": "t:sections.all.colors.background_1.label"
        },
        {
          "value": "background-2",
          "label": "t:sections.all.colors.background_2.label"
        },
        {
          "value": "inverse",
          "label": "t:sections.all.colors.inverse.label"
        }
      ],
      "default": "background-1",
      "label": "t:sections.all.colors.label"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 40
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 52
    }
   ],
  "presets": [
    {
      "name": "t:sections.custom-liquid.presets.name"
    }
  ]
}
{% endschema %}
