table {
  border-collapse: collapse;
  border-color: inherit;
  text-indent: 0;
  border-spacing: 0;
  clear: both;
  margin: 0 0 1rem;
  width: 100%;
  overflow: hidden;
}
pre,
table td,
table th {
  border: 1px solid rgba(var(--color-base-accent-2), 0.2);border-right:none;
}
table tr:last-child td{border-right: 1px solid rgba(var(--color-base-accent-2), 0.2);}
table td,
table th {
  padding: 10px;
  text-align: center;
  word-break: break-word;
}

dtx-wishlist-grid table thead > tr {
  background-color: var(--gradient-base-background-2);
  border: 1px solid var(--gradient-base-background-2);
}
dtx-wishlist-grid table thead > tr th {
  border-top: medium none;
  text-align: center;
  text-transform: uppercase;
  vertical-align: middle;
  white-space: nowrap;
  font-weight: 700;
  color: var(--gradient-base-accent-2);
}

dtx-wishlist-grid table tbody > tr td.product-thumbnail img {
  border-radius: Calc(var(--buttons-radius) - 30px);
  width: 100%;
  height: 360px;
}
dtx-wishlist-grid a.remove-item.product-cart svg {
  width: 26px;
  height: 26px;
}
dtx-wishlist-grid table tbody > tr td a {
  color: var(--gradient-base-accent-1);
  font-weight: 700;
}

dtx-wishlist-grid table tbody > tr td a.remove-item.product-cart:hover {
  color: var(--gradient-base-accent-2);
}
.page-width.compare {
  width:100%;
  padding:100px 0;
}
@media only screen and (max-width: 991px) {
  dtx-wishlist-grid table thead {
    display: none;
  }

  dtx-wishlist-grid table tbody > tr td,
  dtx-wishlist-grid table tbody > tr td.product-thumbnail {
    width: 100%;
    display: inline-block;
  }
}
@media only screen and (max-width: 767px) {
  dtx-wishlist-grid table thead {
    display: none;
  }
  dtx-wishlist-grid table tbody > tr td,
  dtx-wishlist-grid table tbody > tr td.product-thumbnail {
    width: 100%;
    display: block;
  }
}

@media only screen and (max-width: 576px) {
  dtx-wishlist-grid table tbody > tr {
    width: 100%;
  }
}
/* @media only screen and (max-width: 1199px) {
  .page-width.wishlist,
  .page-width.compare {
    padding-top: 0rem;
    padding-bottom: 0rem;
  }
} */
table.dtx-table.dtx-grid-hide {
  display: none;
}
.dtx-grid-empty.dtx-grid-hide {
  display: none;
}
.dtx-grid-empty.dtx-grid-show {
  text-align: center;
  display: flex;
  align-items: center;
  flex-direction: column;
}

.dtx-grid-empty.dtx-grid-show img {
  margin-bottom: 30px;display:none;
}
table {
  display: flex;
  justify-content: center;
}
.compare-wrapper .dtx-table tbody {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  display: grid;
  grid-gap: 0rem;
  grid-auto-flow: column;
  grid-auto-columns: 20%;
  min-height: 100%;
  width: 100%;
}
.compare td.product-thumbnail img {
  height: 400px;
  width: 100%;
}
.compare-wrapper .dtx-table tbody tr:first-child th {
    font-weight: 500;
    font-family: var(--font-heading-family);
}
@media only screen and (max-width: 1540px) {
  .compare-wrapper .dtx-table tbody {
    grid-auto-columns: 24.99%;
  }
}
@media only screen and (max-width: 1199px) {
  .page-width.compare{
    padding: 50px 0;
  }
  .compare-wrapper .dtx-table tbody {
    grid-auto-columns: 33.3% ;
  }
}
@media only screen and (max-width: 576px) {
  .compare-wrapper .dtx-table tbody {
    grid-auto-columns:49.99% ;
  }
}
@media only screen and (max-width: 480px) {
.product-name{
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;}
  .compare-wrapper .dtx-table tbody {display:flex;flex:0 0 auto;}
  .compare-wrapper .dtx-table tbody tr{flex:0 0 auto;width:60%;}
  .compare-wrapper .dtx-table tbody tr:first-child{width:39.8%;}
}
.compare-wrapper .dtx-table tbody tr {
  display: flex;
  flex-direction: column;
  /*   justify-content: center; */
}

.compare-wrapper .dtx-table tbody tr:first-child {
  background: rgba(var(--color-base-background-2), 1);
  position: sticky;
  left: 0;
  z-index: 2;
}

.compare th.product-title {
  height: 431px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.compare-wrapper .dtx-table tbody tr:first-child table th {
  border-bottom: 0px solid;
}
td.product-description-cart,
td.product-wishlist-cart {
  border-bottom: 0;
  white-space: initial;
}
/* .compare-wrapper .dtx-table tbody tr:first-child th:last-child {
  height: 255px;
} */
.dtx-grid-empty.dtx-grid-show h2 {
  margin-top: 0;
}

/*custom*/
.dtx-grid-empty {
  animation: cssAnimation 0s 3s forwards;
  visibility: hidden;
}

@keyframes cssAnimation {
  to {
    visibility: visible;
  }
}
.compare td.product-name a {
  color: var(--gradient-base-accent-1);
  font-weight: 400;
}
.compare td.product-name a:hover {
  color: rgb(var(--color-base-outline-button-labels));
}
.compare .remove-item.product-cart {
  display: inline-flex;
  justify-content: center;
  align-items: center;
}
.compare a.remove-item.product-cart span {
  font-size: 1.6rem;
  color: var(--gradient-base-accent-1);
  margin-right: 6px;
  transition: all 0.3s linear;
  line-height:normal;
}
.compare .remove-item.product-cart svg.icon.icon-remove {
  width: 1.6rem;
  height: 1.6rem;
  color: var(--gradient-base-accent-1);
  transition: all 0.3s linear;
}
.compare .remove-item.product-cart:hover span,
.compare .remove-item.product-cart:hover svg.icon.icon-remove {
  color: rgb(var(--color-base-outline-button-labels));
  fill: rgb(var(--color-base-outline-button-labels));
}
.compare td.product-description-cart{  width: 100%; height: 100%; display: flex; align-items: center; justify-content: center;}
td.product-wishlist-cart .dt-sc-btn.product-cart{ padding: 0 30px;    min-height: calc(4rem + var(--buttons-border-width) * 2);}
.compare-wrapper .dtx-table tbody tr th:nth-last-child(2){ min-height: calc(6rem + var(--buttons-border-width) * 2);display: flex;align-items: center;justify-content: center;}
.compare-wrapper td.product-wishlist-cart{height: calc(6rem + var(--buttons-border-width) * 2);}
@media only screen and (max-width: 990px) {
td.product-wishlist-cart .dt-sc-btn.product-cart{width:100%}
}


/* firefox */
//firefox


tbody.slick-wrapper:-webkit-scrollbar {
  width: 20px;
}
tbody.slick-wrapper:-webkit-scrollbar-track {
    background-color: rgba(0, 0, 0, 0.5);
}
tbody.slick-wrapper:-webkit-scrollbar-thumb {
  background-color: red;
  border-radius: 6px;
  border: 3px solid transparent;
}

/* webkit browsers */
tbody.slick-wrapper::-webkit-scrollbar {
  height: 5px;
}

tbody.slick-wrapper::-webkit-scrollbar-track {
   background-color: rgba(0, 0, 0, 0.2);
}

tbody.slick-wrapper::-webkit-scrollbar-thumb {
  height: 5px;
  background-color: rgba(0, 0, 0, 0.5);
}

tbody.slick-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.7);
}

tbody.slick-wrapper::-webkit-scrollbar:vertical {
  display: none;
}
