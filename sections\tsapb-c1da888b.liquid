<!-- --VERSION-17-- -->

{% style %}
:root {
--vtl-neutral-100: #222;
--vtl-neutral-70: #5e5e5e;
--vtl-neutral-03: #f8f8f8;
--vtl-neutral-01: #fff;
--vtl-size-0: 0px;
--vtl-size-1: 1px;
--vtl-size-2: 2px;
--vtl-size-4: 4px;
--vtl-size-6: 6px;
--vtl-size-12: 12px;
--vtl-size-14: 14px;
--vtl-size-16: 16px;
--vtl-size-20: 20px;
--vtl-size-24: 24px;
--vtl-size-32: 32px;
--vtl-size-40: 40px;
--vtl-size-48: 48px;
--vtl-size-80: 80px;
--vtl-size-full: 9999px;
--vtl-font-weight-400: 400;
--vtl-font-weight-600: 600;
--vtl-line-height-140: 1.4;
--vtl-line-height-150: 1.5;
--vtl-text-decoration-none: none;
--vtl-text-decoration-underline: underline;
--vtl-border-radius-full: var(--vtl-size-full);
--vtl-border-width-1: var(--vtl-size-1);
--vtl-color-bg-fill-default-on-light: var(--vtl-neutral-100);
--vtl-color-bg-fill-inverse-on-light: var(--vtl-neutral-01);
--vtl-color-bg-surface-tertiary-on-light: var(--vtl-neutral-03);
--vtl-color-text-disable-on-light: var(--vtl-neutral-70);
--vtl-space-0: var(--vtl-size-0);
--vtl-space-1: var(--vtl-size-1);
--vtl-space-2: var(--vtl-size-2);
--vtl-space-4: var(--vtl-size-4);
--vtl-space-6: var(--vtl-size-6);
--vtl-space-12: var(--vtl-size-12);
--vtl-space-16: var(--vtl-size-16);
--vtl-space-20: var(--vtl-size-20);
--vtl-space-24: var(--vtl-size-24);
--vtl-space-32: var(--vtl-size-32);
--vtl-space-40: var(--vtl-size-40);
--vtl-space-48: var(--vtl-size-48);
--vtl-space-80: var(--vtl-size-80);
--vtl-font-size-12: var(--vtl-size-12);
--vtl-font-size-14: var(--vtl-size-14);
}


{% endstyle %}

<!-- --UUID-c1da888b-- -->

<script>
	if (typeof window.vtlsBreakpoints === 'undefined') {
		window.vtlsBreakpoints = {
			mobile: {
				minWidth: 0,
				maxWidth: 479,
			},
			tablet: {
				minWidth: 480,
				maxWidth: 991,
			},
			desktop: {
				minWidth: 992,
				maxWidth: 10000,
			},
		};
	}
</script>

{% style %}
	

@media(max-width: 479px){.VtlsResponsiveness--TabletAndDesktop{display:none}}@media(min-width: 992px){.VtlsResponsiveness--MobileAndTablet{display:none}}@media(min-width: 480px){.VtlsResponsiveness--Mobile{display:none}}@media not ((min-width: 480px) and (max-width: 991px)){.VtlsResponsiveness--Tablet{display:none}}@media(max-width: 991px){.VtlsResponsiveness--Desktop{display:none}}

{% endstyle %}


{% capture vtls_rating_icon %}
    {% case section.settings.stars_shape %}
        {% when 'stars' %}
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9.46127 2.55063L7.4308 6.70529L2.83882 7.36128C2.02663 7.48624 1.71425 8.48585 2.30777 9.07938L5.58776 12.2969L4.80681 16.8264C4.68186 17.6386 5.55652 18.2633 6.275 17.8885L10.3672 15.7331L14.4281 17.8885C15.1466 18.2633 16.0213 17.6386 15.8963 16.8264L15.1153 12.2969L18.3953 9.07938C18.9889 8.48585 18.6765 7.48624 17.8643 7.36128L13.3035 6.70529L11.2418 2.55063C10.8982 1.83216 9.83613 1.80092 9.46127 2.55063Z" fill="#222222"/>
            </svg>    
        {% when 'outline_stars' %}
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M11.2418 2.55063C10.8982 1.83216 9.83613 1.80092 9.46127 2.55063L7.4308 6.70529L2.83882 7.36128C2.02663 7.48624 1.71425 8.48585 2.30777 9.07938L5.58776 12.2969L4.80681 16.8264C4.68186 17.6386 5.55652 18.2633 6.275 17.8885L10.3672 15.7331L14.4281 17.8885C15.1466 18.2633 16.0213 17.6386 15.8963 16.8264L15.1153 12.2969L18.3953 9.07938C18.9889 8.48585 18.6765 7.48624 17.8643 7.36128L13.3035 6.70529L11.2418 2.55063ZM16.2108 8.84095L12.1777 8.26084L10.3564 4.59071L8.56273 8.26084L4.49356 8.84215L7.41447 11.7074L6.72051 15.7324L10.3702 13.8101L13.9816 15.7269L13.2886 11.7074L16.2108 8.84095Z" fill="#222222"/>
            </svg>  
        {% when 'pointed_stars' %}
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M10.5 1L12.6329 7.56434H19.535L13.9511 11.6213L16.084 18.1857L10.5 14.1287L4.91604 18.1857L7.04893 11.6213L1.46497 7.56434H8.36712L10.5 1Z" fill="#222222"/>
            </svg>    
        {% when 'heart_stars' %}
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M10 4.89321L10.6121 4.27677C12.3022 2.57449 15.0423 2.57448 16.7324 4.27677C18.4225 5.97905 18.4225 8.739 16.7324 10.4413L10 17.2223L3.26757 10.4412C1.57748 8.73895 1.57747 5.979 3.26757 4.27671C4.95767 2.57443 7.69786 2.57443 9.38796 4.27671L10 4.89321Z" fill="#222222"/>
            </svg>     
  {% endcase %}
{% endcapture %}

<vtls-testimonial-text
	class="
		Vtls-{{ section.id | handle }} VtlsTestimonialText
		{% if section.settings.section_width == 100 %}VtlsTestimonialText--fullWidth{% endif %}
	"
>
	<div class="VtlsTestimonialTextContainer VtlsCarouselContainer">
		<div class="VtlsTestimonialTextContent">
			{%- if section.settings.heading != blank -%}
				<h2 class="VtlsTestimonialTextContent__Heading VtlsTestimonialTextContent__Heading--style-{{ section.settings.heading_style }}">
					{{ section.settings.heading }}
				</h2>
			{%- endif -%}
			{%- if section.settings.description != blank -%}
				<div class="VtlsTestimonialTextContent__Description">
					{{ section.settings.description }}
				</div>
			{%- endif -%}
			{% if section.settings.button_label != blank %}
				<div class="VtlsTestimonialTextContent__Button">
					{%- if section.settings.button_label != blank -%}
						<a
							{% if section.settings.new_tab == true %}
								target="_blank"
								rel="noopener noreferrer"
							{% endif %}
							class="VtlsTestimonialTextContentButton VtlsTestimonialTextContentButton--{{ section.settings.button_style }}"
							href="{{ section.settings.button_link | default: "#" }}"
						>
							{{- section.settings.button_label | escape -}}
						</a>
					{%- endif -%}
				</div>
			{% endif %}
		</div>
		<div
			class="VtlsCarousel"
			data-section-width="{{ section.settings.section_width }}"
		>
			<div class="VtlsCarousel__Content">
				<ul class="VtlsCarouselList">
					{%- for block in section.blocks -%}
						{%- liquid
							assign testimonial_text = block.settings.testimonial_text
							assign author_name = block.settings.author_name
							assign author_additional_text = block.settings.author_additional_text
							assign author_photo = block.settings.author_photo
						-%}
						<li
							class="VtlsTestimonialBlock"
							{{ block.shopify_attributes }}
						>
							<div class="VtlsTestimonialBlock__Wrapper">
								<div
									class="
										VtlsTestimonialBlock__Content
										VtlsTestimonialBlock__Content--blockStyle-{{ section.settings.testimonial_box_style }}
									"
								>
									{% if testimonial_text != blank %}
										<div class="VtlsTestimonialBlockDescription">
											<div class="VtlsTestimonialBlockStarShape">
												{%- for i in (1..block.settings.stars_rating) -%}
													{{ vtls_rating_icon }}
												{% endfor %}
											</div>
											{%- liquid
												assign testimonial_text_words = testimonial_text | split: " "
												assign testimonial_html_arr = ""

												for word in testimonial_text_words
													assign testimonial_html_arr = testimonial_html_arr | append: "<span> " | append: word | append: " </span>"
												endfor

												assign testimonial_html = testimonial_html_arr | join: " "
											-%}
											<div class="VtlsTestimonialBlockTextWrapper">
												<vtls-expandable-text class="VtlsTestimonialBlockText">
													{{ testimonial_html }}
												</vtls-expandable-text>
												<span class="VtlsTestimonialBlockText--arrow">
													<svg
														width="14"
														height="8"
														viewBox="0 0 14 8"
														fill="none"
														xmlns="http://www.w3.org/2000/svg"
													>
														<path d="M13 1L7 7L1 1" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
													</svg>
												</span>
											</div>
										</div>
									{% endif %}
									{% if author_name != blank
										or author_additional_text != blank
										or author_photo != blank
									%}
										<div class="VtlsTestimonialBlockAuthor">
											{%- if author_photo != blank -%}
												<div class="VtlsTestimonialBlockAuthor__AuthorPhoto">
													{{-
														author_photo
														| image_url: width: author_photo.width
														| image_tag:
															sizes: "100vw",
															widths: "400, 600, 800, 1200, 1600, 1800, 2400, 2800, 3200",
															fetchpriority: "low",
															loading: "lazy",
															class: "VtlsTestimonialBlockAuthor__AuthorPhoto--photo"
													-}}
												</div>
											{% endif %}
											<div class="VtlsTestimonialBlockAuthor__Text">
												<div class="VtlsTestimonialBlockAuthor__AuthorName">
													{{ author_name }}
												</div>
												<div class="VtlsTestimonialBlockAuthor__AuthorAddText">
													{{ author_additional_text }}
												</div>
											</div>
										</div>
									{% endif %}
								</div>
							</div>
						</li>
					{%- endfor -%}
				</ul>
				<div class="VtlsCarousel__Navigation">
					<div class="VtlsNavigationButtons">
						<button class="VtlsNavigationButtons__Button VtlsNavigationButtons__Button--disabled left-chevron">
							<svg
								xmlns="http://www.w3.org/2000/svg"
								width="24"
								height="24"
								viewBox="0 0 24 24"
								fill="none"
							>
								<path d="M14 18L8 12L14 6" stroke="#222" stroke-linecap="round"></path>
							</svg>
						</button>
						<button class="VtlsNavigationButtons__Button right-chevron">
							<svg
								xmlns="http://www.w3.org/2000/svg"
								width="24"
								height="24"
								viewBox="0 0 24 24"
								fill="none"
							>
								<path d="M9 6L15 12L9 18" stroke="#222" stroke-linecap="round"></path>
							</svg>
						</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</vtls-testimonial-text>

{% schema %}
{
	"name": "❤️ Testimonials & Text",
	"disabled_on": {
		"groups": ["header", "footer"]
	},
	"settings": [
		{
			"type": "header",
			"content": "Design"
		},
		{
			"type": "select",
			"id": "stars_shape",
			"label": "Stars shape",
			"options": [
				{
					"value": "stars",
					"label": "Stars"
				},
				{
					"value": "outline_stars",
					"label": "Outline stars"
				},
				{
					"value": "pointed_stars",
					"label": "Pointed stars"
				},
				{
					"value": "heart_stars",
					"label": "Heart"
				}
			],
			"default": "stars"
		},
		{
			"type": "select",
			"id": "stars_size",
			"label": "Stars size",
			"options": [
				{
					"value": "16",
					"label": "Small"
				},
				{
					"value": "20",
					"label": "Medium"
				},
				{
					"value": "24",
					"label": "Large"
				}
			],
			"default": "20"
		},
		{
			"type": "color",
			"id": "stars_color",
			"label": "Stars color",
			"default": "#222222"
		},
		{
			"type": "select",
			"id": "testimonial_per_row",
			"label": "Testimonial per row",
			"info": "Applies only to desktop",
			"options": [
				{
					"value": "1",
					"label": "1"
				},
				{
					"value": "2",
					"label": "2"
				}
			],
			"default": "2"
		},
		{
			"type": "color",
			"id": "testimonial_color",
			"label": "Testimonial color",
			"default": "#222222"
		},
		{
			"type": "select",
			"id": "testimonial_text_size",
			"label": "Testimonial text size",
			"options": [
				{
					"value": "12",
					"label": "Small"
				},
				{
					"value": "16",
					"label": "Medium"
				},
				{
					"value": "20",
					"label": "Large"
				}
			],
			"default": "16"
		},
		{
			"type": "select",
			"id": "testimonial_box_style",
			"label": "Testimonial box style",
			"options": [
				{
					"value": "color",
					"label": "Color"
				},
				{
					"value": "border",
					"label": "Border"
				}
			],
			"default": "color"
		},
		{
			"type": "color",
			"id": "testimonial_box_color",
			"label": "Testimonial box color",
			"default": "#F8F8F8",
			"visible_if": "{{ section.settings.testimonial_box_style == 'color' }}"
		},
		{
			"type": "color",
			"id": "testimonial_border_color",
			"label": "Testimonial box border color",
			"default": "#222222",
			"visible_if": "{{ section.settings.testimonial_box_style == 'border' }}"
		},
		{
			"type": "range",
			"id": "testimonial_box_corner",
			"min": 0,
			"max": 100,
			"step": 2,
			"unit": "px",
			"label": "Testimonial box corner radius",
			"default": 0
		},
		{
			"type": "color",
			"id": "author_color",
			"label": "Author text color",
			"default": "#222222"
		},
		{
			"type": "color",
			"id": "arrows_color",
			"label": "Arrows color",
			"default": "#222222"
		},
		{
			"type": "color",
			"id": "section_background",
			"label": "Section background"
		},
		{
			"type": "header",
			"content": "Layout"
		},
		{
			"type": "range",
			"id": "section_width",
			"min": 70,
			"max": 100,
			"step": 1,
			"unit": "%",
			"label": "Section width",
			"default": 90,
			"info": "Applies only to desktop"
		},
		{
			"type": "text",
			"id": "section_max_width",
			"label": "Custom max width (optional)",
			"info": "Section max width in pixels"
		},
		{
			"type": "range",
			"id": "testimonials_space_between",
			"min": 10,
			"max": 30,
			"step": 2,
			"unit": "px",
			"label": "Space between testimonials",
			"default": 20
		},
		{
			"type": "range",
			"id": "vertical_padding",
			"min": 0,
			"max": 100,
			"step": 2,
			"unit": "px",
			"label": "Top - bottom padding (desktop)",
			"default": 40
		},
		{
			"type": "range",
			"id": "vertical_margin",
			"min": 0,
			"max": 100,
			"step": 2,
			"unit": "px",
			"label": "Top - bottom margin (desktop)",
			"default": 0
		},
		{
			"type": "range",
			"id": "vertical_padding_mobile",
			"min": 0,
			"max": 100,
			"step": 2,
			"unit": "px",
			"label": "Top - bottom padding (mobile)",
			"default": 20
		},
		{
			"type": "range",
			"id": "vertical_margin_mobile",
			"min": 0,
			"max": 100,
			"step": 2,
			"unit": "px",
			"label": "Top - bottom margin (mobile)",
			"default": 0
		},
		{
			"type": "header",
			"content": "Text settings"
		},
		{
			"type": "text",
			"id": "heading",
			"label": "Heading",
			"default": "Testimonials"
		},
		{
			"type": "select",
			"id": "heading_style",
			"label": "Heading style",
			"options": [
				{
					"value": "regular",
					"label": "Regular"
				},
				{
					"value": "bold",
					"label": "Bold"
				}
			],
			"default": "regular"
		},
		{
			"type": "select",
			"id": "heading_size",
			"label": "Heading size",
			"options": [
				{
					"value": "24",
					"label": "Small"
				},
				{
					"value": "32",
					"label": "Medium"
				},
				{
					"value": "48",
					"label": "Large"
				}
			],
			"default": "32"
		},
		{
			"type": "richtext",
			"id": "description",
			"label": "Description",
			"default": "<p>Show your future customers what kind of reviews your brand has!</p>"
		},
		{
			"type": "select",
			"id": "description_size",
			"label": "Description size",
			"options": [
				{
					"value": "12",
					"label": "Small"
				},
				{
					"value": "16",
					"label": "Medium"
				},
				{
					"value": "20",
					"label": "Large"
				}
			],
			"default": "16"
		},
		{
			"type": "color",
			"id": "text_color",
			"label": "Text color",
			"default": "#222222"
		},
		{
			"type": "header",
			"content": "Button settings"
		},
		{
			"type": "text",
			"id": "button_label",
			"label": "Label"
		},
		{
			"type": "url",
			"id": "button_link",
			"label": "URL"
		},
		{
			"type": "select",
			"id": "button_style",
			"label": "Button style",
			"options": [
				{
					"value": "solid",
					"label": "Filled"
				},
				{
					"value": "outline",
					"label": "Outline"
				},
				{
					"value": "link",
					"label": "Link"
				}
			],
			"default": "solid"
		},
		{
			"type": "color",
			"id": "button_text_color",
			"label": "Text color",
			"default": "#ffffff"
		},
		{
			"type": "color",
			"id": "button_background_color",
			"label": "Background color",
			"default": "#222222",
			"visible_if": "{{ section.settings.button_style == 'solid' }}"
		},
		{
			"type": "color",
			"id": "button_border_color",
			"label": "Border color",
			"default": "#222222",
			"visible_if": "{{ section.settings.button_style == 'outline' }}"
		},
		{
			"type": "range",
			"id": "button_corner",
			"min": 0,
			"max": 100,
			"step": 2,
			"unit": "px",
			"label": "Corner radius",
			"visible_if": "{{ section.settings.button_style != 'link' }}",
			"default": 0
		},
		{
			"type": "checkbox",
			"id": "new_tab",
			"label": "Open link in a new tab",
			"default": false
		}
	],
	"blocks": [
		{
			"type": "testimonial",
			"limit": 12,
			"name": "Testimonial",
			"settings": [
				{
					"type": "range",
					"id": "stars_rating",
					"min": 0,
					"max": 5,
					"step": 1,
					"label": "Stars rating",
					"default": 5
				},
				{
					"type": "textarea",
					"id": "testimonial_text",
					"label": "Testimonial text",
					"default": "High-quality, lightweight, and thoughtfully designed"
				},
				{
					"type": "text",
					"id": "author_name",
					"label": "Author name",
					"default": "Author name"
				},
				{
					"type": "text",
					"id": "author_additional_text",
					"label": "Author additional text"
				},
				{
					"type": "image_picker",
					"id": "author_photo",
					"label": "Author photo (optional)"
				}
			]
		}
	],
	"presets": [
		{
			"name": "❤️ Square: Testimonials & Text",
			"category": "Square sections",
			"blocks": [
				{
					"type": "testimonial"
				},
				{
					"type": "testimonial"
				},
				{
					"type": "testimonial"
				},
				{
					"type": "testimonial"
				},
				{
					"type": "testimonial"
				}
			]
		}
	]
}
{% endschema %}

{% style %}
	.Vtls-{{ section.id | handle }} {
		--section-vertical-margin: {{- section.settings.vertical_margin -}}px;
	       --section-heading-size: {{ section.settings.heading_size }}px;
		--section-vertical-margin-mobile: {{- section.settings.vertical_margin_mobile -}}px;
		--section-background-color: {{- section.settings.section_background -}};
		--section-vertical-padding: {{- section.settings.vertical_padding -}}px;
		--section-vertical-padding-mobile: {{- section.settings.vertical_padding_mobile -}}px;
		--section-max-width: {{- section.settings.section_max_width -}}px;
		--section-width: {{- section.settings.section_width -}}%;
	       --section-stars-size: {{- section.settings.stars_size -}}px;
	       --section-testimonial-text-size: {{- section.settings.testimonial_text_size -}}px;
	       --section-testimonial-border-radius: {{ section.settings.testimonial_box_corner }}px;
	       --section-stars-color: {{- section.settings.stars_color -}};
	       --section-text-color: {{- section.settings.text_color -}};
	       --section-description-size: {{- section.settings.description_size -}}px;
	       --section-author-color: {{- section.settings.author_color -}};
	       --section-testimonial-per-row: {{- section.settings.testimonial_per_row -}};
	       --section-testimonial-color: {{- section.settings.testimonial_color -}};
	       --section-testimonial-border-color: {{- section.settings.testimonial_border_color -}};
	       --section-testimonial-box-color: {{- section.settings.testimonial_box_color -}};
	       --section-arrows-color: {{- section.settings.arrows_color -}};
	       --section-testimonial-space: {{- section.settings.testimonials_space_between -}}px;
	       --section-button-text-color: {{- section.settings.button_text_color -}};
	       --section-button-background-color: {{- section.settings.button_background_color -}};
	       --section-button-corner-radius: {{- section.settings.button_corner -}}px;
	       --section-button-border-color: {{- section.settings.button_border_color -}};
	}

	

.Vtls-{{ section.id | handle }}.VtlsTestimonialText{background-color:var(--section-background-color);display:block}.Vtls-{{ section.id | handle }}.VtlsTestimonialText--fullWidth .VtlsNavigationButtons{padding-right:var(--section-testimonial-space)}.Vtls-{{ section.id | handle }} .VtlsTestimonialTextContainer{margin:var(--section-vertical-margin-mobile) auto;padding:var(--section-vertical-padding-mobile) 0;list-style-type:none;gap:var(--vtl-space-32);position:relative;overflow:hidden;display:flex;flex-direction:column;align-items:flex-start}@media(min-width: 992px){.Vtls-{{ section.id | handle }} .VtlsTestimonialTextContainer{margin:var(--section-vertical-margin) auto;padding:calc(var(--vtl-space-80) + var(--section-vertical-padding)) 0 var(--section-vertical-padding) 0;gap:var(--vtl-space-40);flex-direction:row;padding-left:0;max-width:var(--section-max-width);width:var(--section-width)}}.Vtls-{{ section.id | handle }} .VtlsTestimonialTextContent{flex-shrink:0;display:flex;flex-direction:column;padding:0 var(--vtl-space-20);gap:var(--vtl-space-16);display:flex;width:100%;flex-direction:column;justify-content:center}@media(min-width: 992px){.Vtls-{{ section.id | handle }} .VtlsTestimonialTextContent{width:30%;min-height:var(--largest-block-height, 0px)}}.Vtls-{{ section.id | handle }} .VtlsTestimonialTextContent__Heading{margin:0;font-size:calc(var(--section-heading-size)*.75);text-decoration:none;color:var(--section-text-color);display:block;text-align:var(--section-heading-alignment);pointer-events:auto}.Vtls-{{ section.id | handle }} .VtlsTestimonialTextContent__Heading--style-regular{font-weight:var(--vtl-font-weight-400)}.Vtls-{{ section.id | handle }} .VtlsTestimonialTextContent__Heading--style-bold{font-weight:var(--vtl-font-weight-600)}@media(min-width: 992px){.Vtls-{{ section.id | handle }} .VtlsTestimonialTextContent__Heading{font-size:var(--section-heading-size);padding:0}}.Vtls-{{ section.id | handle }} .VtlsTestimonialTextContent__Button .VtlsTestimonialTextContentButton{display:inline-flex;justify-content:center;align-items:center;font:inherit;font-size:var(--vtl-font-size-14);text-decoration:var(--vtl-text-decoration-none);-webkit-appearance:none;appearance:none;width:fit-content;padding:var(--vtl-space-16) var(--vtl-space-20);transition:all .3s ease-in;line-height:1}.Vtls-{{ section.id | handle }} .VtlsTestimonialTextContent__Button .VtlsTestimonialTextContentButton:hover{opacity:.85;cursor:pointer}.Vtls-{{ section.id | handle }} .VtlsTestimonialTextContent__Button .VtlsTestimonialTextContentButton--solid{color:var(--section-button-text-color);background:var(--section-button-background-color);border-radius:var(--section-button-corner-radius)}.Vtls-{{ section.id | handle }} .VtlsTestimonialTextContent__Button .VtlsTestimonialTextContentButton--outline{color:var(--section-text-color);background:rgba(0,0,0,0);border:var(--vtl-border-width-1) solid var(--section-button-border-color);border-radius:var(--section-button-corner-radius)}.Vtls-{{ section.id | handle }} .VtlsTestimonialTextContent__Button .VtlsTestimonialTextContentButton--link{color:var(--section-button-text-color);background:rgba(0,0,0,0);border:var(--vtl-space-0);padding:0;border-radius:var(--vtl-space-0);text-decoration:var(--vtl-text-decoration-underline);text-underline-offset:var(--vtl-space-4)}.Vtls-{{ section.id | handle }} .VtlsTestimonialTextContent__Description p,.Vtls-{{ section.id | handle }} .VtlsTestimonialTextContent__Description ul,.Vtls-{{ section.id | handle }} .VtlsTestimonialTextContent__Description ol{margin:0;max-width:400px;font-size:calc(var(--section-description-size)*.88);color:var(--section-text-color)}.Vtls-{{ section.id | handle }} .VtlsTestimonialTextContent__Description h1,.Vtls-{{ section.id | handle }} .VtlsTestimonialTextContent__Description h2,.Vtls-{{ section.id | handle }} .VtlsTestimonialTextContent__Description h3,.Vtls-{{ section.id | handle }} .VtlsTestimonialTextContent__Description h4,.Vtls-{{ section.id | handle }} .VtlsTestimonialTextContent__Description h5,.Vtls-{{ section.id | handle }} .VtlsTestimonialTextContent__Description h6{color:var(--section-text-color);margin:0;padding:0}.Vtls-{{ section.id | handle }} .VtlsTestimonialTextContent__Description a{color:var(--section-text-color);margin:0;padding:0;text-decoration:underline;text-underline-offset:var(--vtl-space-2);transition:opacity .3s ease-in}.Vtls-{{ section.id | handle }} .VtlsTestimonialTextContent__Description a:hover{opacity:.85}.Vtls-{{ section.id | handle }} .VtlsTestimonialTextContent__Description ul,.Vtls-{{ section.id | handle }} .VtlsTestimonialTextContent__Description ol{padding-left:var(--vtl-space-20)}@media(min-width: 992px){.Vtls-{{ section.id | handle }} .VtlsTestimonialTextContent__Description p,.Vtls-{{ section.id | handle }} .VtlsTestimonialTextContent__Description ul,.Vtls-{{ section.id | handle }} .VtlsTestimonialTextContent__Description ol{font-size:var(--section-description-size)}}.Vtls-{{ section.id | handle }} .VtlsCarousel{width:100%;overflow:hidden}.Vtls-{{ section.id | handle }} .VtlsCarousel__Content{overflow:hidden;position:relative;padding-left:var(--section-testimonial-space)}@media(min-width: 992px){.Vtls-{{ section.id | handle }} .VtlsCarousel__Content{padding-left:0;padding-bottom:var(--vtl-space-80)}}.Vtls-{{ section.id | handle }} .VtlsCarousel__Content .VtlsCarouselList{display:flex;width:auto;padding-left:0;margin:0;transition:.3s;column-gap:var(--section-testimonial-space)}.Vtls-{{ section.id | handle }} .VtlsCarousel__Content .VtlsCarouselList .VtlsTestimonialBlock{box-sizing:border-box;padding:0;flex:1;overflow:hidden;min-width:140px;max-width:140px;display:flex;flex-direction:column;justify-content:space-between}.Vtls-{{ section.id | handle }} .VtlsCarousel__Navigation{display:flex;position:absolute;bottom:var(--vtl-space-12);align-items:center;width:100%}.Vtls-{{ section.id | handle }} .VtlsCarousel__Navigation .VtlsNavigationButtons{height:var(--vtl-space-40);display:flex;width:100%;justify-content:flex-end;gap:var(--vtl-space-16)}@media(max-width: 992px){.Vtls-{{ section.id | handle }} .VtlsCarousel__Navigation .VtlsNavigationButtons{display:none !important}}.Vtls-{{ section.id | handle }} .VtlsCarousel__Navigation .VtlsNavigationButtons__Button{background-color:rgba(0,0,0,0);border-radius:50%;border:var(--vtl-border-width-1) solid var(--section-arrows-color);width:var(--vtl-space-40);height:var(--vtl-space-40);display:flex;align-items:center;justify-content:center}.Vtls-{{ section.id | handle }} .VtlsCarousel__Navigation .VtlsNavigationButtons__Button path{stroke:var(--section-arrows-color, var(--vtl-color-bg-fill-default-on-light))}.Vtls-{{ section.id | handle }} .VtlsCarousel__Navigation .VtlsNavigationButtons__Button:hover{opacity:.5;cursor:pointer}.Vtls-{{ section.id | handle }} .VtlsCarousel__Navigation .VtlsNavigationButtons__Button--disabled{cursor:default;opacity:.3}.Vtls-{{ section.id | handle }} .VtlsTestimonialBlock__Wrapper{display:flex;flex-direction:column;height:100%;gap:var(--vtl-space-16)}.Vtls-{{ section.id | handle }} .VtlsTestimonialBlock__Wrapper:hover img{transform:scale(1.03)}.Vtls-{{ section.id | handle }} .VtlsTestimonialBlock__Content{display:flex;justify-content:center;flex-direction:column;padding:var(--vtl-space-24);min-height:var(--largest-block-height, 0px);justify-content:space-between;gap:var(--vtl-space-32);border:var(--vtl-border-width-1) solid rgba(0,0,0,0);border-radius:var(--section-testimonial-border-radius)}.Vtls-{{ section.id | handle }} .VtlsTestimonialBlock__Content--blockStyle-color{background-color:var(--section-testimonial-box-color, var(--vtl-color-bg-surface-tertiary-on-light))}.Vtls-{{ section.id | handle }} .VtlsTestimonialBlock__Content--blockStyle-border{border:var(--vtl-border-width-1) solid var(--section-testimonial-border-color)}@media(min-width: 992px){.Vtls-{{ section.id | handle }} .VtlsTestimonialBlock__Content{gap:var(--vtl-space-40);padding:var(--vtl-space-32)}}.Vtls-{{ section.id | handle }} .VtlsTestimonialBlockText{position:relative;white-space:normal;margin:0;display:block;font-size:calc(var(--section-testimonial-text-size)*.88);color:var(--section-testimonial-color);line-height:var(--vtl-line-height-150)}@media(min-width: 992px){.Vtls-{{ section.id | handle }} .VtlsTestimonialBlockText{font-size:var(--section-testimonial-text-size)}}.Vtls-{{ section.id | handle }} .VtlsTestimonialBlockText--arrow{position:absolute;bottom:calc(var(--section-testimonial-text-size) - var(--vtl-space-6));right:0;cursor:pointer;align-items:center;display:none;justify-content:center}.Vtls-{{ section.id | handle }} .VtlsTestimonialBlockText--arrow path{stroke:var(--section-testimonial-color, var(--vtl-color-bg-fill-default-on-light))}.Vtls-{{ section.id | handle }} .VtlsTestimonialBlockText--hidden{text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:7;-webkit-box-orient:vertical;overflow:hidden}@media(min-width: 992px){.Vtls-{{ section.id | handle }} .VtlsTestimonialBlockText--hidden{-webkit-line-clamp:4}}.Vtls-{{ section.id | handle }} .VtlsTestimonialBlockText--open{text-overflow:unset;display:-webkit-box;word-break:normal;-webkit-line-clamp:unset;white-space:normal}.Vtls-{{ section.id | handle }} .VtlsTestimonialBlockText--open .VtlsTestimonialBlockLast::after{display:block}.Vtls-{{ section.id | handle }} .VtlsTestimonialBlockText--open::before{display:none}.Vtls-{{ section.id | handle }} .VtlsTestimonialBlockTextWrapper{position:relative}.Vtls-{{ section.id | handle }} .VtlsTestimonialBlockText.VtlsTestimonialBlockText--hidden+.VtlsTestimonialBlockText--arrow,.Vtls-{{ section.id | handle }} .VtlsTestimonialBlockText.VtlsTestimonialBlockText--open+.VtlsTestimonialBlockText--arrow{display:flex}.Vtls-{{ section.id | handle }} .VtlsTestimonialBlockText.VtlsTestimonialBlockText--open+.VtlsTestimonialBlockText--arrow{transform:rotate(180deg)}.Vtls-{{ section.id | handle }} .VtlsTestimonialBlockLast{display:inline-flex;gap:var(--vtl-space-4);align-items:center}.Vtls-{{ section.id | handle }} .VtlsTestimonialBlockLast span{cursor:pointer;pointer-events:all}.Vtls-{{ section.id | handle }} .VtlsTestimonialBlockLast::after{content:"";position:static;display:none;width:var(--vtl-space-12);height:var(--vtl-space-12);cursor:pointer;z-index:2;mask-image:url("data:image/svg+xml;utf8,%3Csvg%20width%3D%2216%22%20height%3D%2216%22%20viewBox%3D%220%200%2016%2016%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%0A%3Cpath%20d%3D%22M2%2011L8%205L14%2011%22%20stroke%3D%22%23222222%22%20stroke-width%3D%222%22%20stroke-linecap%3D%22round%22%20stroke-linejoin%3D%22round%22%2F%3E%0A%3C%2Fsvg%3E%0A");mask-repeat:no-repeat;mask-size:contain;background-color:var(--section-testimonial-color)}.Vtls-{{ section.id | handle }} .VtlsTestimonialBlockDescription{display:flex;flex-direction:column;gap:var(--vtl-space-16)}.Vtls-{{ section.id | handle }} .VtlsTestimonialBlockAuthor{display:flex;align-items:center;gap:var(--vtl-space-16)}.Vtls-{{ section.id | handle }} .VtlsTestimonialBlockAuthor__Text{display:flex;flex-direction:column}.Vtls-{{ section.id | handle }} .VtlsTestimonialBlockAuthor__AuthorName{margin:0;font-size:var(--vtl-font-size-14);color:var(--section-author-color);font-weight:var(--vtl-font-weight-600)}.Vtls-{{ section.id | handle }} .VtlsTestimonialBlockAuthor__AuthorAddText{margin:0;font-size:var(--vtl-font-size-12);color:var(--section-author-color);opacity:.7}.Vtls-{{ section.id | handle }} .VtlsTestimonialBlockAuthor__AuthorPhoto{width:var(--vtl-space-48);height:var(--vtl-space-48)}.Vtls-{{ section.id | handle }} .VtlsTestimonialBlockAuthor__AuthorPhoto img{width:100%;object-fit:cover;height:100%;border-radius:var(--vtl-border-radius-full)}.Vtls-{{ section.id | handle }} .VtlsTestimonialBlockStarShape{display:flex;gap:var(--vtl-space-1);height:var(--section-stars-size);line-height:1}.Vtls-{{ section.id | handle }} .VtlsTestimonialBlockStarShape svg{width:var(--section-stars-size);height:var(--section-stars-size)}.Vtls-{{ section.id | handle }} .VtlsTestimonialBlockStarShape path{fill:var(--section-stars-color, var(--vtl-color-bg-fill-default-on-light))}

{% endstyle %}

<script>
	(function () {
		const uninstallMessage = 'Displaying this section requires an active Square: Theme Sections & AI Pages subscription. Reinstall the <a href="https://apps.shopify.com/theme-sections-ai-page-builder" target="_blank">Square: Theme Sections & AI Pages app</a> to activate it or consider removing the section.';
		const downgradeMessage = 'You downgraded to a free plan. You need to upgrade your <a href="https://apps.shopify.com/theme-sections-ai-page-builder" target="_blank">Square: Theme Sections & AI Pages app</a> plan to activate it or consider removing the section.';
		const projectId = '5';
		const sectionsSelector = '.shopify-section';
		const apps = {
			'1': {
				prefix: 'vitals',
				hostname: 'https://appsolve.io'
			},
			'5': {
				prefix: 'tsapb',
				hostname: 'https://theme-sections.square-apps.com'
			}
		};

		const cacheKeysMetaFields = '{{ shop.metafields.vitals.tsck }}';
		const shopId = '{{ shop.id }}';

		const getHostname = () => {
			if (apps[projectId].hostname) {
				return apps[projectId].hostname;
			}

			console.error(`hostname for appId ${projectId} cannot be determined.`);
			return undefined;
		}

		const disableSection = (message, selector) => {
			const mainContainer = document.querySelector(selector);
			const contentContainer = document.querySelector(`${selector} > section`);

			if ('{{ request.design_mode }}' === 'true') {
				contentContainer.innerHTML = `<h2 class="VtlsUninstallBG"><div class="VtlsUninstallBG__Text">${message}</div></h2>`;
			} else {
				mainContainer.style.display = 'none';
				mainContainer.remove();
			}
		};

		const getThemeSectionIdsByUUID = (installedThemeSections, uuid, appId) => {
			const listIds = [];

			installedThemeSections.forEach((installedThemeSection) => {
				if (installedThemeSection.id.includes(`${apps[appId].prefix}_${uuid}`)) {
					listIds.push(installedThemeSection.id);
				}
			})

			return listIds;
		}

		const getThemeSectionsStatusList = (url) => fetch(url, {
			method: 'GET',
			headers: { 'Content-Type': 'application/json' },
		}).then((response) => response.json());

		const evaluateThemeSections = (url) => {
			getThemeSectionsStatusList(url).then(response => {
				const { data, f, i } = response;
				const installedThemeSections = document.querySelectorAll(sectionsSelector);

				data.forEach((themeSection) => {
					const { e, u, a } = themeSection;

					if (!e) {
						const themeSectionsWithUUID = getThemeSectionIdsByUUID(installedThemeSections, u, a);
						let message = '';

						if (f) {
							message = downgradeMessage;
						}

						if (!i) {
							message = uninstallMessage;
						}

						themeSectionsWithUUID.forEach((themeSectionSelector) => {
							disableSection(message, `#${themeSectionSelector}`);
						})
					}
				});
			})
		}

		if (shopId) {
			if (window.vtlsSecurityCheckStarted === undefined) {
				window.vtlsSecurityCheckStarted = true;

				let url = '';
				const hostname = getHostname();

				if (!hostname) {
					return;
				}

				const baseUrl = `${hostname}/bundle/api/v2/sf/ts`;

				if (cacheKeysMetaFields) {
					url = `${baseUrl}/${shopId}/${cacheKeysMetaFields}.json`;
				} else {
					const timestamp = Math.round(new Date().getTime() / 1000 / 10000) * 10000;
					url = `${baseUrl}/${shopId}/${timestamp}.json`;
				}

				evaluateThemeSections(url);
			}
		}
	})();
</script>

{% style %}
	

.VtlsUninstallBG{background-color:var(--vtl-color-bg-fill-inverse-on-light);background-image:url("data:image/avif;base64,iVBORw0KGgoAAAANSUhEUgAABOcAAABkCAQAAABnAJxfAAAAIGNIUk0AAHomAACAhAAA+gAAAIDoAAB1MAAA6mAAADqYAAAXcJy6UTwAAAACYktHRAD/h4/MvwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAAd0SU1FB+gLFQgmAgUXFkMAAARaSURBVHja7daxbSRBEATB8ZzvKw0h1dcIlJS4jl25Azlavffn9/3v++e//+vvCwqFQqFQKBRKSklEUCgUCoVCoVAMOgqFQqFQKJSzSiKCQqFQKBQKhWLQUSgUCoVCoZxVEhEUCoVCoVAoFIOOQqFQKBQK5VOURASFQqFQKBQKZVUSERQKhUKhUCiUVUlEUCgUCoVCoVBWZbtOPoVCoVAoFArlovIKERQKhUKhUCiUVXmFCAqFQqFQKBTKqrxCBIVCoVAoFAplVV4hgkKhUCgUCoUyK4kICoVCoVAoFMqqJCIoFAqFQqFQKKuSiKBQKBQKhUKhrEoigkKhUCgUCoVi0FEoFAqFQqGcVRIRFAqFQqFQKBSDjkKhUCgUCuWskoigUCgUCoVCoRh0FAqFQqFQKGeVRASFQqFQKBQKxaCjUCgUCoVCOaskIigUCoVCoVAoBh2FQqFQKBTKWSURQaFQKBQKhUIx6CgUCoVCoVDOKokICoVCoVAoFIpBR6FQKBQKhXJWSURQKBQKhUKhUAw6CoVCoVAolLNKIoJCoVAoFAqFYtBRKBQKhUKhnFUSERQKhUKhUCgUg45CoVAoFArlrJKIoFAoFAqFQqEYdBQKhUKhUChnlUQEhUKhUCgUCsWgo1AoFAqFQvkUJRFBoVAoFAqFQlmVRASFQqFQKBQKZVUSERQKhUKhUCiUVdmuk0+hUCgUCoVCuai8QgSFQqFQKBQKZVVeIYJCoVAoFAqFsiqvEEGhUCgUCoVCWZVXiKBQKBQKhUKhzEoigkKhUCgUCoWyKokICoVCoVAoFMqqJCIoFAqFQqFQKKuSiKBQKBQKhUKhGHQUCoVCoVAoZ5VEBIVCoVAoFArFoKNQKBQKhUI5qyQiKBQKhUKhUCgGHYVCoVAoFMpZJRFBoVAoFAqFQjHoKBQKhUKhUM4qiQgKhUKhUCgUikFHoVAoFAqFclZJRFAoFAqFQqFQDDoKhUKhUCiUs0oigkKhUCgUCoVi0FEoFAqFQqGcVRIRFAqFQqFQKBSDjkKhUCgUCuWskoigUCgUCoVCoRh0FAqFQqFQKGeVRASFQqFQKBQKxaCjUCgUCoVCOaskIigUCoVCoVAoBh2FQqFQKBTKWSURQaFQKBQKhUIx6CgUCoVCoVA+RUlEUCgUCoVCoVBWJRFBoVAoFAqFQlmVRASFQqFQKBQKZVW26+RTKBQKhUKhUC4qrxBBoVAoFAqFQlmVV4igUCgUCoVCoazKK0RQKBQKhUKhUFblFSIoFAqFQqFQKLOSiKBQKBQKhUKhrEoigkKhUCgUCoWyKokICoVCoVAoFMqqJCIoFAqFQqFQKAYdhUKhUCgUylklEUGhUCgUCoVCMegoFAqFQqFQziqJCAqFQqFQKBSKQUehUCgUCoVyVklEUCgUCoVCoVAMOgqFQqFQKJSzSiKCQqFQKBQKhWLQUSgUCoVCoZxVEhEUCoVCoVAoFIOOQqFQKBQK5dOVX5nMB52iezhxAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDI0LTExLTIxVDA4OjM4OjAyKzAwOjAw30WrCwAAACV0RVh0ZGF0ZTptb2RpZnkAMjAyNC0xMS0yMVQwODozODowMiswMDowMK4YE7cAAAAodEVYdGRhdGU6dGltZXN0YW1wADIwMjQtMTEtMjFUMDg6Mzg6MDIrMDA6MDD5DTJoAAAAAElFTkSuQmCC");background-position:center;background-size:cover;text-align:center;padding:var(--vtl-space-32);margin:0}.VtlsUninstallBG__Text{color:var(--vtl-color-text-disable-on-light);font-family:Inter,sans-serif;font-size:var(--vtl-font-size-14);font-weight:var(--vtl-font-weight-400);line-height:var(--vtl-line-height-140);padding:var(--vtl-space-16) var(--vtl-space-16) var(--vtl-space-16) var(--vtl-space-40);background-color:var(--vtl-color-bg-fill-inverse-on-light);display:inline-block}.VtlsUninstallBG__Text::before{content:url("data:image/svg+xml;utf8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2220%22%20height%3D%2220%22%20viewBox%3D%220%200%2020%2020%22%20fill%3D%22none%22%3E%0A%09%09%09%3Cpath%20d%3D%22M9.99994%206.75C10.4142%206.75%2010.7499%207.08579%2010.7499%207.5V11C10.7499%2011.4142%2010.4142%2011.75%209.99994%2011.75C9.58573%2011.75%209.24994%2011.4142%209.24994%2011V7.5C9.24994%207.08579%209.58573%206.75%209.99994%206.75Z%22%20fill%3D%22%23B28400%22%2F%3E%0A%09%09%09%3Cpath%20d%3D%22M11%2013.5C11%2014.0523%2010.5523%2014.5%2010%2014.5C9.44772%2014.5%209%2014.0523%209%2013.5C9%2012.9477%209.44772%2012.5%2010%2012.5C10.5523%2012.5%2011%2012.9477%2011%2013.5Z%22%20fill%3D%22%23B28400%22%2F%3E%0A%09%09%09%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M10%203.5C8.95471%203.5%208.21616%204.20232%207.84762%204.94672C7.65565%205.33448%206.75984%207.0441%205.84305%208.79353L5.81531%208.84646C4.9229%2010.5493%204.01714%2012.2777%203.80716%2012.702C3.4351%2013.4537%203.32931%2014.4519%203.89953%2015.316C4.46911%2016.1791%205.44222%2016.5%206.36356%2016.5L13.6364%2016.5C14.5577%2016.5%2015.5308%2016.1791%2016.1004%2015.316C16.6707%2014.4519%2016.5649%2013.4537%2016.1928%2012.702C15.9832%2012.2784%2015.08%2010.555%2014.1889%208.85476L14.1569%208.79352C13.24%207.044%2012.3443%205.33454%2012.1524%204.94693C11.784%204.20242%2011.0454%203.5%2010%203.5ZM9.1919%205.61225C9.59605%204.79592%2010.4041%204.79592%2010.8081%205.61225C11.0102%206.02061%2011.9201%207.75686%2012.8296%209.49243C13.7383%2011.2262%2014.6466%2012.9594%2014.8485%2013.3673C15.2525%2014.1837%2014.8485%2015%2013.6364%2015L6.36356%2015C5.1515%2015%204.74746%2014.1837%205.1515%2013.3673C5.35376%2012.9587%206.26468%2011.2205%207.1748%209.4838C8.08283%207.75111%208.99005%206.01994%209.1919%205.61225Z%22%20fill%3D%22%23B28400%22%2F%3E%0A%09%09%3C%2Fsvg%3E");position:absolute;width:var(--vtl-space-20);height:var(--vtl-space-20);margin-left:calc(-1*var(--vtl-space-24))}.VtlsUninstallBG__Text a{color:var(--vtl-color-text-disable-on-light)}.VtlsUninstallBG__Text a:hover{text-decoration:none}

{% endstyle %}


<script>
	(function (options) {
		const debounceFn = (func, delay) => {
			let timeoutId;

			return function (...args) {
				clearTimeout(timeoutId);
				timeoutId = setTimeout(() => func.apply(this, args), delay);
			};
		};

		if (!customElements.get('vtls-testimonial-text')) {
			class VtlsTestimonialText extends HTMLElement {
				constructor() {
					super();
				}

				connectedCallback() {
					setTimeout(() => {
						this.setProperties();

						this.onResize();
					}, 100);
				}

				setProperties() {
					this.expandableTexts = [...this.querySelectorAll('vtls-expandable-text')];
					this.blocks = this.expandableTexts.map((expandableText) => expandableText.block);
					this.contents = this.expandableTexts.map((expandableText) => expandableText.content);

					this.resizeHandler = debounceFn(this.onResize.bind(this), 200);
					window.addEventListener('resize', this.resizeHandler);
				}

				onResize() {
					this.style.setProperty('--largest-block-height', 'initial');
					this.contents.forEach((content) => {
						content.style.flexGrow = '1';
					});

					this.expandableTexts.forEach((expandableText) => expandableText.close());
					const newHeight = Math.max(...this.blocks.map((block) => block.offsetHeight));

					this.style.setProperty('--largest-block-height', newHeight + 'px');

					this.contents.forEach((content) => {
						content.style.flexGrow = '0';
					});

					this.expandableTexts.forEach((expandableText) => expandableText.setArrowPosition());
				}
			}

			customElements.define('vtls-testimonial-text', VtlsTestimonialText);
		}

		if (!customElements.get('vtls-expandable-text')) {
			class VtlsExpandableText extends HTMLElement {
				static ARROW_OFFSET = 20;

				constructor() {
					super();
				}

				connectedCallback() {
					requestAnimationFrame(() => {
						this.setProperties();
						this.checkHeight();
						this.setArrowPosition();

						this.addEventListener('click', this.thisClickHandler);
						this.arrow.addEventListener('click', this.clickHandler);
						this.block.addEventListener('mouseout', this.mouseOutHandler);

						document.body.addEventListener('click', this.bodyClickHandler.bind(this));
					});
				}

				setProperties() {
					this.allSpans = [...this.querySelectorAll('span:not([class])')];
					this.arrow = this.closest('div').querySelector('.VtlsTestimonialBlockText--arrow');
					this.block = this.closest('.VtlsTestimonialBlock__Wrapper');
					this.content = this.block.querySelector('.VtlsTestimonialBlock__Content');
					this.calculateProperties();

					this.thisClickHandler = () => {
						if (this.isMobile) return;
						this.toggle();
					};
					this.clickHandler = (e) => {
						e.stopPropagation();
						this.toggle();
					};
					this.bodyClickHandler = () => {
						if (this.isMobile) {
							this.close();
						}
					};
					this.mouseOutHandler = (e) => {
						if (this.block.contains(e.relatedTarget)) return;

						if (this.isMobile) return;
						this.close();
					};
				}

				calculateProperties() {
					this.isMobile = window.innerWidth <= window.vtlsBreakpoints.tablet.maxWidth;
					this.maxLines = this.isMobile ? 7 : 4;
					this.lineHeight = parseFloat(window.getComputedStyle(this).lineHeight).toFixed(2);
					this.maxHeight = Math.round(this.lineHeight * this.maxLines);
				}

				toggle() {
					if (this.style.maxHeight === `${this.maxHeight}px`) {
						this.open();
					} else {
						this.close();
					}
				}

				open() {
					this.style.maxHeight = 'none';
					this.classList.add('VtlsTestimonialBlockText--open');
					this.style.height = 'auto';
					this.setArrowPosition();
				}

				close() {
					this.style.maxHeight = `${this.maxHeight}px`;
					this.classList.remove('VtlsTestimonialBlockText--open');
					this.setArrowPosition();
				}

				checkHeight() {
					this.calculateProperties();
					this.classList.remove('VtlsTestimonialBlockText--hidden');
					const contentHeight = Math.max(this.offsetHeight, this.scrollHeight);
					const tolerance = 1;
					if (contentHeight - this.maxHeight > tolerance) {
						this.style.maxHeight = this.style.maxHeight === 'none' ? 'none' : `${this.maxHeight}px`;
						this.classList.add('VtlsTestimonialBlockText--hidden');
					} else {
						this.style.maxHeight = 'none';
						this.classList.remove('VtlsTestimonialBlockText--hidden');
					}
				}

				setArrowPosition() {
					if (!this.allSpans.length) return;
					this.checkHeight();

					const { bottom, right } = this.getBoundingClientRect();
					const lastSpan = this.allSpans[this.allSpans.length - 1];

					const lastVisibleSpan =
						this.allSpans.findLast((span) => {
							const { top: spanTop } = span.getBoundingClientRect();
							return spanTop < bottom;
						}) || lastSpan;

					const { right: spanRight } = lastVisibleSpan.getBoundingClientRect();
					const arrowRight = right - spanRight - VtlsExpandableText.ARROW_OFFSET;

					this.arrow.style.right = `${arrowRight}px`;
				}
			}

			customElements.define('vtls-expandable-text', VtlsExpandableText);
		}

		const PARTIAL_VISIBLE_ITEM_SIZE = 0.333;
		const lastRenderedResolution = { width: window.innerWidth, height: window.innerHeight };

		const {
			structure: {
				cardHasBorder,
				columnGap,
				containerId,
				showPortionOfLastImage,
				slideBySet,
				visibleItemsDesktop,
				visibleItemsMobile,
			},
		} = options;
		const containerElement = document.querySelector(containerId);

		if (!containerElement) {
			// perhaps is not created or it exists, but does not have products associated with it
			return;
		}

		const carouselElement = containerElement.querySelector('.VtlsCarousel');
		const listContainerElement = carouselElement.querySelector('.VtlsCarouselList');
		const contentElement = carouselElement.querySelector('.VtlsCarousel__Content');
		const leftButtonElement = containerElement.querySelector('.VtlsNavigationButtons__Button.left-chevron');
		const rightButtonElement = containerElement.querySelector('.VtlsNavigationButtons__Button.right-chevron');
		const listItems = listContainerElement.querySelectorAll('.VtlsTestimonialBlock');
		const navigationElement = containerElement.querySelector('.VtlsCarousel__Navigation');
		const sectionWidth = parseInt(carouselElement.getAttribute('data-section-width'));
		const isMobile = window.innerWidth <= window.vtlsBreakpoints.tablet.maxWidth;
		const itemsLength = listItems.length;

		const getVisibleItems = () => {
			if (window.innerWidth >= window.vtlsBreakpoints.desktop.minWidth) {
				return visibleItemsDesktop;
			} else {
				return visibleItemsMobile;
			}
		};

		const getElementWidth = () => {
			const carouselWidth = getCarouselWidth();
			const visibleItems = getVisibleItems();
			const columnsToShow =
				visibleItems + (showPortionOfLastImage && itemsLength > visibleItems ? PARTIAL_VISIBLE_ITEM_SIZE : 0);

			return Math.floor((carouselWidth - columnGap * visibleItems) / columnsToShow);
		};

		const getNavigationScreensNumber = () => {
			const visibleItems = getVisibleItems();

			return Math.ceil((itemsLength - visibleItems) / getSlideBy(slideBySet) + 1);
		};

		const getCarouselWidth = () => parseInt(window.getComputedStyle(carouselElement).width);

		const getSlideBy = (slideBySet) => {
			return window.innerWidth <= window.vtlsBreakpoints.mobile.maxWidth ? 1 : slideBySet;
		};

		let position = 0;

		const resizeElements = () => {
			const elementWidth = getElementWidth();

			listItems.forEach((item) => {
				const imageContainerElement = item.querySelector('.VtlsTestimonialBlock__Wrapper');

				item.style.minWidth = `${elementWidth}px`;
				item.style.maxWidth = `${elementWidth}px`;

				const containerWidth = elementWidth - (cardHasBorder ? 2 : 0);
				imageContainerElement.style.width = `${containerWidth}px`;
			});
		};

		resizeElements();

		leftButtonElement.addEventListener('click', () => navigate('left'));
		rightButtonElement.addEventListener('click', () => navigate('right'));

		const updateButtonsState = () => {
			const screensNumber = getNavigationScreensNumber();

			leftButtonElement.classList.toggle('VtlsNavigationButtons__Button--disabled', position === 0);
			rightButtonElement.classList.toggle(
				'VtlsNavigationButtons__Button--disabled',
				position === screensNumber - 1
			);
		};

		const navigate = (direction) => {
			const screensNumber = getNavigationScreensNumber();

			if (direction === 'left' && position > 0) {
				position--;
			} else if (direction === 'right' && position < screensNumber - 1) {
				position++;
			} else {
				return;
			}
			const elementWidth = getElementWidth();
			const carouselList = contentElement.firstElementChild;
			const slideBy = getSlideBy(slideBySet);
			let shift = -(position * (elementWidth + columnGap) * slideBy);

			// Check for the last slide
			const isLastSlide = position === screensNumber - 1;
			const partialShift = elementWidth * PARTIAL_VISIBLE_ITEM_SIZE;

			if (isLastSlide) {
				if (sectionWidth === 100) {
					shift += isMobile ? partialShift - columnGap : partialShift;
				} else if (sectionWidth < 100) {
					shift += isMobile ? partialShift - columnGap : partialShift + columnGap;
				}
			}

			carouselList.style.marginLeft = `${shift}px`;
			updateButtonsState();
		};

		const resizeCarousel = () => {
			if (window.innerWidth !== lastRenderedResolution.width) {
				position = 0;

				resizeElements();
				listContainerElement.style.marginLeft = '0';
				updateButtonsState();
				updateNavigationVisibility();

				lastRenderedResolution.width = window.innerWidth;
				lastRenderedResolution.height = window.innerHeight;
			}
		};

		const updateNavigationVisibility = () => {
			const elementWidth = getElementWidth();
			const contentWidth = elementWidth * itemsLength + columnGap * (itemsLength - 1);
			const needsNavigationVisible = contentWidth > carouselElement.offsetWidth;

			navigationElement.style.display = needsNavigationVisible ? 'flex' : 'none';
		};

		updateButtonsState();
		updateNavigationVisibility();

		window.addEventListener('resize', debounceFn(resizeCarousel, 100));

		// touch functionality {
		let xTouchDown = null;
		let yTouchDown = null;

		const getTouches = (evt) => {
			return evt.touches || evt.originalEvent.touches;
		};

		const handleTouchStart = (evt) => {
			const firstTouch = getTouches(evt)[0];
			xTouchDown = firstTouch.clientX;
			yTouchDown = firstTouch.clientY;
		};

		const handleTouchMove = (evt) => {
			if (!xTouchDown) {
				return;
			}

			const xUp = evt.touches[0].clientX;
			const yUp = evt.touches[0].clientY;
			const xDiff = xTouchDown - xUp;
			const yDiff = yTouchDown - yUp;

			if (Math.abs(xDiff) > Math.abs(yDiff)) {
				if (xDiff > 0) {
					navigate('right');
				} else {
					navigate('left');
				}
			}

			xTouchDown = null;
			yTouchDown = null;
		};

		contentElement.addEventListener('touchstart', handleTouchStart, false);
		contentElement.addEventListener('touchmove', handleTouchMove, false);
		// touch functionality }
	})({
		structure: {
			containerId: '.Vtls-{{ section.id | handle }} .VtlsCarouselContainer',
			showPortionOfLastImage: true,
			visibleItemsDesktop: parseInt('{{ section.settings.testimonial_per_row }}'),
			visibleItemsMobile: 1,
			columnGap: parseInt('{{ section.settings.testimonials_space_between }}'),
			slideBySet: 1,
		},
	});
</script>
