.contact img {
  max-width: 100%;
}
.contact .form__message {
  align-items: flex-start;
  border: none; 
  box-shadow: none;
  outline: none;
  outline-offset: unset;
}

.contact .icon-success {
  margin-top: 0.2rem;
}

.contact .field {
  margin-bottom: 1.5rem;
}

@media screen and (min-width: 750px) {
  .contact .field {
    margin-bottom: 2rem;
  }
}

.contact__button {
  margin-top: 3rem;
}

@media screen and (min-width: 750px) {
  .contact__button {
    margin-top: 4rem;
  }
}

@media screen and (min-width: 750px) {
  .contact__fields {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-column-gap: 2rem;
  }
}
/* .contact .title-wrapper--no-top-margin {
    margin-top: 0;
    font-size: 18px;
    font-family: var(--font-body-family);
    font-weight: 400;
    line-height: 2.6rem;
   margin-bottom: 6rem;
} */
.grecaptcha-badge {
  visibility: hidden;
}
.contact .title-wrapper--no-top-margin span{    
  display: block;
    font-size: 3.5rem;
    color: var(--color-icon);
    font-weight: 400;
    margin-bottom: 30px;
    font-family: var(--font-heading-family);}

.section-contact-form .page-width{max-width:120rem; margin:auto;} 
