.deferred-media__poster {
  background-color: transparent;
  border: none;
  cursor: pointer;
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

.media > .deferred-media__poster {
  display: flex;
  align-items: center;
  justify-content: center;
}

.deferred-media__poster img {
  width: auto;
  max-width: 100%;
  height: 100%;
}

.deferred-media {
  overflow: hidden;
}

.deferred-media:not([loaded='true']) template {
  z-index: -1;
}

.deferred-media[loaded='true'] > .deferred-media__poster {
  display: none;
}

.deferred-media__poster:focus-visible {
  outline: none;
  box-shadow: 0 0 0 var(--media-border-width) rgba(var(--color-foreground), var(--media-border-opacity)), 0 0 0 calc(var(--media-border-width) + 0.3rem) rgb(var(--color-background)),0 0 0 calc(var(--media-border-width) + 0.5rem) rgba(var(--color-foreground),.5);
  border-radius: calc(var(--media-radius) - var(--media-border-width));
}

.deferred-media__poster:focus {
  outline: none;
  box-shadow: 0 0 0 var(--media-border-width) rgba(var(--color-foreground), var(--media-border-opacity)), 0 0 0 calc(var(--media-border-width) + 0.3rem) rgb(var(--color-background)),0 0 0 calc(var(--media-border-width) + 0.5rem) rgba(var(--color-foreground),.5);
  border-radius: calc(var(--media-radius) - var(--media-border-width));
}

.deferred-media__poster:focus:not(:focus-visible) {
  outline: 0;
  box-shadow: none;
}

.deferred-media__poster-button {
  background-color: transparent;
  border: 0.2rem solid rgba(var(--color-background), 1);
  border-radius: 50%;
  color: rgb(var(--color-foreground));
  display: flex;
  align-items: center;
      transition: all 0.3s linear;
  justify-content: center;
  height: 14.2rem;
  width: 14.2rem;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) scale(1);
 /* transition: transform var(--duration-long) ease, color var(--duration-sh) ease; */
  z-index: 1;
}
.deferred-media__poster-button:hover span{ letter-spacing: 3.5px;}
.deferred-media__poster-button span{
    color: var(--gradient-background);
    font-size: 1.4rem;
    letter-spacing: 2.6px;
    text-transform: uppercase;
    line-height: 19px;
}
.deferred-media__poster-button:before {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    background: #ffffff00;
    border-radius: 50%;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: -1;
    filter: blur(3px);
    backdrop-filter: brightness(1);
    opacity: 1;
}
.deferred-media__poster-button:hover {
  transform: translate(-50%, -50%) scale(1.1);
}

.deferred-media__poster-button .icon {
  width: 2rem;
  height: 2rem;
}

.deferred-media__poster-button .icon-play {
  margin-left: 0.2rem;
}

