{%- liquid
  assign count = 0 
  assign title = ""
  assign imageUrl = "" 
  assign class_img_1 = ''
  assign class_img_2 = ''
  assign class_img_3 = ''

  if section.settings.heading != blank
    assign title = section.settings.heading
  elsif section.blocks[0].type == 'image_banner' and section.blocks[0].settings.heading != blank
    assign title = section.blocks[0].settings.heading
  elsif section.blocks[1].type == 'image_banner' and section.blocks[1].settings.heading != blank    
    assign title = section.blocks[1].settings.heading
  endif

  if section.settings.image_3 != blank
    assign imageUrl = section.settings.image_3
    assign count = count | plus: 1
  endif
  if section.settings.image_2 != blank
    assign class_img_3 = ' otsb-hidden md:block'
    assign imageUrl = section.settings.image_2
    assign count = count | plus: 1
  endif
  if section.settings.image != blank 
    assign class_img_2 = ' otsb-hidden md:block'
    assign class_img_3 = ' otsb-hidden md:block'
    assign imageUrl = section.settings.image
    assign count = count | plus: 1
  endif
  assign imageUrlMobile = imageUrl
  if section.settings.image_mobile != blank 
    assign imageUrlMobile = section.settings.image_mobile
    if request.design_mode
      assign class_img_1 = ' otsb-hidden md:block'
      assign class_img_3 = ' otsb-hidden md:block'
      assign class_img_2 = ' otsb-hidden md:block'
    endif
  endif
  
-%}

{%- style -%}
  {%- if section.settings.image_mobile != blank -%}
    .otsb__root .mobile-{{ section.id }}-natural {
      padding-bottom: {{ 1 | divided_by: section.settings.image_mobile.aspect_ratio | times: 100 }}%; 
    }
  {%- endif -%}
  .otsb__root .image-{{ section.id }}-1,
  .otsb__root .image-{{ section.id }}-2,
  .otsb__root .image-{{ section.id }}-3 {
    object-position: {{ imageUrlMobile.presentation.focal_point }};
  }
  #shopify-section-{{ section.id }} parallax-movement.no_parallax_effect { 
    height: var(--height-parallax, 100%);
  }
  #shopify-section-{{ section.id }}.section-promotion-banner {
    display: block;
  }
  @media screen and (min-width: 768px) {
    {%- if section.settings.image != blank -%} 
    .otsb__root .desktop-{{ section.id }}-natural {
        padding-bottom: {{ 1 | divided_by: section.settings.image.aspect_ratio | times: 100 }}%; 
      } 
    {%- endif -%}
    .otsb__root .image-{{ section.id }}-1 {
      object-position: {{ section.settings.image.presentation.focal_point }};
    }
    .otsb__root .image-{{ section.id }}-2 {
      object-position: {{ section.settings.image_2.presentation.focal_point }};
    }
    .otsb__root .image-{{ section.id }}-3 {
      object-position: {{ section.settings.image_3.presentation.focal_point }};
    }
  }
  {%- if section.settings.content_position == "custom" -%}
    @media screen and (min-width: 1024px) {
      .otsb__root .promotion-banner--{{ section.id }} {
        left:{{ section.settings.content_horizontal }}%;
        top:{{ section.settings.content_vertical }}%;
        transform: translate(-{{ section.settings.content_horizontal }}%,-{{ section.settings.content_vertical }}%);
      }
    }
  {%- endif -%}
{%- endstyle -%}
{% if request.design_mode %}
  <style>
    .otsb_nope {
      display: none !important;
      height: 0 !important;
      overflow: hidden !important;
      visibility: hidden !important;
      width: 0 !important;
      opacity: 0 !important;
    }
    ._otsb_warning {
      position: relative;
      box-shadow: 0rem 0.1875rem 0.0625rem -0.0625rem rgba(26, 26, 26, 0.07);
      border-radius: 1rem;
    }
    ._otsb_warning::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 0;
      box-shadow: 0.0625rem 0rem 0rem 0rem rgba(0, 0, 0, 0.13) inset, -0.0625rem 0rem 0rem 0rem rgba(0, 0, 0, 0.13) inset, 0rem -0.0625rem 0rem 0rem rgba(0, 0, 0, 0.17) inset, 0rem 0.0625rem 0rem 0rem rgba(204, 204, 204, 0.5) inset;
      border-radius: 1rem;
      pointer-events: none;
      mix-blend-mode: luminosity;
    }
    .otsb_warning_root {
      margin-top:36px;
      margin-bottom:36px;
    }
    .otsb_warning_root ._otsb_warning_1 {border-top-left-radius:1rem;border-top-right-radius:1rem;border:1px solid #fcaf0a;background:#fcb827;padding:1rem}
    .otsb_warning_root ._otsb_warning_2 {align-items:center;gap:8px;display:flex;flex-direction:row;flex-wrap:nowrap;justify-content:space-between}
    .otsb_warning_root ._otsb_warning_3 {display:flex;gap:4px;flex-direction:row;flex-wrap:nowrap;justify-content:space-between}
    .otsb_warning_root ._otsb_warning ._otsb_warning__icon {display:block;height:20px;width:20px;max-height:100%;max-width:100%;margin:auto}
    .otsb_warning_root h2 {overflow-wrap:anywhere;word-break:normal;font-size:100%;font-weight:650;line-height:1.25;color:rgb(37,26,0)}
    .otsb_warning_root * {
      margin: 0;
      padding: 0;
      font-family: var(--font-body-family);
      line-height: 1.375;
    }
    .otsb_warning_root ul {
      list-style-type: disc;
    }
    .otsb_warning_root a {
      color: rgb(0, 0, 238);
      text-decoration: underline;
    }
    .otsb_warning_root .otsb_warning_message_container {
      display: flex;
      flex-direction: column;
      gap: 1rem;
      padding:1rem;
      color:rgb(37,26,0);
    }
    .otsb_warning_root .otsb_warning_message_container ul {
      padding-inline-start:3rem;
    }
  </style>
  <div x-data="otsb_script_require" class="page-width otsb_warning_root">
    <div class="_otsb_warning">
      <div class="_otsb_warning_1">
        <div class="_otsb_warning_2">
          <div class="_otsb_warning_3">
            <span class="_otsb_warning__icon">
              <svg viewBox="0 0 20 20" class="Polaris-Icon__Svg" focusable="false" aria-hidden="true"><path d="M10 6.75a.75.75 0 0 1 .75.75v3.5a.75.75 0 1 1-1.5 0v-3.5a.75.75 0 0 1 .75-.75Z"></path><path d="M11 13.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"></path><path fill-rule="evenodd" d="M10 3.5c-1.045 0-1.784.702-2.152 1.447a449.26 449.26 0 0 1-2.005 3.847l-.028.052a403.426 403.426 0 0 0-2.008 3.856c-.372.752-.478 1.75.093 2.614.57.863 1.542 1.184 2.464 1.184h7.272c.922 0 1.895-.32 2.464-1.184.57-.864.465-1.862.093-2.614-.21-.424-1.113-2.147-2.004-3.847l-.032-.061a429.497 429.497 0 0 1-2.005-3.847c-.368-.745-1.107-1.447-2.152-1.447Zm-.808 2.112c.404-.816 1.212-.816 1.616 0 .202.409 1.112 2.145 2.022 3.88a418.904 418.904 0 0 1 2.018 3.875c.404.817 0 1.633-1.212 1.633h-7.272c-1.212 0-1.617-.816-1.212-1.633.202-.408 1.113-2.147 2.023-3.883a421.932 421.932 0 0 0 2.017-3.872Z"></path></svg>
            </span>
            <h2>App Embeds Are Disabled</h2>
          </div>
        </div>
      </div>
      <div class="otsb_warning_message_container">
        <p>To use this section, the app embeds of OT: Theme Sections must be enabled in the theme editor. Please follow these steps to activate them:</p>
        <ul>
          <li>In the left panel, click the last icon that says <b>“App embeds”</b>.</li>
          <li>Enter <b>“OT”</b> on the search bar to quickly find and embed the apps from OT: Theme Sections.</li>
          <li>Turn on the Toggle buttons of "Section Builder Script" and "Section Builder Style", then click <b>Save</b>.</li>
        </ul>
        <p>Please refer to the User Guide <a href="https://support.omnithemes.com/blogs/ot-theme-sections-get-started/1-embed-app-to-shopify-theme" target="_blank">here</a></p>
        <p>For further support: feel free to contact us at <a href="mailto:<EMAIL>"><EMAIL></a>!</p>
      </div>
    </div>
  </div>
{% endif %}
<div class="otsb__root otsb_nope" x-data="otsb_script_1">
<div class="pt-[{{ section.settings.padding_top_mobile }}px] md:pt-[{{ section.settings.padding_top }}px] pb-[{{ section.settings.padding_bottom_mobile }}px] md:pb-[{{ section.settings.padding_bottom }}px]{% if section.settings.full_width %} {% if section.settings.padding_full_width %} md:pl-5 md:pr-5{% else %} md:pl-0 md:pr-0{% endif %}{% else %} otsb-page-width{% endif %}{% if section.settings.full_width_mobile %} otsb-full-width-mobile{% else %} pl-5 pr-5{% endif %}">
  <div class="promotion section--{{ section.id }} w-full flex relative overflow-hidden{% if section.settings.desktop_height == "natural" %}{% if section.settings.image != blank %} md:h-0 desktop-{{ section.id }}-natural{% else %} md:promotion:h-[650px]{% endif %}{% else %} md:promotion:h-[{{ section.settings.desktop_height }}] md:promotion:pb-0{% endif %}{% if section.settings.rounded_corner_image %} md:rounded-[10px] z-20{% else %} md:rounded-none{% endif %}{% if section.settings.rounded_corner_image_mobile %}  z-20 rounded-[10px]{% endif %}
    {% if section.settings.mobile_height == "natural" %}{% if section.settings.image_mobile != blank %} h-0 mobile-{{ section.id }}-natural{% else %} h-[550px]{% endif %}{% else %}h-[{{ section.settings.mobile_height }}] md:h-auto{% endif %}"
    x-data='{
      data: {
        promotion: {
          source: "promotion-banner",
          title: "{{ title | escape }}",
          image: "{{ imageUrl | image_url | split: '//cdn.shopify.com/s/files' }}",
          image_host: "//cdn.shopify.com/s/files"
        }
      },
      setData(data) {
        this.data = data;
      }
    }'
    x-intersect.once.threshold.70='$store.xCustomerEvent.fire("promotion_viewed", $el, data)'
    {% if section.settings.image_mobile != blank %}
      x-init='$nextTick(() => {
        if (screen.width < 768) {
          setData({
            promotion: {
              source: "promotion-banner",
              title: "{{ title | escape }}",
              image: "{{ imageUrlMobile | image_url | split: '//cdn.shopify.com/s/files' }}",
              image_host: "//cdn.shopify.com/s/files"
            }
          })
        }
      })'
    {% endif %}>
    <div class="w-full h-full{% if section.settings.mobile_height == 'natural' %} absolute top-0 left-0 bottom-0{% else %} relative{% endif %}{% if section.settings.desktop_height == "natural" %} md:absolute md:top-0 md:left-0 md:bottom-0{% else %} md:promotion:relative{% endif %}">
      <div class="absolute top-0 left-0 bottom-0 right-0 flex z-0 gap-0">
        {% if section.settings.image_link != blank %}
          <a 
            href="{{ section.settings.image_link }}" 
            aria-label="Banner link"
            class="z-10 absolute top-0 left-0 bottom-0 right-0 disable-effect"
            {% if section.settings.open_new_window %}target="_blank"{% endif %}
          >
        {% endif %}
        {% style %}
          #shopify-section-{{ section.id }} .otsb__root .otsb-image-treatment-overlay {
            background: rgb({{ section.settings.image_overlay_color.red }}, {{ section.settings.image_overlay_color.green }}, {{ section.settings.image_overlay_color.blue }})
          }
        {% endstyle %}
        <span class="z-10 absolute top-0 left-0 bottom-0 right-0 otsb-image-treatment-overlay opacity-{{ section.settings.image_overlay_opacity }}"></span>
        {% if section.settings.image_link != blank %}
          </a>
        {% endif %}
        {%- if section.settings.image == blank and section.settings.image_2 == blank and section.settings.image_3 == blank -%}
          <div style="background-color: #c9c9c9" class="w-full md:block text-[#acacac]{% if section.settings.image_mobile != blank %} otsb-hidden{%- endif -%}">
            {{ 'lifestyle-2' | placeholder_svg_tag: 'w-full h-full' }}
          </div>
        {%- endif -%}
        {%- if section.settings.image != blank -%}
          {% if section.settings.show_hero %}
            <div class="otsb-hidden">
              {%- if section.settings.image_mobile != blank -%}
                {{ imageUrlMobile | image_url: width: 1500 | image_tag: widths: '375, 450, 750, 900, 1100, 1500', preload: true, loading: 'lazy' }}
              {%- endif -%}
              {{ section.settings.image | image_url: width: 3840 | image_tag: widths: '750, 900, 1100, 1500, 1780, 2000, 3000, 3840', preload: true, loading: 'lazy' }}
            </div>
          {% endif %}
          <div class="w-full{{ class_img_1 }}">
            <parallax-image class="block h-full w-full">
              <parallax-movement x-data="xParallax" x-intersect.once.margin.200px="load({{ section.settings.disable_parallax_effect }})" class="{% if  section.settings.disable_parallax_effect == true %}no_parallax_effect {% endif %}">
                {% comment %}theme-check-disable ImgLazyLoading{% endcomment %}
                <picture class="h-full">
                  <source 
                    srcset="{{ imageUrlMobile | image_url: width: 375 }} 375w,
                      {{ imageUrlMobile | image_url: width: 450 }} 450w,
                      {{ imageUrlMobile | image_url: width: 750 }} 750w,
                      {{ imageUrlMobile | image_url: width: 900 }} 900w,
                      {{ imageUrlMobile | image_url: width: 1100 }} 1100w,
                      {{ imageUrlMobile | image_url: width: 1500 }} 1500w"
                    media="(max-width: 767px)"
                    width="{{ imageUrlMobile.width }}"
                    height="{{ imageUrlMobile.height }}"
                  >
                  <img
                    srcset="{{ section.settings.image | image_url: width: 750 }} 750w,
                      {{ section.settings.image | image_url: width: 900 }} 900w,
                      {{ section.settings.image | image_url: width: 1100 }} 1100w,
                      {{ section.settings.image | image_url: width: 1500 }} 1500w,
                      {{ section.settings.image | image_url: width: 1780 }} 1780w,
                      {{ section.settings.image | image_url: width: 2000 }} 2000w,
                      {{ section.settings.image | image_url: width: 3000 }} 3000w,
                      {{ section.settings.image | image_url: width: 3840 }} 3840w"
                    {% unless section.settings.show_hero %}
                      loading="lazy"
                    {% else %}
                      loading="eager"
                      fetchpriority="high"
                      decoding="sync"
                    {% endunless %}
                    src="{{ section.settings.image | image_url: width: 3840 }}"
                    {% if section.settings.image_mobile != blank %}
                      :alt="(screen.width < 768) ? '{{ section.settings.image_mobile.alt }}' : '{{ section.settings.image.alt }}'"
                    {% else %}
                      alt="{{ section.settings.image.alt | escape }}"
                    {% endif %}
                    sizes="(min-width: 767px) calc(100vw / {{ count }}), 100vw"
                    width="{{ section.settings.image.width }}"
                    height="{{ section.settings.image.height }}"
                    class="w-full h-full object-cover z-0 image-{{ section.id }}-1"
                    style="(min-width: 767px) { object-position: {{ section.settings.image.presentation.focal_point }}; } 
                    object-position: {{ imageUrlMobile.presentation.focal_point }}"
                  >
                  {% comment %}theme-check-enable ImgLazyLoading{% endcomment %}
                  </picture>   
              </parallax-movement>
            </parallax-image>
          </div>
        {%- endif -%}
        {%- if section.settings.image_2 != blank -%}
          {% if section.settings.show_hero %}
            <div class="otsb-hidden">
              {{ imageUrlMobile | image_url: width: 1500 | image_tag: widths: '375, 450, 750, 900, 1100, 1500', preload: true }}
              {{ section.settings.image_2 | image_url: width: 3840 | image_tag: widths: '750, 900, 1100, 1500, 1780, 2000, 3000, 3840', preload: true }}
            </div>
          {% endif %}
          <div class="w-full{{ class_img_2 }}">
            <parallax-image class="block h-full w-full">
              <parallax-movement x-data="xParallax" x-intersect.once.margin.200px="load({{ section.settings.disable_parallax_effect }})" class="{% if  section.settings.disable_parallax_effect == true %}no_parallax_effect {% endif %}">
                {% comment %}theme-check-disable ImgLazyLoading{% endcomment %}
                <picture class="h-full">
                  <source
                    srcset="{{ imageUrlMobile | image_url: width: 375 }} 375w,
                      {{ imageUrlMobile | image_url: width: 450 }} 450w,
                      {{ imageUrlMobile | image_url: width: 750 }} 750w,
                      {{ imageUrlMobile | image_url: width: 900 }} 900w,
                      {{ imageUrlMobile | image_url: width: 1100 }} 1100w,
                      {{ imageUrlMobile | image_url: width: 1500 }} 1500w"
                    media="(max-width: 767px)"
                    width="{{ imageUrlMobile.width }}"
                    height="{{ imageUrlMobile.height }}"
                  >
                  <img
                    srcset="{{ section.settings.image_2 | image_url: width: 750 }} 750w,
                      {{ section.settings.image_2 | image_url: width: 900 }} 900w,
                      {{ section.settings.image_2 | image_url: width: 1100 }} 1100w,
                      {{ section.settings.image_2 | image_url: width: 1500 }} 1500w,
                      {{ section.settings.image_2 | image_url: width: 1780 }} 1780w,
                      {{ section.settings.image_2 | image_url: width: 2000 }} 2000w,
                      {{ section.settings.image_2 | image_url: width: 3000 }} 3000w,
                      {{ section.settings.image_2 | image_url: width: 3840 }} 3840w"
                    {% unless section.settings.show_hero %}
                      loading="lazy"
                    {% else %}
                      loading="eager"
                      fetchpriority="high"
                      decoding="sync"
                    {% endunless %}
                    src="{{ section.settings.image_2 | image_url: width: 3840 }}"
                    {% if section.settings.image_mobile != blank %}
                      :alt="(screen.width < 768) ? '{{ section.settings.image_mobile.alt }}' : '{{ section.settings.image_2.alt }}'"
                    {% else %}
                      alt="{{ section.settings.image_2.alt | escape }}"
                    {% endif %}
                    sizes="(min-width: 767px) calc(100vw / {{ count }}), 100vw"
                    width="{{ section.settings.image_2.width }}"
                    height="{{ section.settings.image_2.height }}"
                    class="w-full h-full object-cover z-0 image-{{ section.id }}-2"
                    style="(min-width: 767px) { object-position: {{ section.settings.image_2.presentation.focal_point }}; } 
                    object-position: {{ imageUrlMobile.presentation.focal_point }}"
                  >
                  </picture>
                {% comment %}theme-check-enable ImgLazyLoading{% endcomment %}
              </parallax-movement>
            </parallax-image>
          </div>
        {%- endif -%}
        {%- if section.settings.image_3 != blank -%}
          {% if section.settings.show_hero %}
            <div class="otsb-hidden">
              {{ imageUrlMobile | image_url: width: 1500 | image_tag: widths: '375, 450, 750, 900, 1100, 1500', preload: true }}
              {{ section.settings.image_3 | image_url: width: 3840 | image_tag: widths: '750, 900, 1100, 1500, 1780, 2000, 3000, 3840', preload: true }}
            </div>
          {% endif %}
          <div class="w-full{{ class_img_3 }}">
            <parallax-image class="block h-full w-full">
              <parallax-movement x-data="xParallax" x-intersect.once.margin.200px="load({{ section.settings.disable_parallax_effect }})" class="{% if  section.settings.disable_parallax_effect == true %}no_parallax_effect {% endif %}">
                {% comment %}theme-check-disable ImgLazyLoading{% endcomment %}
                <source
                  srcset="{{ imageUrlMobile | image_url: width: 375 }} 375w,
                    {{ imageUrlMobile | image_url: width: 450 }} 450w,
                    {{ imageUrlMobile | image_url: width: 750 }} 750w,
                    {{ imageUrlMobile | image_url: width: 900 }} 900w,
                    {{ imageUrlMobile | image_url: width: 1100 }} 1100w,
                    {{ imageUrlMobile | image_url: width: 1500 }} 1500w"
                  media="(max-width: 767px)"
                  width="{{ imageUrlMobile.width }}"
                  height="{{ imageUrlMobile.height }}"
                >
                <img
                  srcset="{{ section.settings.image_3 | image_url: width: 750 }} 750w,
                    {{ section.settings.image_3 | image_url: width: 900 }} 900w,
                    {{ section.settings.image_3 | image_url: width: 1100 }} 1100w,
                    {{ section.settings.image_3 | image_url: width: 1500 }} 1500w,
                    {{ section.settings.image_3 | image_url: width: 1780 }} 1780w,
                    {{ section.settings.image_3 | image_url: width: 2000 }} 2000w,
                    {{ section.settings.image_3 | image_url: width: 3000 }} 3000w,
                    {{ section.settings.image_3 | image_url: width: 3840 }} 3840w"
                  {% unless section.settings.show_hero %}
                    loading="lazy"
                  {% else %}
                    loading="eager"
                    fetchpriority="high"
                    decoding="sync"
                  {% endunless %}
                  src="{{ section.settings.image_3 | image_url: width: 3840 }}"
                  {% if section.settings.image_mobile != blank %}
                    :alt="(screen.width < 768) ? '{{ section.settings.image_mobile.alt }}' : '{{ section.settings.image_3.alt }}'"
                  {% else %}
                    alt="{{ section.settings.image_3.alt | escape }}"
                  {% endif %}
                  sizes="(min-width: 767px) calc(100vw / {{ count }}), 100vw"
                  width="{{ section.settings.image_3.width }}"
                  height="{{ section.settings.image_3.height }}"
                  class="w-full h-full object-cover z-0 image-{{ section.id }}-3"
                  style="(min-width: 767px) { object-position: {{ section.settings.image_3.presentation.focal_point }}; } 
                  object-position: {{ imageUrlMobile.presentation.focal_point }}"
                > 
                {% comment %}theme-check-enable ImgLazyLoading{% endcomment %}
              </parallax-movement>
            </parallax-image>
          </div>
        {%- endif -%}
        {%- if section.settings.image_mobile != blank and count == 0 or request.design_mode -%}
          <div class="w-full md:otsb-hidden">
            <parallax-image class="block h-full w-full">
              <parallax-movement x-data="xParallax" x-intersect.once.margin.200px="load({{ section.settings.disable_parallax_effect }})" class="{% if  section.settings.disable_parallax_effect == true %}no_parallax_effect {% endif %}">
                {% comment %}theme-check-disable ImgLazyLoading{% endcomment %}
                <img
                  srcset="{{ section.settings.image_mobile | image_url: width: 540 }} 540w,
                  {{ section.settings.image_mobile | image_url: width: 767 }} 767w,
                  {{ section.settings.image_mobile | image_url }} {{ section.settings.image_moblie.width }}w"
                  src="{{ section.settings.image_mobile | image_url: width: 767 }}"
                  alt="{{ section.settings.image_mobile.alt | escape }}"
                  {% unless section.settings.show_hero and forloop.first %}
                    loading="lazy"
                  {% else %}
                    loading="eager"
                    fetchpriority="high"
                    decoding="sync"
                  {% endunless %}
                  width="{{ section.settings.image_mobile.width }}"
                  height="{{ section.settings.image_mobile.height }}"
                  class="w-full h-full object-cover z-0"
                  style="object-position: {{ section.settings.image_mobile.presentation.focal_point }};"
                >
                {% comment %}theme-check-enable ImgLazyLoading{% endcomment %}
              </parallax-movement>
            </parallax-image>
          </div>
        {%- endif -%} 
      </div>
      <div class="{% if section.settings.content_position != "custom" %}otsb-page-width ml-5 mr-5 lg:mx-auto absolute top-0 left-0 bottom-0 right-0{% else %} w-full h-full{% endif %}">
        {%- if section.settings.image_link != blank -%}
          <a href="{{ section.settings.image_link }}"{% if section.settings.open_new_window %} target="_blank"{% endif %} class="absolute top-0 left-0 right-0 bottom-0 disable-effect" aria-label="Banner link"> </a>
        {%- endif -%}
        <div class="promotion-banner--{{ section.id }} w-11/12 overflow-hidden h-fit promotion-alignment--{{ section.settings.position_content_mobile }} absolute lg:max-w-3xl lg:w-3/5{% if section.settings.content_position != "custom" %} lg:promotion-alignment--{{ section.settings.content_position }}{% else %} p-5{% endif %}">
          {%- if section.settings.image_link != blank -%}
            <a
              href="{{ section.settings.image_link }}" 
              class="absolute top-0 left-0 right-0 bottom-0 disable-effect"
              aria-label="Banner link"
              {% if section.settings.open_new_window %}target="_blank"{% endif %}
            > </a>
          {%- endif -%}
          {%- for block in section.blocks -%}
            {%- case block.type -%}
              {%- when 'image_banner' -%}
                {%- liquid 
                  assign heading_size =  block.settings.heading_size | times: 100 | times: 0.000225
                  assign size_text =  block.settings.text_size | times: 100 | times: 0.0000875

                  assign add_class_light = false
                  assign add_class_dark = false
                  if block.settings.color_content_box_light.alpha != 0.0 or block.settings.add_frame
                    assign add_class_light = true
                  endif

                  if block.settings.color_content_box_dark.alpha != 0.0 or block.settings.add_frame
                    assign add_class_dark = true
                  endif
                -%}
                {%- style -%}
                    #shopify-section-{{ section.id }} .highlight.hl-font {
                      color: var(--color-highlight);
                    }
                    #shopify-section-{{ section.id }} .highlight.hl-underline {
                      transition: color .3s cubic-bezier(.06,.14,.8,.91);
                      transition-delay: .4s;
                      color: var(--color-highlight);
                    }
                    #shopify-section-{{ section.id }} .highlight-anm-start .svg-underline path, 
                    #shopify-section-{{ section.id }} .highlight-anm-start .svg-circle path {
                      stroke-dashoffset: 0;
                    }
                    #shopify-section-{{ section.id }} .svg-underline path {
                      stroke-dasharray: 3000;
                      stroke-dashoffset: 3000;
                      transition: stroke-dashoffset 1.5s ease-in-out;
                    }
                  {% unless block.settings.color_content_box_light.alpha == 0.0 %}
                    .otsb__root .content--{{ block.id }} {  
                      background:{{ block.settings.color_content_box_light }}; 
                    }
                  {% endunless %}
                  .otsb__root .content--{{ block.id }}:after {
                    border-color: rgba(var(--image-treatment-text));
                  }
                  .otsb__root .heading--{{ block.id }}{
                    font-size: {{ heading_size | times: 0.5 }}rem;
                  }
                  .otsb__root .sub-heading-{{ block.id }} {
                    font-size: {{ heading_size | times: 0.3 }}rem;
                  } 
                  .otsb__root .content-text-{{ block.id }} {
                    font-size: {{ size_text | times: 0.9 }}rem;
                  }
                  @media screen and (min-width: 768px) { 
                    .otsb__root .sub-heading-{{ block.id }} {
                      font-size: {{ heading_size | times: 0.58 }}rem;
                    }
                    .otsb__root .content-text-{{ block.id }} {
                      font-size: {{ size_text }}rem;
                    }
                    .otsb__root .heading--{{ block.id }}{
                      font-size: {{ heading_size }}rem;
                    }
                  }
                {%- endstyle -%} 
                {%- if block.settings.heading != blank or block.settings.text != blank or block.settings.subheading != blank -%}
                  {%- style -%}
                    #shopify-section-{{ section.id }} .otsb__root .content--{{ block.id }} {
                      --image-treatment-text: {{ block.settings.color_text.red }}, {{ block.settings.color_text.green }}, {{ block.settings.color_text.blue }};
                    }
                    #shopify-section-{{ section.id }} .otsb__root .sub-heading-{{ block.id }},
                    #shopify-section-{{ section.id }} .otsb__root .heading--{{ block.id }} {
                      color: {% if block.settings.color_text.alpha != 0 %}{{ block.settings.color_text }}{% else %}rgb(var(--color-foreground)){% endif %};
                    }
                  {%- endstyle -%}
                  
                  <div class="content--{{ block.id }} relative mx-auto mt-2 mb-2 z-20 lg:max-w-3xl w-full lg:block lg:w-full lg:static lg:translate-x-0 lg:translate-y-0
                    {% if add_class_light %} pt-5 pb-5 pl-5 pr-5{% endif %}
                    {% if add_class_dark %} otsb-dark:pt-5 otsb-dark:pb-5 otsb-dark:pl-5 otsb-dark:pr-5{% else %} otsb-dark:pt-0 otsb-dark:pb-0 otsb-dark:pr-0 otsb-dark:pl-0{% endif %}
                    {%- if block.settings.content_box_type == "round" %} rounded-full{% endif -%}
                    {%- if block.settings.add_frame %} pt-5 pb-5 pl-5 pr-5 after:-z-10 after:top-1 after:left-1 after:right-1 after:bottom-1 after:border after:solid after:absolute after:{{ block.settings.content_box_type }}{% endif %}"
                  >
                    <div class="promotion-box-content--{{ block.settings.content_box_type }}{%- if add_class_light and block.settings.content_box_type == "round" %} pl-8 pb-8 pt-8 pr-8{% else %} pb-1 md:pb-2{% endif %}{%- if add_class_dark and block.settings.content_box_type == "round" %} otsb-dark:pl-8 otsb-dark:pb-8 otsb-dark:pt-8 otsb-dark:pr-8{% else %} otsb-dark:pl-0 otsb-dark:pr-0 otsb-dark:pt-0 otsb-dark:pb-1 md:otsb-dark:pb-2{% endif %} text-{{ section.settings.content_alignment }}"{{ block.shopify_attributes }}>
                      {%- if block.settings.subheading != blank -%}
                        <p class="sub-heading-{{ block.id }} leading-tight empty:otsb-hidden italic mt-1 otsb-p-break-words otsb-image-treatment-text">
                          {{ block.settings.subheading | escape }}
                        </p>
                      {%- endif -%}
                      {%- if block.settings.heading != blank -%}
                        <{{ block.settings.heading_tag }} class="empty:otsb-hidden otsb-h2 block leading-tight mt-1 otsb-image-treatment-text p-break-words heading--{{ block.id }} heading-{{ section.id }}">
                        {% render 'otsb-heading-highlight',
                            headingId: section.id,
                            heading: block.settings.heading,
                            highlight_type: block.settings.highlight_type,
                            color_heading_highlight_light: block.settings.marker_color,
                            color_text: block.settings.text_color
                          %}
                        </{{ block.settings.heading_tag }}>
                      {%- endif -%}
                      {%- if block.settings.text != blank -%} 
                        <p class="content-text-{{ block.id }} otsb-p-break-words empty:otsb-hidden leading-tight otsb-image-treatment-text{% if block.settings.heading != blank %} mt-3 lg:mt-4{% endif %}">
                          {{ block.settings.text | escape }}
                        </p>
                      {%- endif -%}
                    </div>
                  </div>
                {%- endif -%}
              {%- when 'buttons' -%}
                {%- style -%}
                  .button--{{ block.id }}.otsb-button-solid,
                  .button--{{ block.id }}.otsb-button-solid:before { 
                    {%- unless block.settings.color_button.alpha == 0.0 -%}
                      --colors-line-and-border: {{ block.settings.color_button.red }}, {{ block.settings.color_button.green }}, {{ block.settings.color_button.blue }};
                      --colors-button: {{ block.settings.color_button.red }}, {{ block.settings.color_button.green }}, {{ block.settings.color_button.blue }};
                    {%- else -%}
                      --colors-line-and-border: var(--colors-button);
                    {%- endunless -%}
                    {%- unless block.settings.color_button_hover.alpha == 0.0 -%}
                      --colors-button-hover: rgb({{ block.settings.color_button_hover.red }}, {{ block.settings.color_button_hover.green }}, {{ block.settings.color_button_hover.blue }});
                    {%- endunless -%}
                    {%- unless block.settings.color_text_button.alpha == 0.0 -%}
                      --colors-button-text: {{ block.settings.color_text_button.red }}, {{ block.settings.color_text_button.green }}, {{ block.settings.color_text_button.blue }};
                    {%- endunless -%}
                    {%- unless block.settings.color_text_button_hover.alpha == 0.0 -%}
                      --colors-button-text-hover: {{ block.settings.color_text_button_hover.red }}, {{ block.settings.color_text_button_hover.green }}, {{ block.settings.color_text_button_hover.blue }};
                    {%- endunless -%}
                  }
                  .button--{{ block.id }}.otsb-button-outline {
                    {%- if block.settings.secondary_button_text.alpha != 0.0 -%} 
                      --colors-secondary-button: {{ block.settings.secondary_button_text.red }}, {{ block.settings.secondary_button_text.green }}, {{ block.settings.secondary_button_text.blue }}; 
                      --colors-line-secondary-button: {{ block.settings.secondary_button_text.red }}, {{ block.settings.secondary_button_text.green }}, {{ block.settings.secondary_button_text.blue }}; 
                      --background-secondary-button: transparent;
                    {% endif %}
                    {%- if block.settings.color_button_secondary.alpha != 0.0 -%} 
                      --background-secondary-button: {{ block.settings.color_button_secondary.red }}, {{ block.settings.color_button_secondary.green }}, {{ block.settings.color_button_secondary.blue }}; 
                      --colors-line-secondary-button: {{ block.settings.color_button_secondary.red }}, {{ block.settings.color_button_secondary.green }}, {{ block.settings.color_button_secondary.blue }}; 
                    {% endif %}
                  }
                {%- endstyle -%}
                {%- if block.settings.button_label != blank or block.settings.button_label_2 != blank -%}
                  {% comment %} Button design {% endcomment %}
                    {%- liquid
                      case block.settings.button_type
                        when 'rounded'
                          assign borderRadius = '100px'
                        when 'rounded_corners'
                          assign borderRadius = '6px'
                        when 'mixed'
                          assign borderRadius = '6px 0 6px 0'
                        else
                          assign borderRadius = '0'
                      endcase
                    %}
                    {% style %}
                      .x-button-{{ block.id }} .button--{{ block.id }} {
                        --border-radius: {{ borderRadius }};
                        {% if block.settings.button_animation == 'slide' %}
                          --button-width: 102%;
                          --button-height: 500%;
                          --button-transform: rotate3d(0,0,1,-10deg) translate3d(-130%,-10em,0);
                          --button-transform-origin: 100% 0%;
                        {% elsif block.settings.button_animation == 'fill_up' %}
                          --button-width: 120%;
                          --button-height: 100%;
                          --button-transform: rotate3d(0,0,1,10deg) translate3d(-1.2em,110%,0);
                          --button-transform-origin: 0% 100%;
                        {% endif %}
                      }

                      {% if block.settings.button_color_mobile == "hover" %}
                        .otsb__root .x-button-{{ block.id }} [role="button"], .otsb__root .x-button-{{ block.id }} [type="button"], .otsb__root .x-button-{{ block.id }} .otsb-button {
                          color: rgb(var(--colors-button-text-hover));
                        }
                        .x-button-{{ block.id }} button.otsb-button-solid, .x-button-{{ block.id }} .otsb-button.otsb-button-solid {
                          border: none;
                          background-color: var(--colors-button-hover);
                        }
                        .x-button-{{ block.id }} button.otsb-button-disable-effect, .x-button-{{ block.id }} .otsb-button.otsb-button-disable-effect {
                          color: rgba(var(--colors-button-text-hover));
                          background-color: var(--colors-button-hover);
                        }
                        .otsb__root .x-button-{{ block.id }} .otsb-button-action {
                          border: none;
                          color: rgba(var(--colors-button-text-hover));
                          background-color: var(--colors-button-hover);
                        }
                      {% else %}
                        .otsb__root .x-button-{{ block.id }} [role="button"], .otsb__root .x-button-{{ block.id }} [type="button"], .otsb__root .x-button-{{ block.id }} .otsb-button {
                          color: rgb(var(--colors-button-text));
                        }
                        .x-button-{{ block.id }} button.otsb-button-solid, .x-button-{{ block.id }} .otsb-button.otsb-button-solid {
                          border: none;
                          background-color: rgba(var(--colors-button));
                        }
                        .x-button-{{ block.id }} button.otsb-button-disable-effect, .x-button-{{ block.id }} .otsb-button.otsb-button-disable-effect {
                          color: rgb(var(--colors-button-text));
                          background-color: rgba(var(--colors-button));
                        }
                        .otsb__root .x-button-{{ block.id }} .otsb-button-action {
                          border: none;
                          color: rgb(var(--colors-button-text));
                          background-color: rgba(var(--colors-button));
                        }
                      {% endif %}
                        .otsb__root .x-button-{{ block.id }} [role="button"], .otsb__root .x-button-{{ block.id }} [type="button"], .otsb__root .x-button-{{ block.id }} .otsb-button {
                          direction: ltr;
                        }

                        {% if block.settings.button_animation == 'sliced' %}
                          .x-button-{{ block.id }} .otsb-button.otsb-button-outline:not(.not-icon), .x-button-{{ block.id }} .otsb-button.otsb-button-solid:not(.not-icon) {
                            display: inline-flex;
                            align-items: center;
                            justify-content: center;
                            padding-left: 1.5rem;
                            padding-right: 1.5rem;
                          }
                          .x-button-{{ block.id }} .otsb-button-solid .otsb-button-icon, .x-button-{{ block.id }} .otsb-button-solid .otsb-button-text {
                            transition-timing-function: cubic-bezier(0,.71,.4,1);
                          }
                          .x-button-{{ block.id }} .otsb-button-solid .otsb-button-icon {
                            transition: opacity .25s,transform .5s;
                          }
                          .x-button-{{ block.id }} .otsb-button-solid .otsb-button-text {
                            transition: transform .5s;
                            transform: translateX(0.625rem);
                          }
                          .x-button-{{ block.id }} .otsb-button-solid:hover .otsb-button-text {
                            opacity: 1;
                            transform: translateX(0px);
                          }
                          .x-button-{{ block.id }} .otsb-button-solid:hover .otsb-button-icon {
                            opacity: 1;
                            transform: translateX(0.3125rem);
                          }
                        {% endif %}
                        {% if block.settings.button_animation == 'underline' %}
                          .x-button-{{ block.id }} .otsb-button-solid .otsb-button-text {
                            position: relative;
                            display: block;
                          }
                          .x-button-{{ block.id }} .otsb-button-solid .otsb-button-text::after {
                            content: "";
                            pointer-events: none;
                            bottom: 1px;
                            left: 50%;
                            position: absolute;
                            width: 0%;
                            height: 1px;
                            background-color: rgba(var(--colors-button-text));
                            transition-timing-function: cubic-bezier(0.25, 0.8, 0.25, 1);
                            transition-duration: 400ms;
                            transition-property: width, left;
                          }
                          .x-button-{{ block.id }} .otsb-button-solid:hover .otsb-button-text::after {
                            {% if block.settings.button_color_mobile == "hover" %}
                              background-color: rgba(var(--colors-button-text-hover));
                            {% else %}
                              background-color: rgba(var(--colors-button-text));
                            {% endif %}
                              width: 100%;
                              left: 0%;
                          }
                        {% endif %}

                        @media (min-width: 1024px){
                          .otsb__root .x-button-{{ block.id }} [role="button"], .otsb__root .x-button-{{ block.id }} [type="button"], .otsb__root .x-button-{{ block.id }} .otsb-button { 
                            color: rgba(var(--colors-button-text));
                          }
                          .x-button-{{ block.id }} button.otsb-button-solid, .x-button-{{ block.id }} .otsb-button.otsb-button-solid {
                            border: none;
                            box-shadow: none;
                            color: rgb(var(--colors-button-text));
                            background-color: rgba(var(--colors-button));
                            overflow: hidden;
                            background-origin: border-box;
                          }
                          .x-button-{{ block.id }} button.otsb-button-solid:hover, .x-button-{{ block.id }} .otsb-button.otsb-button-solid:hover {
                            {% if block.settings.button_animation == 'sliced' or block.settings.button_animation == 'underline' %}
                              transition-duration: 0.2s;
                            {% else %}
                              transition-delay: 0.5s;
                            {% endif %}
                              transition-property: background-color;
                              background-color: var(--colors-button-hover);
                              color: rgba(var(--colors-button-text-hover));
                              background-origin: border-box;
                          }
                          .otsb__root .x-button-{{ block.id }} .otsb-button-action {
                            border: none;
                            color: rgba(var(--colors-button-text-hover));
                            background-color: var(--colors-button-hover);
                          }
                          .x-button-{{ block.id }} button.otsb-button-disable-effect, .x-button-{{ block.id }} .otsb-button.otsb-button-disable-effect {
                            color: rgb(var(--colors-button-text));
                            background-color: rgba(var(--colors-button));
                          }
                          .x-button-{{ block.id }} button.otsb-button-disable-effect:hover, .x-button-{{ block.id }} .otsb-button.otsb-button-disable-effect:hover {
                            color: rgba(var(--colors-button-text-hover));
                            background-color: var(--colors-button-hover);
                          }
                          {% if block.settings.button_animation == 'slide' or block.settings.button_animation == 'fill_up' %}
                            .x-button-{{ block.id }} button.otsb-button-solid:before, .x-button-{{ block.id }} .otsb-button.otsb-button-solid:before {
                              content: "";
                              z-index: -1;
                              position: absolute;
                              top: 0;
                              right: 0;
                              bottom: 0;
                              left: 0;
                              width: var(--button-width);
                              height: var(--button-height);
                              background-color: var(--colors-button-hover);
                              backface-visibility: hidden;
                              will-change: transform;
                              transform: var(--button-transform);
                              transform-origin: var(--button-transform-origin);
                              transition: transform 0.5s ease;
                            }
                            .x-button-{{ block.id }} button.otsb-button-solid:hover:before, .x-button-{{ block.id }} .otsb-button.otsb-button-solid:hover:before {
                              transform: rotate3d(0,0,1,0) translateZ(0);
                            }
                          {% endif %}
                          {% if block.settings.button_animation == 'underline' %}
                            .x-button-{{ block.id }} .otsb-button-solid:hover .otsb-button-text::after {
                              background-color: rgba(var(--colors-button-text-hover));
                            }
                          {% endif %}
                        }
                    {% endstyle %}
                  {% comment %} End button design {% endcomment %}
                <div class="selection:bg-[rgba(var(--image-treatment-text),0.2)] z-20 mt-3 lg:mt-9 mb-2 mx-auto otsb-ltr">
                  <div class="x-button-{{ block.id }} flex lg:justify-{{ section.settings.content_alignment }} items-{{ section.settings.content_alignment }} lg:flex-row flex-col gap-x-2" {{ block.shopify_attributes }}>
                    {%- if block.settings.button_label != blank -%}
                      {%- if block.settings.button_link != blank -%}
                        <a 
                          href="{{ block.settings.button_link }}"
                          class="disable-effect" 
                          @click='$store.xCustomerEvent.fire("promotion_selected", $el, data)'
                          aria-label="{{ block.settings.button_label | escape }}"
                          {% if block.settings.open_new_window_button %}target="_blank"{% endif %}
                        >
                      {%- endif -%}
                        <div class="otsb-button otsb-p-break-words button--{{ block.id }}{% if block.settings.primary_button_1 %} otsb-button-solid{% else %} otsb-button-outline{% endif %} empty:otsb-hidden leading-normal text-center mt-1 mb-1 lg:mt-0 lg:mb-0 lg:ml-0 lg:mr-0 h-full pl-7 pr-7 lg:pl-9 lg:pr-9 pt-2.5 pb-2.5 md:pt-3 md:pb-3{% unless block.settings.button_link != blank %} opacity-70 hover:cursor-not-allowed{% endunless %}">
                          {% render 'otsb-button-label', button_animation: block.settings.button_animation, custom_icon_button: block.settings.custom_icon_button, button_label: block.settings.button_label, show_button_primary: block.settings.primary_button_1 %}
                        </div>
                      {%- if block.settings.button_link != blank -%}
                        </a>
                      {%- endif -%}
                    {%- endif -%}
                    {%- if block.settings.button_label_2 != blank -%}
                      {%- if block.settings.button_link_2 != blank -%}
                        <a 
                          href="{{ block.settings.button_link_2 }}" 
                          class="disable-effect" 
                          @click='$store.xCustomerEvent.fire("promotion_selected", $el, data)' 
                          aria-label="{{ block.settings.button_label_2 | escape }}"
                          {% if block.settings.open_new_window_button_2 %}target="_blank"{% endif %}
                        >
                      {%- endif -%}
                        <div class="otsb-button otsb-p-break-words button--{{ block.id }}{% if block.settings.primary_button_2 %} otsb-button-solid{% else %} otsb-button-outline{% endif %} empty:otsb-hidden leading-normal text-center mt-1 mb-1 lg:mt-0 lg:mb-0 lg:ml-0 lg:mr-0 h-full pl-7 pr-7 lg:pl-9 lg:pr-9 pt-2.5 pb-2.5 md:pt-3 md:pb-3{% unless block.settings.button_link_2 != blank %} opacity-70 hover:cursor-not-allowed{% endunless %}">
                          {% render 'otsb-button-label', button_animation: block.settings.button_animation, custom_icon_button: block.settings.custom_icon_button, button_label: block.settings.button_label_2, show_button_primary: block.settings.primary_button_2 %}
                        </div>
                      {%- if block.settings.button_link_2 != blank -%}
                        </a>
                      {%- endif -%} 
                    {%- endif -%}
                  </div>
                </div>
                {%- endif -%}
              {%- when 'countdown_timer' -%}
                {% style %}
                  .otsb__root .timer--{{ block.id }} {
                    font-size: {{ 100 | times: 0.013 }}rem;
                    {% unless block.settings.timer_color_countdown.alpha == 0.0 %}
                      color: {{ block.settings.timer_color_countdown }} !important;
                    {% else %}
                      color: var(--colors-button-hover) !important;
                    {% endunless %}
                  }
                  .otsb__root .x-countdown-{{ block.id }} {
                    margin-left: -{{ block.settings.spacing_col_mobile | times: 0.5 }}px;
                    margin-right: -{{ block.settings.spacing_col_mobile | times: 0.5 }}px;
                  }
                  .otsb__root .countdown--{{ block.id }} { 
                    margin-left: {{ block.settings.spacing_col_mobile | times: 0.5 }}px;
                    margin-right: {{ block.settings.spacing_col_mobile | times: 0.5 }}px;
                  }
                  @media screen and (min-width: 768px) {
                    .otsb__root .timer--{{ block.id }} {
                      font-size: {{ 100 | times: 0.019 }}rem;
                    }
                    .otsb__root .x-countdown-{{ block.id }} {
                      margin-left: -{{ block.settings.spacing_col | times: 0.5 }}px;
                      margin-right: -{{ block.settings.spacing_col | times: 0.5 }}px;
                    }
                    .otsb__root .countdown--{{ block.id }} {
                      margin-left: {{ block.settings.spacing_col | times: 0.5 }}px;
                      margin-right: {{ block.settings.spacing_col | times: 0.5 }}px;
                    }
                  }
                  .otsb__root .countdown--{{ block.id }} {
                    {% if block.settings.bg_color_countdown.alpha != 0.0 %}
                      background-color: rgba({{ block.settings.bg_color_countdown.red }}, {{ block.settings.bg_color_countdown.green }}, {{ block.settings.bg_color_countdown.blue }}, {{ block.settings.overlay_opacity }}%);
                    {% endif %}
                    {% unless block.settings.text_color_countdown.alpha == 0.0 %}
                      color: {{ block.settings.text_color_countdown }};
                      border-color: rgba({{ block.settings.text_color_countdown.red }}, {{ block.settings.text_color_countdown.green }}, {{ block.settings.text_color_countdown.blue }});
                    {% else %}
                      color: rgba(var(--image-treatment-text));
                      border-color: rgba(var(--image-treatment-text));
                    {% endunless %}
                  }
                {% endstyle %}
                <div 
                  id="x-countdown-banner"
                  class="block-promotion-banner pb-1.5 mt-4 mb-2.5 md:mb-5"
                  x-data="{ loading: true, textLoad: '-', days: 0, hours: 0, minutes: 0, seconds: 0 }"
                  x-intersect.once.margin.200px='$nextTick(() => {
                    $store.xHelper.countdown({
                      "id": "{{ block.id }}",
                      "timezone": {{ block.settings.timezone | default: 0 }},
                      "end_year": {{ block.settings.end_year | default: 0 }},
                      "end_month": {{ block.settings.end_month | default: 0 }},
                      "end_day": {{ block.settings.end_day | default: 0 }},
                      "end_hour": {{ block.settings.end_hour | default: 0 }},
                      "end_minute": {{ block.settings.end_minute | default: 0 }},
                      "justify": "{{ section.settings.content_alignment }}"
                    }, (canShow, s, m, h, d) => {
                      days = d;
                      hours = h;
                      minutes = m;
                      seconds = s;
                      loading = false;
                    })
                  })'
                  {{ block.shopify_attributes }}
                >
                  <div class="x-countdown-promo-banner x-countdown-{{ block.id }} justify-{{ section.settings.content_alignment }} flex otsb-rtl:flex-row-reverse">
                    <div class="countdown--{{ block.id }} relative w-full max-w-[86px] min-h-[60px] md:max-w-[112px] md:min-h-[80px] bg-color--{{ block.id }}{% if block.settings.border_style != 'none' %} border border-dashed border-color--{{ block.id }}{% endif %}{% if block.settings.timer_style == 'square' %}{% if block.settings.edges_type == 'rounded_corners' %} rounded{% endif %}{% else %} flex-1 rounded-full before:block before:h-0 before:pb-[100%]{% endif %}">
                      <div class="flex flex-col items-center justify-center absolute top-0 left-0 h-full w-full">
                        <span x-html="loading?textLoad:days" class="otsb-h2 timer--{{ block.id }}"></span>
                        <span>Days</span>
                      </div>
                    </div>
                    <div class="countdown--{{ block.id }} relative w-full max-w-[86px] min-h-[60px] md:max-w-[112px] md:min-h-[80px] bg-color--{{ block.id }}{% if block.settings.border_style != 'none' %} border border-dashed border-color--{{ block.id }}{% endif %}{% if block.settings.timer_style == 'square' %}{% if block.settings.edges_type == 'rounded_corners' %} rounded{% endif %}{% else %} flex-1 rounded-full before:block before:h-0 before:pb-[100%]{% endif %}">
                      <div class="flex flex-col items-center justify-center absolute top-0 left-0 h-full w-full">
                        <span x-html="loading?textLoad:hours" class="otsb-h2 timer--{{ block.id }}"></span>
                        <span>Hours</span>
                      </div>
                    </div>
                    <div class="countdown--{{ block.id }} relative w-full max-w-[86px] min-h-[60px] md:max-w-[112px] md:min-h-[80px] bg-color--{{ block.id }}{% if block.settings.border_style != 'none' %} border border-dashed border-color--{{ block.id }}{% endif %}{% if block.settings.timer_style == 'square' %}{% if block.settings.edges_type == 'rounded_corners' %} rounded{% endif %}{% else %} flex-1 rounded-full before:block before:h-0 before:pb-[100%]{% endif %}">
                      <div class="flex flex-col items-center justify-center absolute top-0 left-0 h-full w-full">
                        <span x-html="loading?textLoad:minutes" class="otsb-h2 timer--{{ block.id }}"></span>
                        <span>Minutes</span>
                      </div>
                    </div>
                    <div class="countdown--{{ block.id }} relative w-full max-w-[86px] min-h-[60px] md:max-w-[112px] md:min-h-[80px] bg-color--{{ block.id }}{% if block.settings.border_style != 'none' %} border border-dashed border-color--{{ block.id }}{% endif %}{% if block.settings.timer_style == 'square' %}{% if block.settings.edges_type == 'rounded_corners' %} rounded{% endif %}{% else %} flex-1 rounded-full before:block before:h-0 before:pb-[100%]{% endif %}">
                      <div class="flex flex-col items-center justify-center absolute top-0 left-0 h-full w-full">
                        <span x-html="loading?textLoad:seconds" class="otsb-h2 timer--{{ block.id }}"></span>
                        <span>Seconds</span>
                      </div>
                    </div>
                  </div>
                </div>
              {%- when 'menu' -%}
                {% liquid  
                  assign menu_text_size =  block.settings.menu_text_size | times: 100 | times: 0.000225
                %}
                <style>
                  .otsb__root .menu--{{ block.id }} {
                    font-size: {{ menu_text_size | times: 0.3 }}rem;
                  }
                  .otsb__root .menu--{{ block.id }} .link-active {
                    color: rgba({{ block.settings.text_color_menu_hover.red }}, {{ block.settings.text_color_menu_hover.green }}, {{ block.settings.text_color_menu_hover.blue }});
                    border-bottom: 1px solid rgba({{ block.settings.text_color_menu_hover.red }}, {{ block.settings.text_color_menu_hover.green }}, {{ block.settings.text_color_menu_hover.blue }});
                  }
                  @media screen and (min-width: 768px) { 
                    .otsb__root .menu--{{ block.id }} {
                      font-size: {{ menu_text_size | times: 0.58 }}rem;
                    }
                  }

                  .otsb__root a.otsb-nav-link:not(.otsb-effect-inline) {
                    color: rgba({{ block.settings.text_color_menu.red }}, {{ block.settings.text_color_menu.green }}, {{ block.settings.text_color_menu.blue }});
                  }
                  .otsb__root a.otsb-nav-link:not(.otsb-effect-inline):hover {
                    transition: transform 0.15s linear;
                    color: rgba({{ block.settings.text_color_menu_hover.red }}, {{ block.settings.text_color_menu_hover.green }}, {{ block.settings.text_color_menu_hover.blue }});
                    --colors-text-link: {{ block.settings.text_color_menu_hover.red }}, {{ block.settings.text_color_menu_hover.green }}, {{ block.settings.text_color_menu_hover.blue }};
                  }
                  .otsb__root a.otsb-nav-link:not(.otsb-effect-inline):after {
                    border-bottom: 1px solid rgba({{ block.settings.text_color_menu_hover.red }}, {{ block.settings.text_color_menu_hover.green }}, {{ block.settings.text_color_menu_hover.blue }});
                  }
                </style>
                <nav class="menu--{{ block.id }} otsb-image-treatment-text justify-{{ section.settings.content_alignment }} {% if block.settings.display_mobile %} flex {% else %} otsb-hidden lg:flex {% endif %}">
                  <ul class="list-menu inline-flex flex-wrap items-center gap-4 lg:gap-12">
                    {%- for link in block.settings.menu.links -%}
                        <li class="py-1">
                          <a
                            href="{{ link.url }}"
                            class="relative otsb-p-break-words flex items-center hover-text-link font-bold is-focus:link-active{% if link.current or link.child_active %} link-active {% else %} otsb-nav-link {% endif %}"
                          >
                            <span>{{ link.title | escape }}</span>
                          </a>
                        </li>
                    {%- endfor -%}
                  </ul>
                </nav>
              {%- endcase -%}
            {%- endfor -%}
        </div>
      </div>
    </div>
  </div>
</div>
</div>
