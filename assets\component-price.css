.price {
  font-size: clamp(1.4rem, 1.32rem + 0.4vw, 1.8rem);
/*   letter-spacing: 0.1rem; */
  line-height: normal;
  color: rgb(var(--color-foreground));
}

.price > * {
  display: inline-block;
  vertical-align: top;
}

.price.price--unavailable {
  visibility: hidden;
}

.price--end {
  text-align: right;
}

.price .price-item {
  margin: 0 1rem 0 0;
}

.price__regular .price-item--regular {
  margin-right: 0;
}

.price:not(.price--show-badge) .price-item--last:last-of-type {
  margin: 0;
}

@media screen and (min-width: 750px) {
/*   .price {
    margin-bottom: 0;
  } */
}

.price--large {
  font-size: 1.8rem;
  line-height: calc(1 + 0.5 / var(--font-body-scale));
  letter-spacing: 0.13rem;
   font-weight: 700;
}

@media screen and (min-width: 750px) {
  .price--large {
    font-size: 1.6rem;
  }
}

.price--sold-out .price__availability,
.price__regular {
  display: block;
}

.price__sale,
.price__availability,
.price .price__badge-sale,
.price .price__badge-sold-out,
.price--on-sale .price__regular,
.price--on-sale .price__availability {
  display: none;
}

.price--sold-out .price__badge-sold-out,
.price--on-sale .price__badge-sale {
  display: inline-block;
}

.price--on-sale .price__sale {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: var(--card-text-alignment);
}

.price--center {
  display: initial;
  justify-content: center;
}

.price--on-sale .price-item--regular {
  text-decoration: line-through;
  color: var(--gradient-base-accent-2);
  font-size: clamp(1.4rem, 1.32rem + 0.4vw, 1.8rem); 
  transition:all 0.3s linear;
}

.unit-price {
  display: block;
  font-size: 1.1rem;
  letter-spacing: 0.04rem;
  line-height: calc(1 + 0.2 / var(--font-body-scale));
  margin-top: 0.2rem;
  text-transform: uppercase;
  color: rgba(var(--color-foreground), 0.7);
}

.card-information .price {
    font-family: var(--font-additional-family);
    font-size: 1.8rem;
    font-weight: 300;
}
.quick-add-modal {
    display: none;
}
price-item.price-item--sale.price-item--last{
  transition:all 0.3s linear;
}
.widget.product-sidebar-type-collection .product-list-style  span.price-item.price-item--sale.price-item--last {
    display: block;
    font-size: clamp(1.4rem, 1.32rem + .4vw, 1.8rem);
}
span.price-item.price-item--sale.price-item--last{display:block;}