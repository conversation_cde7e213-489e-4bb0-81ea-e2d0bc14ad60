{{ 'component-image-banner.css' | asset_url | stylesheet_tag }}
{{ 'component-slider.css' | asset_url | stylesheet_tag }}
{{ 'component-home-slider-with-hot-spots.css' | asset_url | stylesheet_tag }}
{{ 'magnific-popup.css' | asset_url | stylesheet_tag }}
<script src="{{ 'magnific-popup.js' | asset_url }}" defer="defer"></script>
{%- style -%}
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
      padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
    }

    @media screen and (min-width: 750px) {
      .section-{{ section.id }}-padding {
        padding-top: {{ section.settings.padding_top }}px;
        padding-bottom: {{ section.settings.padding_bottom }}px;
      }
    }
{%- if section.blocks.first.settings.image != blank -%}
  
  @media screen and (max-width: 749px) {
    #Slider-{{ section.id }}::before,
    #Slider-{{ section.id }} .media::before,
    #Slider-{{ section.id }}:not(.banner--mobile-bottom) .banner__content::before {
      padding-bottom: {{ 1 | divided_by: section.blocks.first.settings.image.aspect_ratio | times: 100 }}%;
      content: '';
      display: block;
    }
  }

  @media screen and (min-width: 750px) {
    #Slider-{{ section.id }}::before,
    #Slider-{{ section.id }} .media::before {
      padding-bottom: {{ 1 | divided_by: section.blocks.first.settings.image.aspect_ratio | times: 100 }}%;
      content: '';
      display: block;
    }
  }
     {%- for block in section.blocks -%}      
        #Slide-{{ section.id }}-{{ forloop.index }} .banner__media::after {
          opacity: {{ block.settings.image_overlay_opacity | divided_by: 100.0 }};
        }      
    {%  endfor %}
{%- endif -%}  


      #shopify-section-{{section.id}}.home-slideshow-with-hotspot .slide-banner.swiper-wrapper  { height: {{section.settings.slider_image_height}}px;}
      #shopify-section-{{section.id}}.home-slideshow-with-hotspot  .banner--mobile-bottom .slideshow__text-wrapper.banner__content{position:absolute;}
  
   @media screen and (max-width: 1540px) {   
    #shopify-section-{{section.id}}.home-slideshow-with-hotspot .slide-banner { height:{{section.settings.slider_image_height_laptop}}px;}
    }
   @media screen and (max-width: 990px) { 
   #shopify-section-{{section.id}}.home-slideshow-with-hotspot .slide-banner { height:{{section.settings.slider_image_height_tab}}px;}
    }
    @media screen and (max-width: 576px) {
    #shopify-section-{{section.id}}.home-slideshow-with-hotspot .slide-banner { height:{{section.settings.slider_image_height_mobile}}px;}
    #shopify-section-{{section.id}}.home-slideshow-with-hotspot .slide-banner.banner--mobile-bottom{height:100%;}
    #shopify-section-{{section.id}}.home-slideshow-with-hotspot .banner--mobile-bottom .slideshow__text-wrapper.banner__content {position:relative;} 
    }
     
   


   {% for block in section.blocks %}
  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-item{{block.id}}_a.block-type-Slider.open,
  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-item{{block.id}}_b.block-type-Slider.open{z-index:999;}
   #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-item{{block.id}}_a.block-type-Slider,
  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-item{{block.id}}_b.block-type-Slider{z-index:0;}
      #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-item{{block.id}}_a,  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .open-popup-link{{block.id}}_a  {
    top: {{ block.settings.spot_1_top_position }}%;
    left: {{ block.settings.spot_1_left_position }}%;
    transform: translate(-{{ block.settings.spot_1_top_position }}%, -{{ block.settings.spot_1_left_position }}%);    
   }

    #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-item{{block.id}}_b,  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .open-popup-link{{block.id}}_b  {
     top: {{ block.settings.spot_2_top_position }}%;
    left: {{ block.settings.spot_2_left_position }}%;
    transform: translate(-{{ block.settings.spot_2_top_position }}%, -{{ block.settings.spot_2_left_position }}%);     
   }

    #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-item{{block.id}}_c,  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .open-popup-link{{block.id}}_c {    
     top: {{ block.settings.spot_3_top_position }}%;
    left: {{ block.settings.spot_3_left_position }}%;
      transform: translate(-{{ block.settings.spot_3_top_position }}%, -{{ block.settings.spot_3_left_position }}%); 
   }
    #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .open-popup-link{{block.id}}_a,
    #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .open-popup-link{{block.id}}_b,
    #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .open-popup-link{{block.id}}_c{ cursor:pointer;}
 {% endfor %} 

  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-content a[title] {display:inline-block;}
 .dt-sc-hotspot-popup .dt-sc-hotspot-content > *:not(:last-child){margin-bottom:15px}
  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-popup .dt-sc-hotspot-content-title{margin:0;}
  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-item{position:absolute;z-index:3; height: 36px;}
  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-icon span {width: 36px;height: 36px;line-height: 36px;font-size: 16px;}
  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-popup{background:var(--gradient-background);opacity:0;transition:var(--DTBaseTransition);position:absolute;visibility: hidden; padding:3rem}
  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-item.open .dt-sc-hotspot-popup{opacity:1;visibility: visible;}
  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-item .dt-sc-hotspot-popup.on-right { left: 38px; }
  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-item .dt-sc-hotspot-popup.on-left { right: 38px; }
  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-item .dt-sc-hotspot-popup.on-bottom { top: 38px; }
  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-item .dt-sc-hotspot-popup.on-top { bottom: 38px; }
  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-item .dt-sc-hotspot-popup.dt-sc-popup-open.on-right { left: 48px; }
  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-item .dt-sc-hotspot-popup.dt-sc-popup-open.on-left { right: 48px; }
  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-item .dt-sc-hotspot-popup.dt-sc-popup-open.on-bottom { top: 48px; }
  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-item .dt-sc-hotspot-popup.dt-sc-popup-open.on-top { bottom: 48px; }
  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-item .dt-sc-hotspot-popup:before { content: ""; position: absolute; width: 0; height: 0; border-style: solid; }
  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-item .dt-sc-hotspot-popup.on-right { left: 48px; top: 50%; transform: translateY(-50%); }
  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-item .dt-sc-hotspot-popup.on-right:before { left: -10px; border-width: 10px 10px 10px 0; top: 50%; transform: translateY(-50%); }
  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-item .dt-sc-hotspot-popup.on-right.dt-sc-popup-open { left: 58px; }
  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-item .dt-sc-hotspot-popup.on-left { right: 48px; top: 50%; transform: translateY(-50%); }
  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-item .dt-sc-hotspot-popup.on-left:before { right: -10px; border-width: 10px 0 10px 10px; top: 50%; transform: translateY(-50%); }
  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-item .dt-sc-hotspot-popup.on-left.dt-sc-popup-open { right: 58px; }
  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-item .dt-sc-hotspot-popup.on-bottom { top: 48px; left: 50%; transform: translateX(-50%); }
  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-item .dt-sc-hotspot-popup.on-bottom:before { top: -10px; border-width: 0 10px 10px 10px; left: 50%; transform: translateX(-50%); }
  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-item .dt-sc-hotspot-popup.on-bottom.dt-sc-popup-open { top: 58px; }
  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-item .dt-sc-hotspot-popup.on-top { bottom: 48px; left: 50%; transform: translateX(-50%); }
  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-item .dt-sc-hotspot-popup.on-top:before { bottom: -10px; border-width: 10px 10px 0 10px; left: 50%; transform: translateX(-50%); }
  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-item .dt-sc-hotspot-popup.on-top.dt-sc-popup-open { bottom: 58px; }

  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-popup{width: {{ section.settings.hotspot_content_size }}px;}
  @media (max-width: 1540px) {
  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-popup{ width: {{ section.settings.hotspot_content_size_laptop }}px; padding:1.5rem;  }
  }
  @media (max-width: 1199px) {
  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-popup{ width: {{ section.settings.hotspot_content_size_tablet }}px; }
  }
 @media (max-width: 576px) {
  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-popup{ width: {{ section.settings.hotspot_content_size_mobile }}px; }
  }    
  
    #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-popup{
      background:var(--gradient-background);
      border-radius: var(--buttons-radius);
         
          }
     #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-item .dt-sc-hotspot-popup.on-right:before {
        color: var(--gradient-base-background-1);
        border-color: transparent currentcolor transparent transparent;
        }
    #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-item .dt-sc-hotspot-popup.on-left:before {
      color: var(--DTTertiaryColor);
        color: {{ section.settings.hotspot_content_bg_color }};
        border-color: transparent transparent transparent currentcolor; }
    #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-item .dt-sc-hotspot-popup.on-bottom:before {
      color: var(--DTTertiaryColor);
        color: {{ section.settings.hotspot_content_bg_color }};
        border-color: transparent transparent currentcolor transparent;
        }
    #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-item .dt-sc-hotspot-popup.on-top:before {
      color: var(--DTTertiaryColor);
        color: {{ section.settings.hotspot_content_bg_color }};
        border-color: currentcolor transparent transparent transparent; }
   #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-item .dt-sc-hotspot-content {
      color: var(--DTPrimaryColor);
        color: {{ section.settings.hotspot_content_description_color }}; }
   #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-item .dt-sc-hotspot-content .dt-sc-hotspot-content-title a {
      color: var(--DTLinkColor);
        color: {{ section.settings.hotspot_content_title_color }}; }
   #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-item .dt-sc-hotspot-content .dt-sc-hotspot-content-title a:hover {
      color: var(--DTLinkHoverColor);
        color: {{ section.settings.hotspot_content_title_hover_color }}; }
  
    #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-item .dt-sc-hotspot-content a.dt-sc-btn:empty { display: none; }

   #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-item  .dt-sc-hotspot-marker  span:after { display: block; position: absolute; top: 50%; left: 50%; border-radius: 50%; opacity: 0;transform-origin: 50% 50%;
      -webkit-animation: pulsate-animation 3s ease-out infinite; animation: pulsate-animation 3s ease-out infinite; content: ""; transition: var(--DTBaseTransition);
        }
  #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-marker span{ width: {{ section.settings.hotspot_size }}px;
      height: {{ section.settings.hotspot_size }}px; line-height: {{ section.settings.hotspot_size }}px; font-size: {{ section.settings.hotspot_icon_text_size }}px;transition: var(--DTBaseTransition);border-radius:50% }
  
  /* #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-marker:hover span {
      background: var(--DT_Button_BG_Hover_Color);
        color: var(--DT_Button_Text_Hover_Color);
          background: {{ section.settings.dots_hoverbg_color_1 }};
          color: {{ section.settings.dots_hovercolor_1 }};
          } */
    #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-marker  span{
          background: var(--gradient-base-accent-1);
          color: var(--gradient-base-background-1);
          display: flex;
          justify-content: center;
          align-items:center;
          }
    #shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-item .dt-sc-hotspot-marker span:after { width: {{ section.settings.hotspot_size | plus: 6 }}px;
      height: {{ section.settings.hotspot_size | plus: 6 }}px;
      border: {{ section.settings.hotspot_outer_size }}px solid;
      border: {{ section.settings.hotspot_outer_size }}px solid;
      color: var(--gradient-base-accent-1);
       margin: -{{ section.settings.hotspot_size | plus: 10 | divided_by: 2 }}px auto auto -{{ section.settings.hotspot_size | plus: 10 | divided_by: 2 }}px;
        }
   @keyframes pulsate-animation {
      0% { transform: scale(1); opacity: 0.8; }
      45% { transform: scale(1.75); opacity: 0; border-width: {{ section.settings.hotspot_outer_size | times: 5 }}; }
    }
  .slideshow.home-slideshow-with-hotspot .slideshow__text.banner__box > *:not(:last-child) {
    margin-bottom: 2rem;
}


  .dt-sc-hotspot-wrapper {
    position: absolute;
    z-index: 9;
    height: 100%;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    /* pointer-events: none; */
}
#shopify-section-{{section.id}}.home-slideshow-with-hotspot .dt-sc-hotspot-wrapper .dt-sc-hotspot-popup .dt-sc-hotspot-marker{position:absolute;}
  
  {%-  endstyle -%}

<slideshow-component class="{% if section.settings.page_full_width %} page-full-width {% else %} page-width {% endif %} {% if section.settings.page_full_width_spacing %} page-full-width_spacing {% endif %} {% if section.settings.show_text_below %} mobile-text-below{% endif %} section-{{ section.id }}-padding">
   <div class="row">
  <div data-slider-options='{"loop": "true","effect": "{{ section.settings.slider_effect }}", "auto_play": "{{ section.settings.auto_play }}"}'>
  <div class="swiper" data-swiper-slider>
  <div class="swiper-wrapper slide-banner  banner--{{ section.settings.slide_height }} {% if section.settings.show_text_below %} banner--mobile-bottom{% endif %}{% if section.blocks.first.settings.image == blank %} slideshow--placeholder{% endif %}" id="Slider-{{ section.id }}">
    {%- for block in section.blocks -%}      
      <div class="swiper-slide"
        id="Slide-{{ section.id }}-{{ forloop.index }}"
        {{ block.shopify_attributes }}
        role="group"
        aria-roledescription="{{ 'sections.slideshow.slide' | t }}"
        aria-label="{{ forloop.index }} {{ 'general.slider.of' | t }} {{ forloop.length }}"
        tabindex="-1"
      >
        <div class="slideshow__media  {% if block.settings.image == blank %} placeholder{% endif %}">
          {%- if block.settings.image -%}
            <img
              srcset="{%- if block.settings.image.width >= 375 -%}{{ block.settings.image | image_url: width: 375 }} 375w,{%- endif -%}
              {%- if block.settings.image.width >= 550 -%}{{ block.settings.image | image_url: width: 550 }} 550w,{%- endif -%}
              {%- if block.settings.image.width >= 750 -%}{{ block.settings.image | image_url: width: 750 }} 750w,{%- endif -%}
              {%- if block.settings.image.width >= 1100 -%}{{ block.settings.image | image_url: width: 1100 }} 1100w,{%- endif -%}
              {%- if block.settings.image.width >= 1500 -%}{{ block.settings.image | image_url: width: 1500 }} 1500w,{%- endif -%}
              {%- if block.settings.image.width >= 1780 -%}{{ block.settings.image | image_url: width: 1780 }} 1780w,{%- endif -%}
              {%- if block.settings.image.width >= 2000 -%}{{ block.settings.image | image_url: width: 2000 }} 2000w,{%- endif -%}
              {%- if block.settings.image.width >= 3000 -%}{{ block.settings.image | image_url: width: 3000 }} 3000w,{%- endif -%}
              {%- if block.settings.image.width >= 3840 -%}{{ block.settings.image | image_url: width: 3840 }} 3840w,{%- endif -%}
              {{ block.settings.image | image_url }} {{ block.settings.image.width }}w"
              sizes="100vw"
              src="{{ block.settings.image | image_url: width: 1500 }}"
              loading="lazy"
              alt="{{ block.settings.image.alt | escape }}"
              width="{{ block.settings.image.width }}"
              height="{{ block.settings.image.width | divided_by: block.settings.image.aspect_ratio | round }}"
              class="desktop-slider"
            >
          {%- else -%}
            {{ 'lifestyle-2' | placeholder_svg_tag: 'placeholder-svg' }}
          {%- endif -%}
         {%- if block.settings.mobile_image -%}
            <img
              srcset="{%- if block.settings.mobile_image.width >= 375 -%}{{ block.settings.mobile_image | image_url: width: 375 }} 375w,{%- endif -%}
              {%- if block.settings.mobile_image.width >= 550 -%}{{ block.settings.mobile_image | image_url: width: 550 }} 550w,{%- endif -%}
              {%- if block.settings.mobile_image.width >= 750 -%}{{ block.settings.mobile_image | image_url: width: 750 }} 750w,{%- endif -%}
              {%- if block.settings.mobile_image.width >= 1100 -%}{{ block.settings.mobile_image | image_url: width: 1100 }} 1100w,{%- endif -%}
              {%- if block.settings.mobile_image.width >= 1500 -%}{{ block.settings.mobile_image | image_url: width: 1500 }} 1500w,{%- endif -%}
              {%- if block.settings.mobile_image.width >= 1780 -%}{{ block.settings.mobile_image | image_url: width: 1780 }} 1780w,{%- endif -%}
              {%- if block.settings.mobile_image.width >= 2000 -%}{{ block.settings.mobile_image | image_url: width: 2000 }} 2000w,{%- endif -%}
              {%- if block.settings.mobile_image.width >= 3000 -%}{{ block.settings.mobile_image | image_url: width: 3000 }} 3000w,{%- endif -%}
              {%- if block.settings.mobile_image.width >= 3840 -%}{{ block.settings.mobile_image | image_url: width: 3840 }} 3840w,{%- endif -%}
              {{ block.settings.mobile_image | image_url }} {{ block.settings.mobile_image.width }}w"
              sizes="100vw"
              src="{{ block.settings.mobile_image | image_url: width: 1500 }}"
              loading="lazy"
              alt="{{ block.settings.mobile_image.alt | escape }}"
              width="{{ block.settings.mobile_image.width }}"
              height="{{ block.settings.mobile_image.width | divided_by: block.settings.mobile_image.aspect_ratio | round }}"
              class="mobile-slider"
            >          
          {%- endif -%}

          
        </div>
        
<div class="dt-sc-hotspot dt-sc-hotspot-wrapper">
    <div class="dt-sc-hotspot-item dt-sc-hotspot-item{{block.id}}_a block-type-{{block.type}}">      
      <a class="open-popup-link{{block.id}}_a dt-sc-hotspot-marker dt-sc-hotspot-marker {{block.id}}_a icon-link"  href="#dt-sc-hotspot-marker{{block.id}}_a"><span class="{{ section.settings.icon }}"></span></a>
      {% if block.settings.spot_1_title != blank or block.settings.spot_1_text != blank or block.settings.spot_1_link_text != blank %}
      <div id="dt-sc-hotspot-marker{{block.id}}_a" class="dt-sc-hotspot-popup {{block.settings.spot_1_text_position}}">
        <div class="dt-sc-hotspot-content {{ block.settings.spot_1_content_alignment}}">
          {% if block.settings.spot_1_title != blank %}
          <a href="{{block.settings.spot_1_link}}" title="{{ block.settings.spot_1_title}}"><h4 class="dt-sc-hotspot-content-title">{{ block.settings.spot_1_title}}</h4></a>
          {% endif %}     
          {% if block.settings.spot_1_text != blank %}
          <p>{{block.settings.spot_1_text }}</p>
          {% endif %}     
           {% if block.settings.spot_1_image != blank %}
          {{ block.settings.spot_1_image | image_url: width: 400 | image_tag: sizes: '(min-width:1600px) 960px, (min-width: 750px) calc((100vw - 11.5rem) / 2), calc(100vw - 4rem)' }}             
          {% endif %} 
          {% if block.settings.spot_1_link_text != blank and block.settings.spot_1_link != blank %}
          <a class="link" href="{{block.settings.spot_1_link}}">{{block.settings.spot_1_link_text}}</a>
          {% endif %}     
        </div>
      </div>
      {% endif %} 
      
    </div>
  
<div class="dt-sc-hotspot-item dt-sc-hotspot-item{{block.id}}_b block-type-{{block.type}}">
  <a class="open-popup-link{{block.id}}_b dt-sc-hotspot-marker dt-sc-hotspot-marker {{block.id}}_b icon-link" href="#dt-sc-hotspot-marker{{block.id}}_b"><span class="{{ section.settings.icon }}"></span></a>  
      {% if block.settings.spot_2_title != blank or block.settings.spot_2_text != blank or block.settings.spot_2_link_text != blank %}
      <div id="dt-sc-hotspot-marker{{block.id}}_b" class="dt-sc-hotspot-popup {{block.settings.spot_2_text_position}}">
        <div class="dt-sc-hotspot-content {{ block.settings.spot_2_content_alignment}}">
          {% if block.settings.spot_2_title != blank %}
          <a href="{{block.settings.spot_2_link}}" title="{{ block.settings.spot_2_title}}"><h4 class="dt-sc-hotspot-content-title">{{ block.settings.spot_2_title}}</h4></a>
          {% endif %}     
          {% if block.settings.spot_2_text != blank %}
          <p>{{block.settings.spot_2_text }}</p>
          {% endif %}     
          {% if block.settings.spot_2_image != blank %}
          {{ block.settings.spot_2_image |image_url: width: 400 | image_tag: sizes: '(min-width:1600px) 960px, (min-width: 750px) calc((100vw - 11.5rem) / 2), calc(100vw - 4rem)' }}
          {% endif %}     
          {% if block.settings.spot_2_link_text != blank and block.settings.spot_2_link != blank %}
          <a class="link" href="{{block.settings.spot_2_link}}">{{block.settings.spot_2_link_text}}</a>
          {% endif %}     
        </div>
      </div>
      {% endif %}  
      
    </div>

<div class="dt-sc-hotspot-item dt-sc-hotspot-item{{block.id}}_c block-type-{{block.type}}">
<a class="large-hide open-popup-link{{block.id}}_c dt-sc-hotspot-marker dt-sc-hotspot-marker{{block.id}}_c icon-link" href="#dt-sc-hotspot-marker{{block.id}}_c"><span class="{{ section.settings.icon }}"></span></a>      
      {% if block.settings.spot_3_title != blank or block.settings.spot_3_text != blank or block.settings.spot_3_link_text != blank %}
      <div id="dt-sc-hotspot-marker{{block.id}}_c" class="dt-sc-hotspot-popup {{block.settings.spot_3_text_position}}">
        <div class="dt-sc-hotspot-content {{ block.settings.spot_3_content_alignment}}">
          {% if block.settings.spot_3_title != blank %}
          <a href="{{block.settings.spot_3_link}}" title="{{ block.settings.spot_3_title}}"><h4 class="dt-sc-hotspot-content-title">{{ block.settings.spot_3_title}}</h4></a>
          {% endif %}     
          {% if block.settings.spot_3_text != blank %}
          <p>{{block.settings.spot_3_text }}</p>
          {% endif %}   
          {% if block.settings.spot_3_image != blank %}
          {{ block.settings.spot_3_image | image_url: width: 400 | image_tag: sizes: '(min-width:1600px) 960px, (min-width: 750px) calc((100vw - 11.5rem) / 2), calc(100vw - 4rem)' }}
          {% endif %} 
          {% if block.settings.spot_3_link_text != blank and block.settings.spot_3_link != blank %}
          <a class="link" href="{{block.settings.spot_3_link}}">{{block.settings.spot_3_link_text}}</a>
          {% endif %}     
        </div>
      </div>
      {% endif %}             
    </div>
</div>
        <div class="slideshow__text-wrapper banner__content banner__content--{{ block.settings.box_align }} page-width{% if block.settings.show_text_box == false %} banner--desktop-transparent{% endif %}">
          <div class="slideshow__text banner__box content-container content-container--full-width-mobile color-{{ block.settings.color_scheme }} gradient slideshow__text--{{ block.settings.text_alignment }} slideshow__text-mobile--{{ block.settings.text_alignment_mobile }}">
            {%- if block.settings.subheading != blank -%}
            <h2 class="banner__sub_heading">{{ block.settings.subheading }}</h2>
            {%- endif -%}
            {%- if block.settings.heading != blank -%}
              <h2 class="banner__heading {{ block.settings.heading_size }}">{{ block.settings.heading }}</h2>
            {%- endif -%}           
            {%- if block.settings.text != blank -%}
              <div class="banner__text" {{ block.shopify_attributes }}>
                <span>{{ block.settings.text | escape }}</span>
              </div>
            {%- endif -%}
            {%- if block.settings.button_label != blank -%}
              <div class="banner__buttons">
                <a{% if block.settings.link %} href="{{ block.settings.link }}"{% else %} role="link" aria-disabled="true"{% endif %} class="button {% if block.settings.button_style_secondary %}button--secondary{% else %}button--primary{% endif %}">{{ block.settings.button_label | escape }}</a>
              </div>
            {%- endif -%}
          </div>
        </div>
      </div>
    {%- endfor -%}
  </div>
    {% if section.settings.swiper_navigation != blank %}
    <div class="swiper-button-next"><span></span></div>
    <div class="swiper-button-prev"><span></span></div>
    {% endif %}    
    {% if section.settings.swiper_pagination != blank %}
    <div class="swiper-pagination"></div>
    {% endif %}
    </div>
  </div>
   </div>
</slideshow-component>

{%- if request.design_mode -%}
  <script src="{{ 'theme-editor.js' | asset_url }}" defer="defer"></script>
{%- endif -%}


{% schema %}
{
  "name": "t:sections.home-slider-with-hot-spots.name",
  "tag": "section",
  "class": "section slideshow home-slideshow-with-hotspot",
  "settings": [
    {
      "type":"checkbox",
      "id":"page_full_width",
       "default": false,
      "label": "t:sections.all.page_full_width.label"
    },
    {
      "type":"checkbox",
      "id":"page_full_width_spacing",
       "default": false,
      "label": "t:sections.all.page_full_width_spacing.label"
    },
      {
      "type": "header",
      "content": "t:sections.slideshow.settings.slider_image_height_settings.content"
    },
    {
      "type": "range",
      "id": "slider_image_height",
      "min": 400,
      "max": 1200,
      "step": 10,
      "unit": "px",
      "default": 900,
      "label": "t:sections.slideshow.settings.slider_image_height.label"
   },
     {
      "type": "range",
      "id": "slider_image_height_laptop",
      "min": 400,
      "max": 1200,
      "step": 10,
      "unit": "px",
      "default": 700,
      "label": "t:sections.slideshow.settings.slider_image_height_laptop.label"
    },
     {
      "type": "range",
      "id": "slider_image_height_tab",
       "min": 400,
      "max": 1200,
      "step": 10,
      "unit": "px",
      "default": 500,
      "label": "t:sections.slideshow.settings.slider_image_height_tab.label"
    },
     {
      "type": "range",
      "id": "slider_image_height_mobile",
      "min": 400,
      "max": 1200,
      "step": 10,
      "unit": "px",
      "default": 400,
      "label": "t:sections.slideshow.settings.slider_image_height_mobile.label"
    },
    {
      "type": "header",
      "content": "t:sections.all.swiper.swiper_slider_title"
    },
    {
      "type": "checkbox",
      "id": "swiper_pagination",
      "default": false,
      "label": "t:sections.all.swiper.swiper_pagination"
    },
    {
      "type": "checkbox",
      "id": "swiper_navigation",
      "default": false,
      "label": "t:sections.all.swiper.swiper_navigation"
    },
    {
      "type": "range",
      "id": "auto_play",
      "min": 0,
      "max": 5,
      "step": 1,
      "label": "t:sections.all.swiper.auto_play",
      "default": 0
    },
    {
      "type": "select",
      "id": "slider_effect",
      "options": [
        {
          "value": "slide",
          "label": "t:sections.all.swiper.slide"
        },
        {
          "value": "fade",
          "label": "t:sections.all.swiper.fade"
        }
      ],
      "default": "slide",
      "label": "t:sections.all.swiper.effects"
    },
     
    {
      "type": "header",
      "content": "t:sections.home-slider-with-hot-spots.settings.accessibility.content"
    },
    {
      "type": "text",
      "id": "accessibility_info",
      "label": "t:sections.home-slider-with-hot-spots.settings.accessibility.label",
      "info": "t:sections.home-slider-with-hot-spots.settings.accessibility.info",
      "default": "Slideshow about our brand"
    },
    {
"type": "header",
"content": "t:sections.hotspot.settings.hotspot_font_size_settings.content"
},	
{	
"type": "range",	
"id": "hotspot_size",	
"label": "t:sections.hotspot.settings.hotspot_size.label",	
"min": 25,	
"max": 50,	
"step": 1,	
"default": 36,	
"unit": "px"	
},	
{	
"type": "range",	
"id": "hotspot_outer_size",	
"label": "t:sections.hotspot.settings.hotspot_outer_size.label",	
"min": 1,	
"max": 20,	
"step": 1,	
"default": 2,	
"unit": "px"	
},		
{
"type": "header",
"content": "t:sections.hotspot.settings.hotspot_content_size_settings.content"
},
{	
"type": "range",	
"id": "hotspot_content_size",	
"label": "t:sections.hotspot.settings.hotspot_content_size.label",	
"min": 200,	
"max": 400,	
"step": 5,	
"default": 300,	
"unit": "px"	
},
{	
"type": "range",	
"id": "hotspot_content_size_laptop",	
"label": "t:sections.hotspot.settings.hotspot_content_size_laptop.label",	
"min": 200,	
"max": 400,	
"step": 5,	
"default": 300,	
"unit": "px"	
},	
{	
"type": "range",	
"id": "hotspot_content_size_tablet",	
"label": "t:sections.hotspot.settings.hotspot_content_size_tablet.label",	
"min": 200,	
"max": 400,	
"step": 5,	
"default": 275,	
"unit": "px"	
},
{	
"type": "range",	
"id": "hotspot_content_size_mobile",	
"label": "t:sections.hotspot.settings.hotspot_content_size_mobile.label",	
"min": 200,	
"max": 400,	
"step": 5,	
"default": 250,	
"unit": "px"	
},
{
"type": "text",
"id": "icon",
"label": "t:sections.hotspot.settings.icon.label",
"default":"fa fa-plus"
},
       {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    }
  ],
  "blocks": [
    {
      "type": "slide",
      "name": "t:sections.home-slider-with-hot-spots.blocks.slide.name",
      "limit": 5,
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.image.label"
        },
        {
          "type": "image_picker",
          "id": "mobile_image",
          "label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.mobile_image.label"
        },
        {
          "type": "text",
          "id": "heading",
          "default": "Image slide",
          "label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.heading.label"
        },
        {
          "type": "select",
          "id": "heading_size",
          "options": [
            {
              "value": "h2",
              "label": "t:sections.all.heading_size.options__1.label"
            },
            {
              "value": "h1",
              "label": "t:sections.all.heading_size.options__2.label"
            },
            {
              "value": "h0",
              "label": "t:sections.all.heading_size.options__3.label"
            }
          ],
          "default": "h1",
          "label": "t:sections.all.heading_size.label"
        },
        {
          "type": "text",
          "id": "subheading",
          "default": "Image sub heading",
          "label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.subheading.label"
        },
      {
          "type": "text",
          "id": "text",
          "default": "Tell your brand's story through images",
          "label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.text.label"
        },
        {
          "type": "text",
          "id": "button_label",
          "default": "Button label",
          "label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.button_label.label",
          "info": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.button_label.info"
        },
        {
          "type": "url",
          "id": "link",
          "label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.link.label"
        },
        {
          "type": "checkbox",
          "id": "button_style_secondary",
          "label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.secondary_style.label",
          "default": false
        },
        {
          "type": "select",
          "id": "box_align",
          "options": [
            {
              "value": "top-left",
              "label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.box_align.options__1.label"
            },
            {
              "value": "top-center",
              "label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.box_align.options__2.label"
            },
            {
              "value": "top-right",
              "label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.box_align.options__3.label"
            },
            {
              "value": "middle-left",
              "label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.box_align.options__4.label"
            },
            {
              "value": "middle-center",
              "label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.box_align.options__5.label"
            },
            {
              "value": "middle-right",
              "label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.box_align.options__6.label"
            },
            {
              "value": "bottom-left",
              "label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.box_align.options__7.label"
            },
            {
              "value": "bottom-center",
              "label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.box_align.options__8.label"
            },
            {
              "value": "bottom-right",
              "label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.box_align.options__9.label"
            }
          ],
          "default": "middle-center",
          "label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.box_align.label",
          "info": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.box_align.info"
        },
        {
          "type": "checkbox",
          "id": "show_text_box",
          "label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.show_text_box.label",
          "default": true
        },
        {
          "type": "select",
          "id": "text_alignment",
          "options": [
            {
              "value": "left",
              "label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.text_alignment.option_1.label"
            },
            {
              "value": "center",
              "label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.text_alignment.option_2.label"
            },
            {
              "value": "right",
              "label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.text_alignment.option_3.label"
            }
          ],
          "default": "center",
          "label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.text_alignment.label"
        },
        {
          "type": "range",
          "id": "image_overlay_opacity",
          "min": 0,
          "max": 100,
          "step": 10,
          "unit": "%",
          "label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.image_overlay_opacity.label",
          "default": 0
        },
       {
          "type": "header",
           "content": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot1.content"
        },
{
"type" : "text",
"id" : "spot_1_title",
"label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot_1_title.label",
"default" : "Title"
},
{   
"type" : "textarea",
"id" : "spot_1_text",
"label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot_1_text.label"
},
{   
"type" : "image_picker",
"id" : "spot_1_image",
"label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot_1_image.label"
},  
{
"type": "text",
"id": "spot_1_link_text",
"label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot_1_link_text.label",
"default":"Shop Now"
},
{   
"type" : "url",
"id" : "spot_1_link",
"label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot_1_link.label"
},


{
"type": "range",
"id": "spot_1_top_position",
"label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot_1_top_position.label",
"min": 1,
"max": 100,
"step": 1,
"unit": "%",
"default": 40
}, 
{
"type": "range",
"id": "spot_1_left_position",
"label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot_1_left_position.label",
"min": 1,
"max": 100,
"step": 1,
"unit": "%",
"default": 40
},
{
"type": "select",
"id": "spot_1_text_position",
"label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot_1_text_position.label",
"default": "on-right",
"options": [
{
"value": "on-top",
"label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot_1_text_position.options__1.label"
},
{
"value": "on-right",
"label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot_1_text_position.options__2.label"
},
{
"value": "on-left",
"label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot_1_text_position.options__3.label"
},
{
"value": "on-bottom",
"label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot_1_text_position.options__4.label"
}
]
},
{
          "type": "header",
           "content": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot2.content"
        },
{
"type" : "text",
"id" : "spot_2_title",
"label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot_2_title.label",
"default" : "Title"
},
{   
"type" : "textarea",
"id" : "spot_2_text",
"label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot_2_text.label"
},
{   
"type" : "image_picker",
"id" : "spot_2_image",
"label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot_2_image.label"
},  
{
"type": "text",
"id": "spot_2_link_text",
"label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot_2_link_text.label",
"default":"Shop Now"
},
{   
"type" : "url",
"id" : "spot_2_link",
"label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot_2_link.label"
},


{
"type": "range",
"id": "spot_2_top_position",
"label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot_2_top_position.label",
"min": 1,
"max": 100,
"step": 1,
"unit": "%",
"default": 40
}, 
{
"type": "range",
"id": "spot_2_left_position",
"label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot_2_left_position.label",
"min": 1,
"max": 100,
"step": 1,
"unit": "%",
"default": 40
},
{
"type": "select",
"id": "spot_2_text_position",
"label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot_2_text_position.label",
"default": "on-right",
"options": [
{
"value": "on-top",
"label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot_2_text_position.options__1.label"
},
{
"value": "on-right",
"label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot_2_text_position.options__2.label"
},
{
"value": "on-left",
"label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot_2_text_position.options__3.label"
},
{
"value": "on-bottom",
"label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot_2_text_position.options__4.label"
}
]
},
{
          "type": "header",
           "content": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot3.content"
        },
{
"type" : "text",
"id" : "spot_3_title",
"label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot_3_title.label",
"default" : "Title"
},
{   
"type" : "textarea",
"id" : "spot_3_text",
"label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot_3_text.label"
},
{   
"type" : "image_picker",
"id" : "spot_3_image",
"label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot_3_image.label"
},  
{
"type": "text",
"id": "spot_3_link_text",
"label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot_3_link_text.label",
"default":"Shop Now"
},
{   
"type" : "url",
"id" : "spot_3_link",
"label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot_3_link.label"
},


{
"type": "range",
"id": "spot_3_top_position",
"label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot_3_top_position.label",
"min": 1,
"max": 100,
"step": 1,
"unit": "%",
"default": 40
}, 
{
"type": "range",
"id": "spot_3_left_position",
"label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot_3_left_position.label",
"min": 1,
"max": 100,
"step": 1,
"unit": "%",
"default": 40
},
{
"type": "select",
"id": "spot_3_text_position",
"label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot_3_text_position.label",
"default": "on-right",
"options": [
{
"value": "on-top",
"label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot_3_text_position.options__1.label"
},
{
"value": "on-right",
"label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot_3_text_position.options__2.label"
},
{
"value": "on-left",
"label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot_3_text_position.options__3.label"
},
{
"value": "on-bottom",
"label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.spot_3_text_position.options__4.label"
}
]
},
        {
          "type": "select",
          "id": "color_scheme",
          "options": [
            {
              "value": "accent-1",
              "label": "t:sections.all.colors.accent_1.label"
            },
            {
              "value": "accent-2",
              "label": "t:sections.all.colors.accent_2.label"
            },
            {
              "value": "background-1",
              "label": "t:sections.all.colors.background_1.label"
            },
            {
              "value": "background-2",
              "label": "t:sections.all.colors.background_2.label"
            },
            {
              "value": "inverse",
              "label": "t:sections.all.colors.inverse.label"
            }
          ],
          "default": "background-1",
          "label": "t:sections.all.colors.label",
          "info": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.color_scheme.info"
        },
        {
          "type": "header",
          "content": "t:sections.home-slider-with-hot-spots.settings.mobile.content"
        },
        {
          "type": "select",
          "id": "text_alignment_mobile",
          "options": [
            {
              "value": "left",
              "label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.text_alignment_mobile.options__1.label"
            },
            {
              "value": "center",
              "label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.text_alignment_mobile.options__2.label"
            },
            {
              "value": "right",
              "label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.text_alignment_mobile.options__3.label"
            }
          ],
          "default": "center",
          "label": "t:sections.home-slider-with-hot-spots.blocks.slide.settings.text_alignment_mobile.label"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.home-slider-with-hot-spots.presets.name",
      "blocks": [
        {
          "type": "slide"
        },
        {
          "type": "slide"
        }
      ]
    }
  ]
}
{% endschema %}
