
{{ 'dt-collapsible-content.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

.video-popup[style="display: flex;"] {
    animation: fadeIn var(--anim-time) ease both;
}
.video-popup {
    display: none;
    z-index: 10;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    border: none;
    padding: 0px;
    background-color: rgb(0 0 0 / 90%);
    border-radius: 0px;
    width: 100%;
    height: 100%;
    align-items: center;
    justify-content: center;
}


.video-popup .close:hover {

    background: transparent;
}
.video-popup .close {
    position: absolute;
    right: 0px;
    top: 0px;
    margin: 1rem;
    font-weight: 500;
    font-size: 28px;
    color: var(--gradient-base-accent-1);
    background: transparent;
    padding: 0;
    border-bottom: none;
    cursor: pointer;
    line-height: normal;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    transition: all linear var(--duration-default);
    border-radius: var(--buttons-radius);
}
.video-popup .close:before { content:''; position: absolute; height: 30px; width: 30px; background: #fc7b7e; top: 3px; z-index: -1; transition: var(--duration-default); }
.video-popup .close:hover:before { background: var(--gradient-base-background-2); }
{%- endstyle -%}
<script>
 
$(document).ready(function () {
  $(".video__btn-play").on("click", function (e) {
    e.preventDefault();
    $('body').addClass('overlay-active');
    $(".video-container, .video-wrapper, .pop-up__video-close , .video-popup").css('display','flex');
    var srchref = "",
        autoplay = "",
        id = $(this).data("id");
    if ($(this).data("type") == "vimeo")
      var srchref = "//player.vimeo.com/video/";
    else if ($(this).data("type") == "youtube")
      var srchref = "https://www.youtube.com/embed/";
    if ($(this).data("autoplay") == true) autoplay = "?autoplay=1";
    $("#video-popup-iframe").attr("src", srchref + id + autoplay);
    $("#video-popup-iframe").on("load", function () {
      $(".video-wrapper").show();
    });
  });

  $(".pop-up__video-close").on("click", function (e) {
     $('body').removeClass('overlay-active');
    $(".video-container, .video-wrapper, .pop-up__video-close , .video-popup").css('display','none');
    $("#video-popup-iframe").attr("src", "");
  });
});
</script>
<div class="woofly-collapsible-content">
<div class="color-{{ section.settings.color_scheme }} gradient">
  <div class="collapsible-content {{ section.settings.custom_class_name }} collapsible-{{ section.settings.layout }}-layout isolate{% if section.settings.layout == 'section' %} page-width{% elsif section.settings.layout == 'none' %} content-container content-container--full-width{% endif %}">
    <div class="collapsible-content__wrapper section-{{ section.id }}-padding{% if section.settings.layout == 'section' %} content-container color-{{ section.settings.container_color_scheme }} gradient{% endif %}">
      <div class="{% if section.settings.image == blank %}page-width{% else %}page-width{% endif %}">
     
        <div class="grid grid--1-col grid--2-col-tablet collapsible-content__grid{% if section.settings.desktop_layout == 'image_second' %} collapsible-content__grid--reverse{% endif %}">
          {%- if section.settings.enable_address_block != blank -%}
            <div class="grid__item collapsible-content__grid-item">
              
              <div class="collapsible-content__media collapsible-content__media--{{ section.settings.image_ratio }} media global-media-settings gradient"
                {% if section.settings.image_ratio == 'adapt' %} style="padding-bottom: {{ 1 | divided_by: section.settings.image.aspect_ratio | times: 100 }}%;"{% endif %}
              >
                 {%- if section.settings.icon_2 != blank -%}
<!--               <div class="image__block_icon">
                  {% if section.settings.icon_2_text != blank %}
                <span>{{ section.settings.icon_2_text}}</span>
                 {% endif %}  
                <img src="{{ section.settings.icon_2 | image_url: width: 500 }}" alt="{{ section.settings.icon_2.alt | escape }}">
              </div> -->
              {% endif %}
                {%- if section.settings.image != blank -%}
                <img
                  srcset="{%- if section.settings.image.width >= 165 -%}{{ section.settings.image | image_url: width: 165 }} 165w,{%- endif -%}
                    {%- if section.settings.image.width >= 360 -%}{{ section.settings.image | image_url: width: 360 }} 360w,{%- endif -%}
                    {%- if section.settings.image.width >= 535 -%}{{ section.settings.image | image_url: width: 535 }} 535w,{%- endif -%}
                    {%- if section.settings.image.width >= 750 -%}{{ section.settings.image | image_url: width: 750 }} 750w,{%- endif -%}
                    {%- if section.settings.image.width >= 1070 -%}{{ section.settings.image | image_url: width: 1070 }} 1070w,{%- endif -%}
                    {%- if section.settings.image.width >= 1250 -%}{{ section.settings.image | image_url: width: 1250 }} 1250w,{%- endif -%}
                    {%- if section.settings.image.width >= 1500 -%}{{ section.settings.image | image_url: width: 1500 }} 1500w,{%- endif -%}
                    {{ section.settings.image | image_url }} {{ section.settings.image.width }}w"
                  src="{{ section.settings.image | image_url: width: 1500 }}"
                  sizes="(min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 | divided_by: 2 }}px, (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px) / 2)"
                  alt="{{ section.settings.image.alt | escape }}"
                  loading="lazy"
                  width="{{ section.settings.image.width }}"
                  height="{{ section.settings.image.height }}"
                  class="woofly-img"
                >
                  {% else %}
                    {{ 'product-apparel-1' | placeholder_svg_tag: 'placeholder_svg' }}
                {% endif %} 
                {%  if section.settings.video_url != blank %}
                <div class="video-play-icon">
                <a href="#" class="watch-more video__btn-play" data-type="youtube" data-id="{{ section.settings.video_url.id }}" data-autoplay='true'>
                  {%  if section.settings.cover_image != blank %}
                   <img src="{{  section.settings.cover_image | image_url: width: 160 }}" > 
                    {% else %}
                    <div class="play-btn">
                       {{ 'product-apparel-2' | placeholder_svg_tag: 'placeholder_svg' }}
                    </div>
                  {% endif %}
                </a>
                </div>
                {% endif %}
                 {%- if section.settings.icon_1 != blank -%}
                <div class="image__block_icon">
                <img src="{{ section.settings.icon_1 | image_url: width: 500 }}" alt="{{ section.settings.icon_1.alt | escape }}">
                 {% if section.settings.icon_1_text != blank %}
                <span>{{ section.settings.icon_1_text }}</span>
                 {% endif %}                    
              </div>
              {% endif %}
              </div>
               
              <div class="collapsible_address-block">
                {% if section.settings.address_heading != blank %}
                <h3 class="address-block-heading h4">{{ section.settings.address_heading}}</h3>
                 {% endif %}  
                <ul class=" list-unstyled">
                    {% if section.settings.collapsible_address != blank %}
                        <li class="address"> 
                         {%- render 'icon-location' -%}
                          <address>{{ section.settings.collapsible_address }}</address>
                        </li>
                        {% endif %}  
                       {% if section.settings.collapsible_contact_id != blank %}
                        <li class="office-mail">
                          <a href="mailto:{{ section.settings.collapsible_contact_id}}" class="link">
                            {%- render 'icon-mail' -%}<span>{{ section.settings.collapsible_contact_id }}</span></a>     
                        </li>
                        {% endif %}
                        {% if section.settings.collapsible_contact_no != blank %}
                        <li class="contact-phone">    
                          <a href="tel:{{ section.settings.collapsible_contact_no }}" class="link">{%- render 'icon-phone' -%}{{ section.settings.collapsible_contact_no }}</a>    
                        </li>
                        {% endif %}
                    </ul>
              </div>
            </div>
          {% endif %}
          <div class="grid__item">
            {%- unless section.settings.title == blank -%}
      <div class="title-wrapper-with-link title-wrapper--self-padded-mobile title-wrapper--no-top-margin collapsible-content__header content-align--{{ section.settings.column_alignment }}">
         {%- if section.settings.sub_heading != blank -%}  
         <h6 class="sub-heading">{{ section.settings.sub_heading | escape }}</h6>
         {%- endif -%} 
         {%- if section.settings.title != blank -%}  
          <h2 class="title {{ section.settings.heading_size }}">
            {{ section.settings.title | escape }}
          </h2>
          {%- endif -%} 
          {%- if section.settings.description != blank -%}  
          <p class="description">{{ section.settings.description }}</p>
          {%- endif -%}   
          {%- if section.settings.button_label != blank -%}
            <a {% if section.settings.button_link == blank %} role="link" aria-disabled="true"{% else %} href="{{ section.settings.button_link }}"{% endif %} class="button{% if section.settings.button_style_secondary %} button--secondary{% else %} button--primary{% endif %}">{{ section.settings.button_label | escape }}</a>
          {%- endif -%}
      </div>
    {%- endunless -%}
            {%- for block in section.blocks -%}
              <div class="accordion{% if section.settings.layout == 'row' %} content-container color-{{ section.settings.container_color_scheme }} gradient{% endif %}" {{ block.shopify_attributes }}>
                <details class="dt-details" id="Details-{{ block.id }}-{{ section.id }}"{% if section.settings.open_first_collapsible_row and forloop.first %} open{% endif %}>
                  <summary id="Summary-{{ block.id }}-{{ section.id }}" class="dt-sumary">
                    {% render 'icon-accordion', icon: block.settings.icon %}
                    <h3 class="accordion__title h2">
                      {{ block.settings.heading | default: block.settings.page.title }}
                    </h3>

              {% render 'icon-checkmark' %}
                  </summary>
                  <div class="accordion__content rte dt-accordion__content" id="CollapsibleAccordion-{{ block.id }}-{{ section.id }}" role="region" aria-labelledby="Summary-{{ block.id }}-{{ section.id }}">
                    {{ block.settings.row_content }}
                    {{ block.settings.page.content }}
                  </div>
                </details>
              </div>
            {%- endfor -%}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</div>
{% schema %}
{
  "name": "Collapsible content",
  "tag": "section",
  "class": "section section-collapsible-content",
  "settings": [
      {
      "type": "text",
      "id": "title",
      "default": "Collapsible Content",
      "label": "t:sections.all.title.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__3.label"
        }
      ],
      "default": "h1",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "text",
      "id": "sub_heading",
      "label": "t:sections.all.sub_heading.label"
    },
     {
      "type": "text",
      "id": "description",
      "label": "t:sections.all.description.label"
    },
    {
      "type": "text",
      "id": "button_label",
      "default": "Button label",
      "label": "t:sections.grid-banner.settings.button_label.label"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "t:sections.grid-banner.settings.button_link.label"
    },
    {
      "type": "checkbox",
      "id": "button_style_secondary",
      "default": false,
      "label": "t:sections.all.button_style_secondary.label"
      },
    {
      "type": "select",
      "id": "column_alignment",
      "label": "t:sections.collapsible_content.settings.column_alignment.label",
      "options": [
        {
          "value": "left",
          "label": "t:sections.collapsible_content.settings.column_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.collapsible_content.settings.column_alignment.options__2.label"
        }
      ],
      "default": "center"
    },
    {
      "type": "select",
      "id": "layout",
      "label": "t:sections.collapsible_content.settings.layout.label",
      "options": [
        {
          "value": "none",
          "label": "t:sections.collapsible_content.settings.layout.options__1.label"
        },
        {
          "value": "row",
          "label": "t:sections.collapsible_content.settings.layout.options__2.label"
        },
        {
          "value": "section",
          "label": "t:sections.collapsible_content.settings.layout.options__3.label"
        }
      ],
      "default": "none"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "t:sections.all.colors.accent_1.label"
        },
        {
          "value": "accent-2",
          "label": "t:sections.all.colors.accent_2.label"
        },
        {
          "value": "background-1",
          "label": "t:sections.all.colors.background_1.label"
        },
        {
          "value": "background-2",
          "label": "t:sections.all.colors.background_2.label"
        },
        {
          "value": "inverse",
          "label": "t:sections.all.colors.inverse.label"
        }
      ],
      "default": "background-1",
      "label": "t:sections.all.colors.label"
    },
    {
      "type": "select",
      "id": "container_color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "t:sections.all.colors.accent_1.label"
        },
        {
          "value": "accent-2",
          "label": "t:sections.all.colors.accent_2.label"
        },
        {
          "value": "background-1",
          "label": "t:sections.all.colors.background_1.label"
        },
        {
          "value": "background-2",
          "label": "t:sections.all.colors.background_2.label"
        },
        {
          "value": "inverse",
          "label": "t:sections.all.colors.inverse.label"
        }
      ],
      "default": "background-2",
      "label": "t:sections.collapsible_content.settings.container_color_scheme.label",
      "info": "t:sections.collapsible_content.settings.container_color_scheme.info"
    },
    {
      "type": "checkbox",
      "id": "open_first_collapsible_row",
      "default": false,
      "label": "t:sections.collapsible_content.settings.open_first_collapsible_row.label"
    },
    {
      "type": "header",
      "content": "t:sections.collapsible_content.settings.header.content"
    },
    {
    "type": "checkbox",
    "id":"enable_address_block",
    "label":"t:sections.collapsible_content.settings.enable_address_block.label"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "t:sections.collapsible_content.settings.image.label",
      "info": "Size: 552x821"
    },
    {
      "type": "image_picker",
      "id": "cover_image",
      "label": "Video icon"
    },
    {
      "type": "video_url",
      "id": "video_url",
      "accept": [
        "youtube",
        "vimeo"
      ],
      "default": "https://www.youtube.com/watch?v=_9VUPq3SxOc",
      "label": "t:sections.video-pop-up.settings.video_url.label",
      "placeholder": "t:sections.video-pop-up.settings.video_url.placeholder",
      "info": "t:sections.video-pop-up.settings.video_url.info"
    },
    {
      "type": "image_picker",
      "id": "icon_1",
      "label": "Icon 1"
    },
    {
      "type": "html",
      "id": "icon_1_text",
      "label": "Icon 1 text"
    },
    
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.collapsible_content.settings.image_ratio.options__1.label"
        },
        {
          "value": "small",
          "label": "t:sections.collapsible_content.settings.image_ratio.options__2.label"
        },
        {
          "value": "large",
          "label": "t:sections.collapsible_content.settings.image_ratio.options__3.label"
        }
      ],
      "default": "adapt",
      "label": "t:sections.collapsible_content.settings.image_ratio.label"
    },
    
    {
      "type": "select",
      "id": "desktop_layout",
      "options": [
        {
          "value": "image_first",
          "label": "t:sections.collapsible_content.settings.desktop_layout.options__1.label"
        },
        {
          "value": "image_second",
          "label": "t:sections.collapsible_content.settings.desktop_layout.options__2.label"
        }
      ],
      "default": "image_second",
      "label": "t:sections.collapsible_content.settings.desktop_layout.label",
      "info": "t:sections.collapsible_content.settings.desktop_layout.info"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
     {
    "type": "header",
    "content": "t:sections.all.custom_class_heading.content"
    },
    {
    "type": "text",
    "id": "custom_class_name",
    "label": "t:sections.all.custom_class_name.label"
    }
  ],
  "blocks": [
    {
      "type": "collapsible_row",
      "name": "t:sections.collapsible_content.blocks.collapsible_row.name",
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "default": "Collapsible row",
          "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.heading.label",
          "info": "t:sections.collapsible_content.blocks.collapsible_row.settings.heading.info"
        },
        {
          "type": "select",
          "id": "icon",
          "options": [
            {
              "value": "none",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__1.label"
            },
            {
              "value": "apple",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__2.label"
            },
            {
              "value": "banana",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__3.label"
            },
            {
              "value": "bottle",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__4.label"
            },
            {
              "value": "box",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__5.label"
            },
            {
              "value": "carrot",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__6.label"
            },
            {
              "value": "chat_bubble",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__7.label"
            },
            {
              "value": "check_mark",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__8.label"
            },
            {
              "value": "clipboard",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__9.label"
            },
            {
              "value": "dairy",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__10.label"
            },
            {
              "value": "dairy_free",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__11.label"
            },
            {
              "value": "dryer",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__12.label"
            },
            {
              "value": "eye",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__13.label"
            },
            {
              "value": "fire",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__14.label"
            },
            {
              "value": "gluten_free",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__15.label"
            },
            {
              "value": "heart",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__16.label"
            },
            {
              "value": "iron",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__17.label"
            },
            {
              "value": "leaf",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__18.label"
            },
            {
              "value": "leather",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__19.label"
            },
            {
              "value": "lightning_bolt",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__20.label"
            },
            {
              "value": "lipstick",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__21.label"
            },
            {
              "value": "lock",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__22.label"
            },
            {
              "value": "map_pin",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__23.label"
            },
            {
              "value": "nut_free",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__24.label"
            },
            {
              "value": "pants",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__25.label"
            },
            {
              "value": "paw_print",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__26.label"
            },
            {
              "value": "pepper",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__27.label"
            },
            {
              "value": "perfume",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__28.label"
            },
            {
              "value": "plane",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__29.label"
            },
            {
              "value": "plant",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__30.label"
            },
            {
              "value": "price_tag",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__31.label"
            },
            {
              "value": "question_mark",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__32.label"
            },
            {
              "value": "recycle",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__33.label"
            },
            {
              "value": "return",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__34.label"
            },
            {
              "value": "ruler",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__35.label"
            },
            {
              "value": "serving_dish",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__36.label"
            },
            {
              "value": "shirt",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__37.label"
            },
            {
              "value": "shoe",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__38.label"
            },
            {
              "value": "silhouette",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__39.label"
            },
            {
              "value": "snowflake",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__40.label"
            },
            {
              "value": "star",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__41.label"
            },
            {
              "value": "stopwatch",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__42.label"
            },
            {
              "value": "truck",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__43.label"
            },
            {
              "value": "washing",
              "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.options__44.label"
            }
          ],
          "default": "check_mark",
          "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.icon.label"
        },
        {
          "type": "richtext",
          "id": "row_content",
          "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.row_content.label"
        },
        {
          "type": "page",
          "id": "page",
          "label": "t:sections.collapsible_content.blocks.collapsible_row.settings.page.label"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Collapsible content",
      "blocks": [
        {
          "type": "collapsible_row"
        },
        {
          "type": "collapsible_row"
        },
        {
          "type": "collapsible_row"
        },
        {
          "type": "collapsible_row"
        }
      ]
    }
  ]
}
{% endschema %}
