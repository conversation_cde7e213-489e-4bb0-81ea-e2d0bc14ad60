.drawer {
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100%;
  display: flex;
  justify-content: flex-end;
  background-color: rgba(var(--color-foreground), 0.5);
  transition: visibility var(--duration-default) linear;
}

.drawer.active {
  visibility: visible;
}
/* cart-drawer.drawer.active div#CartDrawer-Overlay{background-color: rgba(var(--color-foreground), 0.5);} */
.drawer__inner {
  height: 100%;
  width: 40rem;
  max-width: calc(100vw - 3rem);
  /*   padding: 0 1.5rem; */
  border: 0.1rem solid rgba(var(--color-foreground), 0.2);
  border-right: 0;
  background-color: rgb(var(--color-background));
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transform: translateX(100%);
  transition: transform var(--duration-default) ease;
}

.drawer__inner-empty {
  height: 100%;
  padding: 0 1.5rem;
  background-color: rgb(var(--color-background));
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.cart-drawer__warnings {
  display: flex;
  flex-direction: column;
  flex: 1;
  justify-content: center;
}

cart-drawer.is-empty .drawer__inner {
  display: grid;
  grid-template-rows: 1fr;
  align-items: center;
  padding: 0;
}

cart-drawer.is-empty .drawer__header {
  display: none;
}

cart-drawer:not(.is-empty) .cart-drawer__warnings
/* ,cart-drawer:not(.is-empty) .cart-drawer__collection */ {
  display: none;
}

.cart-drawer__warnings--has-collection .cart__login-title {
  margin-top: 2.5rem;
}

.drawer.active .drawer__inner {
  transform: translateX(0);
}

.drawer__header {
  position: relative;
  background-color: rgb(var(--color-background));
  padding: 3rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  /*   border-bottom: 0.1rem solid rgba(var(--color-foreground),.2); */
  box-shadow: 0px 0px 10px 0px rgb(0 0 0 / 10%);
}

.drawer__heading {
  margin: 0;
  font-size: 2.4rem;
  font-weight: 500;
  text-transform: capitalize;
}

.drawer__close {
  display: inline-block;
  padding: 0;
  min-width: 1.4rem;
  min-height: 1.4rem;
  box-shadow: 0 0 0 0.2rem rgba(var(--color-button), 0);
  position: absolute;
  top: 38px;
  right: 30px;
  color: rgb(var(--color-foreground));
  background-color: transparent;
  border: none;
  cursor: pointer;
}

.cart-drawer__warnings .drawer__close {
  right: 15px;
  top: 15px;
}
.cart-drawer__collection.is-empty,
.drawer__footer.is-empty {
  display: none;
}
/* .drawer__close svg {
  height: 1.4rem;
  width: 1.4rem;
} */

.drawer__contents {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  padding:0;
}

.drawer__footer {
  background-color: rgb(var(--color-background));
  /*   border-top: 0.1rem solid rgba(var(--color-foreground), 0.2); */
  padding: 30px;
  box-shadow: 0px 0px 10px 0px rgb(0 0 0 / 10%);
  /* position: relative; */
}

cart-drawer-items.is-empty + .drawer__footer {
  display: none;
}

.drawer__footer > details {
  margin-top: -1.5rem;
  border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.2);
}
.drawer__footer .cart-drawer__footer {
  margin-top: 30px;
}
.drawer__footer > details[open] {
  padding-bottom: 1.5rem;
}

.drawer__footer summary {
  display: flex;
  position: relative;
  line-height: 1;
  padding: 0 0 2rem 0;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.drawer__footer summary svg {
  width: 30px;
  height: 22px;
  transition: var(--duration-default) linear all;
}
.drawer__footer .cart-group :where(summary) {
  transition: var(--duration-default) linear all;
}
.drawer__footer .cart-group :where(summary):hover {
    color: rgba(var(--color-base-outline-button-labels));
    fill: rgba(var(--color-base-outline-button-labels));
}
.drawer__footer summary .summary__title {
  font-size: 12px;
  font-weight: 500;
  margin-top: 8px;
}
.drawer__footer .cart-group {
  border-bottom: 1px solid rgba(var(--color-base-accent-1), 0.2);
}
.drawer__footer .drawer-details {
  display: block;
  width: 100%;
  position: absolute;
  left: 0;
  background: var(--gradient-background);
  bottom: 0;
  z-index: 2;
  box-shadow: 0 0 20px rgba(var(--color-base-accent-1), 0.2);
  padding: 40px 30px 30px;
}
.drawer__footer .drawer-details .close {
  top: 10px;
  padding: 0;
  position: absolute;
  right: 20px;
}
.drawer__footer .drawer-details .close svg {
  width: 1rem;
  height: 1rem;
}
#Discount-Wrapper .drawer-details input {
  min-width: calc(12rem + var(--buttons-border-width) * 2);
  min-height: calc(4rem + var(--buttons-border-width) * 2);
  padding: 0 1.5rem;
  border-radius: var(--buttons-radius);
  outline: 0;
  box-shadow: none;
  border: var(--inputs-border-width) solid rgb(var(--color-foreground),0.2);
  width: 100%;
  transition:all 0.3s linear;
}
#Discount-Wrapper .drawer-details input:focus {border: var(--inputs-border-width) solid rgb(var(--color-foreground));}
#Discount-Wrapper .drawer-details .button {
  margin-top: 16px;
}

.drawer__footer > details + .cart-drawer__footer {
  padding-top: 3rem;
}
.cart-drawer__footer .totals > h2,
.cart-drawer__footer .totals .totals__subtotal-value {
  font-size: 2rem;
  font-weight: 500;
}
cart-drawer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100%;
}

.cart-drawer__overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.cart-drawer__overlay:empty {
  display: block;
}

.cart-drawer__form {
  flex-grow: 1;
  display: flex;
  flex-wrap: wrap;
}

.cart-drawer__collection {
    padding: 3rem 2rem ;
    overflow: hidden;
    border-top: 1px solid rgba(var(--color-base-accent-1),0.2);
    border-bottom: 1px solid rgba(var(--color-base-accent-1),0.2);
}

.cart-drawer .drawer__cart-items-wrapper {
  flex-grow: 1;
}

.cart-drawer .cart-items,
.cart-drawer tbody {
  display: block;
  width: 100%;
}

.cart-drawer thead {
  display: none;
  width: 100%;
  position: sticky;
  top: 0;
  z-index: 2;
  background-color: rgb(var(--color-base-background-1));
}

cart-drawer-items {
  /*   overflow: auto; */
  flex: 1;
  /*   padding:3rem; */
}

@media screen and (max-height: 650px) {
  cart-drawer-items {
    overflow: visible;
  }

  .drawer__inner {
    overflow: hidden;
  }
}

.cart-drawer .cart-item {
  display: flex;
  grid-template: repeat(2, auto) / repeat(4, 1fr);
  /*   gap: 1.5rem; */
  margin-bottom: 0;
  justify-content: space-between;
  position: relative;
}
.cart-drawer .cart-item:not(:last-child) {
  border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.2);
}
.cart-drawer .cart-item:last-child {
  margin-bottom: 0rem;
}

.cart-drawer .cart-item__media {
  grid-row: 1 / 3;
}

.cart-drawer .cart-item__image {
  max-width: 100%;
  width:100px;
  height:90px;
}

.cart-drawer .cart-items thead {
  margin-bottom: 0.5rem;
}

.cart-drawer .cart-items thead th:first-child,
.cart-drawer .cart-items thead th:last-child {
  width: 0;
  padding: 0;
}

.cart-drawer .cart-items thead th:nth-child(2) {
  width: 50%;
  padding-left: 0;
}

.cart-drawer .cart-items thead tr {
  display: table-row;
  margin-bottom: 0;
}

.cart-drawer .cart-items th {
  border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.08);
}

.cart-drawer .cart-item:last-child {
  margin-bottom: 0rem;
}

.cart-drawer .cart-item .loading-overlay {
  right: 5px;
  padding-top: 2.5rem;
}

.cart-drawer .cart-items td {
  padding-top: 0;
  padding-bottom: 0;
}

.cart-drawer .cart-item > td + td {
  padding-left: 2rem;
}

.cart-drawer .cart-item__details {
  width: calc(100% - 30%);
  grid-column: 2 / 4;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  text-align: left;
}

.cart-drawer .cart-item__totals {
  pointer-events: none;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}

.cart-drawer.cart-drawer .cart-item__price-wrapper > *:only-child {
  margin-top: 0;
}

.cart-drawer .cart-item__price-wrapper .cart-item__discounted-prices {
  display: flex;
  flex-direction: column;
  gap: 0.6rem;
}

.cart-drawer .unit-price {
  margin-top: 0.6rem;
}

.cart-drawer .cart-items .cart-item__quantity {
  padding-top: 0;
  padding-left: 0;
  grid-column: 2 / 5;
  margin-top: 1rem;
}

@media screen and (max-width: 749px) {
  .cart-drawer .cart-item cart-remove-button {
    margin-left: 0;
  }
}

.cart-drawer__footer > * + * {
  margin-top: 1rem;
}

.cart-drawer .totals {
  justify-content: space-between;
}

.cart-drawer .price {
  line-height: 1;
  font-size: 1.4rem;
  text-align: left;
}

.cart-drawer .tax-note {
  margin: 1.2rem 0 1rem auto;
  text-align: left;
  font-size: 1.4rem;
}

.cart-drawer .product-option dd {
  word-break: break-word;
}

.cart-drawer details[open] > summary .icon-caret {
  transform: rotate(180deg);
}

.cart-drawer .cart__checkout-button {
  max-width: none;
}

.drawer__footer .cart__dynamic-checkout-buttons {
  max-width: 100%;
}

.drawer__footer #dynamic-checkout-cart ul {
  flex-wrap: wrap !important;
  flex-direction: row !important;
  margin: 0.5rem -0.5rem 0 0 !important;
  gap: 0.5rem;
}

.drawer__footer [data-shopify-buttoncontainer] {
  justify-content: flex-start;
}

.drawer__footer #dynamic-checkout-cart ul > li {
  flex-basis: calc(50% - 0.5rem) !important;
  margin: 0 !important;
}

.drawer__footer #dynamic-checkout-cart ul > li:only-child {
  flex-basis: 100% !important;
  margin-right: 0.5rem !important;
}

@media screen and (min-width: 750px) {
  .drawer__footer #dynamic-checkout-cart ul > li {
    flex-basis: calc(100% / 3 - 0.5rem) !important;
    margin: 0 !important;
  }

  .drawer__footer #dynamic-checkout-cart ul > li:first-child:nth-last-child(2),
  .drawer__footer
    #dynamic-checkout-cart
    ul
    > li:first-child:nth-last-child(2)
    ~ li,
  .drawer__footer #dynamic-checkout-cart ul > li:first-child:nth-last-child(4),
  .drawer__footer
    #dynamic-checkout-cart
    ul
    > li:first-child:nth-last-child(4)
    ~ li {
    flex-basis: calc(50% - 0.5rem) !important;
  }
}
@media screen and (max-width: 576px) {
  .cart-drawer .cart-item > td + td {
    padding-left: 1rem;
  }
}
cart-drawer-items::-webkit-scrollbar {
  width: 3px;
}

cart-drawer-items::-webkit-scrollbar-thumb {
  background-color: rgba(var(--color-foreground), 0.7);
  border-radius: 100px;
}

cart-drawer-items::-webkit-scrollbar-track-piece {
  margin-top: 31px;
}
.cart-drawer .cart-item__details .product-option:not(.Color) {
  display: none;
}
.cart-drawer .cart-item__details .product-option * {
  font-size: 1.4rem;
}
.cart-drawer .cart-item__details .product-option dt {
  color: var(--gradient-base-accent-2);
}
.cart-drawer .cart-item__details .cart-item__quantity-wrapper .quantity {
  height: 36px;
  min-height: calc((var(--inputs-border-width) * 2) + 3.2rem);
  width: calc(
    10.8rem / var(--font-body-scale) + var(--inputs-border-width) * 2
  );
}
.cart-drawer .quantity__button svg {
  width: 1rem;
  pointer-events: none;
}
.cart-drawer .quantity__button {
  width: calc(3.5rem / var(--font-body-scale));
}
.cart-drawer .cart-item__details > * + * {
  margin-top: 1.2rem;
}

.cart-drawer .cart-item cart-remove-button {
  position: absolute;
  top: 30px;
  right: 20px;
  margin: 0;
  width:2rem;
  height:2rem;
}
.cart-drawer cart-remove-button .button {
  margin: 0;
  background: var(--gradient-base-background-1);
  border-radius: 50%;
  box-shadow: 0 0 20px rgb(0 0 0 / 15%);
  min-width: 2rem;
  min-height: 2rem;
}
.cart-drawer cart-remove-button .button svg {
  width: 0.8rem;
  height: 0.8rem;
  fill: currentcolor;
  color: var(--gradient-base-accent-1);
  transition: all var(--duration-default) linear;
}
.cart-drawer .cart-item {
  padding: 30px 20px;
}
.cart-drawer .cart-item__name {
  max-width: 160px;
}
.cart-drawer .cart__view_cart-button {
  text-decoration: none;
  position: relative;
  margin: auto;
  width: max-content;
}
.cart-drawer .cart__view_cart-button:after {
  content: "";
  width: 100%;
  height: 1px;
  background: currentcolor;
  position: absolute;
  bottom: 4px;
  left: 0;
}
.cart-drawer .cart__view_cart-button {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    border: 0;
    cursor: pointer;
    text-decoration: none;
    transition: box-shadow var(--duration-short) ease;
    -webkit-appearance: none;
    white-space: nowrap;
    appearance: none;
    transition: all 0.3s linear;
    font-family: var(--font-heading-family);
    font-weight: 400;
    transition: all var(--duration-default) linear;
    border-radius: 60px;
    letter-spacing: 0.9px;
    min-height: 4.5rem;
    width:100%;
    border:1px solid;
}
.cart-drawer .cart__view_cart-button:hover {
  border: none;
  color: var(--color-icon);
  background-color: rgb(var(--color-base-outline-button-labels));
}
.cart-drawer .cart__view_cart-button:after {
  display: none;
}
.cart-drawer .drawer__close svg {
  transition: all var(--duration-default) linear;
}
.cart-drawer cart-remove-button .button:hover {
  background: rgb(var(--color-base-outline-button-labels));
}
.cart-drawer cart-remove-button .button:hover svg {
  color: var(--gradient-base-background-1);
}

@media screen and (max-width: 400px) {
  .cart-drawer__footer .totals > h2,
  .cart-drawer__footer .totals .totals__subtotal-value {
    font-size: 1.8rem;
  }
  .cart-drawer .cart-item cart-remove-button {
    top: 10px;
    right: 10px;
  }
  .drawer__footer {
    padding: 3rem 2rem;
  }
  .cart-drawer .cart-item {
    padding: 30px 20px;
  }
  .cart-drawer cart-remove-button .button {
    min-width: 2rem;
    min-height: 2rem;
    width: 2rem;
    height: 2rem;
  }
  .cart-drawer cart-remove-button .button svg {
    width: 0.8rem;
    height: 0.8rem;
  }
}

/*cart-drawer*/
cart-drawer.drawer .card.card--card.card--media {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 20px;
}
cart-drawer.drawer .swiper {
  overflow: visible;
  padding-bottom: 0;
}
cart-drawer.drawer .card__information .card__heading {
  font-size: 1.6rem;
}
.cart-drawer__collection h4 {
  font-size: 1.8rem;
  font-weight: 500;
  text-transform: uppercase;
  text-align: center;
  margin: 0;
  text-decoration: underline;
  text-underline-offset: 0.3rem;
}
cart-drawer.drawer .card-wrapper .quick-add__submit.button {
  background: none;
  color: var(--color-icon);
  padding: 0;
  min-width: fit-content;
  min-height: fit-content;
  margin: 0;
  left: 0;
  position: relative;
  text-decoration: underline;
  width: auto;
  display: inline-block;
  transition: all 0.3s linear;
}
cart-drawer.drawer .card-wrapper .quick-add__submit.button:hover {
  color: rgba(var(--color-base-outline-button-labels));
}
cart-drawer.drawer .quick-add {
  grid-row-start: 2;
  position: relative;
}
cart-drawer.drawer .swiper-pagination {
  bottom: -30px;
  right: 0;
  width: auto;
  left: 0;
  margin: auto;
  overflow: visible;
  display:flex;
  justify-content:center;
}
cart-drawer.drawer .card--card .quick-add {
  margin: 0;
  justify-content: flex-start;
  line-height: normal;
}

.cart__items-widget {
  overflow: auto;
  /*     display:flex; */
  flex-direction: column;
  flex: 1;
}
.cart-drawer__collection  .swiper-wrapper{align-items:center;}
.cart-drawer__collection .card--card .card__information{text-align:left;}
.cart-drawer__collection .card--card .card__information .price--on-sale .price__sale{justify-content:flex-start;}
.cart-drawer__collection .card--card .card__information .price{line-height:normal;}
.cart-drawer__collection .card--card.card--media>.card__content{
    padding: 0;
    align-self: center;
    display: flex;
    flex-direction: column;
}
.cart-drawer .cart-progress{padding:30px 30px 0;}
.cart-drawer .cart-progress p{margin:0;}
.drawer__footer .cart-drawer-detail:after{
    content: "";
    background: rgb(255 255 255 / 90%);
    opacity: 0;
    position: absolute;
    transition: var(--bls-transition);
    visibility: hidden;
    bottom: 0;
    left: 0;
    right: 0;
    top: 0;
    z-index: 1;
}
.drawer__footer details[open] .cart-drawer-detail:after{opacity:1;visibility: visible;}

@media screen and (max-width: 576px) {
.cart-drawer__collection{display:none;}  
}
input#drawer-discount::placeholder{font-family: var(--font-body-family);}
div#CartDrawer-CartErrors {
    padding-inline: 1rem;
}