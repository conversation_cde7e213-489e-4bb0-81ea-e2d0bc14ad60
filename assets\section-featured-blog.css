.blog-placeholder {
  margin: 0 1.5rem;
  background: rgb(var(--color-background));
}


.blog__posts.articles-wrapper .article.blog-list-style .article-card-wrapper .card.article-card { width:100%; align-self: self-start; overflow: hidden; display: flex; flex-direction: row; gap: var(--grid-desktop-horizontal-spacing); }


/* new css code */
.blog__posts.articles-wrapper .article.blog-overlay-style .card__inner { position: absolute; left: 0; top: 0; height: 100%; overflow: hidden; z-index: 0; }
.blog__posts.articles-wrapper .article.blog-overlay-style { scroll-snap-align: start; position: relative; margin-left: 0; }
.blog__posts.articles-wrapper .blog__post .card--standard>.card__content .card__information { padding:20px 0; }
.featured-blog  .slider:not(.slider--everywhere):not(.slider--desktop)+.slider-buttons {  display: flex; align-items: center; justify-content: center; }
.featured-blog  .slider--tablet:after {  content: ""; width: 0; padding-left: 1.5rem; margin-left: calc(-1 * var(--grid-desktop-horizontal-spacing)); }
.featured-blog  .slider.slider--tablet.contains-card--standard .slider__slide:not(.collection-list__item--no-media) {  padding: var(--focus-outline-padding); }
.featured-blog  .slider.slider--tablet { position: relative; flex-wrap: inherit; overflow-x: auto; scroll-snap-type: x mandatory; scroll-behavior: smooth; scroll-padding-left: auto; }


.featured-blog .slider:not(.slider--everywhere):not(.slider--desktop)+.slider-buttons {
    display: flex;margin-top:30px;
}
.featured-blog .slider-buttons .slider-button {
    width: 30px;
    height: 30px;
    background: var(--gradient-base-background-1);
    box-shadow: 0 0 20px #00000026;
}
.featured-blog .slider-buttons .slider-button:hover {
    color: var(--gradient-base-background-1);
    background: var(--gradient-base-accent-1);
}
.featured-blog .slider-buttons .slider-button[disabled]:hover { background: var(--gradient-base-background-1);}
.blog__posts .blog__post.blog-overlay-style .card:not(.ratio)>.card__content { z-index: 1; grid-template-rows: max-content minmax(0,1fr) max-content auto; }



@media screen and (max-width: 576px) {
.page-width-desktop .grid--peek .grid__item {  min-width: 100%; }

}
@media screen and (max-width: 749px) {
 .page-width-desktop .grid--peek.slider .grid__item:first-of-type { margin-left: auto; }
}

@media screen and (max-width: 989px) {
.featured-blog .slider:not(.slider--everywhere):not(.slider--mobile)+.slider-buttons {  display: none; }
 
}


/* new css code */

@media screen and (min-width: 750px) {
  .blog-placeholder {
    text-align: center;
    width: 50%;
    margin: 0;
  }
}

.blog-placeholder__content {
  padding: 3rem;
  background: rgba(var(--color-foreground), 0.04);
}

.blog-placeholder .placeholder {
  position: relative;
}

.blog-placeholder h2 {
  margin: 0;
}

.blog-placeholder .rte-width {
  margin-top: 1.2rem;
  color: rgba(var(--color-foreground), 0.75);
}

@media screen and (min-width: 990px) {
  .grid--1-col-desktop .article-card .card__content {
    text-align: center;
  }
}

.blog__title {
  margin: 0;
}

.blog__posts.articles-wrapper {
  margin-bottom: 1rem;
}

@media screen and (min-width: 990px) {
  .blog__posts.articles-wrapper {
    margin-bottom: 0;
  }
}

.blog__posts.articles-wrapper .article {
  scroll-snap-align: start;
}

@media screen and (max-width: 749px) {
/*   .blog__post.article {
    width: calc(100% - 3rem);
  } */
  .blog__posts.articles-wrapper .article.blog-list-style .article-card-wrapper .card.article-card { flex-direction: column; gap: var(--grid-desktop-horizontal-spacing); }
  
}

.background-secondary .blog-placeholder__content {
  background-color: rgb(var(--color-background));
}

/* .blog__posts .card-wrapper {
  width: 100%;
}
 */

/* .blog .swiper-button-next:after, .blog .swiper-button-prev:after{display:none;}
.blog .swiper-button-next, .blog .swiper-button-prev{    
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: var(--gradient-base-background-1);
    transition: all 0.3s linear;} */

/* check for flexbox gap in older Safari versions */
@supports not (inset: 10px) {
  @media screen and (min-width: 750px) {
    .blog__posts .article + .article {
      margin-left: var(--grid-desktop-horizontal-spacing);
    }
  }
}
/*List type*/
/* @media screen and (min-width: 577px) {
.blog-list-style .card.article-card {
    display: flex;
    flex-direction: row;
}
}
@media screen and (max-width: 576px) {
.blog-list-style .card.article-card {
    display: flex;
    flex-direction:column;
}
} */

/*List type*/
@media screen and (min-width: 400px) {
.blog-list-style .card.article-card {
    display: flex;
    flex-direction: row;
}
}
.article-card__info span:not(:last-child):after {
    content: "|";
    display: inline-block;
    position: relative;
    padding: 0 15px;
}
.article-card__info span{display:flex; align-items:center; line-height:normal; padding:5px 0;   }
.article-card__info{display:flex; align-items:center; flex-wrap: wrap;}
.article-card__info span svg{    margin-right: 10px; width:1.8rem; height:1.8rem;}


@media screen and (max-width: 576px) {
.blog__posts.grid--peek.grid--1-col-tablet-down .grid__item {
    width: 100%;
    max-width: 100%;
}  
}
