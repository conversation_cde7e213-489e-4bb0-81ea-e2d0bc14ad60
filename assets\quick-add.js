if (!customElements.get('quick-add-modal')) {
  customElements.define('quick-add-modal', class QuickAddModal extends ModalDialog {
    constructor() {
      super();
      this.modalContent = this.querySelector('[id^="QuickAddInfo-"]');
    }

    hide(preventFocus = false) {
      const cartNotification = document.querySelector('cart-notification') || document.querySelector('cart-drawer');
      if (cartNotification) cartNotification.setActiveElement(this.openedBy);
      this.modalContent.innerHTML = '';

      if (preventFocus) this.openedBy = null;
      super.hide();
    }

    show(opener) {
      opener.setAttribute('aria-disabled', true);
      opener.classList.add('loading');
      const dtLoader = document.createElement('div');
      dtLoader.className = 'dT_loading';
     dtLoader.innerHTML = `<div class="loading"></div>`;
      //document.getElementById('MainContent').appendChild(dtLoader);    
    //  document.querySelector('.main-product_info').appendChild(dtLoader);    
      
      setTimeout( () => {
       	$('.quick-add-modal[open]').find('.main-product_info').addClass('quick-add-modal-in-progress');
        //$('.quick-add-modal[open]').addClass('test');
      }, 2000);//wait 2 seconds
       setTimeout( () => {
         $('.main-product_info').removeClass('quick-add-modal-in-progress');
      }, 4000);//wait 2 seconds
      opener.querySelector('.loading-overlay__spinner').classList.remove('hidden');
      fetch(opener.getAttribute('data-product-url'))
        .then((response) => response.text())
        .then((responseText) => {
          const responseHTML = new DOMParser().parseFromString(responseText, 'text/html');
          this.productElement = responseHTML.querySelector('section[id^="MainProduct-"]');
          this.preventDuplicatedIDs();        
          this.removeDOMElements();
          this.setInnerHTML(this.modalContent, this.productElement.innerHTML);
          if (window.Shopify && Shopify.PaymentButton) {
            Shopify.PaymentButton.init();
          }

          if (window.ProductModel) window.ProductModel.loadShopifyXR();

          this.removeGalleryListSemantic();         
          this.preventVariantURLSwitching();
          super.show(opener);
        })
        .finally(() => {
          opener.removeAttribute('aria-disabled');
          opener.classList.remove('loading');
          opener.querySelector('.loading-overlay__spinner').classList.add('hidden');   
         // document.querySelector('.dT_loading').classList.add('hidden');           
          const hideDtLoad = document.querySelectorAll('.dT_loading.hidden');
         // hideDtLoad.forEach(div => {
          //  div.remove();
        //  });
        });
    }

    setInnerHTML(element, html) {
      element.innerHTML = html;

      // Reinjects the script tags to allow execution. By default, scripts are disabled when using element.innerHTML.
      element.querySelectorAll('script').forEach(oldScriptTag => {
        const newScriptTag = document.createElement('script');
        Array.from(oldScriptTag.attributes).forEach(attribute => {
          newScriptTag.setAttribute(attribute.name, attribute.value)
        });
        newScriptTag.appendChild(document.createTextNode(oldScriptTag.innerHTML));
        oldScriptTag.parentNode.replaceChild(newScriptTag, oldScriptTag);
      });
    }

    preventVariantURLSwitching() {
    const variantPicker = this.modalContent.querySelector('variant-radios,variant-selects');
     if (!variantPicker) return;      
     variantPicker.setAttribute('data-update-url', 'false');
      
      $('input:radio[name="Color"]').click(function() {      
      $('input:radio[name="Color"]').next('.swatch-element').removeClass('clicked');
      $(this).next('.swatch-element').addClass('clicked');
          });
    }

    removeDOMElements() {
      const pickupAvailability = this.productElement.querySelector('pickup-availability');
      if (pickupAvailability) pickupAvailability.remove();

      const productModal = this.productElement.querySelector('product-modal');
      if (productModal) productModal.remove();      
       
      const productEnquiry = this.productElement.querySelector('enquiryPopUp');
      if (productEnquiry) productEnquiry.remove();  

      const modalDialog = this.productElement.querySelectorAll('modal-dialog');
        if (modalDialog) modalDialog.forEach((modal) => modal.remove());
      
    }

    preventDuplicatedIDs() {
      const sectionId = this.productElement.dataset.section;
      this.productElement.innerHTML = this.productElement.innerHTML.replaceAll(sectionId, `quickadd-${ sectionId }`);
      this.productElement.querySelectorAll('variant-selects, variant-radios').forEach((variantSelect) => {
        variantSelect.dataset.originalSection = sectionId;
      });      
          
    }

    removeGalleryListSemantic() {      
      const galleryList = this.modalContent.querySelector('[id^="Slider-Gallery"]');      
      if (!galleryList) return;     
      galleryList.setAttribute('role', 'presentation');
      galleryList.querySelectorAll('[id^="Slide-"]').forEach(li => li.setAttribute('role', 'presentation'));
      

      const FBT = this.modalContent.querySelector('[id^="dT_bundleSelector"]');        
      if (FBT) FBT.remove();
      const Tabs = this.modalContent.querySelector('[class^="product__info-as-bottom-tabs"]');
      if (Tabs) Tabs.remove();
      
    }

  });
}
