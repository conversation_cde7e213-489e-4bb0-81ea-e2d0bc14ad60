{% comment %}
WARNING: UNAUTHORIZED MODIFICATION PROHIBITED
This code is proprietary property of Versio Studio. Any unauthorized tampering, 
alteration, or distribution of this file is strictly forbidden and will be prosecuted to the maximum extent permitted by law.
All modifications are logged.

If you require assistance or have questions regarding this code, <NAME_EMAIL>.
{% endcomment %}

{%- liquid
  assign text_alignment = 'center'
  assign blocks_alignment = 'left'
  assign desktop_blocks_gap = section.settings.desktop_blocks_gap
  assign mobile_blocks_gap = section.settings.mobile_blocks_gap
  assign block_padding = section.settings.block_padding
  assign block_elements_gap = section.settings.block_elements_gap
  assign header_blocks_gap = section.settings.header_blocks_gap
  assign stars_title_gap = section.settings.stars_title_gap
  assign rating_container_gap = section.settings.rating_container_gap

  assign enable_fade_effect = section.settings.enable_fade_effect

  assign block_count = section.blocks.size
-%}

{%- style -%}
  {{ section.settings.title_font | font_face: font_display: 'swap' }}
  {{ section.settings.block_title_font | font_face: font_display: 'swap' }}
  {{ section.settings.block_description_font | font_face: font_display: 'swap' }}

  {% if section.settings.custom_title_font %}
    #Star-Cards-{{ section.id }} .vso-star-cards__title {
      font-family: {{ section.settings.title_font.family }}, {{ section.settings.title_font.fallback_families }};
      font-style: {{ section.settings.title_font.style }};
    }
  {% endif %}

  {% if section.settings.custom_block_title_font %}
    #Star-Cards-{{ section.id }} .vso-star-cards__block-title {
      font-family: {{ section.settings.block_title_font.family }}, {{ section.settings.block_title_font.fallback_families }};
      font-style: {{ section.settings.block_title_font.style }};
    }
  {% endif %}

  {% if section.settings.custom_block_description_font %}
    #Star-Cards-{{ section.id }} .vso-star-cards__block-description {
      font-family: {{ section.settings.block_description_font.family }}, {{ section.settings.block_description_font.fallback_families }};
      font-style: {{ section.settings.block_description_font.style }};
    }
  {% endif %}

  .vso-star-cards {
    padding: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .vso-star-cards__title {
    margin: 0;
    line-height: {{ section.settings.title_line_height }}%;
    font-size: {{ section.settings.title_size_desktop }}px;
    text-align: center;
    color: {{ section.settings.title_color }};
    font-weight: {{ section.settings.title_font_weight }};
  }

  .vso-star-cards__header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: {{ header_blocks_gap }}px;
    width: 100%;
  }

  .vso-star-cards__rating-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: {{ rating_container_gap }}px;
    margin-top: {{ section.settings.rating_container_top_spacing }}px;
    margin-bottom: {{ section.settings.rating_container_bottom_spacing }}px;
    background-color: {{ section.settings.rating_container_bg_color }};
    padding: {{ section.settings.rating_container_padding_vertical }}px {{ section.settings.rating_container_padding_horizontal }}px;
    border-radius: {{ section.settings.rating_container_border_radius }}px;
    {% if section.settings.rating_container_shadow_enable %}
      box-shadow: {{ section.settings.rating_container_shadow_color | color_modify: 'alpha', 0.24 }} 0px 3px 8px;
    {% endif %}
  }

  .vso-star-cards__rating-text {
    margin: 0;
    font-size: {{ section.settings.rating_text_size_desktop }}px;
    color: {{ section.settings.rating_text_color }};
    line-height: {{ section.settings.rating_text_line_height }}%;
    font-weight: {{ section.settings.rating_text_font_weight }};
  }

  .vso-star-cards__rating-value-container {
    display: flex;
    align-items: baseline;
    gap: 4px;
  }

  .vso-star-cards__rating-number {
    margin: 0;
    font-size: {{ section.settings.rating_text_size_desktop | times: 1.2 | round: 0 }}px;
    color: {{ section.settings.rating_number_color }};
    line-height: {{ section.settings.rating_text_line_height }}%;
    font-weight: {{ section.settings.rating_number_font_weight }};
  }

  .vso-star-cards__rating-value-text {
    margin: 0;
    font-size: {{ section.settings.rating_text_size_desktop }}px;
    color: {{ section.settings.rating_text_color }};
    line-height: {{ section.settings.rating_text_line_height }}%;
    font-weight: {{ section.settings.rating_value_text_font_weight }};
  }

  .vso-star-cards__rating-stars {
    display: flex;
    gap: 0px;
    align-items: center;
  }

  .vso-star-cards__rating-star {
    width: {{ section.settings.rating_star_size_desktop }}px;
    height: {{ section.settings.rating_star_size_desktop }}px;
  }

  .vso-star-cards__rating-star svg {
    width: 100%;
    height: 100%;
  }

  .vso-star-cards__description {
    margin: 0;
    font-size: {{ section.settings.description_size_desktop }}px;
    color: {{ section.settings.description_color }};
    line-height: {{ section.settings.description_line_height }}%;
    text-align: center;
    margin-top: {{ section.settings.description_top_spacing }}px;
    margin-bottom: {{ section.settings.description_bottom_spacing }}px;
    max-width: {{ section.settings.description_max_width }}px;
    margin-left: auto;
    margin-right: auto;
    padding: 0 20px;
  }

  .vso-star-cards__swiper {
    width: 100%;
    padding: 50px 0px !important;
    overflow: visible;
    position: relative;
  }


  .vso-node { display: none; }
  .vso-node--v2 { display: block; }

  @media screen and (min-width: 750px) {
    {% if enable_fade_effect %}
      .vso-star-cards__swiper {
        mask: linear-gradient(to right, transparent 0%, black 10%, black 90%, transparent 100%);
        -webkit-mask: linear-gradient(to right, transparent 0%, black 10%, black 90%, transparent 100%);
      }
    {% endif %}
  }
  @media screen and (max-width: 749px) {
    {% if section.settings.enable_fade_effect_mobile %}
      .vso-star-cards__swiper {
        mask: linear-gradient(to right, transparent 0%, black 10%, black 90%, transparent 100%);
        -webkit-mask: linear-gradient(to right, transparent 0%, black 10%, black 90%, transparent 100%);
      }
    {% endif %}
  }

  {% if section.settings.enable_equal_height %}
    .vso-star-cards__swiper {
      height: auto;
    }

    .vso-star-cards__swiper .swiper-wrapper {
      align-items: stretch;
      display: flex;
    }

    .vso-star-cards__swiper .swiper-slide {
      height: auto;
      display: flex;
      flex-direction: column;
    }

    .vso-star-cards__block {
      justify-content: flex-start;
    }

    .vso-star-cards__author-container {
      margin-top: auto;
    }
  {% endif %}

  .vso-star-cards__slide {
    width: auto;
    max-width: {{ section.settings.desktop_card_width }}px;
    min-width: 280px;
    {% if section.settings.enable_equal_height %}
      display: flex;
      height: 100%;
    {% endif %}
  }

  .vso-star-cards__block {
    display: flex;
    flex-direction: column;
    align-items: {{ blocks_alignment }};
    padding: {{ block_padding }}px;
    background-color: {{ section.settings.block_background_color }};
    border-radius: {{ section.settings.block_border_radius }}px;
    border: {{ section.settings.block_border_width }}px solid {{ section.settings.block_border_color }};
    gap: {{ block_elements_gap }}px;
    text-align: {{ blocks_alignment }};
    transition: transform 0.3s ease;
    {% if section.settings.enable_card_shadow %}
      box-shadow: {{ section.settings.card_shadow_color | color_modify: 'alpha', 0.24 }} 0px 3px 8px;
    {% endif %}
    {% if section.settings.enable_equal_height %}
      width: 100%;
      height: 100%;
    {% endif %}
  }

  @media screen and (min-width: 750px) {
    .swiper-slide-active .vso-star-cards__block,
    .swiper-slide-duplicate-active .vso-star-cards__block {
      transform: scale(1.1);
    }
  }

  .vso-star-cards__header-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: {{ blocks_alignment }};
    gap: {{ stars_title_gap }}px;
  }

  .vso-star-cards__stars {
    display: flex;
    gap: 0px;
    margin-bottom: 0px;
  }

  .vso-star-cards__star {
    width: {{ section.settings.star_size }}px;
    height: {{ section.settings.star_size }}px;
  }

  .vso-star-cards__star svg {
    width: 100%;
    height: 100%;
  }

  .vso-star-cards__star svg g#SVGRepo_bgCarrier rect {
    fill: {{ section.settings.star_background_color }};
  }

  .vso-star-cards__star svg path {
    fill: {{ section.settings.star_color }};
  }

  .vso-star-cards__block-title {
    width: 100%;
    margin: 0;
    line-height: {{ section.settings.block_title_line_height }}%;
    font-size: {{ section.settings.block_title_size_desktop }}px;
    color: {{ section.settings.block_title_color }};
    font-weight: {{ section.settings.block_title_font_weight }};
  }

  .vso-star-cards__block-description {
    width: 100%;
    margin: 0;
    line-height: {{ section.settings.block_description_line_height }}%;
    font-size: {{ section.settings.block_description_size_desktop }}px;
    color: {{ section.settings.block_description_color }};
    position: relative;
    padding-left: {{ section.settings.description_line_spacing }}px;
    font-weight: {{ section.settings.block_description_font_weight }};
  }

  .vso-star-cards__block-description::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: {{ section.settings.description_line_width }}px;
    background-color: {{ section.settings.description_line_color }};
    border-radius: 10px;
    height: 100%;
  }

  .vso-star-cards__author-container {
    width: 100%;
    display: flex;
    align-items: center;
    gap: {{ section.settings.author_container_gap }}px;
  }

  .vso-star-cards__author-circle {
    width: {{ section.settings.author_circle_size }}px;
    height: {{ section.settings.author_circle_size }}px;
    border-radius: 50%;
    background-color: {{ section.settings.author_circle_background_color }};
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: {{ section.settings.author_circle_text_size }}px;
    font-weight: 600;
    color: {{ section.settings.author_circle_text_color }};
    flex-shrink: 0;
  }

  .vso-star-cards__author-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
    flex: 1;
  }

  .vso-star-cards__block-author {
    width: 100%;
    margin: 0;
    line-height: {{ section.settings.block_author_line_height }}%;
    font-size: {{ section.settings.block_author_size_desktop }}px;
    color: {{ section.settings.block_author_color }};
    font-weight: {{ section.settings.block_author_font_weight }};
  }

  .vso-star-cards__block-verified {
    width: 100%;
    margin: 0;
    line-height: {{ section.settings.block_verified_line_height }}%;
    font-size: {{ section.settings.block_verified_size_desktop }}px;
    color: {{ section.settings.block_verified_color }};
    font-weight: {{ section.settings.block_verified_font_weight }};
  }

  @media screen and (max-width: 749px) {
    .vso-star-cards__swiper {
      padding: 30px 0px !important;
    }

    .vso-star-cards__slide {
      min-width: 260px;
      max-width: 300px;
    }

    .vso-star-cards__title {
      font-size: {{ section.settings.title_size_mobile }}px;
    }

    .vso-star-cards__block-title {
      font-size: {{ section.settings.block_title_size_mobile }}px;
    }

    .vso-star-cards__block-description {
      font-size: {{ section.settings.block_description_size_mobile }}px;
    }

    .vso-star-cards__block-author {
      font-size: {{ section.settings.block_author_size_mobile }}px;
    }

    .vso-star-cards__block-verified {
      font-size: {{ section.settings.block_verified_size_mobile }}px;
    }

    .vso-star-cards__author-circle {
      width: {{ section.settings.author_circle_size_mobile }}px;
      height: {{ section.settings.author_circle_size_mobile }}px;
      font-size: {{ section.settings.author_circle_text_size_mobile }}px;
    }

    .vso-star-cards__rating-text {
      font-size: {{ section.settings.rating_text_size_mobile }}px;
    }

    .vso-star-cards__rating-number {
      font-size: {{ section.settings.rating_text_size_mobile | times: 1.2 | round: 0 }}px;
    }

    .vso-star-cards__rating-value-text {
      font-size: {{ section.settings.rating_text_size_mobile }}px;
    }

    .vso-star-cards__rating-star {
      width: {{ section.settings.rating_star_size_mobile }}px;
      height: {{ section.settings.rating_star_size_mobile }}px;
    }

    .vso-star-cards__description {
      font-size: {{ section.settings.description_size_mobile }}px;
    }
  }

  .section-{{ section.id }} {
    padding-top: {{ section.settings.padding_top_desktop }}px;
    padding-bottom: {{ section.settings.padding_bottom_desktop }}px;
    margin-top: {{ section.settings.margin_top_desktop }}px;
    margin-bottom: {{ section.settings.margin_bottom_desktop }}px;
    padding-left: 0;
    padding-right: 0;
  }

  @media screen and (max-width: 749px) {
    .section-{{ section.id }} {
      padding-top: {{ section.settings.padding_top_mobile }}px;
      padding-bottom: {{ section.settings.padding_bottom_mobile }}px;
      margin-top: {{ section.settings.margin_top_mobile }}px;
      margin-bottom: {{ section.settings.margin_bottom_mobile }}px;
      padding-left: 0;
      padding-right: 0;
    }
  }

  .vso-star-cards__navigation {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: {{ section.settings.nav_arrow_gap }}px;
    margin-top: {{ section.settings.nav_arrow_top_spacing }}px;
  }
  @media screen and (min-width: 750px) {
    {% unless section.settings.show_navigation_desktop %}
      .vso-star-cards__navigation {
        display: none;
      }
    {% endunless %}
  }

  @media screen and (max-width: 749px) {
    {% unless section.settings.show_navigation_mobile %}
      .vso-star-cards__navigation {
        display: none;
      }
    {% endunless %}
  }

  .vso-star-cards__nav-prev,
  .vso-star-cards__nav-next {
    width: {{ section.settings.nav_arrow_size }}px;
    height: {{ section.settings.nav_arrow_size }}px;
    background-color: {{ section.settings.nav_arrow_bg_color }};
    border: {{ section.settings.nav_arrow_border_width }}px solid {{ section.settings.nav_arrow_border_color }};
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    {% if section.settings.nav_arrow_shadow_enable %}
      box-shadow: {{ section.settings.nav_arrow_shadow_color | color_modify: 'alpha', 0.24 }} 0px 3px 8px;
    {% endif %}
  }

  .vso-star-cards__nav-prev svg,
  .vso-star-cards__nav-next svg {
    width: {{ section.settings.nav_arrow_size | times: 0.4 | round: 0 }}px;
    height: {{ section.settings.nav_arrow_size | times: 0.4 | round: 0 }}px;
    color: {{ section.settings.nav_arrow_icon_color }};
    transition: color 0.3s ease;
  }

  .vso-star-cards__nav-prev:hover,
  .vso-star-cards__nav-next:hover {
    border-color: {{ section.settings.nav_arrow_border_color | color_darken: 10 }};
    {% if section.settings.nav_arrow_shadow_enable %}
      box-shadow: {{ section.settings.nav_arrow_shadow_color | color_modify: 'alpha', 0.32 }} 0px 4px 12px;
    {% endif %}
  }

  .vso-star-cards__nav-prev:hover svg,
  .vso-star-cards__nav-next:hover svg {
    color: {{ section.settings.nav_arrow_icon_color | color_darken: 20 }};
  }

  .vso-star-cards__nav-prev.swiper-button-disabled,
  .vso-star-cards__nav-next.swiper-button-disabled {
    opacity: 0.4;
    cursor: not-allowed;
  }
{%- endstyle -%}



<style>
  #shopify-section-{{ section.id }} {
    {% if section.settings.background_style == 'gradient' %}
      background: linear-gradient(to bottom, {{ section.settings.gradient_start_color }}, {{ section.settings.gradient_end_color }});
    {% else %}
      background-color: {{ section.settings.background_color }};
    {% endif %}
    width: 100%;
    max-width: 100%;
  }
</style>

<div class="vso-node section-{{ section.id }}" style="{% if section.settings.background_style == 'gradient' %}background: linear-gradient(to bottom, {{ section.settings.gradient_start_color }}, {{ section.settings.gradient_end_color }});{% else %}background-color: {{ section.settings.background_color }};{% endif %}">
  <div class="vso-star-cards" id="Star-Cards-{{ section.id }}">
        {% if section.settings.title != blank %}
          <div class="vso-star-cards__header">
            <h2 class="vso-star-cards__title">
              {{ section.settings.title }}
            </h2>
          </div>
        {% endif %}

        {% unless section.settings.rating_text == blank and section.settings.rating_value == 0 and section.settings.rating_star_count == 0 %}
          <div class="vso-star-cards__rating-container">
            {% unless section.settings.rating_text == blank %}
              <span class="vso-star-cards__rating-text">{{ section.settings.rating_text }}</span>
            {% endunless %}
            {% if section.settings.rating_value > 0 %}
              <div class="vso-star-cards__rating-value-container">
                <span class="vso-star-cards__rating-number">{{ section.settings.rating_value }}</span>
                <span class="vso-star-cards__rating-value-text">/</span>
                <span class="vso-star-cards__rating-value-text">5</span>
              </div>
            {% endif %}
            {% if section.settings.rating_star_count > 0 %}
              <div class="vso-star-cards__rating-stars">
                                 {% for i in (1..5) %}
                   <div class="vso-star-cards__rating-star">
                     {% assign star_index = forloop.index %}
                     {% assign filled = false %}
                     {% assign half_filled = false %}
                     {% assign half_threshold = star_index | minus: 0.5 %}
                     {% if section.settings.rating_star_count >= star_index %}
                       {% assign filled = true %}
                     {% elsif section.settings.rating_star_count >= half_threshold %}
                       {% assign half_filled = true %}
                     {% endif %}
                    
                    <svg fill="{{ section.settings.rating_star_color }}" viewBox="-3.2 -3.2 38.40 38.40" xmlns="http://www.w3.org/2000/svg">
                      {% if half_filled %}
                        <defs>
                          <linearGradient id="ratingGrad{{ section.id }}{{ star_index }}">
                            <stop offset="0%" stop-color="{{ section.settings.rating_star_filled_color }}" />
                            <stop offset="50%" stop-color="{{ section.settings.rating_star_filled_color }}" />
                            <stop offset="50%" stop-color="{{ section.settings.rating_star_empty_color }}" />
                            <stop offset="100%" stop-color="{{ section.settings.rating_star_empty_color }}" />
                          </linearGradient>
                        </defs>
                      {% endif %}
                      <g id="SVGRepo_bgCarrier" stroke-width="0" transform="translate(3.2,3.2), scale(0.8)">
                        <rect x="-3.2" y="-3.2" width="38.40" height="38.40" rx="0" fill="{% if half_filled %}url(#ratingGrad{{ section.id }}{{ star_index }}){% elsif filled %}{{ section.settings.rating_star_filled_color }}{% else %}{{ section.settings.rating_star_empty_color }}{% endif %}" stroke-width="0"></rect>
                      </g>
                      <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" stroke="#CCCCCC" stroke-width="0.256"></g>
                      <g id="SVGRepo_iconCarrier">
                        <path d="M16 4.588l2.833 8.719H28l-7.416 5.387 2.832 8.719L16 22.023l-7.417 5.389 2.833-8.719L4 13.307h9.167L16 4.588z"></path>
                      </g>
                    </svg>
                  </div>
                  {% assign half_filled = false %}
                {% endfor %}
              </div>
            {% endif %}
          </div>
        {% endunless %}

        {% if section.settings.description != blank %}
          <p class="vso-star-cards__description">
            {{ section.settings.description }}
          </p>
        {% endif %}

        {% if section.blocks.size > 0 %}
          <div class="swiper vso-star-cards__swiper">
            <div class="swiper-wrapper">
              {% for block in section.blocks %}
                <div class="swiper-slide vso-star-cards__slide">
                  <div class="vso-star-cards__block" {{ block.shopify_attributes }}>
                    <div class="vso-star-cards__header-content">
                      <div class="vso-star-cards__stars">
                        {% for i in (1..5) %}
                          <div class="vso-star-cards__star">
                            <svg fill="{{ section.settings.star_color }}" viewBox="-3.2 -3.2 38.40 38.40" xmlns="http://www.w3.org/2000/svg">
                              <g id="SVGRepo_bgCarrier" stroke-width="0" transform="translate(3.2,3.2), scale(0.8)">
                                <rect x="-3.2" y="-3.2" width="38.40" height="38.40" rx="0" fill="{{ section.settings.star_background_color }}" stroke-width="0"></rect>
                              </g>
                              <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" stroke="#CCCCCC" stroke-width="0.256"></g>
                              <g id="SVGRepo_iconCarrier">
                                <path d="M16 4.588l2.833 8.719H28l-7.416 5.387 2.832 8.719L16 22.023l-7.417 5.389 2.833-8.719L4 13.307h9.167L16 4.588z"></path>
                              </g>
                            </svg>
                          </div>
                        {% endfor %}
                      </div>

                      {% if block.settings.title != blank %}
                        <h3 class="vso-star-cards__block-title">
                          {{ block.settings.title }}
                        </h3>
                      {% endif %}
                    </div>

                    {% if block.settings.description != blank %}
                      <p class="vso-star-cards__block-description">
                        {{ block.settings.description }}
                      </p>
                    {% endif %}

                    {% if block.settings.author != blank or block.settings.verified_text != blank %}
                      <div class="vso-star-cards__author-container">
                        {% if block.settings.author != blank %}
                          {% assign first_initial = block.settings.author | slice: 0, 1 | upcase %}
                          <div class="vso-star-cards__author-circle">
                            {{ first_initial }}
                          </div>
                        {% endif %}
                        <div class="vso-star-cards__author-info">
                          {% if block.settings.author != blank %}
                            <p class="vso-star-cards__block-author">
                              {{ block.settings.author }}
                            </p>
                          {% endif %}
                          {% if block.settings.verified_text != blank %}
                            <p class="vso-star-cards__block-verified">
                              {{ block.settings.verified_text }}
                            </p>
                          {% endif %}
                        </div>
                      </div>
                    {% endif %}
                  </div>
                </div>
              {% endfor %}
              
              {% for block in section.blocks %}
                <div class="swiper-slide vso-star-cards__slide">
                  <div class="vso-star-cards__block">
                    <div class="vso-star-cards__header-content">
                      <div class="vso-star-cards__stars">
                        {% for i in (1..5) %}
                          <div class="vso-star-cards__star">
                            <svg fill="{{ section.settings.star_color }}" viewBox="-3.2 -3.2 38.40 38.40" xmlns="http://www.w3.org/2000/svg">
                              <g id="SVGRepo_bgCarrier" stroke-width="0" transform="translate(3.2,3.2), scale(0.8)">
                                <rect x="-3.2" y="-3.2" width="38.40" height="38.40" rx="0" fill="{{ section.settings.star_background_color }}" stroke-width="0"></rect>
                              </g>
                              <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" stroke="#CCCCCC" stroke-width="0.256"></g>
                              <g id="SVGRepo_iconCarrier">
                                <path d="M16 4.588l2.833 8.719H28l-7.416 5.387 2.832 8.719L16 22.023l-7.417 5.389 2.833-8.719L4 13.307h9.167L16 4.588z"></path>
                              </g>
                            </svg>
                          </div>
                        {% endfor %}
                      </div>

                      {% if block.settings.title != blank %}
                        <h3 class="vso-star-cards__block-title">
                          {{ block.settings.title }}
                        </h3>
                      {% endif %}
                    </div>

                    {% if block.settings.description != blank %}
                      <p class="vso-star-cards__block-description">
                        {{ block.settings.description }}
                      </p>
                    {% endif %}

                    {% if block.settings.author != blank or block.settings.verified_text != blank %}
                      <div class="vso-star-cards__author-container">
                        {% if block.settings.author != blank %}
                          {% assign first_initial = block.settings.author | slice: 0, 1 | upcase %}
                          <div class="vso-star-cards__author-circle">
                            {{ first_initial }}
                          </div>
                        {% endif %}
                        <div class="vso-star-cards__author-info">
                          {% if block.settings.author != blank %}
                            <p class="vso-star-cards__block-author">
                              {{ block.settings.author }}
                            </p>
                          {% endif %}
                          {% if block.settings.verified_text != blank %}
                            <p class="vso-star-cards__block-verified">
                              {{ block.settings.verified_text }}
                            </p>
                          {% endif %}
                        </div>
                      </div>
                    {% endif %}
                  </div>
                </div>
              {% endfor %}
            </div>
          </div>

          <div class="vso-star-cards__navigation">
            <div class="vso-star-cards__nav-prev">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="transform: scaleX(-1);">
                <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                <g id="SVGRepo_iconCarrier">
                  <path fill-rule="evenodd" clip-rule="evenodd" d="M8.29289 4.29289C8.68342 3.90237 9.31658 3.90237 9.70711 4.29289L16.7071 11.2929C17.0976 11.6834 17.0976 12.3166 16.7071 12.7071L9.70711 19.7071C9.31658 20.0976 8.68342 20.0976 8.29289 19.7071C7.90237 19.3166 7.90237 18.6834 8.29289 18.2929L14.5858 12L8.29289 5.70711C7.90237 5.31658 7.90237 4.68342 8.29289 4.29289Z" fill="currentColor"></path>
                </g>
              </svg>
            </div>
            <div class="vso-star-cards__nav-next">
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                <g id="SVGRepo_iconCarrier">
                  <path fill-rule="evenodd" clip-rule="evenodd" d="M8.29289 4.29289C8.68342 3.90237 9.31658 3.90237 9.70711 4.29289L16.7071 11.2929C17.0976 11.6834 17.0976 12.3166 16.7071 12.7071L9.70711 19.7071C9.31658 20.0976 8.68342 20.0976 8.29289 19.7071C7.90237 19.3166 7.90237 18.6834 8.29289 18.2929L14.5858 12L8.29289 5.70711C7.90237 5.31658 7.90237 4.68342 8.29289 4.29289Z" fill="currentColor"></path>
                </g>
              </svg>
            </div>
          </div>
        {% endif %}
  </div>
</div>

<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
<script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js" defer></script>

<script defer>
document.addEventListener('DOMContentLoaded', function() {
  const starCardsSwiper = new Swiper('.vso-star-cards__swiper', {
    loop: true,
    slidesPerView: 'auto',
    centeredSlides: true,
    spaceBetween: {{ desktop_blocks_gap }},
    grabCursor: true,
    speed: 600,
    
    {% if section.settings.enable_equal_height %}
    watchSlidesProgress: true,
    {% endif %}
    
    navigation: {
      nextEl: '.vso-star-cards__nav-next',
      prevEl: '.vso-star-cards__nav-prev',
    },
    
    breakpoints: {
      0: {
        spaceBetween: {{ mobile_blocks_gap }},
      },
      750: {
        spaceBetween: {{ desktop_blocks_gap }},
      }
    }{% if section.settings.enable_equal_height %},
    
    on: {
      init: function() {
        setTimeout(() => {
          this.updateSlidesClasses();
          equalizeSlideHeights();
        }, 100);
      },
      slideChangeTransitionEnd: function() {
        equalizeSlideHeights();
      }
    }{% endif %}
  });

  {% if section.settings.enable_equal_height %}
  function equalizeSlideHeights() {
    const slides = document.querySelectorAll('.vso-star-cards__swiper .swiper-slide');
    const blocks = document.querySelectorAll('.vso-star-cards__swiper .vso-star-cards__block');
    
    if (slides.length === 0 || blocks.length === 0) return;
    
    blocks.forEach(block => {
      block.style.height = 'auto';
    });
    
    let maxHeight = 0;
    blocks.forEach(block => {
      const height = block.offsetHeight;
      if (height > maxHeight) {
        maxHeight = height;
      }
    });
    
    blocks.forEach(block => {
      block.style.height = maxHeight + 'px';
    });
  }

  window.addEventListener('resize', function() {
    setTimeout(equalizeSlideHeights, 100);
  });
  {% endif %}
});
</script>

<script>
(function(){
  const sectionId = '{{ section.id }}';
  const outer = document.getElementById('shopify-section-' + sectionId);
  if(outer){
    outer.classList.add('section--full-width');
  }
})();
</script>

{% schema %}
{
  "name": "VSO - Testimonials 02",
  "tag": "section",
  "class": "section",
  "settings": [
    {
    "type": "header",
    "content": "Gap between cards"
    },
    {
      "type": "range",
      "id": "desktop_blocks_gap",
      "min": 16,
      "max": 200,
      "step": 4,
      "unit": "px",
      "label": "Gap between desktop cards",
      "default": 32
    },
    {
      "type": "range",
      "id": "mobile_blocks_gap",
      "min": 8,
      "max": 200,
      "step": 4,
      "unit": "px",
      "label": "Gap between mobile cards",
      "default": 16
    },
    {
      "type": "header",
      "content": "Section Title"
    },
    {
      "type": "inline_richtext",
      "id": "title",
      "label": "Title",
      "default": "Customer Reviews"
    },
    {
      "type": "range",
      "id": "title_size_desktop",
      "min": 20,
      "max": 80,
      "step": 2,
      "unit": "px",
      "label": "Title size (Desktop)",
      "default": 40
    },
    {
      "type": "range",
      "id": "title_size_mobile",
      "min": 16,
      "max": 60,
      "step": 2,
      "unit": "px",
      "label": "Title size (Mobile)",
      "default": 32
    },
    {
      "type": "checkbox",
      "id": "custom_title_font",
      "label": "Use custom title font",
      "default": false
    },
    {
      "type": "font_picker",
      "id": "title_font",
      "label": "Title font",
      "default": "helvetica_n4"
    },
    {
      "type": "color",
      "id": "title_color",
      "label": "Title color",
      "default": "#000000"
    },
    {
      "type": "range",
      "id": "title_line_height",
      "min": 50,
      "max": 200,
      "step": 10,
      "unit": "%",
      "label": "Title line height",
      "default": 130
    },
    {
      "type": "range",
      "id": "title_font_weight",
      "min": 100,
      "max": 900,
      "step": 100,
      "label": "Title font weight",
      "default": 700
    },
    {
      "type": "header",
      "content": "Section Description"
    },
    {
      "type": "inline_richtext",
      "id": "description",
      "label": "Description",
      "default": "Based on <strong>1 046 </strong>verified reviews"
    },
    {
      "type": "range",
      "id": "description_size_desktop",
      "min": 14,
      "max": 38,
      "step": 1,
      "unit": "px",
      "label": "Description size (Desktop)",
      "default": 20
    },
    {
      "type": "range",
      "id": "description_size_mobile",
      "min": 12,
      "max": 34,
      "step": 1,
      "unit": "px",
      "label": "Description size (Mobile)",
      "default": 18
    },
    {
      "type": "range",
      "id": "description_line_height",
      "min": 120,
      "max": 200,
      "step": 10,
      "unit": "%",
      "label": "Description line height",
      "default": 150
    },
    {
      "type": "color",
      "id": "description_color",
      "label": "Description color",
      "default": "#0B0B0B"
    },
    {
      "type": "range",
      "id": "description_max_width",
      "min": 400,
      "max": 1000,
      "step": 50,
      "unit": "px",
      "label": "Description max width",
      "default": 600
    },
    {
      "type": "range",
      "id": "description_top_spacing",
      "min": 0,
      "max": 60,
      "step": 1,
      "unit": "px",
      "label": "Description top spacing",
      "default": 15
    },
    {
      "type": "range",
      "id": "description_bottom_spacing",
      "min": 0,
      "max": 80,
      "step": 4,
      "unit": "px",
      "label": "Description bottom spacing",
      "default": 0
    },
    {
      "type": "header",
      "content": "Rating Display"
    },
    {
      "type": "text",
      "id": "rating_text",
      "label": "Rating text",
      "default": "Excellent"
    },
    {
      "type": "range",
      "id": "rating_value",
      "min": 0,
      "max": 5,
      "step": 0.1,
      "label": "Rating value",
      "default": 4.6,
      "info": "Set to 0 to hide the rating number"
    },
    {
      "type": "range",
      "id": "rating_star_count",
      "min": 0,
      "max": 5,
      "step": 0.5,
      "label": "Number of filled stars",
      "default": 4.5,
      "info": "Set to 0 to hide stars"
    },
    {
      "type": "header",
      "content": "Rating Typography"
    },
    {
      "type": "range",
      "id": "rating_text_size_desktop",
      "min": 12,
      "max": 48,
      "step": 1,
      "unit": "px",
      "label": "Rating text size (Desktop)",
      "default": 22
    },
    {
      "type": "range",
      "id": "rating_text_size_mobile",
      "min": 12,
      "max": 34,
      "step": 1,
      "unit": "px",
      "label": "Rating text size (Mobile)",
      "default": 20
    },
    {
      "type": "range",
      "id": "rating_text_line_height",
      "min": 100,
      "max": 160,
      "step": 10,
      "unit": "%",
      "label": "Rating text line height",
      "default": 120
    },
    {
      "type": "range",
      "id": "rating_text_font_weight",
      "min": 100,
      "max": 900,
      "step": 100,
      "label": "Rating text font weight",
      "default": 400
    },
    {
      "type": "color",
      "id": "rating_text_color",
      "label": "Rating text color",
      "default": "#121212"
    },
    {
      "type": "color",
      "id": "rating_number_color",
      "label": "Rating number color",
      "default": "#121212"
    },
    {
      "type": "header",
      "content": "Rating Stars"
    },
    {
      "type": "range",
      "id": "rating_star_size_desktop",
      "min": 16,
      "max": 48,
      "step": 1,
      "unit": "px",
      "label": "Rating star size (Desktop)",
      "default": 32
    },
    {
      "type": "range",
      "id": "rating_star_size_mobile",
      "min": 14,
      "max": 48,
      "step": 1,
      "unit": "px",
      "label": "Rating star size (Mobile)",
      "default": 24
    },
    {
      "type": "color",
      "id": "rating_star_filled_color",
      "label": "Rating star filled color",
      "default": "#05B67C"
    },
    {
      "type": "color",
      "id": "rating_star_empty_color",
      "label": "Rating star empty color",
      "default": "#e5e7eb"
    },
    {
      "type": "color",
      "id": "rating_star_color",
      "label": "Rating star icon color",
      "default": "#ffffff"
    },
    {
      "type": "header",
      "content": "Rating Container"
    },
    {
      "type": "range",
      "id": "rating_container_gap",
      "min": 0,
      "max": 32,
      "step": 1,
      "unit": "px",
      "label": "Gap between rating elements",
      "default": 8
    },
    {
      "type": "range",
      "id": "rating_container_top_spacing",
      "min": 0,
      "max": 60,
      "step": 1,
      "unit": "px",
      "label": "Rating container top spacing",
      "default": 8
    },
    {
      "type": "range",
      "id": "rating_container_bottom_spacing",
      "min": 0,
      "max": 60,
      "step": 1,
      "unit": "px",
      "label": "Rating container bottom spacing",
      "default": 0
    },
    {
      "type": "color",
      "id": "rating_container_bg_color",
      "label": "Rating container background",
      "default": "#ffffff"
    },
    {
      "type": "range",
      "id": "rating_container_padding_vertical",
      "min": 0,
      "max": 30,
      "step": 2,
      "unit": "px",
      "label": "Rating container vertical padding",
      "default": 12
    },
    {
      "type": "range",
      "id": "rating_container_padding_horizontal",
      "min": 0,
      "max": 40,
      "step": 4,
      "unit": "px",
      "label": "Rating container horizontal padding",
      "default": 20
    },
    {
      "type": "range",
      "id": "rating_container_border_radius",
      "min": 0,
      "max": 20,
      "step": 2,
      "unit": "px",
      "label": "Rating container border radius",
      "default": 8
    },
    {
      "type": "checkbox",
      "id": "rating_container_shadow_enable",
      "label": "Enable rating container shadow",
      "default": true
    },
    {
      "type": "color",
      "id": "rating_container_shadow_color",
      "label": "Rating container shadow color",
      "default": "#000000"
    },
    {
      "type": "header",
      "content": "Card Stars"
    },
    {
      "type": "range",
      "id": "star_size",
      "min": 16,
      "max": 60,
      "step": 1,
      "unit": "px",
      "label": "Star size",
      "default": 32
    },
    {
      "type": "color",
      "id": "star_color",
      "label": "Star color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "star_background_color",
      "label": "Star background color",
      "default": "#05B67C"
    },
    {
      "type": "range",
      "id": "stars_title_gap",
      "min": 4,
      "max": 24,
      "step": 2,
      "unit": "px",
      "label": "Space between stars and title",
      "default": 8,
      "info": "Controls the gap between the star rating and card title"
    },
    {
      "type": "header",
      "content": "Card Title Typography"
    },
    {
      "type": "checkbox",
      "id": "custom_block_title_font",
      "label": "Use custom card title font",
      "default": false
    },
    {
      "type": "font_picker",
      "id": "block_title_font",
      "label": "Card title font",
      "default": "helvetica_n4"
    },
    {
      "type": "range",
      "id": "block_title_size_desktop",
      "min": 14,
      "max": 36,
      "step": 1,
      "unit": "px",
      "label": "Card title size (Desktop)",
      "default": 20
    },
    {
      "type": "range",
      "id": "block_title_size_mobile",
      "min": 12,
      "max": 28,
      "step": 1,
      "unit": "px",
      "label": "Card title size (Mobile)",
      "default": 18
    },
    {
      "type": "range",
      "id": "block_title_line_height",
      "min": 50,
      "max": 200,
      "step": 10,
      "unit": "%",
      "label": "Card title line height",
      "default": 120
    },
    {
      "type": "range",
      "id": "block_title_font_weight",
      "min": 100,
      "max": 900,
      "step": 100,
      "label": "Card title font weight",
      "default": 600
    },
    {
      "type": "color",
      "id": "block_title_color",
      "label": "Card title color",
      "default": "#000000"
    },
    {
      "type": "header",
      "content": "Card Description Typography"
    },
    {
      "type": "checkbox",
      "id": "custom_block_description_font",
      "label": "Use custom card description font",
      "default": false
    },
    {
      "type": "font_picker",
      "id": "block_description_font",
      "label": "Card description font",
      "default": "helvetica_n4"
    },
    {
      "type": "range",
      "id": "block_description_size_desktop",
      "min": 12,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Card description size (Desktop)",
      "default": 16
    },
    {
      "type": "range",
      "id": "block_description_size_mobile",
      "min": 12,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Card description size (Mobile)",
      "default": 14
    },
    {
      "type": "range",
      "id": "block_description_line_height",
      "min": 50,
      "max": 200,
      "step": 10,
      "unit": "%",
      "label": "Card description line height",
      "default": 150
    },
    {
      "type": "range",
      "id": "block_description_font_weight",
      "min": 100,
      "max": 900,
      "step": 100,
      "label": "Card description font weight",
      "default": 400
    },
    {
      "type": "color",
      "id": "block_description_color",
      "label": "Card description color",
      "default": "#232323"
    },
    {
      "type": "header",
      "content": "Author Typography"
    },
    {
      "type": "range",
      "id": "block_author_size_desktop",
      "min": 12,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Card author size (Desktop)",
      "default": 14
    },
    {
      "type": "range",
      "id": "block_author_size_mobile",
      "min": 12,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Card author size (Mobile)",
      "default": 13
    },
    {
      "type": "range",
      "id": "block_author_line_height",
      "min": 50,
      "max": 200,
      "step": 10,
      "unit": "%",
      "label": "Card author line height",
      "default": 140
    },
    {
      "type": "range",
      "id": "block_author_font_weight",
      "min": 100,
      "max": 900,
      "step": 100,
      "label": "Card author font weight",
      "default": 600
    },
    {
      "type": "color",
      "id": "block_author_color",
      "label": "Card author color",
      "default": "#333333"
    },
    {
      "type": "range",
      "id": "block_verified_size_desktop",
      "min": 10,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Card verified size (Desktop)",
      "default": 12
    },
    {
      "type": "range",
      "id": "block_verified_size_mobile",
      "min": 10,
      "max": 18,
      "step": 1,
      "unit": "px",
      "label": "Card verified size (Mobile)",
      "default": 11
    },
    {
      "type": "range",
      "id": "block_verified_line_height",
      "min": 50,
      "max": 200,
      "step": 10,
      "unit": "%",
      "label": "Card verified line height",
      "default": 130
    },
    {
      "type": "range",
      "id": "block_verified_font_weight",
      "min": 100,
      "max": 900,
      "step": 100,
      "label": "Card verified font weight",
      "default": 400
    },
    {
      "type": "color",
      "id": "block_verified_color",
      "label": "Card verified color",
      "default": "#888888"
    },
    {
      "type": "header",
      "content": "Author Information"
    },
    {
      "type": "range",
      "id": "author_circle_size",
      "min": 30,
      "max": 60,
      "step": 2,
      "unit": "px",
      "label": "Circle size (Desktop)",
      "default": 40
    },
    {
      "type": "range",
      "id": "author_circle_size_mobile",
      "min": 24,
      "max": 50,
      "step": 2,
      "unit": "px",
      "label": "Circle size (Mobile)",
      "default": 36
    },
    {
      "type": "range",
      "id": "author_circle_text_size",
      "min": 10,
      "max": 40,
      "step": 1,
      "unit": "px",
      "label": "Circle text size (Desktop)",
      "default": 22
    },
    {
      "type": "range",
      "id": "author_circle_text_size_mobile",
      "min": 8,
      "max": 36,
      "step": 1,
      "unit": "px",
      "label": "Circle text size (Mobile)",
      "default": 20
    },
    {
      "type": "color",
      "id": "author_circle_background_color",
      "label": "Circle background color",
      "default": "#05B67C"
    },
    {
      "type": "color",
      "id": "author_circle_text_color",
      "label": "Circle text color",
      "default": "#ffffff"
    },
    {
      "type": "range",
      "id": "author_container_gap",
      "min": 0,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Gap between circle and text",
      "default": 8
    },
    {
      "type": "header",
      "content": "Card Description Line"
    },
    {
      "type": "range",
      "id": "description_line_width",
      "min": 1,
      "max": 10,
      "step": 1,
      "unit": "px",
      "label": "Line width",
      "default": 2
    },
    {
      "type": "color",
      "id": "description_line_color",
      "label": "Line color",
      "default": "#05B67C"
    },
    {
      "type": "range",
      "id": "description_line_spacing",
      "min": 0,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Line spacing",
      "default": 12,
      "info": "Space between line and description text"
    },
    {
      "type": "header",
      "content": "Card Styling"
    },
    {
      "type": "color",
      "id": "block_background_color",
      "label": "Block background color",
      "default": "#ffffff"
    },
    {
      "type": "range",
      "id": "block_padding",
      "min": 0,
      "max": 60,
      "step": 4,
      "unit": "px",
      "label": "Block padding",
      "default": 24,
      "info": "Inner spacing of each block"
    },
    {
      "type": "range",
      "id": "block_elements_gap",
      "min": 8,
      "max": 48,
      "step": 4,
      "unit": "px",
      "label": "Space between elements",
      "default": 16,
      "info": "Space between stars, title, and description"
    },
    {
      "type": "range",
      "id": "block_border_radius",
      "min": 0,
      "max": 40,
      "step": 2,
      "unit": "px",
      "label": "Block border radius",
      "default": 8
    },
    {
      "type": "range",
      "id": "block_border_width",
      "min": 0,
      "max": 10,
      "step": 1,
      "unit": "px",
      "label": "Block border width",
      "default": 0
    },
    {
      "type": "color",
      "id": "block_border_color",
      "label": "Block border color",
      "default": "#e0e0e0"
    },
    {
      "type": "checkbox",
      "id": "enable_card_shadow",
      "label": "Enable card shadow",
      "default": true
    },
    {
      "type": "color",
      "id": "card_shadow_color",
      "label": "Card shadow color",
      "default": "#696969"
    },
    {
      "type": "checkbox",
      "id": "enable_equal_height",
      "label": "Enable equal height cards",
      "default": false,
      "info": "Makes all cards the same height regardless of content length"
    },
    {
      "type": "header",
      "content": "Carousel Layout"
    },
    {
      "type": "range",
      "id": "desktop_card_width",
      "min": 280,
      "max": 500,
      "step": 10,
      "unit": "px",
      "label": "Desktop card max width",
      "default": 350,
      "info": "Maximum width of each card on desktop devices"
    },
    {
      "type": "range",
      "id": "header_blocks_gap",
      "min": 0,
      "max": 80,
      "step": 4,
      "unit": "px",
      "label": "Space between title and blocks",
      "default": 0
    },
    {
      "type": "checkbox",
      "id": "enable_fade_effect",
      "label": "Enable fade effect on sides",
      "default": true,
      "info": "Adds a fading effect when cards scroll in and out"
    },
    {
      "type": "checkbox",
      "id": "enable_fade_effect_mobile",
      "label": "Enable fade effect on mobile",
      "default": false,
      "info": "Adds a fading effect on mobile devices when cards scroll in and out"
    },
    {
      "type": "header",
      "content": "Navigation Controls"
    },
    {
      "type": "checkbox",
      "id": "show_navigation_desktop",
      "label": "Show navigation arrows (Desktop)",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_navigation_mobile",
      "label": "Show navigation arrows (Mobile)",
      "default": true
    },
    {
      "type": "range",
      "id": "nav_arrow_size",
      "min": 40,
      "max": 80,
      "step": 5,
      "unit": "px",
      "label": "Arrow button size",
      "default": 50
    },
    {
      "type": "range",
      "id": "nav_arrow_gap",
      "min": 10,
      "max": 50,
      "step": 5,
      "unit": "px",
      "label": "Gap between arrows",
      "default": 20
    },
    {
      "type": "range",
      "id": "nav_arrow_top_spacing",
      "min": -30,
      "max": 80,
      "step": 5,
      "unit": "px",
      "label": "Margin top",
      "default": 0
    },
    {
      "type": "color",
      "id": "nav_arrow_bg_color",
      "label": "Arrow background color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "nav_arrow_icon_color",
      "label": "Arrow icon color",
      "default": "#6b7280"
    },
    {
      "type": "color",
      "id": "nav_arrow_border_color",
      "label": "Arrow border color",
      "default": "#e5e7eb"
    },
    {
      "type": "range",
      "id": "nav_arrow_border_width",
      "min": 0,
      "max": 5,
      "step": 1,
      "unit": "px",
      "label": "Arrow border width",
      "default": 0
    },
    {
      "type": "checkbox",
      "id": "nav_arrow_shadow_enable",
      "label": "Enable arrow shadow",
      "default": true
    },
    {
      "type": "color",
      "id": "nav_arrow_shadow_color",
      "label": "Arrow shadow color",
      "default": "#000000"
    },
    {
      "type": "header",
      "content": "Section Background"
    },
    {
      "type": "select",
      "id": "background_style",
      "label": "Background style",
      "options": [
        { "value": "solid", "label": "Solid color" },
        { "value": "gradient", "label": "Gradient" }
      ],
      "default": "gradient"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Section background color",
      "default": "#F7F7F7"
    },
    {
      "type": "color",
      "id": "gradient_start_color",
      "label": "Gradient start color (top)",
      "default": "#D0EDC8"
    },
    {
      "type": "color",
      "id": "gradient_end_color",
      "label": "Gradient end color (bottom)",
      "default": "#FFFFFF"
    },
    {
      "type": "header",
      "content": "Section Spacing - Desktop"
    },
    {
      "type": "range",
      "id": "padding_top_desktop",
      "min": 0,
      "max": 180,
      "step": 4,
      "unit": "px",
      "label": "Padding top",
      "default": 60
    },
    {
      "type": "range",
      "id": "padding_bottom_desktop",
      "min": 0,
      "max": 180,
      "step": 4,
      "unit": "px",
      "label": "Padding bottom",
      "default": 60
    },
    {
      "type": "range",
      "id": "margin_top_desktop",
      "min": 0,
      "max": 120,
      "step": 4,
      "unit": "px",
      "label": "Margin top",
      "default": 0
    },
    {
      "type": "range",
      "id": "margin_bottom_desktop",
      "min": 0,
      "max": 120,
      "step": 4,
      "unit": "px",
      "label": "Margin bottom",
      "default": 0
    },
    {
      "type": "header",
      "content": "Section Spacing - Mobile"
    },
    {
      "type": "range",
      "id": "padding_top_mobile",
      "min": 0,
      "max": 80,
      "step": 4,
      "unit": "px",
      "label": "Padding top",
      "default": 32
    },
    {
      "type": "range",
      "id": "padding_bottom_mobile",
      "min": 0,
      "max": 80,
      "step": 4,
      "unit": "px",
      "label": "Padding bottom",
      "default": 32
    },
    {
      "type": "range",
      "id": "margin_top_mobile",
      "min": 0,
      "max": 80,
      "step": 4,
      "unit": "px",
      "label": "Margin top",
      "default": 0
    },
    {
      "type": "range",
      "id": "margin_bottom_mobile",
      "min": 0,
      "max": 80,
      "step": 4,
      "unit": "px",
      "label": "Margin bottom",
      "default": 0
    }
  ],
  "blocks": [
    {
      "type": "star_card",
      "name": "Testimonial Card",
      "limit": 10,
      "settings": [
        {
          "type": "header",
          "content": "Card Content"
        },
        {
          "type": "inline_richtext",
          "id": "title",
          "label": "Title",
          "default": "Review Title ⭐"
        },
        {
          "type": "inline_richtext",
          "id": "description",
          "label": "Description",
          "default": "<em>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam.</em>"
        },
        {
          "type": "inline_richtext",
          "id": "author",
          "label": "Author",
          "default": "Sarah M."
        },
        {
          "type": "inline_richtext",
          "id": "verified_text",
          "label": "Verified text",
          "default": "Verified customer"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "VSO - Testimonials 02",
      "blocks": [
        {
          "type": "star_card",
        },
        {
          "type": "star_card",
        },
        {
          "type": "star_card",
        },
        {
          "type": "star_card",
        },
        {
          "type": "star_card",
        }
      ]
    }
  ]
}
{% endschema %}
