
{% unless settings.goal == blank %}
<div class="cart-progress">
<p style="text-align:center" id="main-cart-progress" data-id="{{ section.id }}" class="free-shipping {% if cart.total_price >= goal %}success{% endif %}">  
  {% assign goal = settings.goal %}
  {% if cart.items.size == 0 %}
            {{ 'templates.cart.free_shipping_spend' | t }} {{ goal | money }}
  {% endif %}
  {% if cart.items.size != 0 %}
      {% if cart.total_price >= goal %} <span>{{ 'templates.cart.free_shipping_success' | t }} <strong>{{ 'templates.cart.free_shipping_success_text' | t  }}</strong></span>
      {% elsif cart.total_price < goal %} {{ 'templates.cart.free_shipping_text_1' | t }}<span>{{ goal | minus: cart.total_price | money }}</span> {{ 'templates.cart.free_shipping_text_2' | t }}
      {% endif %}
  {% endif %}
</p>

{% assign percentage = cart.total_price |  divided_by:100 %}
<div class="cart-progress-bar">
<progress max="{{ goal }}" value="{{ cart.total_price }}" class="{% if cart.total_price >= goal %}free-shipp-ready{% endif %}">{{ percentage }}
</progress>   
<div class="progress-icon">
</div>
</div>
</div>
<style>

progress[value] {
	-webkit-appearance:none;
    -moz-appearance:none;        
    appearance: none;
	border: none;
	position: absolute;
	margin: 0 0 ; 
    height: 6px;
    transition: all 0.3s linear;
}
 .cart-progress-bar{
   position: relative;
    background: rgba(255, 188, 18, 0.4);
    height: 6px;
    border-radius: 5px;
    margin-top: 15px;} 
 .progress-icon{position: absolute;
    width: 35px;
    height: 35px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #ffffff;
    color: #ffbc12;
    border-radius: 50%;
    border:1px solid;
    top: 0;
    right: -4px;
    animation:blinkers 1.5s  infinite;}  
 .progress-icon:before {
    content: "";
    width: 26px;
    height: 26px;
    -webkit-mask-repeat: no-repeat;
    -webkit-mask-size: contain;
    -webkit-mask-position: center;
    color: currentcolor;
  -webkit-mask-image: url({{ "shipping.svg" | asset_url}});
    mask-image:  url({{ "shipping.svg" | asset_url}});
    background: currentColor;
    display:block;
} 

progress[value]::-webkit-progress-bar {
	background-color: whiteSmoke;
}
 
progress[value]::-webkit-progress-value {
	position: relative;
	border-radius:10px;
  background-color:  #ffbc12;
}

progress[value]::-moz-progress-bar {

	border-radius:10px;
  	background-color: #ffbc12;
}
@-webkit-keyframes blinkers {
  0% {
    transform: translateY(-50%) scale(0.9);
  }
  50% {
    transform : translateY(-50%) scale(0.95);
  }
  100% {
    transform: translateY(-50%) scale(1);
  }
}
@keyframes blinkers {
  0% {
    transform: translateY(-50%) scale(0.9);
  }
  50% {
     transform : translateY(-50%) scale(1);
  }
  100% {
    transform: translateY(-50%) scale(0.9);
  }
}
progress:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 1;
    -webkit-animation: move 5s linear infinite;
    border-radius: 10px;
    overflow: hidden;
    background-size: 1rem 1rem;
}
@keyframes move{
0% {
    background-position: 0 0;
}
100% {
    background-position: -60px -60px;
}
  }
  
</style>
{% endunless %}
