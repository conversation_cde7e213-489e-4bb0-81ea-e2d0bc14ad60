{{ 'address-block.css' | asset_url | stylesheet_tag }}
<link rel="stylesheet" href="{{ 'component-slider.css' | asset_url }}" media="print" onload="this.media='all'">
<noscript>{{ 'component-slider.css' | asset_url | stylesheet_tag }}</noscript>

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.60 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.60 | round: 0 }}px;
  }

  @media screen and (min-width: 1541px) {
     .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
  @media screen and (max-width: 990px) {
     .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: 0px;
    }
  }
  .cartrek-address .contact-address-block-card .contact-address-block-card__image-wrapper{
       width: {{ section.settings.icon_outer_size }}px; 
      height: {{ section.settings.icon_outer_size }}px;
      border-radius:{{ section.settings.image_radius }};
    }

  .cartrek-address .contact-address-block-card .contact-address-block-card__image-wrapper img{
    border-radius:{{ section.settings.image_radius }};
  }
  
  .cartrek-address .contact-address-block-card .contact-address-block-card__image-wrapper img{
       width:  {{ section.settings.icon_size }}px; 
      height: {{ section.settings.icon_size }}px;
      object-fit: contain;   
   }
   .cartrek-address .contact-address-block-list__item.list__item .contact-address-block-card .contact-address-block-card__info{
     width:calc( 100% - {{ section.settings.icon_outer_size }}px );
   }
   .cartrek-address .contact-address-block{position:relative;}
   .cartrek-address .contact-address-block:after{content:'';background:url('{{ section.settings.bg_image | image_url: width: 1920 }}');width:100%;height:100%;top:0;z-index:0;position:absolute;}
   
   {%- if section.settings.hover_image != blank -%}
    .cartrek-address .contact-address-block-list__item .contact-address-block-card:after{content:'';width:100%;height:100%;z-index:-1;position:absolute;transition: all 0.3s linear;opacity:0;}
     .cartrek-address .contact-address-block-list__item .contact-address-block-card:hover:after{opacity:1}
     {%- endif -%}
  
    
    {%- for block in section.blocks -%}
     .cartrek-address .contact-address-block #Slide-{{ section.id }}-{{ forloop.index }} .contact-address-block-card .contact-address-block-card__image-wrapper:before{
       content:'';
       -webkit-mask-size: contain;
       -webkit-mask-repeat: no-repeat;
       width: 30px;
       height: 30px;
       -webkit-mask-position: center center;
       transition:all 0.3s linear;
       background: currentcolor;
       {%- if block.settings.address_icon != blank -%}
      -webkit-mask-image:url('{{  block.settings.address_icon | image_url: width: 100 }}');
      mask-image:url('{{  block.settings.address_icon | image_url: width: 100 }}');
        {% else %}
        background-image:url('data:image/svg+xml,<svg class="placeholder-svg" preserveAspectRatio="xMaxYMid slice" viewBox="0 0 1300 730" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(%23clip0_779_1238)"><path d="M1300 410H0v320h1300V410Z" fill="%235BA7B1"></path><path d="M1300 0H0v410h1300V0Z" fill="%23E8BE9E"></path><path d="M474 410c28.51-39.81 73.78-89.8 142-120 113.63-50.31 194.66-3.1 266-52 41.04-28.12 81.7-89.98 80-238h338v410H474Z" fill="%23EDAB8E"></path><path d="M1174 0c-4.57 45.64-17.01 110.48-52 180-69.25 137.58-182.37 205.13-230 230h408V0h-126Z" fill="%23EA9A81"></path><path d="M126 410c124.14 0 213.59-14.83 242-66 38.93-70.13-74.2-158.33-34-262 15.92-41.06 49.03-66.82 74-82H0v410h126Z" fill="%23EDAB8E"></path><path d="M126 410c-68.88-117.13-69.26-250.08-2-334 36.03-44.96 83.52-65.93 116-76H0v410h126Z" fill="%23EA9A81"></path><path d="M442 410h88c-3.51-10.52-7.01-21.04-10.52-31.56-1.16-3.48-6.05-3.57-7.34-.14-1.42 3.8-2.85 7.6-4.27 11.39-1.29 3.44-6.18 3.35-7.34-.14l-7.65-22.96c-1.08-3.25-5.52-3.62-7.13-.6-2.61 4.89-5.22 9.79-7.83 14.68-1.55 2.91-5.79 2.69-7.04-.36-3.69-9.02-7.38-18.03-11.06-27.05-1.35-3.29-6.03-3.21-7.26.13l-10.53 28.59v28l-.03.02Z" fill="%23108060"></path><path d="M1300 224H758.35c-2.89 0-3.07-4.27-.19-4.51l75.83-6.32A92.708 92.708 0 0 0 896.78 181l30.62-35.85c14.34-16.79 39.96-17.8 55.57-2.18l12.34 12.34c21.76 21.76 57.58 19.93 77-3.95l34.73-42.7c25.81-31.73 74.62-30.56 98.88 2.36 19.11 25.93 56.68 29.09 79.85 6.72l14.24-13.75v120l-.01.01Z" fill="%23F7E1D5"></path><path d="M220.89 256h405.42c2.16 0 2.3-3.2.14-3.38l-56.76-4.73a69.338 69.338 0 0 1-46.99-24.08l-22.92-26.83c-10.74-12.57-29.91-13.32-41.6-1.63l-9.24 9.24c-16.29 16.29-43.1 14.91-57.63-2.96l-25.99-31.96c-19.32-23.75-55.85-22.87-74.01 1.77L264.3 208.1 212 222.22l8.89 33.78Z" fill="%23EAD1C1"></path><path d="m980 410 73.94-92.43a55.18 55.18 0 0 1 35.49-20.18l33.63-4.67a55.168 55.168 0 0 0 37.31-22.58l35.94-50.31c8.42-11.79 25.37-13.3 35.75-3.19l67.94 66.24V410H980Z" fill="%239FA5AB"></path><path opacity=".3" d="M1214.49 209.95c-6.95.32-13.75 3.67-18.18 9.87l-35.94 50.31a55.168 55.168 0 0 1-37.31 22.58l-33.63 4.67a55.132 55.132 0 0 0-35.49 20.18L980 409.99h178l58.33-104.66c5.57-9.99 3.05-22.54-5.95-29.61a23.25 23.25 0 0 1-7.94-24.85l12.04-40.94.01.02Z" fill="%23D2D5D9"></path><path d="m464 410-46.64-91.42a12.72 12.72 0 0 0-10.74-6.92l-55.29-2.51c-15.35-.7-28.79-10.52-34.11-24.93l-30.7-83.14c-5.19-14.05-18.11-23.78-33.05-24.87l-33.65-2.46a38.223 38.223 0 0 1-32.69-23.92l-12.8-31.99a6.86 6.86 0 0 0-8.35-4.02L0 164v246s.06.02.09 0H464Z" fill="%23818990"></path><path d="m96 410 6-66 21-56c1.03-2.73 4.9-2.71 5.89.04l12.38 34.4c.97 2.69 4.74 2.79 5.84.15l9.65-22.91c1.12-2.67 4.95-2.52 5.87.23l12.46 37.38c.95 2.84 4.95 2.87 5.94.04l7.24-20.67c1.05-3 5.39-2.72 6.03.4l6.24 29.93c.56 2.68 4.04 3.41 5.63 1.18l12.31-17.24c1.48-2.07 4.68-1.61 5.52.79l10.63 30.55c1.02 2.93 5.21 2.76 6-.23l4.5-17.11c.81-3.08 5.16-3.13 6.05-.08l8.73 29.92c.78 2.68 4.4 3.08 5.76.65l12.7-22.86c1.35-2.44 4.97-2.03 5.76.65l9.5 32.56c.82 2.81 4.69 3.07 5.88.4l8.75-19.69c1.22-2.74 5.22-2.37 5.92.55l6.1 25.6c.65 2.72 4.26 3.3 5.72.92l8.26-13.42c1.44-2.33 4.96-1.83 5.7.8l8.07 29.07H96Z" fill="%2302614E"></path><path d="M0 410h218l-9.65-26.54a39.431 39.431 0 0 0-23.85-23.68l-51.05-18.15a39.436 39.436 0 0 1-25.57-30.02L102 279.66a39.44 39.44 0 0 0-24.53-29.63L0 220v190Z" fill="%23686E72"></path><path d="M0 410h88c-3.73-11.18-7.46-22.37-11.18-33.55-.94-2.82-4.9-2.89-5.95-.11-1.91 5.11-3.83 10.21-5.74 15.32-1.04 2.78-5.01 2.71-5.95-.11l-8.86-26.59c-.88-2.63-4.47-2.93-5.78-.49-3.13 5.87-6.26 11.73-9.39 17.6-1.26 2.36-4.69 2.18-5.7-.29-4.13-10.09-8.26-20.18-12.38-30.27-1.09-2.66-4.88-2.6-5.88.1C7.46 361.74 3.73 371.87 0 381.99V410Z" fill="%2302614E"></path><path d="m636.01 410 36.48-43.78c14.28-17.14 37.37-24.17 58.78-17.92l59.17 17.3c21.57 6.3 44.82-.88 59.06-18.26l53.45-65.19c3.24-3.95 7.88-6.51 12.95-7.15l16.59-2.07a51.1 51.1 0 0 1 40.94 13.11L1108 409.99H636l.01.01Z" fill="%23818990"></path><path d="m1279.24 295.49-12.18 41.97c-.91 3.13-5.33 3.17-6.29.05l-9.05-29.41c-1-3.24-5.64-3.03-6.35.28l-9.35 44.07c-.65 3.08-4.84 3.56-6.18.72l-7.92-16.84c-1.31-2.79-5.41-2.39-6.15.6l-5.64 22.58c-.74 2.94-4.73 3.4-6.11.7l-15.16-29.66c-1.36-2.67-5.3-2.26-6.09.63l-7.07 25.92c-.84 3.08-5.14 3.27-6.25.27l-6.49-17.62c-1.14-3.1-5.62-2.76-6.29.47l-6.46 31.11c-.66 3.18-5.05 3.57-6.26.55l-12.18-30.46c-1.18-2.96-5.46-2.67-6.23.42l-8.87 35.48c-.79 3.16-5.21 3.36-6.28.28l-8.77-25.21c-1.07-3.08-5.49-2.88-6.28.28l-6.1 24.4c-.77 3.09-5.05 3.38-6.23.42l-7.67-19.18c-1.14-2.84-5.19-2.72-6.16.18l-10.21 30.62c-.98 2.94-5.12 3.01-6.19.1l-7.89-21.41c-1.03-2.79-4.95-2.88-6.1-.14l-9.33 22.17c-1.18 2.81-5.22 2.63-6.15-.27l-12.04-37.45c-.99-3.07-5.35-3.02-6.27.07l-10.43 35.2c-.87 2.93-4.93 3.19-6.15.38l-7.13-16.3c-1.18-2.71-5.06-2.59-6.09.18l-7.76 21.07c-1.09 2.96-5.33 2.83-6.23-.2-3.37-11.38-6.74-22.76-10.12-34.15-.92-3.11-5.32-3.14-6.28-.04-3.9 12.55-7.79 25.1-11.69 37.65-.95 3.07-5.3 3.08-6.26.02l-6.47-20.48c-.88-2.78-4.68-3.12-6.04-.53l-18.34 35.01h404v-76l-14.53-38.75c-1.11-2.96-5.34-2.8-6.22.24l-.02.01Z" fill="%2302614E"></path><path d="M576 186c35.346 0 64-28.654 64-64 0-35.346-28.654-64-64-64-35.346 0-64 28.654-64 64 0 35.346 28.654 64 64 64Z" fill="%23EAD1C1"></path><path d="M576 170c26.51 0 48-21.49 48-48s-21.49-48-48-48-48 21.49-48 48 21.49 48 48 48Z" fill="%23fff"></path><path d="m264.3 269.34 4.38 12.32c11.72 32.97 41.95 55.78 76.87 58.01a87.466 87.466 0 0 0 63.73-21.95l4.15-3.69a12.71 12.71 0 0 0-6.82-2.37l-55.29-2.51c-15.35-.7-28.79-10.52-34.11-24.93l-30.7-83.14c-5.19-14.05-18.11-23.78-33.05-24.87l-33.65-2.46a38.223 38.223 0 0 1-32.69-23.92l-12.8-31.99a6.822 6.822 0 0 0-3.17-3.51l-10.98 32.29c-11.16 32.84 6.32 68.52 39.11 79.83l33.29 11.48a51.472 51.472 0 0 1 31.72 31.41h.01Z" fill="%239FA5AB"></path><path d="M51.84 244.38a39.431 39.431 0 0 1 16.74 34.63l-1.91 32.43a39.42 39.42 0 0 0 17.67 35.25l45.23 29.81a39.47 39.47 0 0 1 17.51 28.69l.52 4.8h70.52l-9.65-26.54a39.431 39.431 0 0 0-23.85-23.68l-51.05-18.15A39.436 39.436 0 0 1 108 311.6l-5.89-31.95a39.44 39.44 0 0 0-24.53-29.63L38 234.67l13.84 9.7v.01Z" fill="%23818990"></path><path d="m756.08 443.99.04.01-.04-.01Z" fill="%23686E72"></path><path opacity=".8" d="m790.66 365.67 39.39 11.51c21.9 6.4 45.55.69 62.12-14.99a64.199 64.199 0 0 0 19.25-56.93l-4.38-26.98a19.967 19.967 0 0 0-4.21 3.85l-53.45 65.19a56.03 56.03 0 0 1-58.71 18.35h-.01ZM706 388c-.24-15.7 16.55-32.5 41.81-34.86l-16.54-4.84c-21.41-6.26-44.5.78-58.78 17.92L636.01 410H718c-3.29-2.83-11.83-10.97-12-22Z" fill="%239FA5AB"></path><path d="M416.96 410a27.009 27.009 0 0 0 17.23 10.44l74.31 12.16c4.49.73 4.13 7.3-.41 7.54l-90.19 4.96c-4.91.27-4.9 7.51.01 7.77l95.5 4.97c4.71.25 5.01 7.08.34 7.74l-77.82 10.96c-4.62.65-4.39 7.4.27 7.73L558.37 493c6.93.49 7.28 10.54.41 11.52l-26.87 3.84c-4.68.67-4.34 7.53.38 7.74l118.58 5.33c4.61.21 5.09 6.85.55 7.71l-30.86 5.88c-4.44.85-4.11 7.31.39 7.7l41.36 3.57c37.51 3.23 75.27 1.58 112.35-4.93l42.85-7.52c4.39-.77 4.25-7.11-.17-7.69l-88.29-11.52c-4.63-.6-4.47-7.35.18-7.74l70.24-5.77c4.8-.39 4.75-7.44-.06-7.76l-63.91-4.32c-4.75-.32-4.88-7.25-.15-7.75l112.28-11.82c4.77-.5 4.58-7.51-.2-7.76l-91.17-4.75c-6.25-.33-6.45-9.48-.22-10.08l30.04-2.91c4.65-.45 4.7-7.22.06-7.74l-52.89-5.97c-4.63-.52-4.44-7.31.22-7.57l58.3-3.24c9.03-.5 17.68-3.81 24.74-9.46H416.94l.02.01Z" fill="%2363B5B1"></path><path d="M0 478c15.69 2.92 39.93 5.53 68 0 42.62-8.4 48.21-26.53 84-34 45.2-9.43 57.35 15.07 114 14 9.94-.19 18.2-1.11 25.64-2.55 36.52-7.09 62.17-18.56 68.36-21.45 22.81-10.63 66.5-17.19 157.8-.42 67.4-3.19 134.8-6.39 202.2-9.58 6.3-.79 18.55-2.14 33.98-2.49 57.4-1.32 91.51 12.68 158.02 16.49 17.53 1 29.44.78 43.36-1.93 24.93-4.85 34.21-15.04 78.64-12.07 71.18 4.75 89.94 33.73 158 38 45.51 2.86 83.37-7.2 108-16v-36H0v68Z" fill="%2363B5B1"></path><path opacity=".5" d="m425.74 101.25 12.14 6.54a6.7 6.7 0 0 0 6.98-.39l10.76-7.46c1.24-.86.32-2.8-1.13-2.37l-10.43 3.05c-2.24.65-4.6.76-6.89.32l-10.59-2.06c-1.44-.28-2.14 1.69-.85 2.38l.01-.01ZM729.78 162.53l11.66 7.35a6.686 6.686 0 0 0 6.99.09l11.25-6.7c1.3-.77.51-2.77-.97-2.44l-10.61 2.32c-2.28.5-4.64.45-6.89-.15l-10.42-2.78c-1.42-.38-2.25 1.54-1.01 2.32v-.01Z" fill="%23964F48"></path><path opacity=".75" d="m656.07 194.86 16.65 2.66a8.18 8.18 0 0 0 7.91-3.26l9.43-12.95c1.09-1.49-.76-3.36-2.26-2.28l-10.82 7.72a17.873 17.873 0 0 1-7.83 3.14l-13.06 1.89c-1.78.26-1.79 2.81-.02 3.09v-.01Z" fill="%23964F48"></path><path d="m695.71 113.63 12.93 12.86a8.834 8.834 0 0 0 9 2.13l16.46-5.4c1.9-.62 1.46-3.42-.54-3.43l-14.37-.06c-3.08-.01-6.12-.77-8.85-2.19l-12.65-6.6c-1.72-.9-3.35 1.33-1.98 2.7v-.01Z" fill="%23964F48"></path><path d="M894.938 386.359c-13.528-2.239-26.508 6.204-29.834 19.39l-4.757 17.749a44.424 44.424 0 0 0 0 21.713c2.119 8.43 8.757 15.009 17.26 17.109 5.908 1.461 9.304 7.609 7.381 13.326L877.172 499h37.145L920 420.202l-25.076-33.857.014.014Z" fill="%23E8BE9E"></path><path d="m911 466 7.311 29.252L920.224 506h6.612L929 466h-18Z" fill="%23EA9A81"></path><path d="m865.215 624.829-52.827-51.996c-9.913-9.757-23.901-14.346-37.776-12.39-17.18 2.412-31.364 14.429-36.348 30.788l-11.005 36.107c-1.162 3.817 1.736 7.662 5.796 7.662h127.89c5.39 0 8.079-6.408 4.27-10.157v-.014Z" fill="%232E5157"></path><path d="m744.04 632.85 10.992-36.111c4.979-16.36 19.145-28.379 36.305-30.791a44.677 44.677 0 0 1 11.663-.096 45.066 45.066 0 0 0-28.445-5.417c-17.159 2.412-31.326 14.431-36.305 30.791l-10.992 36.111c-1.16 3.818 1.735 7.663 5.79 7.663h10.754a6.013 6.013 0 0 1 .238-2.15Z" fill="%233C7980"></path><path d="M819.933 546c-1.406 3.619-2.617 7.307-3.55 11.063L797 635h29.492L857 572.915 819.947 546h-.014Z" fill="%23E8BE9E"></path><path d="M954.273 598.986a80.22 80.22 0 0 0 35.466-32.084l7.624-12.954c18.687-31.722 5.937-72.604-27.437-88.137-10.528-4.895-16.993-15.715-15.932-27.26l2.164-23.732c1.215-13.275-2.904-26.619-11.897-36.463-14.856-16.286-38.649-19.911-57.472-9.467l-14.075 7.808c-7.386 4.099-10.612 12.995-7.582 20.86l10.515 27.315a107.614 107.614 0 0 0 52.375 57.601c19.256 9.621 25.469 34.078 13.112 51.689l-19.688 28.083L954.259 599l.014-.014Z" fill="%236E3A35"></path><path opacity=".75" d="m938.181 562.986 19.499-27.951c12.225-17.529 6.085-41.871-12.986-51.448-23.813-11.949-42.317-32.392-51.873-57.332l-10.413-27.188c-3.001-7.827.207-16.681 7.509-20.762l13.94-7.772c5.781-3.22 12.031-5.065 18.351-5.634-11.685-3.442-24.533-2.249-35.637 3.941l-13.94 7.772c-7.316 4.08-10.51 12.935-7.509 20.762l10.413 27.188c9.556 24.94 28.059 45.383 51.873 57.332 19.07 9.576 25.224 33.919 12.986 51.448l-19.5 27.951L938.181 563v-.014Z" fill="%23AF5947"></path><path d="M973.436 592.368c-.621-16.691-4.045-32.654-9.993-47.368L934 574.442 951.167 635H975l-1.579-42.632h.015Z" fill="%23E8BE9E"></path><path d="M969 559.741c-1.419-5.037-3.082-9.964-5.059-14.741L934 574.442 951.457 635h15.665l-12.598-43.703c-2.408-8.359 0-17.322 6.307-23.526l8.155-8.016.014-.014Z" fill="%23EA9A81"></path><path d="M945.231 561.25 962 543.979c-6.536-16.619-16.174-31.641-28.581-44.303-7.366-7.511-17.655-11.676-28.926-11.676h-18.002c-9.568 0-19.303 2.999-27.874 8.566-18.154 11.815-32.126 29.128-39.617 48.635l24.108 21.339c4.32 4.318 5.456 10.898 2.852 16.424L824.137 635h105.447l2.575-45.039c.596-10.398 5.29-20.714 13.072-28.725v.014Z" fill="%2302614E"></path><path opacity=".25" d="M962 543.948c-6.397-16.622-15.83-31.647-27.974-44.311-6.804-7.096-16.17-11.207-26.47-11.637l12.022 40.048a99.609 99.609 0 0 1 1.125 53.129L907 635h23.271l2.521-45.047c.583-10.401 5.178-20.718 12.795-28.731L962 543.948Z" fill="%23142924"></path><path d="M863.006 501.368c4.692-5.373 10.126-9.885 15.994-13.368-6.919 1.213-13.739 3.892-19.93 7.953-18.361 12-32.493 29.585-40.07 49.397L834.35 559c4.314-20.94 14.16-41.035 28.656-57.618v-.014Z" fill="%2300735C"></path><path d="M494 630.718v-51.341c0-9.728 7.693-17.945 18.007-19.234l144.139-17.973c9.282-1.15 18.229 3.63 21.867 11.695l37.366 82.95c2.467 5.488 2.104 11.738-.99 16.948l-18.578 31.262c-3.791 6.374-11.066 10.213-18.857 9.964l-145.714-4.698c-8.223-.263-15.498-5.044-18.55-12.181l-17.199-40.214a18.377 18.377 0 0 1-1.477-7.206l-.014.028Z" fill="%23975D48"></path><path d="M471 632.718v-51.341c0-9.728 7.693-17.946 18.007-19.234l144.139-17.973c9.282-1.15 18.229 3.63 21.867 11.695l37.366 82.95c2.467 5.488 2.104 11.738-.99 16.948l-18.578 31.262c-3.791 6.375-11.066 10.213-18.857 9.964l-145.714-4.698c-8.223-.263-15.498-5.044-18.55-12.181l-17.199-40.214a18.376 18.376 0 0 1-1.477-7.205l-.014.027Z" fill="%23BF8563"></path><path opacity=".5" d="M557.941 687.156 541.061 556 517 559.089l16.664 129.508a6.902 6.902 0 0 0 2.899 4.807l18.113.596a6.439 6.439 0 0 0 1.639-1.358 7.008 7.008 0 0 0 1.626-5.472v-.014ZM636.059 691.273a6.993 6.993 0 0 0 6.569 5.351l11.133.376h.238c2.157 0 4.16-.961 5.49-2.647 1.331-1.686 1.821-3.846 1.317-5.922L626.662 545 602 548.079c.028.223.07.46.126.683l33.919 142.497.014.014Z" fill="%23975D48"></path><path d="M530.223 558.016c-.468-3.43-3.489-6.016-7.021-6.016-.312 0-.624.014-.936.055l-11.106 1.439c-3.872.497-6.609 3.982-6.099 7.758l17.46 129.359c.454 3.36 3.305 5.891 6.794 6.002l11.347.387h.241a7.18 7.18 0 0 0 5.333-2.351 6.778 6.778 0 0 0 1.702-5.462l-17.701-131.185-.014.014ZM648.837 690.47l-33.746-144.113c-.743-3.159-3.495-5.357-6.686-5.357-.303 0-.606.014-.908.056l-10.524 1.419a6.902 6.902 0 0 0-4.76 2.95 7.061 7.061 0 0 0-1.032 5.552L624.5 693.281c.716 3.047 3.371 5.246 6.452 5.343l10.937.376h.234c2.119 0 4.086-.96 5.393-2.644a6.97 6.97 0 0 0 1.293-5.913l.028.027Z" fill="%236D493C"></path><path d="m1137.25 392.823-26.98-23.175c-7.2-6.174-17.37-7.453-25.7-3.01-9.63 5.133-17 14.246-19.86 25.482l-.37 1.491a109.471 109.471 0 0 0-2.37 41.372c.61 4.515 2.69 8.691 5.92 11.841a19.422 19.422 0 0 0 10.87 5.358l10.65.717c4.08.802 6.57 5.035 5.34 9.071 0 0-1.85 6.089-3.45 11.335 9.59 3.796 19.46 5.695 29.33 5.695 9.21 0 18.42-1.688 27.37-4.978-4.93-5.949-8.17-15.315-7.51-21.84l4.9-38.011c1.04-8.058-2.03-16.102-8.12-21.348h-.02Z" fill="%23975D48"></path><path opacity=".5" d="M1131.49 470.042 1148 473c-4.98-5.792-8.26-14.926-7.59-21.265l4.95-37.013-6.6-10.722-11.98 45.078c-1.95 7.326-.18 15.117 4.73 20.951l-.02.013Z" fill="%236D493C"></path><path d="m1161.96 402.99-1.18-25.362c-.87-13.77-11.14-25.419-24.75-27.027-3.17-.375-6.19-.194-8.75.61a20.941 20.941 0 0 1-17.26-2.163l-5.88-3.633a29.637 29.637 0 0 0-34.75 2.634l-.09.083c-4.16 3.842-6.73 9.125-7.23 14.797-.58 6.683 2.38 13.173 7.65 17.167 1.61 1.22 3.05 2.635 4.36 4.174 4.29 5.075 6.5 11.551 6.67 18.207.05 2.177-.06 4.119-.33 5.464l-.22 1.081c-.68 3.231 1.65 6.31 4.92 6.546.35.027.71 0 1.08-.07 1.77-.346 3.01-1.872 3.38-3.647 1.1-5.283 4.92-9.166 9.46-9.166 5.42 0 9.8 5.519 9.8 12.328 0 3.564-1.2 6.767-3.13 9.014-3.49 4.076-3.46 10.22-.15 14.449a18.682 18.682 0 0 0 6.31 5.158c2.54 1.29 5.35 1.886 8.19 1.983l12.66.375a18.64 18.64 0 0 0 15.57-7.585l5.41-7.378c.4-.554.8-1.109 1.17-1.678 5.15-7.737 7.45-17.042 7.09-26.361Z" fill="%23142924"></path><path opacity=".25" d="m1077.42 364.743.1-.081c10.97-8.995 20.24-10.145 32.47-2.854l6.57 3.923a24.105 24.105 0 0 0 19.29 2.34c8.85-2.705 15.65-2.056 24.15 1.366-3.43-10.064-12.34-17.801-23.47-19.072-3.19-.365-6.22-.189-8.8.595-5.84 1.772-12.17 1.001-17.38-2.11l-5.92-3.544c-11.02-6.574-25.12-5.546-35 2.57l-.08.081c-4.19 3.747-6.78 8.9-7.28 14.433-.57 6.452 2.34 12.714 7.53 16.61a24.355 24.355 0 0 1 7.84-14.257h-.02Z" fill="%236B7177"></path><path d="M1217 571.844 1249.18 541l39.82 86.272-33.9 2.728-38.1-58.156ZM1056 584.222 1017.4 562a1983.872 1983.872 0 0 0-23.4 95.638c10.25 3.375 20.39 6.833 29.06 10.362l32.93-83.778h.01Z" fill="%23975D48"></path><path d="M1072.4 481.732c-10.04 5.728-19.03 13.161-26.38 22.088-9.86 11.945-17.59 25.259-23.14 39.356-.23.559-.45 1.118-.66 1.677-2.44 6.231-4.63 10.506-6.22 16.989l21.32 15.409 25.26 3.647 5.59-10.66c.94 29.116-5.2 55.646-4.13 84.762a2012.614 2012.614 0 0 1 160.89-.489c-5.34-33.475-14.87-64.406-21.41-97.839 3.65 4.764 5.87 10.716 9.44 15.494 7.25-.307 14.51-.573 21.76-.796 4.69-7.545 14.45-18.791 19.28-26.308-3.98-6.077-8.01-12.126-12.11-18.176-14.09-18.986-32.73-34.927-54.82-46.691L1158.58 473a92.251 92.251 0 0 1-8.45 4.596c-11.71 5.631-24.18 8.662-36.77 8.872-13.42.21-23.58-1.649-35.83-7.684l-5.14 2.934.01.014Z" fill="%23DE6A5A"></path><path opacity=".1" d="M1068.87 495.403c.13-.111.25-.222.38-.319a567.35 567.35 0 0 1 3.56-3.133 84.583 84.583 0 0 1 10.19-7.624c-2.8-.957-5.55-2.093-8.25-3.327l-2.69 1.539c-9.98 5.683-18.91 13.058-26.22 21.916-9.8 11.852-17.49 25.063-23 39.05-.23.555-.45 1.109-.66 1.664-2.42 6.182-4.6 10.424-6.18 16.856l8.28 5.975c1.45-5.24 3.17-10.425 5.2-15.498.22-.569.44-1.137.68-1.691 8.29-20.78 21.24-39.868 38.74-55.394l-.03-.014Z" fill="%23F7E1D5"></path><path d="M1241.86 527.309c-12.03-16.169-27.39-30.133-45.37-41.182-5.07-3.111-10.38-5.817-15.86-8.147l-18.69-7.98c-2.77 1.688-10.08 8.273-12.94 9.64l3.38 1.186c22.55 28.236 32.78 65.902 28.39 101.741L1172.64 649c10.58-.098 40.7-.112 51.29-.056-4.9-30.231-13.89-57.923-19.77-88.112 3.4 3.488 5.38 8.161 8.72 11.663 13.51-.572 30.99-11.342 38.17-22.488l2.95-4.576a1284.8 1284.8 0 0 0-12.13-18.15l-.01.028Z" fill="%23CD5747"></path><path d="m1016.92 560.014-3.44 10.32a9.342 9.342 0 0 0 4.04 10.964c8.09 4.899 20.37 10.238 30.03 12.461 4.07.947 8.27-.961 10.32-4.57l5.13-8.989c-15.69-1.825-36.49-10.127-46.06-20.2l-.02.014Z" fill="%23F7E1D5"></path><path d="M1252.85 546c-10.61 12.254-28.02 23.477-41.85 27.046 2.09 2.872 4.61 5.897 6.95 8.867 2.19 2.76 5.95 3.806 9.29 2.579 9.06-3.332 22.49-12.059 30.14-19.016 2.83-2.579 3.46-6.762 1.44-9.982a2476.29 2476.29 0 0 0-5.97-9.494Z" fill="%23E8BE9E"></path><path d="M1151.47 463.304a9.745 9.745 0 0 0-7.1.895c-9.8 5.395-20.34 8.334-30.94 8.519-6.92.113-13.83-.952-20.49-3.138a9.678 9.678 0 0 0-7.26.483l-7.99 6.02c-2.57 1.931-2.13 6.048.79 7.326 11.04 4.813 23.7 7.78 35.06 7.582 8.67-.142 18.38-2.088 27.36-5.225 6.1-2.13 11.8-5.381 16.9-9.499l3.7-2.996c2.4-1.931 1.82-5.835-1.02-6.928-3.03-1.164-6.53-2.428-9.01-3.053v.014Z" fill="%23F7E1D5"></path><path d="m1063 639 11.11-8.488c9.33-17.356 11.3-40.094 9.03-61.118-.74-6.9-9.93-8.797-13.43-2.796l-1.71 2.923-5 69.479Z" fill="%23CD5747"></path><path d="M1160.44 466.42c-3.09-1.186-6.66-2.473-9.18-3.11a9.973 9.973 0 0 0-7.25.911 70.47 70.47 0 0 1-13.01 5.569c8.12 1.75 15.11 5.497 20.34 11.21a60.322 60.322 0 0 0 6.36-4.484l3.77-3.052c2.44-1.967 1.86-5.945-1.04-7.059l.01.015Z" fill="%23E8BE9E"></path><path d="M318.148 584.026 389.152 730H1300V612.215l-113.51 12.627a1077.374 1077.374 0 0 1-158.28 5.902L622.569 616.03a1076.718 1076.718 0 0 1-207.552-27.898l-84.334-19.823c-9.117-2.144-16.635 7.28-12.535 15.717Z" fill="%23142924"></path><path opacity=".25" d="M1186.49 624.842a1077.374 1077.374 0 0 1-158.28 5.902L622.569 616.03a1079.098 1079.098 0 0 1-173.044-20.394 1049.917 1049.917 0 0 1-34.508-7.504l-84.334-19.823c-9.117-2.144-16.635 7.28-12.535 15.717L389.152 730h126.889l-41.958-86.254c-5.907-12.139 4.267-25.948 17.567-23.819a1079.754 1079.754 0 0 0 130.919 12.808l405.641 14.714c52.84 1.921 105.74-.056 158.28-5.902L1300 628.92v-16.705l-113.51 12.627Z" fill="%236B7177"></path></g><defs><clipPath id="clip0_779_1238"><path fill="%23fff" d="M0 0h1300v730H0z"></path></clipPath></defs></svg>');
      {% endif %}
       background-size: cover;
       background-repeat: no-repeat;
     }
      
     .cartrek-address .contact-address-block #Slide-{{ section.id }}-{{ forloop.index }} .contact-address-block-card .contact-address-block-card__image-wrapper.phone-icon:before{
       content:'';
       -webkit-mask-size: contain;
       -webkit-mask-repeat: no-repeat;
       width: 30px;
       height: 30px;
       -webkit-mask-position: center center;
       transition:all 0.3s linear;
       background: currentcolor;
       {%- if block.settings.address_icon != blank -%}
      -webkit-mask-image:url('{{  block.settings.address_icon | image_url: width: 100 }}');
      mask-image:url('{{  block.settings.address_icon | image_url: width: 100 }}');
        {% else %}
        background-image:url('data:image/svg+xml,<svg class="placeholder-svg" preserveAspectRatio="xMidYMin slice" viewBox="0 0 1300 731" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(%23clip0_755_1426)"><path d="M1300 0H0v730h1300V0Z" fill="%23F4D7AC"></path><path d="M1300 0H0v730h1300V0Z" fill="%23F4D7AC"></path><path d="M1300 541.81V0h-152.51c-4.24 182.83-58.15 262.15-112.53 299.42-100.54 68.9-214.72 2.39-374.85 73.28-96.14 42.56-159.93 113-200.11 169.1h840v.01ZM50.66 222.7c76.68-23.54 56.31-165.54 157.16-212.37C218.47 5.38 229.25 2.08 239.86 0H0v221.93c18.27 5.02 35.19 5.52 50.66.77Z" fill="%23F2CF9B"></path><path d="M0 221.81V326h506.8c3.17 0 3.37-4.69.21-4.96l-83.27-6.94a101.827 101.827 0 0 1-68.95-35.33l-33.62-39.36c-15.75-18.44-43.88-19.55-61.03-2.4l-13.55 13.55c-23.9 23.9-63.23 21.88-84.55-4.34l-38.13-46.88c-28.34-34.84-81.94-33.56-108.58 2.6L0 221.81Z" fill="%23F4D7AC"></path><path opacity=".5" d="M0 221.81V326h506.8c3.17 0 3.37-4.69.21-4.96l-83.27-6.94a101.827 101.827 0 0 1-68.95-35.33l-33.62-39.36c-15.75-18.44-43.88-19.55-61.03-2.4l-13.55 13.55c-23.9 23.9-63.23 21.88-84.55-4.34l-38.13-46.88c-28.34-34.84-81.94-33.56-108.58 2.6L0 221.81Z" fill="%23fff"></path><path d="M596 541.55 458.03 386.32c-19.56-23.48-51.19-33.11-80.52-24.54l-81.05 23.69c-29.54 8.64-61.39-1.21-80.91-25.01l-60.52-73.81c-4.16-5.08-8.96-9.6-14.26-13.47a77.457 77.457 0 0 0-36.04-14.28l-2.86-.36A69.98 69.98 0 0 0 45.8 276.5L0 318.69V730h596V541.55ZM1300 294.46l-45.02 54.91c-21.98 26.8-57.83 37.88-91.1 28.16l-91.26-26.68c-33.03-9.65-68.63 1.2-90.66 27.63L879.7 513.86v215.85H1300V294.45v.01Z" fill="%2370B3A0"></path><path d="m1300 294.46-45.02 54.91c-21.98 26.8-57.83 37.88-91.1 28.16l-91.26-26.68c-33.03-9.65-68.63 1.2-90.66 27.63l-121.84 161.3a143.612 143.612 0 0 1-39.67 35.95l-9.18 5.61a143.558 143.558 0 0 1-75.28 21.04l1.7 115.15 84-92 210.01-197.37c34.51-32.44 84-43.42 128.99-28.63 57.56 18.92 119.15-14.59 134.54-73.19l4.76-18.15v-13.72l.01-.01ZM392.25 432.27c17.49-22.28 49.8-26.03 71.92-8.33l94.48 75.59-100.62-113.2c-19.56-23.48-51.19-33.11-80.52-24.54l-81.05 23.69c-29.54 8.64-61.39-1.21-80.91-25.01l-60.52-73.81c-4.16-5.08-8.96-9.6-14.26-13.47a78.53 78.53 0 0 0-6.11-4.03l89.5 151.92c36.45 61.88 123.75 67.69 168.09 11.2v-.01Z" fill="%2388C0B0"></path><path d="m1296.87 65.5-30.76 37.82c-17.2 21.15-48.93 22.78-68.21 3.5l-10.93-10.93c-13.83-13.83-36.52-12.94-49.23 1.93l-27.12 31.75a82.13 82.13 0 0 1-55.62 28.5l-67.17 5.6c-2.55.21-2.39 4 .17 4h312V62c-1.07 1.12-2.13 2.27-3.13 3.5Z" fill="%23F2CF9B"></path><path opacity=".5" d="m1296.87 65.5-30.76 37.82c-17.2 21.15-48.93 22.78-68.21 3.5l-10.93-10.93c-13.83-13.83-36.52-12.94-49.23 1.93l-27.12 31.75a82.13 82.13 0 0 1-55.62 28.5l-67.17 5.6c-2.55.21-2.39 4 .17 4h312V62c-1.07 1.12-2.13 2.27-3.13 3.5Z" fill="%23fff"></path><path d="M232 185c34.242 0 62-27.758 62-62 0-34.242-27.758-62-62-62-34.242 0-62 27.758-62 62 0 34.242 27.758 62 62 62Z" fill="%23fff"></path><path d="M0 730.19h1300V437.2l-142.42 59.78a221.775 221.775 0 0 1-142.54 9.92l-211.98-56.04a221.838 221.838 0 0 0-125.12 3.45l-174.85 56.71a221.825 221.825 0 0 1-119.55 4.85l-185.29-43.88c-38.16-9.04-78.03-7.8-115.55 3.59L0 500.69v229.5Z" fill="%23288D70"></path><path d="m1300 390.57-11.47-14.65c-2.29-2.93-6.99-1.02-6.59 2.67l3.81 35.62c.39 3.65-4.19 5.58-6.53 2.75l-23.6-28.49c-2.29-2.76-6.78-.98-6.54 2.61l2.73 41.14c.24 3.67-4.43 5.41-6.65 2.47l-18.12-24.04c-2.21-2.94-6.89-1.2-6.65 2.47l1.88 28.3c.24 3.58-4.25 5.37-6.54 2.61l-14.86-17.94c-2.2-2.65-6.51-1.13-6.55 2.32l-.38 36.4c-.04 3.5-4.46 5-6.61 2.24l-15.86-20.26c-2.07-2.64-6.31-1.39-6.6 1.96l-2.36 27.02c-.3 3.42-4.69 4.62-6.69 1.83l-25.85-36.04c-2.12-2.96-6.79-1.4-6.71 2.24l.96 41.38c.08 3.45-4.19 5.12-6.47 2.54l-13.28-15.03c-2.21-2.5-6.33-1.03-6.47 2.3l-1.06 25.3c-.15 3.56-4.74 4.87-6.75 1.93-7.55-11.05-15.1-22.11-22.65-33.16-2.06-3.02-6.79-1.53-6.76 2.13.15 14.21.29 28.41.44 42.62 32.8 1.56 65.74-4.17 96.38-17.03L1300.02 437v-46.44l-.02.01ZM198.26 471.8l109.38 25.91-18.72-49.99c-1.03-2.84-4.99-3.02-6.28-.28l-9.47 20.15c-1.42 3.02-5.88 2.41-6.44-.88l-6.81-40.31c-.56-3.32-5.08-3.9-6.46-.83a34402.7 34402.7 0 0 0-15.11 33.7c-1.34 2.99-5.71 2.54-6.43-.65l-5.07-22.73c-.67-2.99-4.64-3.65-6.23-1.03l-9.58 15.77c-1.65 2.71-5.78 1.89-6.27-1.24l-5.86-37.61c-.52-3.31-5-3.96-6.44-.94l-17.54 36.82c-1.36 2.85-5.53 2.48-6.36-.57l-6.53-24.07c-.81-2.98-4.85-3.43-6.29-.7l-11.07 20.92c-1.5 2.84-5.75 2.21-6.35-.95l-6.26-32.88c-.59-3.11-4.75-3.8-6.31-1.04l-10.53 18.65c-1.62 2.88-5.98 1.99-6.35-1.3l-2.9-25.92c-.38-3.36-4.89-4.18-6.42-1.16l-12.5 24.7c-1.53 3.02-6.04 2.2-6.42-1.16l-4.22-37.69c-.37-3.28-4.72-4.17-6.35-1.29l-16.73 29.62c-1.66 2.94-6.12 1.93-6.36-1.43l-2.34-32.86c-.24-3.41-4.8-4.37-6.4-1.35l-9.1 17.21c-1.55 2.93-5.94 2.14-6.38-1.14l-3.69-27.62c-.41-3.08-4.4-4.04-6.17-1.49L-.02 382.79v117.72l82.71-25.11a221.782 221.782 0 0 1 115.55-3.59l.02-.01Z" fill="%2300735C"></path><path d="M293.32 685.57c-55.57-19.92-69.85 10.69-116.4-6.61-52.06-19.34-55.82-65.66-117.56-89.25-24.29-9.28-45.39-10.4-59.36-9.92v150.4h362c-14.45-14.11-37.74-33.53-68.68-44.62ZM1020.15 730.19H1300V516.24c-3.45 2.15-6.94 4.5-10.45 7.05-69.66 50.68-60.1 119.33-119 161.26-52.68 37.49-79.89-3.48-142.99 40.22-2.53 1.75-5 3.57-7.41 5.42Z" fill="%2302614E"></path><path d="m1241.9 467.47 10-10.27-94.32 39.59a221.775 221.775 0 0 1-142.54 9.92l-73.71-19.49 31.49 18.02c87.81 50.24 198.49 34.71 269.07-37.78l.01.01ZM163.05 486.59l95.91 55.47c70.82 40.96 158.45 39.61 227.97-3.52l75.92-47.09-59.75 19.38a221.825 221.825 0 0 1-119.55 4.85L198.26 471.8a221.781 221.781 0 0 0-108.33 1.54c25.15-4.18 51 .47 73.12 13.26v-.01Z" fill="%23409980"></path><path opacity=".2" d="M1300 0H0v730h1300V0Z" fill="%23fff"></path><path d="m899.84 730-67.57-318-84-412h-252l-68 340-11.94 390h483.51Z" fill="%23D2D5D9"></path><path opacity=".5" d="m623.18 507.52 5.88 5.39a440.16 440.16 0 0 0 173.77 98l125.46 36.73L911.52 662l-104.31-28.93A440.106 440.106 0 0 1 612.3 518.83c-5.25-5.3-.85-14.25 6.55-13.32 1.62.2 3.13.9 4.33 2v.01Z" fill="%23fff"></path><path d="m1146.93 393.49-60.89-138.61c-.62 1.3-1.38 2.55-2.29 3.72l-10.7 13.76a159.976 159.976 0 0 1-96.36 58.94l-11.82 2.25c-1.84.35-3.69.43-5.5.27l42.43 69.34a9.99 9.99 0 0 1-1.58 12.41L894.93 517.23a9.994 9.994 0 0 0-2.26 11.1l11.86 27.95a230.023 230.023 0 0 0 35.62 58.11l13.13 15.63c3.66 4.35 10.21 4.79 14.4.95l159.97-146.26c25.3-23.14 33.05-59.82 19.26-91.22h.02Z" fill="%23D2D5D9"></path><path d="m748.23 62.75-4.52-22.15c-38.73 31.99-88.4 51.21-142.56 51.21-39.85 0-77.27-10.42-109.69-28.67l-4.15 20.74C521.3 101.83 560.04 112 601.15 112c55.23 0 106.16-18.34 147.07-49.25h.01Z" fill="%23B6BABF"></path><path opacity=".5" d="M736.47 5.1 735.43 0h-17.71c-31.07 27.42-71.87 44.07-116.57 44.07-37.01 0-71.34-11.41-99.7-30.9l-3.5 17.5c29.84 18.95 65.23 29.93 103.2 29.93 52.73 0 100.51-21.18 135.32-55.49V5.1Z" fill="%23fff"></path><path d="M501.46 13.17c28.36 19.49 62.69 30.9 99.7 30.9 44.69 0 85.49-16.65 116.57-44.07H504.09l-2.63 13.17Z" fill="%23E8BE9E"></path><path d="M501.46 13.17a175.538 175.538 0 0 0 53.4 24.75 50.547 50.547 0 0 1-5.21-10.49L539.81 0h-35.72l-2.63 13.17ZM658.63 34.47a176.117 176.117 0 0 0 50.21-27.12L707.37 0h-35.72l-13.02 34.47Z" fill="%23D4AD90"></path><path d="m1146.93 393.49-52.94-120.52-7.95-18.09c-.62 1.3-1.38 2.55-2.29 3.72l-10.7 13.76c-1.45 1.87-2.94 3.7-4.47 5.49a160.016 160.016 0 0 1-91.88 53.45l-11.82 2.25c-1.84.35-3.69.43-5.5.27l11.04 18.03c.82-.05 1.64-.14 2.46-.3l11.82-2.25a159.976 159.976 0 0 0 69.35-31.71c12.02-9.51 29.81-4.98 35.98 9.05l32.56 74.13c10.37 23.6 4.54 51.18-14.48 68.58L944.21 619.2l9.09 10.82c3.66 4.35 10.21 4.79 14.4.95l159.97-146.26c25.3-23.14 33.05-59.82 19.26-91.22Z" fill="%23B6BABF"></path><path d="m304.14 305.27-18.77 216.1a79.996 79.996 0 0 0 37.89 75.12l89.17 54.67 60.62-75.98-13.95-236.61-154.96-33.29v-.01Z" fill="%23D2D5D9"></path><path d="m746 694.03-17.19 4.46a135.827 135.827 0 0 1-43.11 4.06l-3.41-.23a135.574 135.574 0 0 1-64.46-21.29c-4.34-2.79-8.78-5.65-13.03-8.39a601.648 601.648 0 0 1-39.9-28.04l-5.38-4.12c-6.52-4.99-7.54-14.39-2.23-20.64 4.52-5.34 12.25-6.66 18.31-3.14l4.34 2.52c9.57 5.56 19.52 10.45 29.85 14.45a278.19 278.19 0 0 0 95.06 18.72c12.28.24 15.11-17.02 3.41-20.73l-11.78-3.73a278.134 278.134 0 0 1-48.17-20.42l-58.63-31.67c-22.18-11.98-34.29-36.63-30.19-61.4l18.09-109.22c8.94-54 12.19-108.78 9.7-163.44l-2.2-48.21c-1.35-29.64-11.91-58.14-30.22-81.56a371.297 371.297 0 0 0-45.95 111.49l-38 163.87-16.63 179.6v163.04h394v-22a135.78 135.78 0 0 0-102.27-13.97l-.01-.01Z" fill="%23B6BABF"></path><path d="M1085.32 236.36 992.57 74.8a210.044 210.044 0 0 0-65.64-70.18L920 0H719.52l-23.06 53.11a56.515 56.515 0 0 1-16.88 21.9c-18.04 14.21-27.4 36.79-25.26 59.65l6.91 73.98c3.04 32.52 8.45 64.78 16.21 96.51L781.23 730h182.9l3.73-65.33c2.75-48.11-2.61-96.33-15.84-142.66l-73.61-257.63a10.5 10.5 0 0 1 6.5-12.75c4.58-1.68 9.71.19 12.46 4.21l47.26 69.32a20.006 20.006 0 0 0 20.27 8.38l11.82-2.25a159.976 159.976 0 0 0 96.36-58.94l10.7-13.76c4.98-6.41 5.6-15.19 1.56-22.24l-.02.01Z" fill="%234182C4"></path><path opacity=".5" d="m1039.64 392.57-8.71-14.23a20.012 20.012 0 0 0-21.69-9.02l-24.55 5.85 17.13 27.99a9.99 9.99 0 0 1-1.58 12.41l-61.63 59.51 7 24.51 91.68-88.52a14.89 14.89 0 0 0 2.36-18.5h-.01Z" fill="%23fff"></path><path d="M524.85 51.96 496.27 0h-44l-52.94 40.16a220.007 220.007 0 0 0-81.19 124.88l-18.45 78.42a660.038 660.038 0 0 0-15.42 98.24l-2.21 27.41c-.77 9.6 5.4 18.38 14.7 20.91l25.44 6.91c17.27 4.69 35.17 6.62 53.04 5.73l24.08-1.2c-.68 19.52-1.73 39.02-3.15 58.5l-19.76 270.05h124.15l64.85-439.54c4.56-30.93 6.85-62.15 6.85-93.42v-78.51c0-18.79-10.02-36.25-26.41-45.42a54.047 54.047 0 0 1-21.01-21.16h.01Z" fill="%234182C4"></path><path opacity=".5" d="m294.56 415.59-9.19 105.77c-2.06 23.68 6.52 46.67 22.77 63.13.04-.76.09-1.52.16-2.28l11.37-130.92c1.43-16.42-9.17-31.49-25.11-35.7Z" fill="%23fff"></path><path d="M524.85 51.96 496.27 0h-44L441.7 8.02h.03l11.29 21.25-4.82 72c-.58 8.16 8.34 13.53 15.27 9.19l25.81-16.13c11.3-7.07 26.43.45 26.97 13.76.02.37.02.75.01 1.13l-1.41 96.92a639.79 639.79 0 0 1-8.21 93.31L436.72 730h63.84l64.85-439.54c4.56-30.93 6.85-62.15 6.85-93.42v-83.89c0-16.59-9.6-31.48-24.41-38.97a54.091 54.091 0 0 1-23.01-22.22h.01Z" fill="%23336FB2"></path><path d="M548.27 117a9 9 0 0 0 9-9 9 9 0 0 0-9-9 9 9 0 0 0-9 9 9 9 0 0 0 9 9ZM540.27 241a9 9 0 0 0 9-9 9 9 0 0 0-9-9 9 9 0 0 0-9 9 9 9 0 0 0 9 9ZM524.27 381a9 9 0 0 0 9-9 9 9 0 0 0-9-9 9 9 0 0 0-9 9 9 9 0 0 0 9 9ZM500.27 525a9 9 0 0 0 9-9 9 9 0 0 0-9-9 9 9 0 0 0-9 9 9 9 0 0 0 9 9ZM480.27 677a9 9 0 0 0 9-9 9 9 0 0 0-9-9 9 9 0 0 0-9 9 9 9 0 0 0 9 9Z" fill="%23fff"></path><path d="m448.94 101.58 76.79-48L512 0h-59.73l-6.17 1.95a9.992 9.992 0 0 0-6.97 8.87l-5.46 81.57c-.58 8.16 8.34 13.53 15.27 9.19Z" fill="%23549EE8"></path><path d="m398.52 401.49.81-.04c.62-17.84.94-35.69.94-53.54 0-52.89 9.12-105.38 26.96-155.16 3.63-10.14-9.67-17.73-16.53-9.42a220.063 220.063 0 0 0-44.56 89.73l-16.35 70.09a47.585 47.585 0 0 0 10.26 41.84 47.579 47.579 0 0 0 38.47 16.51v-.01Z" fill="%23336FB2"></path><path d="M399.33 40.16a220.007 220.007 0 0 0-81.19 124.88l-18.45 78.42a660.038 660.038 0 0 0-15.42 98.24l-2.21 27.41c-.77 9.6 5.4 18.38 14.7 20.91l9.08 2.47a19.958 19.958 0 0 1-3.78-13.37l2.21-27.41c2.66-33.09 7.82-65.93 15.42-98.24l18.45-78.42c9.5-40.36 30.16-77.01 59.34-105.92a65.137 65.137 0 0 0 17.08-29.45c1.22-4.53-4-8.04-7.74-5.2l-7.49 5.69v-.01ZM399.32 401.54c-.68 19.49-1.72 38.96-3.15 58.41L376.41 730h14.98l19.5-266.55c.66-9.02 1.24-18.04 1.75-27.07.73-12.99-4.1-25.68-13.32-34.85v.01Z" fill="%23549EE8"></path><path d="M1085.32 236.36 992.57 74.8a210.044 210.044 0 0 0-65.64-70.18L920 0h-49.17a256.46 256.46 0 0 1 79.63 85.34l113.25 197.28c.08.15.15.29.23.44 3.18-3.42 6.23-6.99 9.12-10.71l10.7-13.76c4.98-6.41 5.6-15.19 1.56-22.24v.01ZM753.61 107.91c11.29 5.67 24.24-3.87 22.17-16.33L761.01 0h-41.49l-23.06 53.11a56.425 56.425 0 0 1-13.71 19.2l70.87 35.6h-.01Z" fill="%23336FB2"></path><path d="M781.63 1.97c-.11-.68-.29-1.34-.53-1.97h-76.68l-8.74 54.88 84.55 42.47c7.64 3.83 16.4-2.62 15-11.05l-13.6-84.33Z" fill="%23549EE8"></path><path d="M952.56 524.02c-.06-.25-.12-.49-.19-.74l-.36-1.27-73.61-257.63a10.5 10.5 0 0 1 18.77-8.8l-37.78-55.23c-8.18-11.96-26.43-9.78-31.57 3.76l-5.71 15.04a173.053 173.053 0 0 0 5.38 135.4l69.04 145.89 10.19 25.77a455.383 455.383 0 0 1 31.52 148.69l2.27 55.09h23.6l3.73-65.33c2.71-47.41-2.47-94.93-15.29-140.65l.01.01Z" fill="%23336FB2"></path><path d="M696.55 313.15a669.332 669.332 0 0 1-16.21-96.51l-8.15-87.27c-1.02-10.89 2.01-21.44 8.06-30.07 5.02-7.15 2.69-17.05-5.11-20.97a.312.312 0 0 0-.31.02c-.02.01-.03.02-.05.03-14.78 9.77-23.25 26.65-21.6 44.3l8.03 85.96c3.04 32.52 8.45 64.78 16.21 96.51L781.21 730h17.17L696.54 313.15h.01Z" fill="%23549EE8"></path></g><defs><clipPath id="clip0_755_1426"><path fill="%23fff" d="M0 0h1300v730.19H0z"></path></clipPath></defs></svg>');
      {% endif %}
       background-size: contain;
       background-repeat: no-repeat;
     }
    
    {%- if block.settings.address_icon != blank -%}
     .cartrek-address .contact-address-block #Slide-{{ section.id }}-{{ forloop.index }} .contact-address-block-card .contact-address-block-card__image-wrapper.address-icon:before{
       content:'';
       -webkit-mask-image:url('{{ block.settings.address_icon | image_url: width: 100 }}');
       mask-image:url('{{ block.settings.address_icon | image_url: width: 100 }}');
     }
    {%- endif -%} 
    
    .cartrek-address .contact-address-block #Slide-{{ section.id }}-{{ forloop.index }} .contact-address-block-card .contact-address-block-card__image-wrapper.mail-icon:before{
     content: '';
    background-color: currentcolor;
    width: 30px;
    background-repeat: no-repeat;
    height: 30px;
    background-size: contain;
    transition: all 0.3s linear;
      {%- if block.settings.mail_icon != blank -%}
      -webkit-mask-image:url('{{ block.settings.mail_icon | image_url: width: 100 }}');
      mask-image:url('{{ block.settings.mail_icon | image_url: width: 100 }}');
        {% else %}
        background-image:url('data:image/svg+xml,<svg class="placeholder-svg" preserveAspectRatio="xMidYMid slice" width="449" height="448" viewBox="0 0 449 448" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(%23clip0_662_1337)"><path d="M448.04 0H.04v448h448V0Z" fill="%23F2F2F2"></path><path d="m354.57 158.19-33.31-35.87a59.971 59.971 0 0 0-32.2-18.01l-20.99-4.2c-2.72-.49-5.45-.93-8.17-1.33l-.01.01v-.01c-1.29-.21-2.58-.31-3.88-.29-1.3.01-2.6.14-3.88.38l-7.25 1.36-7.08 1.33c-4.54.85-9.13 1.28-13.72 1.27-4.59 0-9.19-.42-13.72-1.27l-7.08-1.33-7.25-1.36c-1.28-.24-2.58-.37-3.88-.38-1.3-.02-2.6.08-3.88.29v.01l-.01-.01c-2.73.4-5.46.83-8.17 1.33l-20.99 4.2a59.971 59.971 0 0 0-32.2 18.01l-33.31 35.87c-3.03 3.26-2.81 8.37.48 11.36l32.37 29.43c3.16 2.87 8.02 2.76 11.04-.26l9.48-9.48c1.89-1.89 5.12-.55 5.12 2.12v136.76c0 4.42 3.58 8 8 8h128c4.42 0 8-3.58 8-8V191.36c0-2.67 3.23-4.01 5.12-2.12l9.48 9.48a7.994 7.994 0 0 0 11.04.26l32.37-29.43c3.29-2.99 3.51-8.1.48-11.36Zm-130.5-26.08h-.34.7H224.07Z" fill="%236C7278"></path><path d="m252.07 98.87-14.35 2.69a74.08 74.08 0 0 1-27.37 0L196 98.87c-2.56-.48-5.17-.51-7.74-.09 1.36 18.63 16.85 33.32 35.78 33.32s34.41-14.69 35.78-33.32c-2.57-.42-5.18-.39-7.74.09h-.01Z" fill="%235B6167"></path><path d="m196.02 109.55 14.34 2.7c9.04 1.7 18.31 1.7 27.35 0l14.34-2.7c1.78-.33 3.58-.44 5.38-.33 1.27-3.27 2.09-6.77 2.35-10.43-2.56-.42-5.18-.39-7.73.09l-14.34 2.7c-9.04 1.7-18.31 1.7-27.35 0l-14.34-2.7c-2.55-.48-5.17-.51-7.73-.09.27 3.66 1.08 7.16 2.35 10.43 1.8-.1 3.61 0 5.38.33Z" fill="%236C7278"></path><path d="M232.42 112.11h-16.76a1.62 1.62 0 0 0-1.62 1.62v7.76c0 .895.725 1.62 1.62 1.62h16.76a1.62 1.62 0 0 0 1.62-1.62v-7.76a1.62 1.62 0 0 0-1.62-1.62Z" fill="%23fff"></path><path d="M160.04 155.95v-51.88l-.95.19a60.02 60.02 0 0 0-32.2 18l-31.06 33.45 44.22 40.37 5.74-5.74a48.64 48.64 0 0 0 14.25-34.39ZM321.19 122.27a59.984 59.984 0 0 0-32.2-18l-.95-.19v51.88c0 12.9 5.12 25.27 14.25 34.39l5.79 5.76 44.2-40.36-31.09-33.48Z" fill="%23818990"></path><path d="M174.04 226.11c0 2.82.24 5.59.69 8.29.16.98 1 1.71 1.99 1.71h94.65c.99 0 1.83-.73 1.99-1.71.45-2.7.69-5.47.69-8.29v-.02c0-1.1-.91-1.98-2.01-1.98h-95.98c-1.1 0-2.01.88-2.01 1.98v.02h-.01ZM270.5 216.11c1.31 0 2.28-1.24 1.95-2.52-5.56-21.56-25.13-37.48-48.42-37.48-23.29 0-42.86 15.93-48.42 37.48a2.02 2.02 0 0 0 1.95 2.52H270.5ZM178.58 246.95c.53 1.15 1.1 2.29 1.71 3.39.61 1.1 1.73 1.77 2.97 1.77h81.55c1.24 0 2.37-.69 2.97-1.77.6-1.08 1.18-2.24 1.71-3.39.61-1.33-.38-2.84-1.84-2.84h-87.22c-1.46 0-2.45 1.51-1.84 2.84h-.01ZM197.57 264.11c-1.99 0-2.78 2.59-1.12 3.69a49.713 49.713 0 0 0 27.59 8.31c10.2 0 19.68-3.06 27.59-8.31 1.66-1.1.87-3.69-1.12-3.69h-52.94Z" fill="%23EB836F"></path><path d="m95.85 155.74-2.23 2.4c-3.03 3.26-2.81 8.37.48 11.36l32.37 29.43c3.16 2.87 8.02 2.76 11.04-.26l2.56-2.56-44.22-40.37ZM185.2 96.07c1.65-.29 3.18.86 3.45 2.52 2.73 17.09 17.53 30.16 35.39 30.16s32.66-13.06 35.39-30.16c.26-1.66 1.79-2.81 3.45-2.52l5.93 1.04c1.59.28 2.68 1.78 2.43 3.38-3.64 22.79-23.38 40.21-47.2 40.21-23.82 0-43.56-17.42-47.2-40.21-.25-1.6.84-3.1 2.43-3.38l5.93-1.04Z" fill="%2342474C"></path><path d="M293.9 195.51a74.154 74.154 0 0 0-10.11 51.02l.04.27c.53 3.19 1.18 6.58 1.84 10.38 1.52 8.8 2.26 17.72 2.26 26.65V295c0 14-9.37 26.26-22.87 29.95a89.888 89.888 0 0 1-42.54 1.17l-15.36-3.29a90.172 90.172 0 0 0-38.42.15l-16.73 3.73v1.41c0 4.42 3.58 8 8 8h128c4.42 0 8-3.58 8-8v-136l-2.1 3.4-.01-.01Z" fill="%23818990"></path><path d="m354.57 158.19-33.31-35.87a59.971 59.971 0 0 0-32.2-18.01l-17.92-3.58c-.57 3.35-1.49 6.59-2.72 9.67l12.12 2.42a59.971 59.971 0 0 1 32.2 18.01l33.31 35.87c2.32 2.49 2.73 6.07 1.32 8.95l6.71-6.1c3.29-2.99 3.51-8.1.48-11.36h.01Z" fill="%239FA5AB" opacity=".4"></path><path d="m352.29 155.74 2.23 2.4c3.03 3.26 2.81 8.37-.48 11.36l-32.37 29.43c-3.16 2.87-8.02 2.76-11.04-.26l-2.56-2.56 44.22-40.37Z" fill="%2342474C"></path></g><defs><clipPath id="clip0_662_1337"><path fill="%23fff" d="M.04 0h448v448H.04z"></path></clipPath></defs></svg>');
      {% endif %}
    }
    
    {%- if block.settings.phone_icon != blank -%}
    .cartrek-address .contact-address-block #Slide-{{ section.id }}-{{ forloop.index }} .contact-address-block-card .contact-address-block-card__image-wrapper.phone-icon:before{content:'';-webkit-mask-image:url('{{ block.settings.phone_icon | image_url: width: 100 }}');mask-image:url('{{ block.settings.phone_icon | image_url: width: 100 }}');}
    {%- endif -%}    
    {%- endfor  -%}   
   @media screen and (min-width: 750px) and (max-width: 989px){
   .cartrek-address .contact-address-block.custom-address-block .grid--1-col-tablet-down.grid--peek .grid__item {
    width: calc(50% - var(--grid-desktop-horizontal-spacing) * 2 / 4);
    max-width: calc(50% - var(--grid-desktop-horizontal-spacing) * 2 / 4); margin: 0;
   }  
   }
  
{%- endstyle -%}

{%- liquid
  assign columns_mobile_int = section.settings.columns_mobile | plus: 0
  assign show_mobile_slider = false
  if section.settings.swipe_on_mobile and section.blocks.size > columns_mobile_int
    assign show_mobile_slider = true
  endif
  if section.settings.swiper_enable
  assign enable_slider = true  
  endif
-%}
<div class="cartrek-address">
<div class="contact-address-block {{ section.settings.custom_class_name }} color-{{ section.settings.color_scheme }} gradient{% unless section.settings.background_style == 'none' and settings.text_boxes_border_thickness > 0 or settings.text_boxes_shadow_opacity > 0 %} background-{{ section.settings.background_style }}{% endunless %}{% if section.settings.title == blank %} no-heading{% endif %}">
  <div class="{% if section.settings.page_full_width %} page-full-width {% else %} page-width {% endif %} {% if section.settings.page_full_width_spacing %} page-full-width_spacing {% endif %}section-{{ section.id }}-padding isolate">
    <div class="row">
    {%- unless section.settings.title == blank -%}
      <div class="title-wrapper-with-link title-wrapper--self-padded-mobile title-wrapper--no-top-margin content-align--{{ section.settings.column_alignment }}">
         {%- if section.settings.sub_heading != blank -%}  
         <h6 class="sub-heading">{{ section.settings.sub_heading | escape }}</h6>
         {%- endif -%} 
         {%- if section.settings.title != blank -%}  
          <h2 class="title {{ section.settings.heading_size }}">
            {{ section.settings.title | escape }}
          </h2>
          {%- endif -%} 
          {%- if section.settings.description != blank -%}  
          <p class="description">{{ section.settings.description }}</p>
          {%- endif -%}   
          {%- if section.settings.button_label != blank -%}
            <a {% if section.settings.button_link == blank %} role="link" aria-disabled="true"{% else %} href="{{ section.settings.button_link }}"{% endif %} class="button{% if section.settings.button_style_secondary %} button--secondary{% else %} button--primary{% endif %}">{{ section.settings.button_label | escape }}</a>
          {%- endif -%}
          </div>
    {%- endunless -%}

    {% unless enable_slider %}
    <slider-component class="slider-mobile-gutter">
      {% else %}
    <swiper-slider>
      <div data-slider-options='{"loop": "true","desktop": "{{ section.settings.desktop_column }}", "tablet": "{{ section.settings.tablet_column }}","mobile": "{{ section.settings.mobile_column }}","auto_play": "{{ section.settings.auto_play }}"}'>
      <div class="swiper" data-swiper-slider>
      {%- endunless -%}
      <ul class="contact-address-block-list contains-content-container slider {% if enable_slider %} swiper-wrapper{% else %} grid grid--{{ section.settings.columns_mobile }}-col-tablet-down grid--{{ section.settings.columns_desktop }}-col-desktop{% if show_mobile_slider %} slider slider--tablet grid--peek{% endif %}{% endif %}"
        id="Slider-{{ section.id }}"
        role="list"
      >
        
        {%- for block in section.blocks -%}
          

          <li id="Slide-{{ section.id }}-{{ forloop.index }}" class="contact-address-block-list__item {% if enable_slider %} swiper-slide{% else %} grid__item{% if section.settings.swipe_on_mobile %} slider__slide{% endif %} {% endif %}{% if section.settings.block_column_alignment == 'center' %} center{% endif %}{{ empty_column }} {{ section.settings.block_style }} " {{ block.shopify_attributes }}>
            <div class="contact-address-block-card content-container color-{{ section.settings.block_color_scheme }} gradient">
               {% if block.settings.block_address != blank %}
                                 
                          <div class="address-wrapper">
                            <div class="contact-address-block-card__image-wrapper address-icon">
                            </div>    
                           <p>{{ block.settings.block_address }}</p> 
                           </div>
                         
                        {% endif %}  
                        {% if block.settings.block_contact_mail != blank %}
                        <div class="office-mail address-wrapper">
                          <div class="contact-address-block-card__image-wrapper mail-icon">
                          </div>  
                          <a href="mailto:{{ block.settings.block_contact_mail}}" class="link">
                            {{ block.settings.block_contact_mail }}</a>     
                        </div>
                        {% endif %}
                        {% if block.settings.block_contact_no != blank %}
                        <div class="contact-phone address-wrapper">
                          <div class="contact-address-block-card__image-wrapper phone-icon">
                          </div>  
                           <a href="tel:{{ block.settings.block_contact_no}}" class="link">
                           {{ block.settings.block_contact_no }}  
                           </a>
                        </div>
                        {% endif %}
            </div>
          </li>
        {%- endfor -%}
      </ul>
        
      {% unless enable_slider %}
      {%- if show_mobile_slider -%}
        {% if section.settings.arrow_on_mobile %}
        <div class="slider-buttons no-js-hidden ">
          <button type="button" class="slider-button slider-button--prev" name="previous" aria-label="{{ 'general.slider.previous_slide' | t }}">{% render 'icon-left-arrow' %}</button>
          <div class="slider-counter caption">
            <span class="slider-counter--current">1</span>
            <span aria-hidden="true"> / </span>
            <span class="visually-hidden">{{ 'general.slider.of' | t }}</span>
            <span class="slider-counter--total">{{ section.blocks.size }}</span>
          </div>
          <button type="button" class="slider-button slider-button--next" name="next" aria-label="{{ 'general.slider.next_slide' | t }}">{% render 'icon-right-arrow' %}</button>
        </div>
      {%- endif -%}
      {%- endif -%}  
    </slider-component>
        {% else %}
        {% if section.settings.swiper_navigation != blank %}
        <div class="swiper-button-next"><span></span></div>
        <div class="swiper-button-prev"><span></span></div>
        {% endif %}
    </div>
    {% if section.settings.swiper_pagination != blank %}
    <div class="swiper-pagination"></div>
    {% endif %}
    </div>
    </swiper-slider>
    {%- endunless -%}
<!--     <div class="center{% if show_mobile_slider %} small-hide medium-hide{% endif %}">
      {%- if section.settings.button_label != blank -%}
        <a class="button button--primary"{% if section.settings.button_link == blank %} role="link" aria-disabled="true"{% else %} href="{{ section.settings.button_link }}"{% endif %}>
          {{ section.settings.button_label | escape }}
        </a>
      {%- endif -%}
    </div> -->
    </div>
  </div>
</div>
</div>
{% schema %}
{
  "name": "Address-block",
  "class": "section",
  "tag": "section",
  "settings": [
    {
      "type":"checkbox",
      "id":"page_full_width",
       "default": false,
      "label": "t:sections.all.page_full_width.label"
    },
    {
      "type":"checkbox",
      "id":"page_full_width_spacing",
       "default": false,
      "label": "t:sections.all.page_full_width_spacing.label"
    },
    {
     "type": "image_picker", 
     "id":"bg_image",
      "label":"t:sections.contact-address-block.settings.bg_image.label"
    },
      {
      "type": "text",
      "id": "title",
      "default": "Support Block",
      "label": "t:sections.all.title.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__3.label"
        }
      ],
      "default": "h1",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "text",
      "id": "sub_heading",
      "label": "t:sections.all.sub_heading.label"
    },
     {
      "type": "text",
      "id": "description",
      "label": "t:sections.all.description.label"
    },
    {
      "type": "text",
      "id": "button_label",
      "default": "Button label",
      "label": "t:sections.all.button_label.label"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "t:sections.all.button_link.label"
    },
    {
      "type": "checkbox",
      "id": "button_style_secondary",
      "default": false,
      "label": "t:sections.all.button_style_secondary.label"
      },
    {
      "type": "select",
      "id": "column_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.contact-address-block.settings.column_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.contact-address-block.settings.column_alignment.options__2.label"
        }
      ],
      "default": "left",
      "label": "t:sections.contact-address-block.settings.column_alignment.label"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 6,
      "step": 1,
      "default": 3,
      "label": "t:sections.contact-address-block.settings.columns_desktop.label"
    },
    {
      "type": "select",
      "id": "background_style",
      "options": [
        {
          "value": "none",
          "label": "t:sections.contact-address-block.settings.background_style.options__1.label"
        },
        {
          "value": "primary",
          "label": "t:sections.contact-address-block.settings.background_style.options__2.label"
        }
      ],
      "default": "primary",
      "label": "t:sections.contact-address-block.settings.background_style.label"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "t:sections.all.colors.accent_1.label"
        },
        {
          "value": "accent-2",
          "label": "t:sections.all.colors.accent_2.label"
        },
        {
          "value": "background-1",
          "label": "t:sections.all.colors.background_1.label"
        },
        {
          "value": "background-2",
          "label": "t:sections.all.colors.background_2.label"
        },
        {
          "value": "inverse",
          "label": "t:sections.all.colors.inverse.label"
        }
      ],
      "default": "background-1",
      "label": "t:sections.all.colors.label"
    },
    {
      "type": "header",
      "content": "t:sections.contact-address-block.settings.block_settings.content"
    },
     
    {
      "type": "text",
      "id": "icon_outer_size",
      "label": "t:sections.contact-address-block.settings.image_outer_size.label"
    },
      {
      "type": "select",
      "id": "block_color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "t:sections.all.colors.accent_1.label"
        },
        {
          "value": "accent-2",
          "label": "t:sections.all.colors.accent_2.label"
        },
        {
          "value": "background-1",
          "label": "t:sections.all.colors.background_1.label"
        },
        {
          "value": "background-2",
          "label": "t:sections.all.colors.background_2.label"
        },
        {
          "value": "inverse",
          "label": "t:sections.all.colors.inverse.label"
        }
      ],
      "default": "background-1",
      "label": "t:sections.all.colors.label"
    },
    {
     "type": "image_picker", 
     "id":"hover_image",
      "label":"t:sections.contact-address-block.settings.hover_image.label"
    },
   {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 160,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
    {
      "type": "header",
      "content": "t:sections.all.swiper.swiper_slider_title"
    },
    {
      "type": "checkbox",
      "id": "swiper_enable",
      "default": false,
      "label": "t:sections.all.swiper.swiper_slider_enable"
    },
    {
      "type": "range",
      "id": "desktop_column",
      "min": 1,
      "max": 10,
      "step": 1,
      "label": "t:sections.all.swiper.desktop_column",
      "default": 4
    },
    {
      "type": "range",
      "id": "tablet_column",
      "min": 1,
      "max": 5,
      "step": 1,
      "label": "t:sections.all.swiper.tablet_column",
      "default": 3
    },
    {
      "type": "range",
      "id": "mobile_column",
      "min": 1,
      "max": 3,
      "step": 1,
      "label": "t:sections.all.swiper.mobile_column",
      "default": 1
    },
    {
      "type": "checkbox",
      "id": "swiper_pagination",
      "default": false,
      "label": "t:sections.all.swiper.swiper_pagination"
    },
    {
      "type": "checkbox",
      "id": "swiper_navigation",
      "default": false,
      "label": "t:sections.all.swiper.swiper_navigation"
    },
    {
      "type": "range",
      "id": "auto_play",
      "min": 0,
      "max": 5,
      "step": 1,
      "label": "t:sections.all.swiper.auto_play",
      "default": 0
    },
    {
      "type": "checkbox",
      "id": "swipe_on_mobile",
      "default": false,
      "label": "t:sections.multicolumn.settings.swipe_on_mobile.label"
    },
    {
      "type": "checkbox",
      "id": "arrow_on_mobile",
      "default": false,
      "label": "t:sections.all.swiper.controls"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "options": [
        {
          "value": "1",
          "label": "t:sections.all.swiper.options__1"
        },
        {
          "value": "2",
          "label": "t:sections.all.swiper.options__2"
        }
      ],
      "default": "1",
      "label": "t:sections.all.swiper.columns_mobile"
    },
    {
    "type": "header",
    "content": "t:sections.all.custom_class_heading.content"
    },
    {
    "type": "text",
    "id": "custom_class_name",
    "label": "t:sections.all.custom_class_name.label"
    }
  ],
  "blocks": [
    {
      "type": "column",
      "name": "t:sections.contact-address-block.blocks.column.name",
      "settings": [
        {
          "type": "image_picker",
          "id": "address_icon",
          "label": "t:sections.contact-address-block.blocks.column.settings.address_icon.label"
        },
        {
          "type": "textarea",
          "id": "block_address",
          "label": "t:sections.contact-address-block.blocks.column.settings.block_address.label"
        },
        {
          "type": "image_picker",
          "id": "mail_icon",
          "label": "t:sections.contact-address-block.blocks.column.settings.mail_icon.label"
        },
        {
          "type": "text",
          "id": "block_contact_mail",
          "label": "t:sections.contact-address-block.blocks.column.settings.block_contact_mail.label"
        },
        {
          "type": "image_picker",
          "id": "phone_icon",
          "label": "t:sections.contact-address-block.blocks.column.settings.phone_icon.label"
        },
        {
          "type": "text",
          "id": "block_contact_no",
          "label": "t:sections.contact-address-block.blocks.column.settings.block_contact_no.label"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Address-block"
    }
  ]
}
{% endschema %}
