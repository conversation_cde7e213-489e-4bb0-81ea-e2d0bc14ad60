{"settings_schema": {"cart": {"name": "<PERSON><PERSON>", "settings": {"cart_type": {"label": "Cart type", "drawer": {"label": "Drawer"}, "page": {"label": "Page"}, "notification": {"label": "Popup notification"}}, "show_vendor": {"label": "Show vendor"}, "show_cart_note": {"label": "Enable cart note"}, "show_gift_wrap": {"label": "Enable gift wrap"}, "show_shipping_info": {"label": "Enable shipping calculator"}, "goal": {"header": "<PERSON><PERSON> goal", "label": "Free shipping minimum amount"}, "show_discount_option": {"label": "Enable discount code"}, "cart_drawer": {"header": "Cart drawer", "collection": {"label": "Collection", "info": "Visible when cart drawer is empty"}}}}, "footer": {"name": "Footer", "settings": {"footer_type": {"label": "Footer style", "style1": {"label": "Style I"}, "style2": {"label": "Style II"}, "style3": {"label": "Style III"}, "style4": {"label": "Style IV"}, "style5": {"label": "Style V"}}}}, "global": {"settings": {"header__border": {"content": "Border"}, "header__shadow": {"content": "Shadow"}, "blur": {"label": "Blur"}, "corner_radius": {"label": "Corner radius"}, "horizontal_offset": {"label": "Horizontal offset"}, "vertical_offset": {"label": "Vertical offset"}, "thickness": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "opacity": {"label": "Opacity"}, "image_padding": {"label": "Image padding"}, "text_alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Text alignment"}}}, "card_style": {"name": "Cards", "settings": {"header__badge": {"content": "Badge"}, "style": {"options__1": {"label": "Icons with button"}, "options__2": {"label": "Icons only"}, "options__3": {"label": "Buttons only"}, "options__4": {"label": "Overlay"}, "options__5": {"label": "Standard"}, "label": "Style"}}}, "badges": {"name": "Badges", "settings": {"position": {"options__1": {"label": "Bottom left"}, "options__2": {"label": "Bottom right"}, "options__3": {"label": "Top left"}, "options__4": {"label": "Top right"}, "label": "Product card position"}, "sale_badge_color_scheme": {"label": "Sale badge color scheme"}, "sold_out_badge_color_scheme": {"label": "Sold out badge color scheme"}}}, "preloader": {"name": "Preloader", "settings": {"preloader_enable": {"label": "Enable"}, "preloader": {"label": "gif <PERSON>"}}}, "colors": {"name": "Colors", "settings": {"gradient_button_1": {"label": "Gradient Button Color"}, "gradient_button_hover": {"label": "Gradient <PERSON> Hover"}, "colors_solid_button_labels": {"label": "Solid button label", "info": "Used as foreground color on accent colors."}, "colors_accent_1": {"label": "Accent 1", "info": "Used for solid button background."}, "gradient_accent_1": {"label": "Accent 1 gradient"}, "colors_accent_2": {"label": "Accent 2"}, "gradient_accent_2": {"label": "Accent 2 gradient"}, "colors_accent_3": {"label": "Accent 3"}, "gradient_accent_3": {"label": "Accent 3 gradient"}, "header__1": {"content": "Primary colors"}, "header__2": {"content": "Secondary colors"}, "colors_text": {"label": "Text", "info": "Used as foreground color on background colors."}, "colors_outline_button_labels": {"label": "Outline button", "info": "Also used for text links."}, "colors_background_1": {"label": "Background 1"}, "gradient_background_1": {"label": "Background 1 gradient"}, "colors_background_2": {"label": "Background 2"}, "gradient_background_2": {"label": "Background 2 gradient"}, "colors_background_3": {"label": "Background 3"}, "gradient_background_3": {"label": "Background 3 gradient"}, "header__3": {"content": "Overlay colors"}, "overlay_color": {"label": "overlay color"}, "colors_border": {"label": "Border color"}}}, "typography": {"name": "Typography", "settings": {"type_header_font": {"label": "Font", "info": "Selecting a different font can affect the speed of your store. [Learn more about system fonts.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"}, "heading_scale": {"label": "Font size scale"}, "header__1": {"content": "Headings"}, "custom_font_script_1": {"label": "Custom font script - Heading"}, "custom_font_family_1": {"label": "Custom heading font", "info": "eg. 'Amatic SC', cursive;'"}, "header__2": {"content": "Body"}, "type_body_font": {"label": "Font", "info": "Selecting a different font can affect the speed of your store. [Learn more about system fonts.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"}, "custom_font_script_2": {"label": "Custom font script - Body"}, "custom_font_family_2": {"label": "Custom body font", "info": "eg. 'Cambay', sans-serif;'"}, "header__3": {"content": "Additional"}, "type_additional_font": {"label": "Font", "info": "Selecting a different font can affect the speed of your store. [Learn more about system fonts.](https://help.shopify.com/manual/online-store/os/store-speed/improving-speed#fonts)"}, "body_scale": {"label": "Font size scale"}, "custom_font_script_3": {"label": "Custom font script - Addtional"}, "custom_font_family_3": {"label": "Custom font - Addtional", "info": "eg. 'Cambay', sans-serif;'"}}}, "buttons": {"name": "Buttons"}, "variant_pills": {"name": "Variant pills"}, "inputs": {"name": "Inputs"}, "content_containers": {"name": "Content containers"}, "popups": {"name": "Dropdowns and pop-ups", "paragraph": "Affects areas like navigation dropdowns, pop-up modals, and cart pop-ups."}, "media": {"name": "Media"}, "drawers": {"name": "Drawers"}, "styles": {"name": "Icons", "settings": {"accent_icons": {"options__3": {"label": "Outline button"}, "options__4": {"label": "Text"}, "label": "Color"}}}, "social-media": {"name": "Social media", "settings": {"header": {"content": "Social accounts"}, "social_twitter_link": {"label": "Twitter", "info": "https://twitter.com/shopify"}, "social_facebook_link": {"label": "Facebook", "info": "https://facebook.com/shopify"}, "social_pinterest_link": {"label": "Pinterest", "info": "https://pinterest.com/shopify"}, "social_instagram_link": {"label": "Instagram", "info": "http://instagram.com/shopify"}, "social_tiktok_link": {"label": "TikTok", "info": "https://tiktok.com/@shopify"}, "social_tumblr_link": {"label": "Tumblr", "info": "https://shopify.tumblr.com"}, "social_snapchat_link": {"label": "Snapchat", "info": "https://www.snapchat.com/add/shopify"}, "social_youtube_link": {"label": "YouTube", "info": "https://www.youtube.com/shopify"}, "social_vimeo_link": {"label": "Vimeo", "info": "https://vimeo.com/shopify"}}}, "search_input": {"name": "Search behavior", "settings": {"header": {"content": "Product card"}, "predictive_search_enabled": {"label": "Enable product suggestions"}, "predictive_search_show_vendor": {"label": "Show vendor", "info": "Visible when product suggestions enabled."}, "predictive_search_show_price": {"label": "Show price", "info": "Visible when product suggestions enabled."}, "search_tags": {"label": "Key search tags", "info": "Separate by a comma, i.e \"featured, trendy, sale, new\"."}, "search_collection": {"label": "Search - Collection", "info": "Show featured product on search model window."}, "products_to_show": {"label": "Maximum products to show"}, "columns_desktop": {"label": "Number of columns on desktop"}, "image_ratio": {"label": "Image ratio", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}}, "show_secondary_image": {"label": "Show second image on hover"}, "show_vendor": {"label": "Show vendor"}, "show_new_tag": {"label": "Show new tag"}, "show_rating": {"label": "Show product rating", "info": "To display a rating, add a product rating app. [Learn more](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-collection-show-product-rating)"}, "enable_quick_buy": {"label": "Enable quick add button", "info": "Optimal with popup or drawer cart type."}}}, "favicon": {"name": "Favicon", "settings": {"favicon": {"label": "Favicon image", "info": "Will be scaled down to 32 x 32px"}, "flash-text1": {"label": "Text1"}, "flash-text2": {"label": "Text2"}}}, "currency_format": {"name": "Currency format", "settings": {"content": "Currency codes", "paragraph": "Cart and checkout prices always show currency codes. Example: $1.00 USD.", "currency_code_enabled": {"label": "Show currency codes"}}}, "layout": {"name": "Layout", "settings": {"page_width": {"label": "Page width"}, "page_width_laptop": {"label": "Page width Laptop"}, "page_width_tab": {"label": "Page width Tab"}, "page_full_width_spacing": {"label": "Page Full Width Spacing (Right & Left)"}, "spacing_sections": {"label": "Vertical space between sections"}, "header__grid": {"content": "Grid"}, "paragraph__grid": {"content": "Affects areas with a multicolumn layout."}, "spacing_grid_horizontal": {"label": "Horizontal space"}, "spacing_grid_vertical": {"label": "Vertical space"}, "header__sidebar": {"content": "Sidebar"}, "sidebar_width": {"label": "<PERSON>bar Width"}}}, "general_settings": {"name": "General settings", "settings": {"display_color_variant": {"label": "Display variant color"}, "display_item_size": {"label": "Display variant size"}, "enable_timer": {"label": "Display Timer"}, "timer_heading": {"label": "Timer Content"}, "enable_wishlist": {"label": "Display wishlist"}, "enable_compare": {"label": "Display compare"}, "enable_quickview": {"label": "Display quickview"}, "enable_quickadd": {"label": "Display cart"}, "button_style": {"icon": "Icon", "button": "<PERSON><PERSON>", "label": "Button Style"}, "enable_search": {"label": "Display search"}, "enable_cart_icon": {"label": "Display cart icon"}, "enable_stock_status_bar": {"label": "Display stock status bar"}, "enable_scroll_to_top": {"label": "Display scroll to top"}, "offer_price_enable": {"label": "Display offer price"}}}}, "layout": {"name": "Layout", "settings": {"page_width": {"label": "Page width"}, "page_full_width_spacing": {"label": "Page Full Width Spacing (Right & Left)"}, "spacing_sections": {"label": "Vertical space between sections"}, "header__grid": {"content": "Grid"}, "paragraph__grid": {"content": "Affects areas with a multicolumn layout."}, "spacing_grid_horizontal": {"label": "Horizontal space"}, "spacing_grid_vertical": {"label": "Vertical space"}}}, "sections": {"all": {"spacing": "Spacing", "page_full_width": {"label": "Show Full Width"}, "page_full_width_spacing": {"label": "Enable Right & Left Spacing (Works only on Fullwidth)"}, "text": {"label": "Text"}, "sub-title": {"label": "sub title"}, "swiper": {"swiper_slider_title": "Slide<PERSON>", "swiper_slider_enable": "Enable", "desktop_column": "Desktop Columns", "laptop_column": "Laptop Columns", "tablet_column": "Tablet Columns", "mobile_column": "Mobile Columns", "swiper_pagination": "Pagination", "swiper_navigation": "Navigation", "auto_play": "Auto Play", "effects": "Effect", "fade": "Fade", "slide": "Slide", "centered_slide": "Centered mode", "controls": "Controls on mobile", "options__1": "1 Column", "options__2": "2 Columns", "columns_mobile": "Columns on mobile"}, "padding": {"section_padding_heading": "Section padding", "padding_top": "Top padding", "padding_bottom": "Bottom padding"}, "notify": {"enable": "Enable back in stock", "notify_text": "Notify Success Text", "notify_error": "Notify Error Text"}, "footer": {"column_width": "Column width"}, "colors": {"accent_1": {"label": "Accent 1"}, "accent_2": {"label": "Accent 2"}, "background_1": {"label": "Background 1"}, "background_2": {"label": "Background 2"}, "inverse": {"label": "Inverse"}, "label": "Color scheme", "has_cards_info": "To change the card color scheme, update your theme settings."}, "heading_size": {"label": "Heading size", "options__1": {"label": "Small"}, "options__2": {"label": "Medium"}, "options__3": {"label": "Large"}}, "content_style": {"label": "Cotent style", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Overlay"}}, "layout": {"label": "Layout", "options__1": {"label": "Full width"}, "options__2": {"label": "Grid"}}, "title": {"label": "Heading"}, "sub_heading": {"label": "Sub Heading"}, "description": {"label": "Description"}, "button_label": {"label": "Button label"}, "button_link": {"label": "Button Link"}, "button_style_secondary": {"label": "Use outline button style"}, "custom_class_heading": {"content": "Add your custom class name here"}, "custom_class_name": {"label": "Custom class"}}, "announcement-bar": {"name": "Announcement bar", "settings": {"disable_announcement_bar": {"label": "Disable  Announcement bar"}, "disable_announcement_bar_mobile": {"label": "Disable Announcement bar in mobile"}}, "blocks": {"announcement": {"name": "Announcement", "settings": {"text": {"label": "Text"}, "link": {"label": "Link"}}}}}, "top-bar": {"name": "Top bar", "settings": {"disable_topbar": {"label": "Disable topbar"}, "disable_topbar_mobile": {"label": "Disable topbar in mobile"}, "enable_text_icon": {"label": "Enable Text Icon"}, "header_mail": {"label": "Mail"}, "header_phone": {"label": "Phone No"}, "text": {"label": "text"}, "link_text": {"label": "Link Text"}, "link": {"label": "Link"}, "show_social": {"label": "Show Social"}, "enable_country_selector": {"label": "Enable country/region selector"}, "header__4": {"info": "To add a country/region, go to your [payment settings.](/admin/settings/payments)"}, "header__5": {"content": "Language selector"}, "header__6": {"info": "To add a language, go to your [language settings.](/admin/settings/languages)"}, "enable_language_selector": {"label": "Enable language selector"}}}, "apps": {"name": "Apps", "settings": {"include_margins": {"label": "Make section margins the same as theme"}}, "presets": {"name": "Apps"}}, "image-gallery": {"name": "Instagram Gallery", "settings": {"image-gallery_layout": {"label": "Instagram Gallery Layout", "options__1": {"label": "Full width"}, "options__2": {"label": "Grid"}}, "title": {"label": "Heading"}, "subtitle": {"label": "Text"}, "text_style": {"label": "Text style", "options__1": {"label": "Subtitle"}, "options__2": {"label": "Uppercase"}}, "text_size": {"label": "Text size", "options__1": {"label": "Small"}, "options__2": {"label": "Medium"}, "options__3": {"label": "Large"}}, "title_image": {"label": "Title Image"}, "image_width": {"label": "Image width", "options__1": {"label": "One-third width of column"}, "options__2": {"label": "Half width of column"}, "options__3": {"label": "Full width of column"}}, "image_ratio": {"label": "Image ratio", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}, "options__4": {"label": "Circle"}}, "columns_desktop": {"label": "Number of columns on desktop"}, "overlay_image_height_settings": {"content": "Image Height Settings"}, "overlay_height": {"label": "Image Height desktop"}, "overlay_height_laptop": {"label": "Image Height laptop"}, "overlay_height_tab": {"label": "Image Height Tab"}, "overlay_height_mobile": {"label": "Image Height Mobile"}, "column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "background_style": {"label": "Secondary background", "options__1": {"label": "None"}, "options__2": {"label": "Show as column background"}}, "banner_style": {"label": "Banner style", "options__1": {"label": "Grid"}, "options__2": {"label": "Overlay"}}, "grid-column": {"label": "Item per row", "options__1": {"label": "1"}, "options__2": {"label": "2"}, "options__3": {"label": "3"}, "options__4": {"label": "4"}, "options__5": {"label": "5"}, "options__6": {"label": "6"}}, "button_label": {"label": "Button label"}, "button_link": {"label": "Button link"}, "desktop_content_alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": " Content Alignment"}}, "blocks": {"text": {"name": "Block", "settings": {"block_image": {"label": "Image"}, "block_image_brand": {"label": "image"}, "show_content": {"label": "Show_content"}, "icon_class": {"label": "Instagram Icon"}, "block_title": {"label": "Heading"}, "enable_title_link": {"label": "Enable title link"}, "block_sub_title": {"label": "Block sub title"}, "block_description": {"label": "Block description"}, "block_button_label": {"label": "<PERSON><PERSON>"}, "block_button_link": {"label": "Content link"}}}}, "presets": {"name": "Image Gallery"}}, "testimonials-home-1": {"name": "Testimonials-home-1", "settings": {"title": {"label": "Heading"}, "text": {"label": "Sub Heading"}, "columns_desktop": {"label": "Number of columns on desktop"}, "column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}}, "content_alignment": {"label": "Content alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}}, "background_style": {"label": "Secondary background", "options__1": {"label": "None"}, "options__2": {"label": "Show as column background"}}, "button_label": {"label": "Button label"}, "button_link": {"label": "Button link"}, "enable_arrows": {"label": "Enable Arrow"}, "custom_class_heading": {"content": "Add your custom class name here"}, "custom_class_name": {"label": "Custom class"}}, "blocks": {"text": {"name": "Text", "settings": {"image": {"label": "Image"}, "mask_image": {"label": "Mask Image"}, "content-heading": {"label": "Content-Heading"}, "author": {"label": "Author"}, "job_title": {"label": "Job Title"}, "quote": {"label": "Quotes"}, "link_label": {"label": "Link label"}, "link": {"label": "Link"}}}}, "presets": {"name": "Brushstrokes testimonial"}}, "breadcrumb": {"name": "Breadcrumb", "settings": {"use_breadcrumb_title": {"label": "Show heading"}, "breadcrumb_image": {"label": "Image"}, "breadcrumb_style": {"label": "Breadcrumb style", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}}}, "presets": {"name": "Breadcrumb"}}, "hotspot": {"name": "Hotspot", "settings": {"title": {"label": "Hotspot section"}, "column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "hotspot_settings": {"content": "Hotspot Settings"}, "hotspot_image": {"label": "Hotspot Image"}, "enable_hotspot_overlay": {"label": "Enable Hotspot Overlay"}, "hotspot_overlay_transparency": {"label": "Hotspot Overlay Transparency"}, "hotspot_tooltip_style": {"label": "Hotspot Tooltip Style", "options__1": {"label": "Style 1 (Displayed on Icon Click)"}, "options__2": {"label": "Style 2 (Displayed by <PERSON><PERSON><PERSON>)"}, "options__3": {"label": "Style 3 (Displayed on Icon Hover)"}}, "hotspot_style": {"label": "Hotspot Outer Style", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Spin"}, "options__3": {"label": "Fixed Border"}, "options__4": {"label": "Blank"}}, "icon_format": {"label": "Hotspot format", "options__1": {"label": "Numbered"}, "options__2": {"label": "Alphabet"}, "options__3": {"label": "Simple"}, "options__4": {"label": "Icon"}}, "icon": {"label": "Icon class (Select Icon Option above for viewing the icon)"}, "enable_box_shadow": {"label": "Enable Box Shadow"}, "hotspot_font_size_settings": {"content": "Hotspot Font Size Settings"}, "hotspot_icon_text_size": {"label": "Hotspot Icon/Text Size"}, "hotspot_size": {"label": "Hotspot Icon/Text Circle Size"}, "hotspot_outer_size": {"label": "Hotspot Icon/Text Outer Circle Size"}, "hotspot_content_size_settings": {"content": "Hotspot Content Size Settings"}, "hotspot_content_size": {"label": "Hotspot Content Size"}, "hotspot_content_size_laptop": {"label": "Hotspot Content Size - Laptop (Small Screen)"}, "hotspot_content_size_tablet": {"label": "Hotspot Content Size - Tablet"}, "hotspot_content_size_mobile": {"label": "Hotspot Content Size - Mobile"}}, "blocks": {"product": {"name": "Product", "settings": {"select_product": {"label": "Select Product"}, "top_position": {"label": "Top Position (in %)"}, "left_position": {"label": "Left Position (in %)"}, "text_position": {"label": "Content Position", "options__1": {"label": "Top"}, "options__2": {"label": "Right"}, "options__3": {"label": "Left"}, "options__4": {"label": "Bottom"}}}}, "content": {"name": "Content", "settings": {"title": {"label": "Title"}, "text": {"label": "Text"}, "link_text": {"label": "Button label"}, "link": {"label": "Link"}, "top_position": {"label": "Top Position (in %)"}, "left_position": {"label": "Left Position (in %)"}, "text_position": {"label": "Content Position", "options__1": {"label": "Top"}, "options__2": {"label": "Right"}, "options__3": {"label": "Left"}, "options__4": {"label": "Bottom"}}, "content_alignment": {"label": "Content Alignment", "options__1": {"label": "Center"}, "options__2": {"label": "Left"}, "options__3": {"label": "Right"}}}}}, "presets": {"name": "Hotspot"}}, "collage": {"name": "Collage", "settings": {"heading": {"label": "Heading"}, "column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "desktop_layout": {"label": "Desktop layout", "options__1": {"label": "Left large block"}, "options__2": {"label": "Right large block"}}, "mobile_layout": {"label": "Mobile layout", "options__1": {"label": "Collage"}, "options__2": {"label": "Column"}}, "card_styles": {"label": "Card style", "info": "Product, collection, and blog card styles can be updated in theme settings.", "options__1": {"label": "Use individual card styles"}, "options__2": {"label": "Style all as product cards"}}}, "blocks": {"image": {"name": "Image", "settings": {"image": {"label": "Image"}}}, "product": {"name": "Product", "settings": {"product": {"label": "Product"}, "secondary_background": {"label": "Show secondary background"}, "second_image": {"label": "Show second image on hover"}}}, "collection": {"name": "Collection", "settings": {"collection": {"label": "Collection"}}}, "video": {"name": "Video", "settings": {"cover_image": {"label": "Cover image"}, "video_url": {"label": "URL", "info": "Video plays in a pop-up if the section contains other blocks.", "placeholder": "Use a YouTube or Vimeo URL"}, "description": {"label": "Video alt text", "info": "Describe the video for customers using screen readers. [Learn more](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video-block)"}}}}, "presets": {"name": "Collage"}}, "collection-list": {"name": "Collection list", "settings": {"title": {"label": "Heading"}, "subtitle": {"label": "Text"}, "text_style": {"label": "Text style", "options__1": {"label": "Subtitle"}, "options__2": {"label": "Uppercase"}}, "text_size": {"label": "Text size", "options__1": {"label": "Small"}, "options__2": {"label": "Medium"}, "options__3": {"label": "Large"}}, "column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "collection_count": {"label": "Enable Collection Product Count"}, "collection_description": {"label": "Enable Collection Description"}, "image_ratio": {"label": "Image ratio", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}, "info": "Add images by editing your collections. [Learn more](https://help.shopify.com/manual/products/collections)"}, "image_style": {"label": "Image Style", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Circle"}, "info": "Image style is default"}, "show_view_all": {"label": "Enable \"View all\" button if list includes more collections than shown"}, "columns_desktop": {"label": "Number of columns on desktop"}, "header_mobile": {"content": "Mobile Layout"}, "columns_mobile": {"label": "Number of columns on mobile", "options__1": {"label": "1 column"}, "options__2": {"label": "2 columns"}}, "swipe_on_mobile": {"label": "Enable swipe on mobile", "controls": "Controls on mobile"}}, "blocks": {"featured_collection": {"name": "Collection", "settings": {"collection": {"label": "Collection"}}}}, "presets": {"name": "Collection list"}}, "form-image": {"name": "Form Image", "settings": {"phone_image": {"label": "Image"}, "layout": {"label": "Layout", "options__1": {"label": "Full width"}, "options__2": {"label": "Grid"}}, "block_alignments": {"label": "Block Alignments", "options__1": {"label": "Map with Form"}, "options__2": {"label": "Image with Form"}}, "contact_section_settings": {"content": "Contact Section Settings"}, "custom_liquid": {"label": "Custom Liquid", "info": "Add app snippets or other Liquid code to create advanced customizations."}, "address_heading": {"label": "Heading"}, "address_desc": {"label": "Description"}, "collapsible_address": {"label": "Address"}, "collapsible_contact_no": {"label": "Phone No"}, "collapsible_contact_id": {"label": "Mail Id"}}, "presets": {"name": "Form Image"}}, "contact-form": {"name": "Contact Form", "settings": {"layout": {"label": "Layout", "options__1": {"label": "Full width"}, "options__2": {"label": "Grid"}}}, "presets": {"name": "Contact form"}}, "custom-liquid": {"name": "Custom Liquid", "settings": {"custom_liquid": {"label": "Custom Liquid", "info": "Add app snippets or other Liquid code to create advanced customizations."}, "address_heading": {"label": "Heading"}, "address_desc": {"label": "Description"}, "collapsible_address": {"label": "Address"}, "collapsible_contact_no": {"label": "Phone No"}, "collapsible_contact_id": {"label": "Mail Id"}}, "presets": {"name": "Custom Liquid"}}, "featured-blog": {"name": "Blog posts", "settings": {"heading": {"label": "Heading"}, "description": {"label": "Description"}, "description_style": {"label": "Description style", "options__1": {"label": "Body"}, "options__2": {"label": "Subtitle"}, "options__3": {"label": "Uppercase"}}, "enable_desktop_slider": {"label": "Enable carousel on desktop"}, "blog": {"label": "Blog"}, "post_limit": {"label": "Number of blog posts to show"}, "columns_desktop": {"label": "Number of columns on desktop"}, "show_view_all": {"label": "Enable \"View all\" button if blog includes more blog posts than shown"}, "show_image": {"label": "Show featured image", "info": "For best results, use an image with a 3:2 aspect ratio. [Learn more](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "show_date": {"label": "Show date"}, "show_author": {"label": "Show Author"}, "enable_comments": {"label": "Show Comments"}, "show_excerpts": {"label": "Show Excerpts"}, "block_banner_style": {"label": "Block banner style", "options__1": {"label": "Grid"}, "options__2": {"label": "List"}, "options__3": {"label": "Overlay"}}, "header_mobile": {"content": "Mobile Layout"}, "columns_mobile": {"label": "Number of columns on mobile", "options__1": {"label": "1 column"}, "options__2": {"label": "2 columns"}}, "column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "swipe_on_mobile": {"label": "Enable swipe on mobile"}}, "presets": {"name": "Blog posts"}}, "cookie-banner": {"name": "GDPR Cookies", "settings": {"show_gdpr": {"label": "Enable GDPR cookies"}, "header": {"header__1": "Shipping rate calculator"}, "style": {"label": "Choose Style", "options__1": {"label": "Left"}, "options__2": {"label": "Right"}}, "color_scheme": {"info": "Visible when container displayed."}, "enable_box_shadow": {"label": "Enable box shadow"}, "text": {"label": "Text"}, "accept": {"label": "Button label"}, "decline": {"label": "Button label"}, "button_padding": {"label": "Button padding", "info": "Format - Top, Right, Bottom, Left"}}}, "newsletter-modal": {"name": "Email signup", "settings": {"color_scheme": {"label": "Color scheme", "options__1": {"label": "Accent 1"}, "options__2": {"label": "Accent 2"}, "options__3": {"label": "Background 1"}, "options__4": {"label": "Background 2"}, "options__5": {"label": "Inverse"}}, "modal_bg_opacity": {"label": "Background Opacity"}, "enable_newsletter_modal": {"label": "Enable newsletter modal"}, "cookie_expires": {"label": "How many days should modal not show", "info": "Setting to 1, will show after One day"}, "cookie_modal_delay": {"label": "Delay modal appearing", "info": "Milliseconds, e.g 1000 = 1s"}, "block_text_align": {"label": "Text alignment", "options__1": {"label": "Center"}, "options__2": {"label": "Left"}, "options__3": {"label": "Right"}}, "modal_bg_image": {"label": "Image"}, "enable_social_icon": {"label": "Enable social icons"}}, "blocks": {"heading": {"name": "Heading", "settings": {"heading": {"label": "Heading"}}}, "paragraph": {"name": "Description", "settings": {"paragraph": {"label": "Description"}}}, "email_form": {"name": "Email form"}}}, "suggested-products": {"name": "Suggested products", "settings": {"display_recent_purchase": {"label": "Enable"}, "placement": {"label": "Widget position", "options__1": {"label": "Left"}, "options__2": {"label": "Right"}}, "text": {"label": "Text"}}, "blocks": {"image": {"name": "Product", "settings": {"product": {"label": "Product"}, "from": {"label": "From"}, "time": {"label": "Time"}}}}}, "featured-collection": {"name": "Featured collection", "settings": {"title": {"label": "Heading"}, "subtitle": {"label": "Text"}, "text_style": {"label": "Text style", "options__1": {"label": "Subtitle"}, "options__2": {"label": "Uppercase"}}, "text_size": {"label": "Text size", "options__1": {"label": "Small"}, "options__2": {"label": "Medium"}, "options__3": {"label": "Large"}}, "description": {"label": "Description"}, "show_description": {"label": "Show collection description from the admin"}, "column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "description_style": {"label": "Description style", "options__1": {"label": "Body"}, "options__2": {"label": "Subtitle"}, "options__3": {"label": "Uppercase"}}, "collection": {"label": "Collection"}, "products_to_show": {"label": "Maximum products to show"}, "columns_desktop": {"label": "Number of columns on desktop"}, "show_view_all": {"label": "Enable \"View all\" if collection has more products than shown"}, "view_all_style": {"label": "\"View all\" style", "options__1": {"label": "Link"}, "options__2": {"label": "Outline button"}, "options__3": {"label": "Solid button"}}, "enable_desktop_slider": {"label": "Enable carousel on desktop"}, "full_width": {"label": "Make products full width"}, "header": {"content": "Product card"}, "image_ratio": {"label": "Image ratio", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}}, "show_secondary_image": {"label": "Show second image on hover"}, "show_vendor": {"label": "Show vendor"}, "show_new_tag": {"label": "Show new tag"}, "show_rating": {"label": "Show product rating", "info": "To display a rating, add a product rating app. [Learn more](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-collection-show-product-rating)"}, "enable_quick_buy": {"label": "Enable quick add button", "info": "Optimal with popup or drawer cart type."}, "header_mobile": {"content": "Mobile Layout"}, "columns_mobile": {"label": "Number of columns on mobile", "options__1": {"label": "1 column"}, "options__2": {"label": "2 columns"}}, "swipe_on_mobile": {"label": "Enable swipe on mobile"}}, "presets": {"name": "Featured collection"}}, "featured-product": {"name": "Featured product", "blocks": {"text": {"name": "Text", "settings": {"text": {"label": "Text"}, "text_style": {"label": "Text style", "options__1": {"label": "Body"}, "options__2": {"label": "Subtitle"}, "options__3": {"label": "Uppercase"}}}}, "title": {"name": "Title"}, "price": {"name": "Price"}, "quantity_selector": {"name": "Quantity selector"}, "variant_picker": {"name": "Variant picker", "settings": {"picker_type": {"label": "Type", "options__1": {"label": "Dropdown"}, "options__2": {"label": "Pills"}}, "picker_method": {"label": "Swatch style", "options__1": {"label": "Color label"}, "options__2": {"label": "Variant image"}}}}, "buy_buttons": {"name": "Buy buttons", "settings": {"show_dynamic_checkout": {"label": "Show dynamic checkout buttons", "info": "Using the payment methods available on your store, customers see their preferred option, like PayPal or Apple Pay. [Learn more](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}, "show_gift_card_recipient": {"label": "Show recipient information form for gift cards", "info": "Allows buyers to send gift cards on a scheduled date along with a personal message. [Learn more](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)"}}}, "description": {"name": "Description"}, "deal": {"name": "Deal Timer"}, "share": {"name": "Share", "settings": {"text": {"label": "Text"}, "featured_image_info": {"content": "If you include a link in social media posts, the page’s featured image will be shown as the preview image. [Learn more](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)"}, "title_info": {"content": "A store title and description are included with the preview image. [Learn more](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)"}}}, "custom_liquid": {"name": "Custom liquid", "settings": {"custom_liquid": {"label": "Custom liquid"}}}, "rating": {"name": "Product rating", "settings": {"paragraph": {"content": "To display a rating, add a product rating app. [Learn more](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#featured-product-rating)"}}}}, "settings": {"product": {"label": "Product"}, "secondary_background": {"label": "Show secondary background"}, "header": {"content": "Media", "info": "Learn more about [media types](https://help.shopify.com/manual/products/product-media)"}, "hide_variants": {"label": "Hide unselected variants’ media on desktop"}, "enable_video_looping": {"label": "Enable video looping"}}, "presets": {"name": "Featured product"}}, "footer": {"name": "Footer", "blocks": {"link_list": {"name": "<PERSON><PERSON>", "settings": {"heading": {"label": "Heading", "info": "Heading required to display the menu."}, "menu": {"label": "<PERSON><PERSON>", "info": "Displays only top-level menu items."}, "alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Alignment on large screen"}}}, "text": {"name": "Text", "settings": {"image": {"label": "Image"}, "heading": {"label": "Heading"}, "subtext": {"label": "Subtext"}, "alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Alignment on large screen"}}}, "social_icons": {"name": "social icons", "settings": {"heading": {"label": "Heading"}, "alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Alignment on large screen"}}}, "address": {"name": "Contact-info", "settings": {"image": {"label": "Logo"}, "address_heading": {"label": "Heading"}, "footer_address": {"label": "Address"}, "footer_contact_no": {"label": "Phone"}, "footer_contact_id": {"label": "E-Mail"}, "office_hours": {"label": "Office hours"}, "alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Alignment on large screen"}}}, "newsletter": {"name": "Newsletter", "settings": {"header__1": {"content": "<PERSON>ail Signup", "info": "Subscribers added automatically to your “accepted marketing” customer list. [Learn more](https://help.shopify.com/manual/customers/manage-customers)"}, "heading": {"label": "Heading"}, "newsletter_subtext": {"label": "text"}, "alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Alignment on large screen"}, "enable_input_field": {"label": "Underline Input Field"}, "button_style": {"options__1": {"label": "<PERSON><PERSON>"}, "options__2": {"label": "Icon"}, "label": "Button Style"}}}}, "settings": {"image": {"label": "Background image"}, "header__2": {"content": "Social media icons", "info": "To display your social media accounts, link them in your theme settings."}, "show_social": {"label": "Show social media icons"}, "header__3": {"content": "Country/region selector"}, "header__4": {"info": "To add a country/region, go to your [payment settings.](/admin/settings/payments)"}, "enable_country_selector": {"label": "Enable country/region selector"}, "header__5": {"content": "Language selector"}, "header__6": {"info": "To add a language, go to your [language settings.](/admin/settings/languages)"}, "enable_language_selector": {"label": "Enable language selector"}, "header__7": {"content": "Payment methods"}, "header__9": {"content": "Follow on Shop", "info": "To allow customers to follow your store on the Shop app from your storefront, Shop Pay must be enabled. [Learn more](https://help.shopify.com/manual/online-store/themes/customizing-themes/follow-on-shop)"}, "enable_follow_on_shop": {"label": "Enable Follow on Shop"}, "payment_enable": {"label": "Show payment icons"}, "copyright_content": {"label": "Custom copyright"}, "footer_default": {"label": "Enable default style"}, "header__8": {"content": "Footer style", "info": "To display your Footer style, link them in your theme settings."}, "border_right": {"label": "Enable border right"}, "margin_top": {"label": "Top margin"}}}, "footer-style1": {"name": "Footer style1", "blocks": {"link_list": {"name": "<PERSON><PERSON>", "settings": {"heading": {"label": "Heading", "info": "Heading required to display the menu."}, "menu": {"label": "<PERSON><PERSON>", "info": "Displays only top-level menu items."}, "alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Alignment on large screen"}}}, "text": {"name": "Text", "settings": {"image": {"label": "Image"}, "heading": {"label": "Heading"}, "subtext": {"label": "Subtext"}, "alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Alignment on large screen"}}}, "address": {"name": "Contact-info", "settings": {"address_heading": {"label": "Heading"}, "footer_address": {"label": "Address"}, "footer_contact_no": {"label": "Phone"}, "footer_contact_id": {"label": "E-Mail"}, "office_hours": {"label": "Office hours"}, "alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Alignment on large screen"}}}, "newsletter": {"name": "Newsletter", "settings": {"header__1": {"content": "<PERSON>ail Signup", "info": "Subscribers added automatically to your “accepted marketing” customer list. [Learn more](https://help.shopify.com/manual/customers/manage-customers)"}, "heading": {"label": "Heading"}, "newsletter_subtext": {"label": "text"}, "alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Alignment on large screen"}, "enable_socialicon": {"label": "Enable Social icon"}}}}, "settings": {"image": {"label": "Background image"}, "header__2": {"content": "Social media icons", "info": "To display your social media accounts, link them in your theme settings."}, "show_social": {"label": "Show social media icons"}, "show_usd": {"label": "Show Enable country/region selector", "info": "Social media icon enable "}, "copyright_content": {"label": "Custom copyright"}, "header__3": {"content": "Country/region selector"}, "header__4": {"info": "To add a country/region, go to your [payment settings.](/admin/settings/payments)"}, "enable_country_selector": {"label": "Enable country/region selector"}, "header__5": {"content": "Language selector"}, "header__6": {"info": "To add a language, go to your [language settings.](/admin/settings/languages)"}, "enable_language_selector": {"label": "Enable language selector"}, "header__7": {"content": "Payment methods"}, "payment_enable": {"label": "Show payment icons"}, "footer_default": {"label": "Enable default style"}, "header__8": {"content": "Footer style", "info": "To display your Footer style, link them in your theme settings."}, "border_right": {"label": "Enable border right"}, "margin_top": {"label": "Top margin"}}}, "footer-style2": {"name": "Footer style2", "blocks": {"link_list": {"name": "<PERSON><PERSON>", "settings": {"heading": {"label": "Heading", "info": "Heading required to display the menu."}, "menu": {"label": "<PERSON><PERSON>", "info": "Displays only top-level menu items."}, "enable_menusocialicon": {"label": "Enable Social icon"}, "alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Alignment on large screen"}}}, "text": {"name": "Text", "settings": {"image": {"label": "Image"}, "heading": {"label": "Heading"}, "subtext": {"label": "Subtext"}, "alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Alignment on large screen"}}}, "address": {"name": "Contact-info", "settings": {"address_heading": {"label": "Heading"}, "footer_address": {"label": "Address"}, "footer_contact_no": {"label": "Phone"}, "footer_contact_id": {"label": "E-Mail"}, "office_hours": {"label": "Office hours"}, "alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Alignment on large screen"}}}, "newsletter": {"name": "Newsletter", "settings": {"header__1": {"content": "<PERSON>ail Signup", "info": "Subscribers added automatically to your “accepted marketing” customer list. [Learn more](https://help.shopify.com/manual/customers/manage-customers)"}, "sub_heading": {"label": "Sub Heading"}, "heading": {"label": "Heading"}, "newsletter_subtext": {"label": "text"}, "alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Alignment on large screen"}, "enable_socialicon": {"label": "Enable Social icon"}}}}, "settings": {"image": {"label": "Background image"}, "header__2": {"content": "Social media icons", "info": "To display your social media accounts, link them in your theme settings."}, "show_social": {"label": "Show social media icons"}, "show_usd": {"label": "Show Enable country/region selector", "info": "Social media icon enable "}, "show_footer_bottom": {"label": "Show Footer bottom"}, "header__3": {"content": "Country/region selector"}, "header__4": {"info": "To add a country/region, go to your [payment settings.](/admin/settings/payments)"}, "enable_country_selector": {"label": "Enable country/region selector"}, "header__5": {"content": "Language selector"}, "header__6": {"info": "To add a language, go to your [language settings.](/admin/settings/languages)"}, "enable_language_selector": {"label": "Enable language selector"}, "header__7": {"content": "Payment methods"}, "payment_enable": {"label": "Show payment icons"}, "footer_default": {"label": "Enable default style"}, "header__8": {"content": "Footer style", "info": "To display your Footer style, link them in your theme settings."}, "border_right": {"label": "Enable border right"}, "margin_top": {"label": "Top margin"}}}, "footer-style3": {"name": "Footer", "blocks": {"link_list": {"name": "<PERSON><PERSON>", "settings": {"heading": {"label": "Heading", "info": "Heading required to display the menu."}, "menu": {"label": "<PERSON><PERSON>", "info": "Displays only top-level menu items."}, "alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Alignment on large screen"}}}, "text": {"name": "Text", "settings": {"image": {"label": "Image"}, "heading": {"label": "Heading"}, "subtext": {"label": "Subtext"}, "alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Alignment on large screen"}}}, "address": {"name": "Contact-info", "settings": {"address_heading": {"label": "Heading"}, "footer_address": {"label": "Address"}, "footer_contact_no": {"label": "Phone"}, "footer_contact_id": {"label": "E-Mail"}, "office_hours": {"label": "Office hours"}, "enable_socialicon": {"label": "Enable Social icon"}, "alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Alignment on large screen"}}}, "newsletter": {"name": "Newsletter", "settings": {"header__1": {"content": "<PERSON>ail Signup", "info": "Subscribers added automatically to your “accepted marketing” customer list. [Learn more](https://help.shopify.com/manual/customers/manage-customers)"}, "sub_heading": {"label": "Sub Heading"}, "heading": {"label": "Heading"}, "newsletter_subtext": {"label": "text"}, "alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Alignment on large screen"}, "enable_socialicon": {"label": "Enable Social icon"}}}}, "settings": {"image": {"label": "Background image"}, "header__2": {"content": "Social media icons", "info": "To display your social media accounts, link them in your theme settings."}, "show_social": {"label": "Show social media icons"}, "show_usd": {"label": "Show Enable country/region selector", "info": "Social media icon enable "}, "show_footer_bottom": {"label": "Show Footer bottom"}, "copyright_content": {"label": "Custom copyright"}, "header__3": {"content": "Country/region selector"}, "header__4": {"info": "To add a country/region, go to your [payment settings.](/admin/settings/payments)"}, "enable_country_selector": {"label": "Enable country/region selector"}, "header__5": {"content": "Language selector"}, "header__6": {"info": "To add a language, go to your [language settings.](/admin/settings/languages)"}, "enable_language_selector": {"label": "Enable language selector"}, "header__7": {"content": "Payment methods"}, "payment_enable": {"label": "Show payment icons"}, "footer_default": {"label": "Enable default style"}, "header__8": {"content": "Footer style", "info": "To display your Footer style, link them in your theme settings."}, "border_right": {"label": "Enable border right"}, "show_bottom-icon": {"label": "Enable footer social icon"}, "margin_top": {"label": "Top margin"}}}, "footer-style4": {"name": "Footer style4", "blocks": {"link_list": {"name": "<PERSON><PERSON>", "settings": {"heading": {"label": "Heading", "info": "Heading required to display the menu."}, "menu": {"label": "<PERSON><PERSON>", "info": "Displays only top-level menu items."}, "alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Alignment on large screen"}}}, "text": {"name": "Text", "settings": {"image": {"label": "Image"}, "new_image": {"label": "Image"}, "heading": {"label": "Heading"}, "subtext": {"label": "Subtext"}, "alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Alignment on large screen"}, "enable_textsocialicon": {"label": "Enable Social icon"}}}, "address": {"name": "Contact-info", "settings": {"address_heading": {"label": "Heading"}, "footer_address": {"label": "Address"}, "footer_contact_no": {"label": "Phone"}, "footer_contact_id": {"label": "E-Mail"}, "office_hours": {"label": "Office hours"}, "enable_socialicon": {"label": "Enable Social icon"}, "alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Alignment on large screen"}}}, "newsletter": {"name": "Newsletter", "settings": {"header__1": {"content": "<PERSON>ail Signup", "info": "Subscribers added automatically to your “accepted marketing” customer list. [Learn more](https://help.shopify.com/manual/customers/manage-customers)"}, "sub_heading": {"label": "Sub Heading"}, "heading": {"label": "Heading"}, "newsletter_subtext": {"label": "text"}, "alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Alignment on large screen"}, "enable_socialicon": {"label": "Enable Social icon"}}}}, "settings": {"image": {"label": "Background image"}, "header__2": {"content": "Social media icons", "info": "To display your social media accounts, link them in your theme settings."}, "show_social": {"label": "Show social media icons"}, "show_usd": {"label": "Show Enable country/region selector", "info": "Social media icon enable "}, "show_footer_bottom": {"label": "Show Footer bottom"}, "header__3": {"content": "Country/region selector"}, "header__4": {"info": "To add a country/region, go to your [payment settings.](/admin/settings/payments)"}, "enable_country_selector": {"label": "Enable country/region selector"}, "header__5": {"content": "Language selector"}, "header__6": {"info": "To add a language, go to your [language settings.](/admin/settings/languages)"}, "enable_language_selector": {"label": "Enable language selector"}, "header__7": {"content": "Payment methods"}, "payment_enable": {"label": "Show payment icons"}, "footer_default": {"label": "Enable default style"}, "header__8": {"content": "Footer style", "info": "To display your Footer style, link them in your theme settings."}, "border_right": {"label": "Enable border right"}, "show_bottom-icon": {"label": "Enable footer social icon"}, "margin_top": {"label": "Top margin"}}}, "header": {"name": "Header", "settings": {"logo": {"label": "Logo image"}, "logo_width": {"unit": "px", "label": "Custom logo width"}, "logo_position": {"label": "Desktop logo position", "options__1": {"label": "Middle left"}, "options__2": {"label": "Middle"}, "options__3": {"label": "Top left"}, "options__4": {"label": "Top center"}, "options__5": {"label": "Logo + Menu"}, "info": "Position is automatically optimized for mobile."}, "menu_alignment": {"label": "Menu Alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}}, "menu": {"label": "<PERSON><PERSON>"}, "menu_type_desktop": {"label": "Desktop menu type", "info": "Menu type is automatically optimized for mobile.", "options__1": {"label": "Dropdown"}, "options__2": {"label": "Mega menu"}}, "mobile_screen_width": {"label": "Mobile Menu start from"}, "show_line_separator": {"label": "Show separator line"}, "enable_sticky_header": {"label": "Enable sticky header", "info": "Header shows on the screen as customers scroll up."}, "enable_transparent_header": {"label": "Enable transparent header"}, "enable_search_box": {"label": "Enable Search Box", "info": "If Only works middle Logo position"}, "show_secondary_menu": {"label": "Enable seconday menu"}, "secondary_menu": {"label": "Secondary Menu", "info": "If Only works Middle Logo position"}, "show_category_menu": {"label": "Enable category menu(If only works logo position top left & top center)"}, "category_menu": {"label": "Category Menu"}, "header__3": {"content": "Country/region selector"}, "header__4": {"info": "To add a country/region, go to your [payment settings.](/admin/settings/payments)"}, "enable_country_selector": {"label": "Enable country/region selector"}, "header__5": {"content": "Language selector"}, "header__6": {"info": "To add a language, go to your [language settings.](/admin/settings/languages)"}, "enable_language_selector": {"label": "Enable language selector"}, "margin_bottom": {"label": "Bottom margin"}, "header": {"content": "Header <PERSON>"}, "show_search": {"label": "Enable Search"}, "show_header_wishlist": {"label": "Enable Wishlist"}, "show_header_compare": {"label": "Enable Co<PERSON>are"}, "show_account": {"label": "Enable Account"}, "show_header_cart": {"label": "Enable <PERSON>"}, "search_style": {"label": "Search style", "options__1": {"label": "Model"}, "options__2": {"label": "Form"}, "options__3": {"label": "None"}}, "header_icon_type": {"label": "Icons type", "options__1": {"label": "Icon"}, "options__2": {"label": "Text"}}}}, "image-banner": {"name": "Image banner", "settings": {"image": {"label": "First image"}, "image_2": {"label": "Second image"}, "image_overlay_opacity": {"label": "Image overlay opacity"}, "image_height": {"label": "Banner height", "options__1": {"label": "Small"}, "options__2": {"label": "Medium"}, "options__3": {"label": "Large"}, "info": "For best results, use an image with a 3:2 aspect ratio. [Learn more](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "desktop_content_position": {"options__1": {"label": "Top Left"}, "options__2": {"label": "Top Center"}, "options__3": {"label": "Top Right"}, "options__4": {"label": "Middle Left"}, "options__5": {"label": "Middle Center"}, "options__6": {"label": "Middle Right"}, "options__7": {"label": "Bottom Left"}, "options__8": {"label": "Bottom Center"}, "options__9": {"label": "Bottom Right"}, "label": "Desktop content position"}, "show_text_box": {"label": "Show container on desktop"}, "desktop_content_alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Desktop content alignment"}, "color_scheme": {"info": "Visible when container displayed."}, "header": {"content": "Section Margin"}, "mobile_content_alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Mobile content alignment"}, "stack_images_on_mobile": {"label": "Stack images on mobile"}, "show_text_below": {"label": "Show container on mobile"}, "adapt_height_first_image": {"label": "Adapt section height to first image size", "info": "Overwrites image banner height setting when checked."}, "margin_top": {"label": "Margin top"}, "margin_bottom": {"label": "Margin bottom"}}, "blocks": {"heading": {"name": "Heading", "settings": {"heading": {"label": "Heading"}}}, "text": {"name": "Text", "settings": {"text": {"label": "Description"}, "text_style": {"options__1": {"label": "Body"}, "options__2": {"label": "Subtitle"}, "options__3": {"label": "Uppercase"}, "label": "Text style"}}}, "list": {"name": "List", "settings": {"text": {"label": "Text"}, "icon": {"label": "Icon"}}}, "buttons": {"name": "Buttons", "settings": {"button_label_1": {"label": "First button label", "info": "Leave the label blank to hide the button."}, "button_link_1": {"label": "First button link"}, "button_style_secondary_1": {"label": "Use outline button style"}, "button_label_2": {"label": "Second button label", "info": "Leave the label blank to hide the button."}, "button_link_2": {"label": "Second button link"}, "button_style_secondary_2": {"label": "Use outline button style"}}}}, "presets": {"name": "Image banner"}}, "image-with-text": {"name": "Image with text", "settings": {"image": {"label": "Image"}, "height": {"options__1": {"label": "Adapt to image"}, "options__2": {"label": "Small"}, "options__3": {"label": "Large"}, "label": "Image height"}, "desktop_image_width": {"options__1": {"label": "Small"}, "options__2": {"label": "Medium"}, "options__3": {"label": "Large"}, "label": "Desktop image width", "info": "Image is automatically optimized for mobile."}, "layout": {"options__1": {"label": "Image first"}, "options__2": {"label": "Image second"}, "label": "Desktop image placement", "info": "Image first is the default mobile layout."}, "desktop_content_alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Desktop content alignment"}, "desktop_content_position": {"options__1": {"label": "Top"}, "options__2": {"label": "Middle"}, "options__3": {"label": "Bottom"}, "label": "Desktop content position"}, "content_layout": {"options__1": {"label": "No overlap"}, "options__2": {"label": "Overlap"}, "label": "Content layout"}, "mobile_content_alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Mobile content alignment"}}, "blocks": {"heading": {"name": "Heading", "settings": {"heading": {"label": "Heading"}}}, "caption": {"name": "Caption", "settings": {"text": {"label": "Text"}, "text_style": {"label": "Text style", "options__1": {"label": "Subtitle"}, "options__2": {"label": "Uppercase"}}, "caption_size": {"label": "Text size", "options__1": {"label": "Small"}, "options__2": {"label": "Medium"}, "options__3": {"label": "Large"}}}}, "text": {"name": "Text", "settings": {"text": {"label": "Content"}, "text_style": {"label": "Text style", "options__1": {"label": "Body"}, "options__2": {"label": "Subtitle"}}}}, "button": {"name": "<PERSON><PERSON>", "settings": {"button_label": {"label": "Button label", "info": "Leave the label blank to hide the button."}, "button_link": {"label": "Button link"}}}}, "presets": {"name": "Image with text"}}, "deal-banner": {"name": "Deal banner", "settings": {"column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "image": {"label": "banner image"}, "image_2": {"label": "Second image"}, "image_overlay_opacity": {"label": "Image overlay opacity"}, "secoundary_image": {"label": "Secondary Image"}, "image_height": {"label": "Banner height", "options__1": {"label": "Small"}, "options__2": {"label": "Medium"}, "options__3": {"label": "Large"}, "info": "For best results, use an image with a 3:2 aspect ratio. [Learn more](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "desktop_content_position": {"options__1": {"label": "Top Left"}, "options__2": {"label": "Top Center"}, "options__3": {"label": "Top Right"}, "options__4": {"label": "Middle Left"}, "options__5": {"label": "Middle Center"}, "options__6": {"label": "Middle Right"}, "options__7": {"label": "Bottom Left"}, "options__8": {"label": "Bottom Center"}, "options__9": {"label": "Bottom Right"}, "label": "Desktop content position"}, "show_text_box": {"label": "Show container on desktop"}, "desktop_content_alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Desktop content alignment"}, "color_scheme": {"info": "Visible when container displayed."}, "header": {"content": "Mobile Layout"}, "mobile_content_alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Mobile content alignment"}, "stack_images_on_mobile": {"label": "Stack images on mobile"}, "show_text_below": {"label": "Show container on mobile"}, "adapt_height_first_image": {"label": "Adapt section height to first image size", "info": "Overwrites image banner height setting when checked."}, "deal_banner_settings": {"content": "Deal Banner Settings"}, "heading": {"label": "Heading"}, "sub-heading": {"label": "Sub Heading"}, "text": {"label": "Description"}, "deal_end_date": {"label": "Deal End Date", "info": "Format should be 25 JAN 2025"}, "enable_two_column_section": {"label": "Enable Two Column Section"}, "button_label_1": {"label": "First button label"}, "button_link_1": {"label": "First button link"}, "button_style_secondary_1": {"label": "Use outline button style"}, "button_label_2": {"label": "Second button label"}, "button_link_2": {"label": "Second button link"}, "button_style_secondary_2": {"label": "Use outline button style"}}, "presets": {"name": "Deal banner"}}, "specification-block": {"name": "Specification block", "settings": {"column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "main_block_heading": {"content": "Main Block Heading"}, "enable_image": {"label": "Enable Image"}, "primary_image": {"label": "Primary Image"}, "primary_title": {"label": "Title"}, "primary_sub_title": {"label": "Sub Title"}, "primary_description": {"label": "Description"}, "primary_button_text": {"label": "Button label"}, "primary_button_link": {"label": "Button link"}, "primary_text_align": {"label": "Banner Text Alignment", "options__1": {"label": "center"}, "options__2": {"label": "Left"}, "options__3": {"label": "Right"}}, "vertical_position": {"label": "vertical position", "options__1": {"label": "vertical top"}, "options__2": {"label": "vertical center"}, "options__3": {"label": "vertical bottom"}}, "banner_style": {"label": "Banner Style", "options__1": {"label": "Grid"}, "options__2": {"label": "Overlay"}}, "specification_style": {"label": "Specification Style", "options__1": {"label": "style1"}, "options__2": {"label": "style2"}, "options__3": {"label": "style3"}}, "overlay_style_height": {"label": "Minimum Height (Only for Overlay Style)"}, "primary_position": {"label": "Banner Content position(For Overlay Style Only)", "options__1": {"label": "Top Left"}, "options__2": {"label": "Top Center"}, "options__3": {"label": "Top Right"}, "options__4": {"label": "Center Left"}, "options__5": {"label": "Center"}, "options__6": {"label": "Center Right"}, "options__7": {"label": "Bottom Left"}, "options__8": {"label": "Botton Center"}, "options__9": {"label": "Bottom Right"}}, "content_reverse": {"label": "content reverse"}, "addition_block_heading": {"content": "Additional Block Heading"}, "additional_heading": {"label": "Title"}, "additional_sub_heading": {"label": "Sub Title"}, "additional_description": {"label": "Description"}, "additional_link_text": {"label": "Additional Button label"}, "additional_link": {"label": "Additional button link"}, "additional_text_align": {"label": "Additional Text Alignment", "options__1": {"label": "center"}, "options__2": {"label": "Left"}, "options__3": {"label": "Right"}}}, "blocks": {"text": {"name": "Images", "settings": {"block-image": {"label": "Block Image"}, "title": {"label": "Title"}, "desc": {"label": "Description"}, "link_text": {"label": "Button label"}, "link": {"label": "Button link"}}}}, "presets": {"name": "Specification Block"}}, "main-article": {"name": "Blog post", "blocks": {"featured_image": {"name": "Featured image", "settings": {"image_height": {"label": "Featured image height", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Small"}, "options__3": {"label": "Medium"}, "options__4": {"label": "Large"}, "info": "For best results, use an image with a 16:9 aspect ratio. [Learn more](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}}, "title": {"name": "Title", "settings": {"blog_show_date": {"label": "Show date"}, "blog_show_author": {"label": "Show author"}}}, "content": {"name": "Content"}, "share": {"name": "Share", "settings": {"text": {"label": "Text"}, "featured_image_info": {"content": "If you include a link in social media posts, the page’s featured image will be shown as the preview image. [Learn more](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "A store title and description are included with the preview image. [Learn more](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}}}}, "settings": {"layout": {"label": "Layout", "options__1": {"label": "Full width"}, "options__2": {"label": "Grid"}}}}, "main-blog": {"name": "Blog posts", "settings": {"blog_layout": {"label": "Blog Layout", "options__1": {"label": "Full width"}, "options__2": {"label": "Grid"}}, "show_social": {"label": "show Social Icon"}, "header": {"content": "Blog post card"}, "show_image": {"label": "Show featured image"}, "show_date": {"label": "Show date"}, "show_author": {"label": "Show author"}, "enable_comments": {"label": "Show Comments"}, "side_show_excerpts": {"label": "Show sidebar Excerpts"}, "paragraph": {"content": "Change excerpts by editing your blog posts. [Learn more](https://help.shopify.com/manual/online-store/blogs/writing-blogs#display-an-excerpt-from-a-blog-post)"}, "layout": {"label": "Desktop layout", "options__1": {"label": "Grid"}, "options__2": {"label": "Collage"}, "options__3": {"label": "List"}, "info": "Posts are stacked on mobile."}, "image_height": {"label": "Featured image height", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Small"}, "options__3": {"label": "Medium"}, "options__4": {"label": "Large"}, "info": "For best results, use an image with a 3:2 aspect ratio. [Learn more](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "show_articles": {"label": "Show articles"}, "article_title": {"label": "Recent articles"}, "limit": {"label": "Limit"}, "sidebar_settings": {"label": "Layout setting", "options__1": {"label": "Left sidebar"}, "options__2": {"label": "Right sidebar"}, "options__3": {"label": "No sidebar"}}, "show_tags": {"label": "Show tags"}, "tags_title": {"label": "Tags"}, "show_excerpts": {"label": "Show excerpts"}, "sidebar__1": {"content": "Carousel"}, "show_carousel": {"label": "Show carousel "}, "enable_quick_buy": {"label": "Enable quick add button"}, "show_promo": {"label": "Show promo"}, "sidebar_image": {"label": "Image"}, "sidebar_title": {"label": "Heading"}, "sidebar_button": {"label": "<PERSON><PERSON>"}, "sidebar_link": {"label": "Link"}, "sidebar__2": {"content": "Promo banner"}, "carousel_title": {"label": "Heading"}, "carousel": {"label": "Collection"}, "carousel_limit": {"label": "Limit"}, "enable_quick_add": {"label": "Enable quick add button"}, "sidebar__3": {"content": "Product list"}, "show_collection": {"label": "Enable"}, "collection_title": {"label": "Heading"}, "collection": {"label": "Collection"}, "collection_limit": {"label": "Limit"}}}, "main-cart-footer": {"name": "Subtotal", "settings": {"show_cart_note": {"label": "Enable cart note"}}, "blocks": {"subtotal": {"name": "Subtotal price"}, "buttons": {"name": "Checkout button"}}}, "main-cart-items": {"name": "Items", "settings": {"show_vendor": {"label": "Show vendor"}}}, "main-collection-banner": {"name": "Collection banner", "settings": {"paragraph": {"content": "Add a description or image by editing your collection. [Learn more](https://help.shopify.com/manual/products/collections/collection-layout)"}, "show_collection_description": {"label": "Show collection description"}, "show_collection_image": {"label": "Show collection image", "info": "For best results, use an image with a 16:9 aspect ratio. [Learn more](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "layout": {"label": "Layout", "options__1": {"label": "Full width"}, "options__2": {"label": "Grid"}}}}, "main-collection-product-grid": {"name": "Product grid", "settings": {"products_per_page": {"label": "Products per page"}, "columns_desktop": {"label": "Number of columns on desktop"}, "layout": {"label": "Layout", "options__1": {"label": "Full width"}, "options__2": {"label": "Grid"}}, "enable_filtering": {"label": "Enable filtering", "info": "Customize [filters](/admin/menus)"}, "filter_type": {"label": "Desktop filter layout", "options__1": {"label": "Horizontal"}, "options__2": {"label": "Vertical"}, "options__3": {"label": "Drawer"}, "info": "Drawer is the default mobile layout."}, "enable_sorting": {"label": "Enable sorting"}, "image_ratio": {"label": "Image ratio", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}}, "show_secondary_image": {"label": "Show second image on hover"}, "show_vendor": {"label": "Show vendor"}, "show_new_tag": {"label": "Show New Tag"}, "show_rating": {"label": "Show product rating", "info": "To display a rating, add a product rating app. [Learn more](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-grid-section-settings)"}, "header__1": {"content": "Filtering and sorting"}, "header__3": {"content": "Product card"}, "enable_tags": {"label": "Enable filtering", "info": "[Customize filters](/admin/menus)"}, "enable_quick_buy": {"label": "Enable quick add button", "info": "Optimal with popup or drawer cart type."}, "collapse_on_larger_devices": {"label": "Collapse on desktop"}, "header_mobile": {"content": "Mobile Layout"}, "columns_mobile": {"label": "Number of columns on mobile", "options__1": {"label": "1 column"}, "options__2": {"label": "2 columns"}}, "sidebar_settings": {"label": "Layout setting", "options__1": {"label": "Left sidebar"}, "options__2": {"label": "Right sidebar"}, "options__3": {"label": "No sidebar"}}, "enable_sort": {"label": "Enable sorting"}, "pagination": {"label": "Pagination style", "numbers": {"label": "Numbers"}, "loadmore_btn": {"label": "Loadmore button"}, "infinit_scroll": {"label": "Infinite scrolling"}}, "enable_item_per_view": {"label": "Enable item view"}, "enable_grid_list": {"label": "Enable grid/list view"}, "sidebar__1": {"content": "Promo banner"}, "show_image": {"label": "Enable"}, "sidebar_image": {"label": "Image"}, "sidebar_title": {"label": "Heading"}, "sidebar_button": {"label": "<PERSON><PERSON>"}, "sidebar_link": {"label": "Link"}, "sidebar__2": {"content": "Carousel"}, "show_carousel": {"label": "Enable"}, "carousel_title": {"label": "Heading"}, "carousel": {"label": "Collection"}, "carousel_limit": {"label": "Limit"}, "sidebar__3": {"content": "Product list"}, "show_collection": {"label": "Enable"}, "collection_title": {"label": "Heading"}, "collection": {"label": "Collection"}, "collection_limit": {"label": "Limit"}, "sidebar__4": {"content": "<PERSON><PERSON>"}, "show_menu": {"label": "Enable"}, "menu_title": {"label": "Heading"}, "linklist": {"label": "<PERSON><PERSON>"}}}, "main-list-collections": {"name": "Collections list page", "settings": {"title": {"label": "Heading"}, "layout": {"label": "Layout", "options__1": {"label": "Full width"}, "options__2": {"label": "Grid"}}, "collection_layout": {"label": "Collection Layout", "options__1": {"label": "Overlay"}, "options__2": {"label": "Grid"}}, "collection_button_label": {"label": "Button label"}, "sort": {"label": "Sort collections by:", "options__1": {"label": "Alphabetically, A-Z"}, "options__2": {"label": "Alphabetically, Z-A"}, "options__3": {"label": "Date, new to old"}, "options__4": {"label": "Date, old to new"}, "options__5": {"label": "Product count, high to low"}, "options__6": {"label": "Product count, low to high"}}, "image_ratio": {"label": "Image ratio", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}, "info": "Add images by editing your collections. [Learn more](https://help.shopify.com/manual/products/collections)"}, "columns_desktop": {"label": "Number of columns on desktop"}, "header_mobile": {"content": "Mobile Layout"}, "columns_mobile": {"label": "Number of columns on mobile", "options__1": {"label": "1 column"}, "options__2": {"label": "2 columns"}}}}, "main-page": {"name": "Page"}, "main-password-footer": {"name": "Password footer"}, "main-password-header": {"name": "Password header", "settings": {"logo": {"label": "Logo image"}, "logo_max_width": {"label": "Custom logo width", "unit": "px"}}}, "main-product": {"name": "Product information", "blocks": {"text": {"name": "Text", "settings": {"text": {"label": "Text"}, "text_style": {"label": "Text style", "options__1": {"label": "Body"}, "options__2": {"label": "Subtitle"}, "options__3": {"label": "Uppercase"}}}}, "breadcrumb": {"name": "Breadcrumb", "settings": {"breadcrumb_position": {"label": "Breadcrumb position", "options__1": {"label": "Left"}, "options__2": {"label": "Right"}}}}, "title": {"name": "Title"}, "rating": {"name": "Product rating", "settings": {"paragraph": {"content": "To display a rating, add a product rating app. [Learn more](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-rating-block)"}}}, "price": {"name": "Price"}, "sub_total": {"name": "Sub total"}, "quantity_selector": {"name": "Quantity selector"}, "product": {"name": "Product", "settings": {"product": {"label": "Select Product"}}}, "variant_picker": {"name": "Variant picker", "settings": {"picker_type": {"label": "Type", "options__1": {"label": "Dropdown"}, "options__2": {"label": "Pills"}}, "picker_method": {"label": "Swatch style", "options__1": {"label": "Color label"}, "options__2": {"label": "Variant image"}}}}, "buy_buttons": {"name": "Buy buttons", "settings": {"show_dynamic_checkout": {"label": "Show dynamic checkout buttons", "info": "Using the payment methods available on your store, customers see their preferred option, like PayPal or Apple Pay. [Learn more](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)"}, "show_gift_card_recipient": {"label": "Show recipient information form for gift cards", "info": "Allows buyers to send gift cards on a scheduled date along with a personal message. [Learn more](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)"}}}, "pickup_availability": {"name": "Pickup availability"}, "description": {"name": "Description"}, "complementary_products": {"name": "Complementary products", "settings": {"paragraph": {"content": "To select complementary products, add the Search & Discovery app. [Learn more](https://help.shopify.com/manual/online-store/search-and-discovery/product-recommendations)"}, "heading": {"label": "Heading"}, "make_collapsible_row": {"label": "Show as collapsible row"}, "icon": {"info": "Visible when collapsible row is displayed."}, "product_list_limit": {"label": "Maximum products to show"}, "products_per_page": {"label": "Number of products per page"}, "pagination_style": {"label": "Pagination style", "options": {"option_1": "Dots", "option_2": "Counter", "option_3": "Numbers"}}, "product_card": {"heading": "Product card"}, "image_ratio": {"label": "Image ratio", "options": {"option_1": "Portrait", "option_2": "Square"}}, "enable_quick_add": {"label": "Enable quick add button"}}}, "inventory": {"name": "Inventory status", "settings": {"text_style": {"label": "Text style", "options__1": {"label": "Body"}, "options__2": {"label": "Subtitle"}, "options__3": {"label": "Uppercase"}}, "inventory_threshold": {"label": "Low inventory threshold", "info": "Choose 0 to always show in stock if available."}, "show_inventory_quantity": {"label": "Show inventory count"}}}, "deal": {"name": "Deal timer"}, "vendor": {"name": "<PERSON><PERSON><PERSON>"}, "type": {"name": "Type"}, "sku": {"name": "S<PERSON>"}, "notify-form": {"name": "Notify form"}, "fbt": {"name": "Bought Together"}, "badge": {"name": "Badge", "settings": {"badge_label": {"label": "Badge Title"}, "image": {"label": "Image"}}}, "share": {"name": "Share", "settings": {"text": {"label": "Text"}, "featured_image_info": {"content": "If you include a link in social media posts, the page’s featured image will be shown as the preview image. [Learn more](https://help.shopify.com/manual/online-store/images/showing-social-media-thumbnail-images)."}, "title_info": {"content": "A store title and description are included with the preview image. [Learn more](https://help.shopify.com/manual/promoting-marketing/seo/adding-keywords#set-a-title-and-description-for-your-online-store)."}}}, "custom_liquid": {"name": "Custom liquid", "settings": {"custom_liquid": {"label": "Custom liquid", "info": "Add app snippets or other Liquid code to create advanced customizations."}}}, "fake_visitors": {"name": "Fake visitors", "settings": {"text": {"label": "People are viewing this right now"}, "min_count": {"label": "Min"}, "max_count": {"label": "Max"}}}, "fake_sales": {"name": "Fake sales", "settings": {"minqty": {"label": "Min"}, "maxqty": {"label": "Max"}, "item_sold": {"label": "Text 1"}, "products": {"label": "Text 2"}, "hours": {"label": "Text 3"}}}, "collapsible_tab": {"name": "Collapsible row", "settings": {"heading": {"info": "Include a heading that explains the content.", "label": "Heading"}, "content": {"label": "Row content"}, "page": {"label": "Row content from page"}, "icon": {"label": "Icon", "options__1": {"label": "None"}, "options__2": {"label": "Apple"}, "options__3": {"label": "Banana"}, "options__4": {"label": "<PERSON><PERSON>"}, "options__5": {"label": "Box"}, "options__6": {"label": "Carrot"}, "options__7": {"label": "Chat bubble"}, "options__8": {"label": "Check mark"}, "options__9": {"label": "Clipboard"}, "options__10": {"label": "Dairy"}, "options__11": {"label": "Dairy free"}, "options__12": {"label": "Dryer"}, "options__13": {"label": "Eye"}, "options__14": {"label": "Fire"}, "options__15": {"label": "Gluten free"}, "options__16": {"label": "Heart"}, "options__17": {"label": "Iron"}, "options__18": {"label": "Leaf"}, "options__19": {"label": "Leather"}, "options__20": {"label": "Lightning bolt"}, "options__21": {"label": "Lipstick"}, "options__22": {"label": "Lock"}, "options__23": {"label": "Map pin"}, "options__24": {"label": "Nut free"}, "options__25": {"label": "<PERSON>ts"}, "options__26": {"label": "Paw print"}, "options__27": {"label": "Pepper"}, "options__28": {"label": "Perfume"}, "options__29": {"label": "Plane"}, "options__30": {"label": "Plant"}, "options__31": {"label": "Price tag"}, "options__32": {"label": "Question mark"}, "options__33": {"label": "Recycle"}, "options__34": {"label": "Return"}, "options__35": {"label": "Ruler"}, "options__36": {"label": "Serving dish"}, "options__37": {"label": "Shirt"}, "options__38": {"label": "Shoe"}, "options__39": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__40": {"label": "Snowflake"}, "options__41": {"label": "Star"}, "options__42": {"label": "Stopwatch"}, "options__43": {"label": "Truck"}, "options__44": {"label": "Washing"}}}}, "popup": {"name": "Pop-up", "settings": {"link_label": {"label": "Link label"}, "page": {"label": "Page"}}}, "size-popup": {"name": "Size guid", "settings": {"link_label": {"label": "Link label"}, "page": {"label": "Page"}}}}, "settings": {"header": {"content": "Media", "info": "Learn more about [media types.](https://help.shopify.com/manual/products/product-media)"}, "enable_sticky_info": {"label": "Enable sticky content on desktop"}, "enable_product_zoom": {"label": "Select product zoom", "inner_zoom": "Inner Zoom", "outer_zoom": "Outer Zoom", "cloud_zoom": "Cloud Zoom", "default_zoom": "Default Zoom"}, "layout": {"label": "Layout", "options__1": {"label": "Full width"}, "options__2": {"label": "Grid"}}, "gallery_layout": {"label": "Desktop layout", "options__1": {"label": "Stacked"}, "options__2": {"label": "Thumbnails"}, "options__3": {"label": "Thumbnail carousel"}}, "thumb_carousel_layout": {"label": "Thumbnails alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Right"}, "options__3": {"label": "Bottom"}}, "media_size": {"label": "Desktop media size", "info": "Media is automatically optimized for mobile.", "options__1": {"label": "Small"}, "options__2": {"label": "Medium"}, "options__3": {"label": "Large"}}, "meta_style": {"label": "Meta style", "options_1": "One half", "options_2": "Full width"}, "mobile_thumbnails": {"label": "Mobile layout", "options__1": {"label": "Show thumbnails"}, "options__2": {"label": "Hide thumbnails"}}, "hide_variants": {"label": "Hide other variants’ media after selecting a variant"}, "show_review_tab": {"label": "Show review tab"}, "show_wishlist": {"label": "Show wishlist"}, "enable_video_looping": {"label": "Enable video looping"}, "sidebar__1": {"content": "Promo banner"}, "show_image": {"label": "Enable"}, "sidebar_image": {"label": "Image"}, "sidebar_title": {"label": "Heading"}, "sidebar_button": {"label": "<PERSON><PERSON>"}, "sidebar_link": {"label": "Link"}, "sidebar__3": {"content": "Product list"}, "show_collection": {"label": "Enable"}, "collection_title": {"label": "Heading"}, "collection": {"label": "Collection"}, "collection_limit": {"label": "Limit"}, "sidebar__4": {"content": "<PERSON><PERSON>"}, "show_menu": {"label": "Enable"}, "menu_title": {"label": "Heading"}, "linklist": {"label": "<PERSON><PERSON>"}, "sidebar_settings": {"label": "Layout setting", "options__1": {"label": "Left sidebar"}, "options__2": {"label": "Right sidebar"}, "options__3": {"label": "No sidebar"}}, "tab-position": {"heading_1": "Tab Section", "label_1": "Tab position", "label_2": "Tab style", "options_1": "Right", "options_2": "Bottom", "options_3": "Tab", "options_4": "Toggle"}, "enable_enquiry": {"label": "Show enquiry form"}, "enquiry_image": {"label": "Enquiry Image"}, "show_adv_product": {"label": "Show advanced product tagging"}, "choose_label": {"label": "Choose style"}, "prev_next_products": {"label": "Show next prev products"}, "show_share": {"label": "Show social sharing"}, "share_label": {"label": "Share"}, "show_quantity_selector": {"label": "Show quantity"}, "rating_script": {"label": "Rating app code", "info": "Add app snippets or other Liquid code to create advanced customizations."}, "content": {"label": "Icon text"}, "iconwithtext": {"label": "Icon with text"}, "heading_1": {"label": "Title"}, "heading_1_text": {"label": "Text"}, "heading_1_icon": {"label": "Icon"}, "heading_2": {"label": "Title"}, "heading_2_text": {"label": "Text"}, "heading_2_icon": {"label": "Icon"}, "heading_3": {"label": "Title"}, "heading_3_text": {"label": "Text"}, "heading_3_icon": {"label": "Icon"}, "html": {"label": "HTML"}}}, "main-search": {"name": "Search results", "settings": {"columns_desktop": {"label": "Number of columns on desktop"}, "image_ratio": {"label": "Image ratio", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}}, "show_secondary_image": {"label": "Show second image on hover"}, "show_vendor": {"label": "Show vendor"}, "show_rating": {"label": "Show product rating", "info": "To display a rating, add a product rating app. [Learn more](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#search-results-section-settings)"}, "header__1": {"content": "Product card"}, "header__2": {"content": "Blog card"}, "article_show_date": {"label": "Show date"}, "article_show_author": {"label": "Show author"}, "header_mobile": {"content": "Mobile Layout"}, "enable_grid_list": {"label": "Enable grid/list view"}, "layout": {"label": "Layout", "options__1": {"label": "Full width"}, "options__2": {"label": "Grid"}}, "enable_filtering": {"label": "Enable filtering", "info": "Customize [filters](/admin/menus)"}, "filter_type": {"label": "Desktop filter layout", "options__1": {"label": "Horizontal"}, "options__2": {"label": "Vertical"}, "options__3": {"label": "Drawer"}, "info": "Drawer is the default mobile layout."}, "sidebar_settings": {"label": "Layout setting", "options__1": {"label": "Left sidebar"}, "options__2": {"label": "Right sidebar"}, "options__3": {"label": "No sidebar"}}, "enable_sorting": {"label": "Enable sorting"}, "columns_mobile": {"label": "Number of columns on mobile", "options__1": {"label": "1 column"}, "options__2": {"label": "2 columns"}}}}, "multicolumn": {"name": "Multicolumn", "settings": {"title": {"label": "Heading"}, "subtitle": {"label": "Text"}, "text_style": {"label": "Text style", "options__1": {"label": "Subtitle"}, "options__2": {"label": "Uppercase"}}, "text_size": {"label": "Text size", "options__1": {"label": "Small"}, "options__2": {"label": "Medium"}, "options__3": {"label": "Large"}}, "title_image": {"label": "Title Image"}, "image_width": {"label": "Image width", "options__1": {"label": "One-third width of column"}, "options__2": {"label": "Half width of column"}, "options__3": {"label": "Full width of column"}}, "image_ratio": {"label": "Image ratio", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}, "options__4": {"label": "Circle"}}, "columns_desktop": {"label": "Number of columns on desktop"}, "column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "background_style": {"label": "Secondary background", "options__1": {"label": "None"}, "options__2": {"label": "Show as column background"}}, "button_label": {"label": "Button label"}, "button_link": {"label": "Button link"}, "enable_overlay": {"label": "Enable Overlay Style"}, "header_mobile": {"content": "Mobile Layout"}, "columns_mobile": {"label": "Number of columns on mobile", "options__1": {"label": "1 column"}, "options__2": {"label": "2 columns"}}, "swipe_on_mobile": {"label": "Enable swipe on mobile", "controls": "Controls on mobile"}}, "blocks": {"column": {"name": "Column", "settings": {"image": {"label": "Image"}, "title": {"label": "Heading"}, "text": {"label": "Description"}, "link_label": {"label": "Link label"}, "link": {"label": "Link"}, "icon_class": {"label": "Icon"}}}}, "presets": {"name": "Multicolumn"}}, "multicolumn-with-text": {"name": "Multicolumn with text", "settings": {"multicolumn-with-text_layout": {"label": "multicolumn Layout", "options__1": {"label": "Full width"}, "options__2": {"label": "Grid"}}, "title": {"label": "Heading"}, "subtitle": {"label": "Text"}, "text_style": {"label": "Text style", "options__1": {"label": "Subtitle"}, "options__2": {"label": "Uppercase"}}, "text_size": {"label": "Text size", "options__1": {"label": "Small"}, "options__2": {"label": "Medium"}, "options__3": {"label": "Large"}}, "title_image": {"label": "Title Image"}, "image_width": {"label": "Image width", "options__1": {"label": "One-third width of column"}, "options__2": {"label": "Half width of column"}, "options__3": {"label": "Full width of column"}}, "image_ratio": {"label": "Image ratio", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}, "options__4": {"label": "Circle"}}, "columns_desktop": {"label": "Number of columns on desktop"}, "column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "background_style": {"label": "Secondary background", "options__1": {"label": "None"}, "options__2": {"label": "Show as column background"}}, "button_label": {"label": "Button label"}, "button_link": {"label": "Button link"}, "header_mobile": {"content": "Mobile Layout"}, "columns_mobile": {"label": "Number of columns on mobile", "options__1": {"label": "1 column"}, "options__2": {"label": "2 columns"}}, "swipe_on_mobile": {"label": "Enable swipe on mobile"}}, "blocks": {"column": {"name": "Column", "settings": {"image": {"label": "Image"}, "second_image": {"label": "Second Image"}, "title": {"label": "Heading"}, "text": {"label": "Description"}, "link_label": {"label": "Link label"}, "link": {"label": "Link"}, "icon_image": {"label": "Icons"}}}}, "presets": {"name": "Multicolumn with text"}}, "brand-logos": {"name": "Brand Logo", "settings": {"title": {"label": "Heading"}, "columns_desktop": {"label": "Number of columns on desktop"}, "background_style": {"label": "Secondary background", "options__1": {"label": "None"}, "options__2": {"label": "Show as column background"}}, "button_label": {"label": "Button label"}, "button_link": {"label": "Button link"}, "block_color_setting": {"content": "Block color setting"}, "image_width": {"label": "Image width", "options__1": {"label": "One-third width of column"}, "options__2": {"label": "Half width of column"}, "options__3": {"label": "Full width of column"}}, "column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "header_mobile": {"content": "Swiper Layout"}, "columns_mobile": {"label": "Number of columns on mobile", "options__1": {"label": "1 column"}, "options__2": {"label": "2 columns"}}, "swipe_on_desktop": {"label": "Enable swiper"}, "swipe_on_mobile": {"label": "Enable swipe on mobile"}}, "blocks": {"column": {"name": "Column", "settings": {"image": {"label": "Image"}, "alter_text": {"label": "Alt Text"}, "link": {"label": "Link"}}}}, "presets": {"name": "Brand Logo"}}, "support-block": {"name": "Support Block", "settings": {"title": {"label": "Heading"}, "image_width": {"label": "Image width", "options__1": {"label": "One-third width of column"}, "options__2": {"label": "Half width of column"}, "options__3": {"label": "Full width of column"}}, "image_ratio": {"label": "Image ratio", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}, "options__4": {"label": "Circle"}}, "columns_desktop": {"label": "Number of columns on desktop"}, "block_column_alignment": {"label": "Block  alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "vertical_position": {"label": "Vertical position(Not for Grid style)", "options__1": {"label": "Vertical Top"}, "options__2": {"label": "Vertical Center"}, "options__3": {"label": "Vertical Bottom"}}, "background_style": {"label": "Secondary background", "options__1": {"label": "None"}, "options__2": {"label": "Show as column background"}}, "button_label": {"label": "Button label"}, "button_link": {"label": "Button link"}, "column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "block_settings": {"content": "Block Settings"}, "block_style": {"label": "Block Style", "options__1": {"label": "Grid"}, "options__2": {"label": "List"}}, "image_size": {"label": "Image size(Should be lesser than Block Image Size)"}, "image_outer_size": {"label": "Image Outer size"}, "image_radius": {"label": "Image Radius(in px) - Set 50% for Rounded Image"}, "header_mobile": {"content": "Mobile Layout"}, "columns_mobile": {"label": "Number of columns on mobile", "options__1": {"label": "1 column"}, "options__2": {"label": "2 columns"}}, "swipe_on_mobile": {"label": "Enable swipe on mobile"}}, "blocks": {"column": {"name": "Column", "settings": {"image": {"label": "Image"}, "icon_class": {"label": "Icon class", "info": "e.g <i class='fa fa-truck' aria-hidden='true'></i> [Learn more](https://fontawesome.com/v4/icons)"}, "svg_icons": {"label": "Svg icons"}, "title": {"label": "Heading"}, "text": {"label": "Description"}, "link_label": {"label": "Link label"}, "link": {"label": "Link"}, "block_color_scheme": {"label": "Block color scheme"}}}}, "presets": {"name": "Support Block"}}, "team-section": {"name": "Team Section", "settings": {"team-section_layout": {"label": "Team section Layout", "options__1": {"label": "Full width"}, "options__2": {"label": "Grid"}}, "title": {"label": "Heading"}, "subtitle": {"label": "Text"}, "text_style": {"label": "Text style", "options__1": {"label": "Subtitle"}, "options__2": {"label": "Uppercase"}}, "text_size": {"label": "Text size", "options__1": {"label": "Small"}, "options__2": {"label": "Medium"}, "options__3": {"label": "Large"}}, "title_image": {"label": "Title Image"}, "image_width": {"label": "Image width", "options__1": {"label": "One-third width of column"}, "options__2": {"label": "Half width of column"}, "options__3": {"label": "Full width of column"}}, "image_ratio": {"label": "Image ratio", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}, "options__4": {"label": "Circle"}}, "columns_desktop": {"label": "Number of columns on desktop"}, "column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "background_style": {"label": "Secondary background", "options__1": {"label": "None"}, "options__2": {"label": "Show as column background"}}, "banner_style": {"label": "Banner style", "options__1": {"label": "Grid"}, "options__2": {"label": "Overlay"}}, "team-section": {"label": "Item per row", "options__1": {"label": "1"}, "options__2": {"label": "2"}, "options__3": {"label": "3"}, "options__4": {"label": "4"}, "options__5": {"label": "5"}, "options__6": {"label": "6"}}, "button_label": {"label": "Button label"}, "button_link": {"label": "Button link"}}, "blocks": {"text": {"name": "Block", "settings": {"block_image": {"label": "Image"}, "show_content": {"label": "Show_content"}, "block_title": {"label": "Heading"}, "block_sub_title": {"label": "Block sub title"}, "block_description": {"label": "Block description"}, "block_button_text": {"label": "<PERSON><PERSON>"}, "block_button_link": {"label": "Button link"}, "desktop_content_alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Desktop Content Alignment"}}}}, "presets": {"name": "Team section"}}, "custom-section": {"name": "Home Custom Section", "settings": {"column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "content_width_settings": {"content": "Content Widths"}, "bg_image": {"label": "Background Image"}, "block_text_image": {"label": "Block text Image"}, "content_width": {"label": "Desktop"}, "lap_content_width": {"label": "Content Width - <PERSON><PERSON><PERSON> (Small Screen)"}, "column_gap": {"label": "Column Gap - Leave empty for Default Gap (Units not needed)"}}, "blocks": {"content": {"name": "Content", "settings": {"block_heading": {"label": "Main heading"}, "block_sub_heading": {"label": "Sub Heading"}, "block_description": {"label": "Description"}, "html": {"label": "Html"}, "block_button_text": {"label": "Link text"}, "block_button_link": {"label": "Link URL"}, "heading_position": {"label": "Heading Position", "options__1": {"label": "Center"}, "options__2": {"label": "Left"}, "options__3": {"label": "Right"}}, "image_settings": {"content": "Image settings"}, "block_img_1": {"label": "Image 1"}, "block_img_2": {"label": "Image 2"}}}}, "presets": {"name": "Home Custom Section"}}, "grid-banner": {"name": "Grid banner", "settings": {"grid-banner_layout": {"label": "grid-banner Layout", "options__1": {"label": "Full width"}, "options__2": {"label": "Grid"}}, "title": {"label": "Heading"}, "subtitle": {"label": "Text"}, "text_style": {"label": "Text style", "options__1": {"label": "Subtitle"}, "options__2": {"label": "Uppercase"}}, "text_size": {"label": "Text size", "options__1": {"label": "Small"}, "options__2": {"label": "Medium"}, "options__3": {"label": "Large"}}, "title_image": {"label": "Title Image"}, "image_width": {"label": "Image width", "options__1": {"label": "One-third width of column"}, "options__2": {"label": "Half width of column"}, "options__3": {"label": "Full width of column"}}, "image_ratio": {"label": "Image ratio", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}, "options__4": {"label": "Circle"}}, "columns_desktop": {"label": "Number of columns on desktop"}, "column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "background_style": {"label": "Secondary background", "options__1": {"label": "None"}, "options__2": {"label": "Show as column background"}}, "banner_style": {"label": "Banner style", "options__1": {"label": "Grid"}, "options__2": {"label": "List"}, "options__3": {"label": "Overlay"}}, "grid-column": {"label": "Item per row", "options__1": {"label": "1"}, "options__2": {"label": "2"}, "options__3": {"label": "3"}, "options__4": {"label": "4"}, "options__5": {"label": "5"}, "options__6": {"label": "6"}}, "button_label": {"label": "Button label"}, "button_link": {"label": "Button link"}, "column_gap": {"label": "Column Gap(units no needed)"}, "overlay_image_height_settings": {"content": "Overlay Image Height Settings"}, "overlay_height": {"label": "Overlay Height desktop"}, "overlay_height_laptop": {"label": "Overlay Height laptop"}, "overlay_height_tab": {"label": "Overlay Height Tab"}, "overlay_height_mobile": {"label": "Overlay Height Mobile"}}, "blocks": {"text": {"name": "Block", "settings": {"block_image": {"label": "Image"}, "image_link": {"label": "Image Link"}, "show_content": {"label": "Show_content"}, "block_title": {"label": "Heading"}, "sub_main_heading": {"label": "Sub Main Heading"}, "enable_title_link": {"label": "Enable title link"}, "block_sub_title": {"label": "Block sub title"}, "block_description": {"label": "Block description"}, "block_description_code": {"label": "Block description code"}, "block_button_text": {"label": "<PERSON><PERSON>"}, "block_button_link": {"label": "Button link"}, "block_signature_image": {"label": "block image"}, "reverse_column": {"label": "Content reverse"}, "desktop_content_position": {"options__1": {"label": "Top Left"}, "options__2": {"label": "Top Center"}, "options__3": {"label": "Top Right"}, "options__4": {"label": "Middle Left"}, "options__5": {"label": "Middle Center"}, "options__6": {"label": "Middle Right"}, "options__7": {"label": "Bottom Left"}, "options__8": {"label": "Bottom Center"}, "options__9": {"label": "Bottom Right"}, "label": "Desktop content position[Only Overlay Style]"}, "desktop_content_alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Desktop Content Alignment"}}}}, "presets": {"name": "Grid banner"}}, "masonry-banner": {"name": "Masonry banner", "settings": {"title": {"label": "Heading"}, "subtitle": {"label": "Text"}, "text_style": {"label": "Text style", "options__1": {"label": "Subtitle"}, "options__2": {"label": "Uppercase"}}, "text_size": {"label": "Text size", "options__1": {"label": "Small"}, "options__2": {"label": "Medium"}, "options__3": {"label": "Large"}}, "column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "button_label": {"label": "Button label"}, "button_link": {"label": "Button link"}, "main--banner-header": {"content": "Main Banner Settings"}, "primary_image": {"label": "Image"}, "primary_title": {"label": "Title"}, "primary_sub_title": {"label": "sub title"}, "primary_description": {"label": "Description"}, "primary_button_text": {"label": "<PERSON><PERSON>"}, "primary_button_link": {"label": "Button link"}, "primary_text_align": {"label": "Main Banner Text alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "primary_position": {"label": "Content position", "options__1": {"label": "Top Left"}, "options__2": {"label": "Top Center"}, "options__3": {"label": "Top Right"}, "options__4": {"label": "Center Left"}, "options__5": {"label": "Center"}, "options__6": {"label": "Center Right"}, "options__7": {"label": "Bottom Left"}, "options__8": {"label": "Botton Center"}, "options__9": {"label": "Bottom Right"}}, "display-setting": {"content": "Display Setting"}, "main_banner_style": {"label": "Main Banner Style", "options__1": {"label": "Grid"}, "options__2": {"label": "Overlay"}}, "additional_banner_style": {"label": "Additional Banner Style", "options__1": {"label": "Grid"}, "options__2": {"label": "Overlay"}}, "content_width": {"label": "Content Width - Desktop"}, "lap_content_width": {"label": "Content Width - <PERSON><PERSON><PERSON> (Small Screen)"}, "content_color_scheme": {"label": "Content Color scheme"}, "additional-banner-header": {"content": "Additional Block Settings"}, "block_style": {"label": "Additional Banners Block Style", "options__1": {"label": "Style 1 (<PERSON><PERSON><PERSON>)"}, "options__2": {"label": "Style 2 (Minimum 2 or 2+ blocks)"}, "options__3": {"label": "Style 3 (Minimum 3 or 3+ blocks)"}, "options__4": {"label": "Style 4 (Minimum 3 or 3+ blocks)"}, "options__5": {"label": "Style 5 (Minimum 3 or 3+ blocks)"}, "options__6": {"label": "Style 6 (Minimum 4 or 4+ blocks)"}, "options__7": {"label": "Style 7 (Minimum 4 or 4+ blocks)"}, "options__8": {"label": "Style 8 (Minimum 5 blocks)"}, "options__9": {"label": "Style 9 (Minimum 5 blocks)"}, "options__10": {"label": "Style 10 (Minimum 5 blocks)"}}, "overlay-height-header": {"content": "Overlay Style Height"}, "overlay_style_height": {"label": "Minimum Height"}, "overlay_style_height_laptop": {"label": "Minimum Height - Laptop (Small Screens)"}, "overlay_style_height_tab": {"label": "Minimum Height - Tablet"}, "overlay_style_height_mobile": {"label": "Minimum Height - Mobile"}}, "blocks": {"text": {"name": "Block", "settings": {"block_image": {"label": "Image"}, "show_image": {"label": "Show_Image"}, "show_content": {"label": "Show_content"}, "block_title": {"label": "Heading"}, "block_sub_title": {"label": "Block sub title"}, "block_description": {"label": "Block description"}, "block_button_text": {"label": "<PERSON><PERSON>"}, "block_button_link": {"label": "Button link"}, "block_text_align": {"label": "Additional Text alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "block_position": {"label": "Content position", "options__1": {"label": "Top Left"}, "options__2": {"label": "Top Center"}, "options__3": {"label": "Top Right"}, "options__4": {"label": "Center Left"}, "options__5": {"label": "Center"}, "options__6": {"label": "Center Right"}, "options__7": {"label": "Bottom Left"}, "options__8": {"label": "Botton Center"}, "options__9": {"label": "Bottom Right"}}}}}, "presets": {"name": "Masonry banner"}}, "number-counter": {"name": "Number counter", "settings": {"title": {"label": "Heading"}, "subtitle": {"label": "Text"}, "text_style": {"label": "Text style", "options__1": {"label": "Subtitle"}, "options__2": {"label": "Uppercase"}}, "text_size": {"label": "Text size", "options__1": {"label": "Small"}, "options__2": {"label": "Medium"}, "options__3": {"label": "Large"}}, "title_image": {"label": "Title Image"}, "image_setting": {"content": "Number Counter Settings"}, "counter_block_image": {"label": "Image"}, "column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "counter_style": {"label": "Counter Style", "options__1": {"label": "Grid"}, "options__2": {"label": "List"}}, "button_label": {"label": "Button label"}, "button_link": {"label": "Button link"}, "columns_desktop": {"label": "Number of columns on desktop"}}, "blocks": {"icon": {"name": "Image", "settings": {"image": {"label": "Icon"}, "title": {"label": "Heading"}, "text": {"label": "Description"}, "value": {"label": "Value"}, "value_text": {"label": "Value text"}, "column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}}}}, "presets": {"name": "Number Counter"}}, "marquee": {"name": "Marquee", "blocks": {"text": {"name": "Text", "settings": {"block_heading": {"label": "Main Heading"}}}}, "settings": {"gradient_text_background": {"label": "Text background"}, "marquee_padding": {"label": "Text padding"}, "marquee_image": {"label": "Scrolling image"}}}, "testimonials": {"name": "Testimonials", "settings": {"title": {"label": "Heading"}, "text": {"label": "Sub Heading"}, "columns_desktop": {"label": "Number of columns on desktop"}, "column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "background_style": {"label": "Secondary background", "options__1": {"label": "None"}, "options__2": {"label": "Show as column background"}}, "button_label": {"label": "Button label"}, "button_link": {"label": "Button link"}, "testimonial_text_alignment": {"label": "Testimonial Text Alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "testimonial_settings": {"content": "Testimonial Settings"}, "quote_icon": {"label": "Quote Icon", "info": "Should be in png format"}, "enable_arrows": {"label": "Enable Arrow"}}, "blocks": {"text": {"name": "Text", "settings": {"image": {"label": "Image"}, "block_color_scheme": {"label": "Block color scheme"}, "author": {"label": "Author"}, "job_title": {"label": "Job Title"}, "quote": {"label": "Quotes"}, "link_label": {"label": "Link label"}, "link": {"label": "Link"}}}}, "presets": {"name": "Testimonials"}}, "testimonials-with-video": {"name": "Testimonials With Video", "settings": {"title": {"label": "Heading"}, "text": {"label": "Sub Heading"}, "columns_desktop": {"label": "Number of columns on desktop"}, "column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "background_style": {"label": "Secondary background", "options__1": {"label": "None"}, "options__2": {"label": "Show as column background"}}, "button_label": {"label": "Button label"}, "button_link": {"label": "Button link"}, "enable_arrows": {"label": "Enable Arrow"}, "testimonial_text_alignment": {"label": "Testimonial Text Alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "testimonial_settings": {"content": "Testimonial Settings"}, "quote_icon": {"label": "Quote Icon", "info": "Should be in png format"}, "Video_banner_settings": {"content": "Video Banner Settings"}, "cover_image": {"label": "Cover image"}, "video_url": {"label": "URL", "placeholder": "Use a YouTube or Vimeo URL", "info": "Video plays in the page."}, "alt_text": {"label": "Video alt text", "info": "Describe the video for customers using screen readers. [Learn more](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video)"}}, "blocks": {"text": {"name": "Text", "settings": {"image": {"label": "Image"}, "author": {"label": "Author"}, "job_title": {"label": "Job Title"}, "quote": {"label": "Quotes"}, "link_label": {"label": "Link label"}, "link": {"label": "Link"}}}}, "presets": {"name": "Testimonials With Video"}}, "product-tab-list": {"name": "Product Tab List", "settings": {"title": {"label": "Heading"}, "text": {"label": "Sub Heading"}, "column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "button_label": {"label": "Button label"}, "button_link": {"label": "Button link"}, "products_to_show": {"label": "Maximum products to show"}, "products_per_row": {"label": "Maximum products per row", "info": "If Image dispayed in Product card, then Set the maximum of 3 products per row"}, "enable_slider": {"label": "Enable slider"}, "enable_dotts": {"label": "Enable slider pagination"}, "enable_arrows": {"label": "Enable slider navigation"}, "enable_tab_title": {"label": "Enable Tab Title"}, "enable_tab_count": {"label": "Enable item count"}, "enable_collection_icon": {"label": "Enable tab icon"}, "header": {"content": "Product card"}, "image_ratio": {"label": "Image ratio", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}}, "show_secondary_image": {"label": "Show second image on hover"}, "add_image_padding": {"label": "Add padding"}, "show_vendor": {"label": "Show vendor"}, "layout": {"options__1": {"label": "Image first"}, "options__2": {"label": "Product first"}, "options__3": {"label": "None"}, "label": "Desktop layout", "info": "Image first is the default mobile layout."}, "image": {"label": "Image"}, "header__2": {"content": "Image Settings"}, "image_block_heading": {"label": "Main Heading"}, "image_block_sub_heading": {"label": "Sub Heading"}, "image_block_description": {"label": "Description"}, "image_block_button_text": {"label": "Button label"}, "image_block_button_link": {"label": "Button link"}, "image_block_horizontal_position": {"label": "Horizontal position", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}}, "image_block_vertical_position": {"label": "Vertical position", "options__1": {"label": "Top"}, "options__2": {"label": "Middle"}, "options__3": {"label": "Bottom"}}, "custom_class_heading": {"content": "Add your custom class name here"}, "custom_class_name": {"label": "Custom class"}}, "blocks": {"tab": {"name": "Collection", "settings": {"collection": {"label": "Select collection"}, "title": {"label": "Heading"}, "collection_image": {"label": "Pick the Collection Icon"}}}}, "presets": {"name": "Product Tab List"}}, "product-tab": {"name": "Product Tab", "settings": {"column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "products_to_show": {"label": "Maximum products to show"}, "products_per_row": {"label": "Maximum products per row", "info": "If Image dispayed in Product card, then Set the maximum of 3 products per row"}, "enable_slider": {"label": "Enable slider"}, "enable_dotts": {"label": "Enable slider pagination"}, "enable_arrows": {"label": "Enable slider navigation"}, "enable_tab_title": {"label": "Enable Tab Title"}, "enable_tab_count": {"label": "Enable item count"}, "enable_collection_icon": {"label": "Enable tab icon"}, "show_view_all": {"label": "Enable View all if collection has more products than shown"}, "tab_alignment": {"label": "Tab alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}}, "header": {"content": "Product card"}, "image_ratio": {"label": "Image ratio", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}}, "show_secondary_image": {"label": "Show second image on hover"}, "add_image_padding": {"label": "Add padding"}, "show_vendor": {"label": "Show vendor"}, "layout": {"options__1": {"label": "Image first"}, "options__2": {"label": "Product first"}, "options__3": {"label": "None"}, "label": "Desktop layout", "info": "Image first is the default mobile layout."}, "image": {"label": "Image"}, "header__2": {"content": "Image Settings"}, "image_block_heading": {"label": "Main Heading"}, "image_block_sub_heading": {"label": "Sub Heading"}, "image_block_description": {"label": "Description"}, "image_block_button_text": {"label": "Button label"}, "image_block_button_link": {"label": "Button link"}, "items_count": {"label": "Items Count"}, "items_count_text": {"label": "Items Count Text"}, "image_block_horizontal_position": {"label": "Horizontal position", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}}, "image_block_vertical_position": {"label": "Vertical position", "options__1": {"label": "Top"}, "options__2": {"label": "Middle"}, "options__3": {"label": "Bottom"}}}, "blocks": {"tab": {"name": "Collection", "settings": {"collection": {"label": "Select collection"}, "title": {"label": "Heading"}, "collection_image": {"label": "Pick the Collection Icon"}}}}, "presets": {"name": "Product Tab"}}, "newsletter": {"name": "Email signup", "settings": {"full_width": {"label": "Make section full width"}, "paragraph": {"content": "Each email subscription creates a customer account. [Learn more](https://help.shopify.com/manual/customers)"}, "newletter_bg": {"label": "Background Image"}}, "blocks": {"heading": {"name": "Heading", "settings": {"heading": {"label": "Heading"}}}, "paragraph": {"name": "Paragraph", "settings": {"paragraph": {"label": "Description"}, "text_style": {"options__1": {"label": "Body"}, "options__2": {"label": "Subtitle"}, "label": "Text style"}}}, "email_form": {"name": "Email form"}}, "presets": {"name": "Email signup"}}, "email-signup-banner": {"name": "Email signup banner", "settings": {"paragraph": {"content": "Each email subscription creates a customer account. [Learn more](https://help.shopify.com/manual/customers)"}, "image": {"label": "Background image"}, "image_overlay_opacity": {"label": "Image overlay opacity"}, "show_background_image": {"label": "Show background image"}, "show_text_box": {"label": "Show container on desktop"}, "desktop_content_position": {"options__1": {"label": "Top Left"}, "options__2": {"label": "Top Center"}, "options__3": {"label": "Top Right"}, "options__4": {"label": "Middle Left"}, "options__5": {"label": "Middle Center"}, "options__6": {"label": "Middle Right"}, "options__7": {"label": "Bottom Left"}, "options__8": {"label": "Bottom Center"}, "options__9": {"label": "Bottom Right"}, "label": "Desktop content position"}, "color_scheme": {"info": "Visible when container displayed."}, "image_height": {"label": "Banner height", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Small"}, "options__3": {"label": "Medium"}, "options__4": {"label": "Large"}, "info": "For best results, use an image with a 16:9 aspect ratio. [Learn more](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "desktop_content_alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Desktop content alignment"}, "header": {"content": "Mobile Layout"}, "mobile_content_alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Mobile content alignment"}, "show_text_below": {"label": "Show content below image on mobile", "info": "For best results, use an image with a 16:9 aspect ratio. [Learn more](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}}, "blocks": {"heading": {"name": "Heading", "settings": {"heading": {"label": "Heading"}}}, "paragraph": {"name": "Paragraph", "settings": {"paragraph": {"label": "Description"}, "text_style": {"options__1": {"label": "Body"}, "options__2": {"label": "Subtitle"}, "label": "Text style"}}}, "email_form": {"name": "Email form"}}, "presets": {"name": "Email signup banner"}}, "page": {"name": "Page", "settings": {"page": {"label": "Page"}}, "presets": {"name": "Page"}}, "product-recommendations": {"name": "Product recommendations", "settings": {"layout": {"label": "Layout", "options__1": {"label": "Full width"}, "options__2": {"label": "Grid"}}, "heading": {"label": "Heading"}, "products_to_show": {"label": "Maximum products to show"}, "columns_desktop": {"label": "Number of columns on desktop"}, "paragraph__1": {"content": "Dynamic recommendations use order and product information to change and improve over time. [Learn more](https://help.shopify.com/themes/development/recommended-products)"}, "header__2": {"content": "Product card"}, "image_ratio": {"label": "Image ratio", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}}, "show_secondary_image": {"label": "Show second image on hover"}, "show_vendor": {"label": "Show vendor"}, "show_rating": {"label": "Show product rating", "info": "To display a rating, add a product rating app. [Learn more](https://help.shopify.com/manual/online-store/themes/theme-structure/page-types#product-recommendations-section-settings)"}, "enable_quick_add": {"label": "Enable quick add button", "info": "Optimal with popup or drawer cart type."}, "header_mobile": {"content": "Mobile Layout"}, "columns_mobile": {"label": "Number of columns on mobile", "options__1": {"label": "1 column"}, "options__2": {"label": "2 columns"}}}}, "rich-text": {"name": "Rich text", "settings": {"full_width": {"label": "Make section full width"}}, "blocks": {"heading": {"name": "Heading", "settings": {"heading": {"label": "Heading"}}}, "text": {"name": "Text", "settings": {"text": {"label": "Description"}}}, "button": {"name": "<PERSON><PERSON>", "settings": {"button_label": {"label": "Button label"}, "button_link": {"label": "Button link"}, "button_style_secondary": {"label": "Use outline button style"}}}}, "presets": {"name": "Rich text"}}, "video": {"name": "Video", "settings": {"heading": {"label": "Heading"}, "sub_heading": {"label": "Sub heading"}, "text": {"label": "Video text"}, "button_label": {"label": "Button label"}, "button_link": {"label": "Button link"}, "button_style_secondary": {"label": "Use outline button style"}, "column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "cover_image": {"label": "Cover image"}, "video_url": {"label": "URL", "placeholder": "Use a YouTube or Vimeo URL", "info": "Video plays in the page."}, "alt_text": {"label": "Video alt text", "info": "Describe the video for customers using screen readers. [Learn more](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video)"}, "image_padding": {"label": "Add image padding", "info": "Select image padding if you don't want your cover image to be cropped."}, "full_width": {"label": "Make section full width"}}, "presets": {"name": "Video"}}, "video-pop-up": {"name": "Video popup", "settings": {"heading": {"label": "Heading"}, "sub_heading": {"label": "Sub heading"}, "text": {"label": "Video text"}, "button_label": {"label": "Button label"}, "button_link": {"label": "Button link"}, "button_style_secondary": {"label": "Use outline button style"}, "column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "cover_image": {"label": "Cover image"}, "video_url": {"label": "URL", "placeholder": "Use a YouTube or Vimeo URL", "info": "Video plays in the page."}, "description": {"label": "Video alt text", "info": "Describe the video for customers using screen readers. [Learn more](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video)"}, "self_video_url": {"label": "Self Hosted Video URL", "info": "Video plays in the page."}, "video_format": {"label": "Video Format (Set Poster Image from above)", "info": "Add Poster Image for Popup Style"}, "image_padding": {"label": "Add image padding", "info": "Select image padding if you don't want your cover image to be cropped."}, "full_width": {"label": "Make section full width"}}, "presets": {"name": "Video popup"}}, "slideshow": {"name": "Slideshow", "settings": {"layout": {"label": "Layout", "options__1": {"label": "Full width"}, "options__2": {"label": "Grid"}}, "slider_image_height_settings": {"content": "Slider Image Height Settings"}, "slider_image_height": {"label": "Desktop"}, "slider_image_height_laptop": {"label": "Laptop"}, "slider_image_height_tab": {"label": "Tab"}, "slider_image_height_mobile": {"label": "Mobile"}, "slide_height": {"label": "Banner height", "options__1": {"label": "Adapt to first image"}, "options__2": {"label": "Small"}, "options__3": {"label": "Medium"}, "options__4": {"label": "Large"}}, "slider_visual": {"label": "Pagination style", "options__1": {"label": "Counter"}, "options__2": {"label": "Dots"}, "options__3": {"label": "Numbers"}}, "auto_rotate": {"label": "Auto-rotate"}, "change_slides_speed": {"label": "Change every"}, "mobile": {"content": "Mobile layout"}, "show_text_below": {"label": "Show content below images on mobile"}}, "blocks": {"slide": {"name": "Slide", "settings": {"image": {"label": "Image"}, "mobile_image": {"label": "Mobile Image"}, "heading": {"label": "Heading"}, "subheading": {"label": "Sub heading"}, "text": {"label": "Text"}, "button_label": {"label": "Button label", "info": "Leave the label blank to hide the button."}, "link": {"label": "Button link"}, "secondary_style": {"label": "Use outline button style"}, "box_align": {"label": "Desktop content position", "info": "Position is automatically optimized for mobile.", "options__1": {"label": "Top left"}, "options__2": {"label": "Top center"}, "options__3": {"label": "Top right"}, "options__4": {"label": "Middle left"}, "options__5": {"label": "Middle center"}, "options__6": {"label": "Middle right"}, "options__7": {"label": "Bottom left"}, "options__8": {"label": "Bottom center"}, "options__9": {"label": "Bottom right"}}, "show_text_box": {"label": "Show container on desktop"}, "text_alignment": {"label": "Desktop content alignment", "option_1": {"label": "Left"}, "option_2": {"label": "Center"}, "option_3": {"label": "Right"}}, "image_overlay_opacity": {"label": "Image overlay opacity"}, "color_scheme": {"info": "Visible when container displayed."}, "text_alignment_mobile": {"label": "Mobile content alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}}}}}, "presets": {"name": "Slideshow"}}, "slideshow-with-promo-image": {"name": "Slideshow With Promo Image", "settings": {"layout": {"label": "Layout", "options__1": {"label": "Full width"}, "options__2": {"label": "Grid"}}, "slide_height": {"label": "Slide height", "options__1": {"label": "Adapt to first image"}, "options__2": {"label": "Small"}, "options__3": {"label": "Medium"}, "options__4": {"label": "Large"}}, "slider_visual": {"label": "Pagination style", "options__1": {"label": "Counter"}, "options__2": {"label": "Dots"}, "options__3": {"label": "Numbers"}}, "auto_rotate": {"label": "Auto-rotate slides"}, "change_slides_speed": {"label": "Change slides every"}, "mobile": {"content": "Mobile layout"}, "accessibility": {"content": "Accessibility", "label": "Slideshow description", "info": "Describe the slideshow for customers using screen readers."}, "grouped_content_settings": {"content": "Grouped Content Settings"}, "content_width": {"label": "Desktop"}, "lap_content_width": {"label": "Laptop"}, "tab_content_width": {"label": "Tablet"}, "grouped_column_gap": {"label": "Grouped Column Gap"}, "reverse_column": {"label": "<PERSON><PERSON><PERSON>"}, "promo_block_settings": {"content": "Promo Block Settings"}, "enable_promo_block": {"label": "Enable Promo Block"}, "promo_block_style": {"label": "Promo Block Style", "grid": {"label": "Grid"}, "overlay": {"label": "Overlay"}}, "promo_height_desktop": {"label": "Promo Height Desktop(Only for Overlay Style)"}, "promo_height_lap": {"label": "Promo Height Laptop(Only for Overlay Style)"}, "promo_height_tab": {"label": "Promo Height Taplet(Only for Overlay Style)"}, "promo_height_mobile": {"label": "Promo Height Mobile(Only for Overlay Style)"}, "additional_block_1": {"content": "Additional Block 1"}, "additional_block_2": {"content": "Additional Block 2"}, "additional_block_3": {"content": "Additional Block 3"}, "enable_additional_block_1": {"label": "Enable Additional Block 1"}, "enable_additional_block_2": {"label": "Enable Additional Block 2"}, "enable_additional_block_3": {"label": "Enable Additional Block 3"}, "image": {"label": "Image"}, "heading": {"label": "Heading"}, "description": {"label": "Description"}, "button_text": {"label": "Button Text"}, "link_url": {"label": "<PERSON>"}}, "blocks": {"slide": {"name": "Slide", "settings": {"image": {"label": "Image"}, "mobile_image": {"label": "Mobile Slide Image"}, "heading": {"label": "Heading"}, "subheading": {"label": "Sub heading"}, "text": {"label": "Text"}, "button_label": {"label": "Button label", "info": "Leave the label blank to hide the button."}, "link": {"label": "Button link"}, "secondary_style": {"label": "Use outline button style"}, "box_align": {"label": "Desktop content position", "info": "Position is automatically optimized for mobile.", "options__1": {"label": "Top left"}, "options__2": {"label": "Top center"}, "options__3": {"label": "Top right"}, "options__4": {"label": "Middle left"}, "options__5": {"label": "Middle center"}, "options__6": {"label": "Middle right"}, "options__7": {"label": "Bottom left"}, "options__8": {"label": "Bottom center"}, "options__9": {"label": "Bottom right"}}, "show_text_box": {"label": "Show container on desktop"}, "text_alignment": {"label": "Desktop content alignment", "option_1": {"label": "Left"}, "option_2": {"label": "Center"}, "option_3": {"label": "Right"}}, "image_overlay_opacity": {"label": "Image overlay opacity"}, "color_scheme": {"info": "Visible when container displayed."}, "text_alignment_mobile": {"label": "Mobile content alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}}}}}, "presets": {"name": "Slideshow With Promo Images"}}, "home-slider-with-hot-spots": {"name": "Slideshow with hotspots", "settings": {"layout": {"label": "Layout", "options__1": {"label": "Full width"}, "options__2": {"label": "Grid"}}, "slide_height": {"label": "Slide height", "options__1": {"label": "Adapt to first image"}, "options__2": {"label": "Small"}, "options__3": {"label": "Medium"}, "options__4": {"label": "Large"}}, "slider_visual": {"label": "Pagination style", "options__1": {"label": "Counter"}, "options__2": {"label": "Dots"}, "options__3": {"label": "Numbers"}}, "auto_rotate": {"label": "Auto-rotate slides"}, "change_slides_speed": {"label": "Change slides every"}, "mobile": {"content": "Mobile layout"}, "show_text_below": {"label": "Show content below images on mobile"}, "accessibility": {"content": "Accessibility", "label": "Slideshow description", "info": "Describe the slideshow for customers using screen readers."}, "icon": {"label": "Icon class (Select Icon Option above for viewing the icon)"}}, "blocks": {"slide": {"name": "Slide", "settings": {"image": {"label": "Image"}, "mobile_image": {"label": "Mobile Slide Image"}, "heading": {"label": "Heading"}, "subheading": {"label": "Sub heading"}, "text": {"label": "Text"}, "button_label": {"label": "Button label", "info": "Leave the label blank to hide the button."}, "link": {"label": "Button link"}, "spot1": {"content": "Spot 1"}, "spot_1_title": {"label": "Title"}, "spot_1_text": {"label": "Text"}, "spot_1_image": {"label": "Image"}, "spot_1_link_text": {"label": "<PERSON><PERSON>"}, "spot_1_link": {"label": "Button Link"}, "spot_1_top_position": {"label": "Poisiont top (in %)"}, "spot_1_left_position": {"label": "<PERSON><PERSON><PERSON> left (in %)"}, "spot_1_text_position": {"label": "Content position", "options__1": {"label": "Top"}, "options__2": {"label": "Right"}, "options__3": {"label": "Left"}, "options__4": {"label": "Bottom"}}, "spot2": {"content": "Spot 2"}, "spot_2_title": {"label": "Title"}, "spot_2_text": {"label": "Text"}, "spot_2_image": {"label": "Image"}, "spot_2_link_text": {"label": "<PERSON><PERSON>"}, "spot_2_link": {"label": "Button Link"}, "spot_2_top_position": {"label": "Poisiont top (in %)"}, "spot_2_left_position": {"label": "<PERSON><PERSON><PERSON> left (in %)"}, "spot_2_text_position": {"label": "Content position", "options__1": {"label": "Top"}, "options__2": {"label": "Right"}, "options__3": {"label": "Left"}, "options__4": {"label": "Bottom"}}, "spot3": {"content": "Spot 3"}, "spot_3_title": {"label": "Title"}, "spot_3_text": {"label": "Text"}, "spot_3_image": {"label": "Image"}, "spot_3_link_text": {"label": "<PERSON><PERSON>"}, "spot_3_link": {"label": "Button Link"}, "spot_3_top_position": {"label": "Poisiont top (in %)"}, "spot_3_left_position": {"label": "<PERSON><PERSON><PERSON> left (in %)"}, "spot_3_text_position": {"label": "Content position", "options__1": {"label": "Top"}, "options__2": {"label": "Right"}, "options__3": {"label": "Left"}, "options__4": {"label": "Bottom"}}, "secondary_style": {"label": "Use outline button style"}, "box_align": {"label": "Desktop content position", "info": "Position is automatically optimized for mobile.", "options__1": {"label": "Top left"}, "options__2": {"label": "Top center"}, "options__3": {"label": "Top right"}, "options__4": {"label": "Middle left"}, "options__5": {"label": "Middle center"}, "options__6": {"label": "Middle right"}, "options__7": {"label": "Bottom left"}, "options__8": {"label": "Bottom center"}, "options__9": {"label": "Bottom right"}}, "show_text_box": {"label": "Show container on desktop"}, "text_alignment": {"label": "Desktop content alignment", "option_1": {"label": "Left"}, "option_2": {"label": "Center"}, "option_3": {"label": "Right"}}, "image_overlay_opacity": {"label": "Image overlay opacity"}, "color_scheme": {"info": "Visible when container displayed."}, "text_alignment_mobile": {"label": "Mobile content alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}}}}}, "presets": {"name": "Slideshow with hotspots"}}, "collapsible_content": {"name": "Collapsible content", "settings": {"caption": {"label": "Caption"}, "heading": {"label": "Heading"}, "column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "layout": {"label": "Layout", "options__1": {"label": "No container"}, "options__2": {"label": "Row container"}, "options__3": {"label": "Section container"}}, "container_color_scheme": {"label": "Container color scheme", "info": "Visible when Layout is set to Row or Section container."}, "open_first_collapsible_row": {"label": "Open first collapsible row"}, "header": {"content": "Image layout"}, "image": {"label": "Image"}, "enable_address_block": {"label": "Enable Address Block"}, "address_heading": {"label": "Heading"}, "collapsible_address": {"label": "Address"}, "collapsible_contact_no": {"label": "Phone No"}, "collapsible_contact_id": {"label": "Mail Id"}, "image_ratio": {"label": "Image ratio", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Small"}, "options__3": {"label": "Large"}}, "desktop_layout": {"label": "Desktop layout", "options__1": {"label": "Image first"}, "options__2": {"label": "Image second"}, "info": "Image is always first on mobile."}}, "blocks": {"collapsible_row": {"name": "Collapsible row", "settings": {"heading": {"info": "Include a heading that explains the content.", "label": "Heading"}, "row_content": {"label": "Row content"}, "page": {"label": "Row content from page"}, "icon": {"label": "Icon", "options__1": {"label": "None"}, "options__2": {"label": "Apple"}, "options__3": {"label": "Banana"}, "options__4": {"label": "<PERSON><PERSON>"}, "options__5": {"label": "Box"}, "options__6": {"label": "Carrot"}, "options__7": {"label": "Chat bubble"}, "options__8": {"label": "Check mark"}, "options__9": {"label": "Clipboard"}, "options__10": {"label": "Dairy"}, "options__11": {"label": "Dairy free"}, "options__12": {"label": "Dryer"}, "options__13": {"label": "Eye"}, "options__14": {"label": "Fire"}, "options__15": {"label": "Gluten free"}, "options__16": {"label": "Heart"}, "options__17": {"label": "Iron"}, "options__18": {"label": "Leaf"}, "options__19": {"label": "Leather"}, "options__20": {"label": "Lightning bolt"}, "options__21": {"label": "Lipstick"}, "options__22": {"label": "Lock"}, "options__23": {"label": "Map pin"}, "options__24": {"label": "Nut free"}, "options__25": {"label": "<PERSON>ts"}, "options__26": {"label": "Paw print"}, "options__27": {"label": "Pepper"}, "options__28": {"label": "Perfume"}, "options__29": {"label": "Plane"}, "options__30": {"label": "Plant"}, "options__31": {"label": "Price tag"}, "options__32": {"label": "Question mark"}, "options__33": {"label": "Recycle"}, "options__34": {"label": "Return"}, "options__35": {"label": "Ruler"}, "options__36": {"label": "Serving dish"}, "options__37": {"label": "Shirt"}, "options__38": {"label": "Shoe"}, "options__39": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__40": {"label": "Snowflake"}, "options__41": {"label": "Star"}, "options__42": {"label": "Stopwatch"}, "options__43": {"label": "Truck"}, "options__44": {"label": "Washing"}}}}}, "presets": {"name": "Collapsible content"}}, "flex-banner": {"name": "Flex banner", "settings": {"column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "item_height_settings": {"content": "Item Height Settings"}, "item_height_desktop": {"label": "Item Height Desktop"}, "item_height_laptop": {"label": "Item Height Laptop"}, "item_height_tab": {"label": "Item Height Tab"}, "item_height_mobile": {"label": "Item Height Mobile"}, "block_settings": {"content": "Block settings"}, "custom_class_name": {"label": "Custom class"}, "content_position": {"label": "Content position", "content-center": {"label": "center"}, "content-start": {"label": "Start"}, "content-end": {"label": "End"}}, "block_text_align": {"label": "Text Alignment", "text-center": {"label": "center"}, "text-start": {"label": "Left"}, "text-end": {"label": "Right"}}, "enable-hotspot": {"label": "Enable Hotspot"}}, "blocks": {"image_image": {"name": "Image", "settings": {"image": {"label": "Image"}, "block_title": {"label": "Title"}, "block_sub_title": {"label": "Sub title"}, "block_description": {"label": "description"}, "block_button_text": {"label": "<PERSON><PERSON>"}, "block_button_link": {"label": "Button Link"}, "product": {"label": "Choose Product"}, "top_position": {"label": "Hotspot from Top"}, "left_position": {"label": "Hotspot from Left"}}}}, "presets": {"name": "Flex banner"}}, "hotspot-product": {"name": "Hotspot With Product", "add-to-cart-text": "Add To Cart Text", "image_position": {"label": "Hotspot Image Position", "options__1": "First", "options__2": "Second"}}, "contact-section": {"name": "Contact Section", "settings": {"column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "contact_section_settings": {"content": "Conatct Section Settings"}, "contact-heading_phone": {"label": "Contact Heading"}, "contact-heading_address": {"label": "Address Heading"}, "contact-heading_social": {"label": "Social Heading"}, "contact_address": {"label": "Address"}, "contact_phone_no": {"label": "Phone No"}, "show_social_icons": {"label": "Show Social Icons"}}, "presets": {"name": "Contact Section"}}, "slider_with_tab": {"name": "Slider with tabs", "settings": {"column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "slider_color_settings": {"content": "Slider Color settings"}, "slider_progress_title_color": {"label": "Slider Progress Title color"}, "slider_progress_bar_color": {"label": "Slider Progress Bar color"}, "slider_progress_bar_bg_color": {"label": "Slider Progress Bar Bg color"}, "disable_arrows": {"label": "Disable Navigation Arrows"}, "enable_two_column_block": {"label": "Enable Two Column Block"}, "slide_image_height": {"content": "Slide Image Height Settings"}, "slider_height": {"label": "Desktop"}, "slider_height_lap": {"label": "Laptop"}, "slider_height_tab": {"label": "Tablet"}, "slider_height_mobile": {"label": "Mobile"}}, "blocks": {"slider_tab": {"name": "Slide Tab", "settings": {"image": {"label": "Desktop Slider Image"}, "mobile_image": {"label": "Mobile Slider Image"}, "show_image": {"label": "Show image"}, "show_content": {"label": "Show content"}, "tab": {"label": "Tab"}, "main_title": {"label": "Main Heading"}, "main_title_size": {"label": "Main Heading Font Size"}, "sub_title": {"label": "Sub Heading"}, "sub_title_size": {"label": "Sub Heading Font Size"}, "text": {"label": "Description"}, "text_size": {"label": "Description Font Size"}, "link_text": {"label": "Link Text"}, "link": {"label": "Link"}, "content_position": {"label": "Content Position", "options__1": {"label": "Top Left"}, "options__2": {"label": "Top center"}, "options__3": {"label": "Top Right"}, "options__4": {"label": "Center left"}, "options__5": {"label": "Center"}, "options__6": {"label": "Center right"}, "options__7": {"label": "Bottom left"}, "options__8": {"label": "Bottom center"}, "options__9": {"label": "Bottom Right"}}, "text_alignment": {"label": "Text Alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Right"}, "options__3": {"label": "Center"}}, "product_settings": {"content": "Product Settings"}, "enable_product_section": {"label": "Enable Product Section"}, "product_image": {"label": "Product Image"}, "product_title": {"label": "Product Title"}, "product_title_link": {"label": "Product Title Link"}, "product_desc": {"label": "product Description"}}}}, "presets": {"name": "Slider with tabs"}}, "isotope-products": {"name": "Isotope Products", "settings": {"cover_image": {"label": "Background Image"}, "column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "tab": {"label": "Select collection"}, "products_to_show": {"label": "Products to show"}, "gallery_spacing": {"label": "Gallery Spacing"}, "shop_by_tags_list": {"label": "Tags list"}, "grid": {"label": "Galleries per row", "options__1": {"label": "1"}, "options__2": {"label": "2"}, "options__3": {"label": "3"}, "options__4": {"label": "4"}, "options__5": {"label": "5"}, "options__6": {"label": "6"}}}, "presets": {"name": "Isotope Products"}}, "isotope-gallery": {"name": "Isotope Gallery", "settings": {"column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "grid": {"label": "Galleries per row", "options__1": {"label": "1"}, "options__2": {"label": "2"}, "options__3": {"label": "3"}, "options__4": {"label": "4"}, "options__5": {"label": "5"}, "options__6": {"label": "6"}}, "gallery_spacing": {"label": "Gallery Spacing"}, "shop_by_tags_list": {"label": "Tags list"}, "gallery_color_settings": {"content": "Gallery Color settings"}, "isotope_overlay_color": {"label": "Isotope Overlay color"}, "isotope_title_color": {"label": "Gallery Title color"}, "isotope_description_color": {"label": "Gallery Description color"}, "isotope_icon_bg_color": {"label": "Gallery Icon Background Color"}, "isotope_icon_color": {"label": "Gallery Icon Color"}, "isotope_icon_hover_bg_color": {"label": "Gallery Icon Hover Background Color"}, "isotope_icon_hover_color": {"label": "Gallery Icon Hover Color"}}, "blocks": {"tab": {"name": "Tab", "settings": {"image": {"label": "Image"}, "tag": {"label": "Tag"}, "title": {"label": "Title"}, "text": {"label": "Text"}, "link": {"label": "Link"}}}}, "presets": {"name": "Isotope Gallery"}}, "insta-gallery": {"name": "Edna Instagram Gallery", "settings": {"insta-gallery_layout": {"label": "Instagram Gallery Layout", "options__1": {"label": "Full width"}, "options__2": {"label": "Grid"}}, "title": {"label": "Heading"}, "subtitle": {"label": "Text"}, "text_style": {"label": "Text style", "options__1": {"label": "Subtitle"}, "options__2": {"label": "Uppercase"}}, "text_size": {"label": "Text size", "options__1": {"label": "Small"}, "options__2": {"label": "Medium"}, "options__3": {"label": "Large"}}, "insta-icon": {"label": "Instagram Icon"}, "bg-img": {"label": "Background Image"}, "after_image": {"label": "After Image"}, "title_image": {"label": "Title Image"}, "image_width": {"label": "Image width", "options__1": {"label": "One-third width of column"}, "options__2": {"label": "Half width of column"}, "options__3": {"label": "Full width of column"}}, "image_ratio": {"label": "Image ratio", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}, "options__4": {"label": "Circle"}}, "columns_desktop": {"label": "Number of columns on desktop"}, "column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "background_style": {"label": "Secondary background", "options__1": {"label": "None"}, "options__2": {"label": "Show as column background"}}, "banner_style": {"label": "Banner style", "options__1": {"label": "Grid"}, "options__2": {"label": "Overlay"}}, "grid-column": {"label": "Item per row", "options__1": {"label": "1"}, "options__2": {"label": "2"}, "options__3": {"label": "3"}, "options__4": {"label": "4"}, "options__5": {"label": "5"}, "options__6": {"label": "6"}}, "button_label": {"label": "Button label"}, "button_link": {"label": "Button link"}}, "blocks": {"text": {"name": "Block", "settings": {"block_image": {"label": "Image"}, "show_content": {"label": "Show_content"}, "icon_class": {"label": "Instagram Icon"}, "block_title": {"label": "Heading"}, "enable_title_link": {"label": "Enable title link"}, "block_sub_title": {"label": "Block sub title"}, "block_description": {"label": "Block description"}, "block_button_text": {"label": "<PERSON><PERSON>"}, "block_button_link": {"label": "Button link"}, "desktop_content_alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Desktop Content Alignment"}}}}, "presets": {"name": "Edna Instagram Gallery"}}, "grid-banner-typ3": {"name": "Grid banner typ1", "settings": {"grid-banner_layout": {"label": "grid-banner Layout", "options__1": {"label": "Full width"}, "options__2": {"label": "Grid"}}, "title": {"label": "Heading"}, "subtitle": {"label": "Text"}, "text_style": {"label": "Text style", "options__1": {"label": "Subtitle"}, "options__2": {"label": "Uppercase"}}, "text_size": {"label": "Text size", "options__1": {"label": "Small"}, "options__2": {"label": "Medium"}, "options__3": {"label": "Large"}}, "title_image": {"label": "Title Image"}, "image_width": {"label": "Image width", "options__1": {"label": "One-third width of column"}, "options__2": {"label": "Half width of column"}, "options__3": {"label": "Full width of column"}}, "image_ratio": {"label": "Image ratio", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}, "options__4": {"label": "Circle"}}, "columns_desktop": {"label": "Number of columns on desktop"}, "column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "background_style": {"label": "Secondary background", "options__1": {"label": "None"}, "options__2": {"label": "Show as column background"}}, "grid-column": {"label": "Item per row", "options__1": {"label": "1"}, "options__2": {"label": "2"}, "options__3": {"label": "3"}, "options__4": {"label": "4"}, "options__5": {"label": "5"}, "options__6": {"label": "6"}}, "button_label": {"label": "Button label"}, "button_link": {"label": "Button link"}, "column_gap": {"label": "Column Gap(units no needed)"}, "overlay_image_height_settings": {"content": "Overlay Image Height Settings"}, "overlay_height": {"label": "Overlay Height desktop"}, "overlay_height_laptop": {"label": "Overlay Height laptop"}, "overlay_height_tab": {"label": "Overlay Height Tab"}, "overlay_height_mobile": {"label": "Overlay Height Mobile"}}, "blocks": {"text": {"name": "Grid block", "settings": {"block_image": {"label": "Image"}, "background-none": {"label": "Background None"}, "show_content": {"label": "Show_content"}, "block_title": {"label": "Heading"}, "sub_main_heading": {"label": "Sub Main Heading"}, "enable_title_link": {"label": "Enable title link"}, "block_sub_title": {"label": "Block sub title"}, "block_description": {"label": "Block description"}, "block_button_text": {"label": "<PERSON><PERSON>"}, "block_button_link": {"label": "Button link"}, "block_signature_image": {"label": "block image"}, "reverse_column": {"label": "Content reverse"}, "desktop_content_position": {"options__1": {"label": "Top Left"}, "options__2": {"label": "Top Center"}, "options__3": {"label": "Top Right"}, "options__4": {"label": "Middle Left"}, "options__5": {"label": "Middle Center"}, "options__6": {"label": "Middle Right"}, "options__7": {"label": "Bottom Left"}, "options__8": {"label": "Bottom Center"}, "options__9": {"label": "Bottom Right"}, "label": "Desktop content position[Only Overlay Style]"}, "desktop_content_alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Desktop Content Alignment"}}}}, "presets": {"name": "Grid banner typ3"}}, "abt-layer-image": {"name": "Abt Layer Image", "settings": {"grid-banner_layout": {"label": "grid-banner Layout", "options__1": {"label": "Full width"}, "options__2": {"label": "Grid"}}, "title": {"label": "Heading"}, "subtitle": {"label": "Text"}, "text_style": {"label": "Text style", "options__1": {"label": "Subtitle"}, "options__2": {"label": "Uppercase"}}, "text_size": {"label": "Text size", "options__1": {"label": "Small"}, "options__2": {"label": "Medium"}, "options__3": {"label": "Large"}}, "title_image": {"label": "Title Image"}, "image_width": {"label": "Image width", "options__1": {"label": "One-third width of column"}, "options__2": {"label": "Half width of column"}, "options__3": {"label": "Full width of column"}}, "image_ratio": {"label": "Image ratio", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}, "options__4": {"label": "Circle"}}, "columns_desktop": {"label": "Number of columns on desktop"}, "column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "background_style": {"label": "Secondary background", "options__1": {"label": "None"}, "options__2": {"label": "Show as column background"}}, "banner_style": {"label": "Banner style", "options__1": {"label": "Grid"}, "options__2": {"label": "List"}, "options__3": {"label": "Overlay"}}, "grid-column": {"label": "Item per row", "options__1": {"label": "1"}, "options__2": {"label": "2"}, "options__3": {"label": "3"}, "options__4": {"label": "4"}, "options__5": {"label": "5"}, "options__6": {"label": "6"}}, "button_label": {"label": "Button label"}, "button_link": {"label": "Button link"}, "custom_class_heading": {"content": "Add your custom class name here"}, "custom_class_name": {"label": "Custom class"}}, "blocks": {"text": {"name": "Block", "settings": {"block_image": {"label": "Image"}, "enable_image_after": {"label": "Enable image"}, "block_image_after": {"label": "After Image"}, "enable_image_before": {"label": "Enable image"}, "block_image_before": {"label": "Before Image"}, "show_content": {"label": "Show_content"}, "show_image_hover": {"label": "Show_image_hover"}, "block_title": {"label": "Heading"}, "enable_title_link": {"label": "Enable title link"}, "block_sub_title": {"label": "Block sub title"}, "block_description": {"label": "Block description"}, "show_border_button": {"label": "Border Button"}, "block_button_text": {"label": "<PERSON><PERSON>"}, "block_button_link": {"label": "Button link"}, "block_color_scheme": {"options__1": {"label": "Accent1"}, "options__2": {"label": "Accent2"}, "options__3": {"label": "Background 1"}, "options__4": {"label": "Background 2"}, "options__5": {"label": "Inverse"}, "options__6": {"label": "None"}, "label": "Background color"}, "desktop_content_alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Desktop Content Alignment"}}}}, "presets": {"name": "Abt Layer Image"}}, "video-banner-typ1": {"name": "video banner typ1 ", "settings": {"heading": {"label": "Heading"}, "sub_heading": {"label": "Sub heading"}, "text": {"label": "Video text"}, "icon_1": {"label": "Top Icon"}, "button_label": {"label": "Button label"}, "button_link": {"label": "Button link"}, "button_style_secondary": {"label": "Use outline button style"}, "column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "cover_image": {"label": "Cover image"}, "before_image": {"label": "Pattern Image(Left)"}, "after_image": {"label": "Pattern Image(Right)"}, "video_url": {"label": "URL", "placeholder": "Use a YouTube or Vimeo URL", "info": "Video plays in the page."}, "alt_text": {"label": "Video alt text", "info": "Describe the video for customers using screen readers. [Learn more](https://help.shopify.com/manual/online-store/themes/theme-structure/theme-features#video)"}, "image_padding": {"label": "Add image padding", "info": "Select image padding if you don't want your cover image to be cropped."}, "full_width": {"label": "Make section full width"}}, "presets": {"name": "video banner typ1"}}, "collection-list-type-2": {"name": "Collection list Type 2", "settings": {"title": {"label": "Heading"}, "subtitle": {"label": "Text"}, "text_style": {"label": "Text style", "options__1": {"label": "Subtitle"}, "options__2": {"label": "Uppercase"}}, "text_size": {"label": "Text size", "options__1": {"label": "Small"}, "options__2": {"label": "Medium"}, "options__3": {"label": "Large"}}, "column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "collection_count": {"label": "Enable Collection Product Count"}, "collection_description": {"label": "Enable Collection Description"}, "collection_arrow": {"label": "Enable Collection arrow(if only works overlay style)"}, "image_ratio": {"label": "Image ratio", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}, "info": "Add images by editing your collections. [Learn more](https://help.shopify.com/manual/products/collections)"}, "image_style": {"label": "Image Style", "options__1": {"label": "<PERSON><PERSON><PERSON>"}, "options__2": {"label": "Circle"}, "info": "Image style is default"}, "show_view_all": {"label": "Enable \"View all\" button if list includes more collections than shown"}, "columns_desktop": {"label": "Number of columns on desktop"}, "header_mobile": {"content": "Mobile Layout"}, "columns_mobile": {"label": "Number of columns on mobile", "options__1": {"label": "1 column"}, "options__2": {"label": "2 columns"}}, "swipe_on_mobile": {"label": "Enable swipe on mobile", "controls": "Controls on mobile"}, "column_gap": {"label": "Column Gap(units no needed)"}}, "blocks": {"featured_collection": {"name": "Collection", "settings": {"collection": {"label": "Collection"}, "Enable_button": {"label": "Enable <PERSON>"}, "Enable_block_image": {"label": "Enable Block Image"}, "block_image": {"label": "Block Image"}}}}, "presets": {"name": "Collection list Type 2"}}, "deal-banner-new": {"name": "deal-banner-new", "settings": {"column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "image": {"label": "banner image"}, "image_2": {"label": "Second image"}, "image_overlay_opacity": {"label": "Image overlay opacity"}, "secoundary_image": {"label": "Secondary Image"}, "image_height": {"label": "Banner height", "options__1": {"label": "Small"}, "options__2": {"label": "Medium"}, "options__3": {"label": "Large"}, "info": "For best results, use an image with a 3:2 aspect ratio. [Learn more](https://help.shopify.com/manual/shopify-admin/productivity-tools/image-editor#understanding-image-aspect-ratio)"}, "desktop_content_position": {"options__1": {"label": "Top Left"}, "options__2": {"label": "Top Center"}, "options__3": {"label": "Top Right"}, "options__4": {"label": "Middle Left"}, "options__5": {"label": "Middle Center"}, "options__6": {"label": "Middle Right"}, "options__7": {"label": "Bottom Left"}, "options__8": {"label": "Bottom Center"}, "options__9": {"label": "Bottom Right"}, "label": "Desktop content position"}, "show_text_box": {"label": "Show container on desktop"}, "desktop_content_alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Desktop content alignment"}, "color_scheme": {"info": "Visible when container displayed."}, "header": {"content": "Mobile Layout"}, "mobile_content_alignment": {"options__1": {"label": "Left"}, "options__2": {"label": "Center"}, "options__3": {"label": "Right"}, "label": "Mobile content alignment"}, "stack_images_on_mobile": {"label": "Stack images on mobile"}, "show_text_below": {"label": "Show container on mobile"}, "adapt_height_first_image": {"label": "Adapt section height to first image size", "info": "Overwrites image banner height setting when checked."}, "deal_banner_settings": {"content": "Deal Banner Settings"}, "heading": {"label": "Heading"}, "sub-heading": {"label": "Sub Heading"}, "text": {"label": "Description"}, "deal_end_date": {"label": "Deal End Date", "info": "Format should be 25 JAN 2025"}, "enable_two_column_section": {"label": "Enable Two Column Section"}, "button_label_1": {"label": "First button label"}, "button_link_1": {"label": "First button link"}, "button_style_secondary_1": {"label": "Use outline button style"}, "button_label_2": {"label": "Second button label"}, "button_link_2": {"label": "Second button link"}, "button_style_secondary_2": {"label": "Use outline button style"}}, "presets": {"name": "deal-banner-new"}}, "contact-address-block": {"name": "Contact Address Block", "settings": {"title": {"label": "Heading"}, "bg_image": {"label": "Background Image"}, "image_width": {"label": "Image width", "options__1": {"label": "One-third width of column"}, "options__2": {"label": "Half width of column"}, "options__3": {"label": "Full width of column"}}, "image_ratio": {"label": "Image ratio", "options__1": {"label": "Adapt to image"}, "options__2": {"label": "Portrait"}, "options__3": {"label": "Square"}, "options__4": {"label": "Circle"}}, "columns_desktop": {"label": "Number of columns on desktop"}, "block_column_alignment": {"label": "Block  alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "background_style": {"label": "Secondary background", "options__1": {"label": "None"}, "options__2": {"label": "Show as column background"}}, "button_label": {"label": "Button label"}, "button_link": {"label": "Button link"}, "column_alignment": {"label": "Column alignment", "options__1": {"label": "Left"}, "options__2": {"label": "Center"}}, "block_settings": {"content": "Block Settings"}, "block_style": {"label": "Block Style", "options__1": {"label": "Grid"}, "options__2": {"label": "List"}}, "image_size": {"label": "Image size(Should be lesser than Block Image Size)"}, "image_outer_size": {"label": "Image Outer size"}, "hover_image": {"label": "Block Hover image"}, "header_mobile": {"content": "Mobile Layout"}, "columns_mobile": {"label": "Number of columns on mobile", "options__1": {"label": "1 column"}, "options__2": {"label": "2 columns"}}, "swipe_on_mobile": {"label": "Enable swipe on mobile"}}, "blocks": {"column": {"name": "Column", "settings": {"address_icon": {"label": "Address Icon"}, "mail_icon": {"label": "Mail Icon"}, "phone_icon": {"label": "Contact Icon"}, "block_contact_no": {"label": "Contact No"}, "block_contact_mail": {"label": "Mail Id"}, "block_address": {"label": "Address"}}}}, "presets": {"name": "Contact Address Block"}}}}