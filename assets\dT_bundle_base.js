/**
 * Currency Helpers
 * -----------------------------------------------------------------------------
 * A collection of useful functions that help with currency formatting
 *
 * Current contents
 * - formatMoney - Takes an amount in cents and returns it as a formatted dollar value.
 *
 * Alternatives
 * - Accounting.js - http://openexchangerates.github.io/accounting.js/
 *
 */
window.theme = window.theme || {};

 theme.Currency = (function() {
    var moneyFormat = '${{amount}}'; // eslint-disable-line camelcase
  
    function formatMoney(cents, format) {
      if (typeof cents === 'string') {
        cents = cents.replace('.', '');
      }
      var value = '';
      var placeholderRegex = /\{\{\s*(\w+)\s*\}\}/;
      var formatString = format || moneyFormat;
  
      function formatWithDelimiters(number, precision, thousands, decimal) {
        thousands = thousands || ',';
        decimal = decimal || '.';
  
        if (isNaN(number) || number === null) {
          return 0;
        }
  
        number = (number / 100.0).toFixed(precision);
  
        var parts = number.split('.');
        var dollarsAmount = parts[0].replace(
          /(\d)(?=(\d\d\d)+(?!\d))/g,
          '$1' + thousands
        );
        var centsAmount = parts[1] ? decimal + parts[1] : '';
  
        return dollarsAmount + centsAmount;
      }
  
      switch (formatString.match(placeholderRegex)[1]) {
        case 'amount':
          value = formatWithDelimiters(cents, 2);
          break;
        case 'amount_no_decimals':
          value = formatWithDelimiters(cents, 0);
          break;
        case 'amount_with_comma_separator':
          value = formatWithDelimiters(cents, 2, '.', ',');
          break;
        case 'amount_no_decimals_with_comma_separator':
          value = formatWithDelimiters(cents, 0, '.', ',');
          break;
        case 'amount_no_decimals_with_space_separator':
          value = formatWithDelimiters(cents, 0, ' ');
          break;
        case 'amount_with_apostrophe_separator':
          value = formatWithDelimiters(cents, 2, "'");
          break;
      }
  
      return formatString.replace(placeholderRegex, value);
    }
  
    return {
      formatMoney: formatMoney
    };
  })();




  window.dT_slate = window.dT_slate || {};

/**
 * Variant Selection scripts
 * ------------------------------------------------------------------------------
 *
 * Handles change events from the variant inputs in any `cart/add` forms that may
 * exist.  Also updates the master select and triggers updates when the variants
 * price or image changes.
 *
 * @namespace variants
 */

dT_slate.Variants = (function() {
  /**
   * Variant constructor
   *
   * @param {object} options - Settings from `product.js`
   */
  function Variants(options) {
    this.container = options.container;
    this.product = options.product;
    this.originalSelectorId = options.originalSelectorId;
    this.enableHistoryState = options.enableHistoryState;
    this.singleOptions = this.container.querySelectorAll(
      options.singleOptionSelector
    );
    this.currentVariant = this._getVariantFromOptions();

    this.singleOptions.forEach(
      function(option) {
        option.addEventListener('change', this._onSelectChange.bind(this));
      }.bind(this)
    );
  }

  Variants.prototype = Object.assign({}, Variants.prototype, {
    /**
     * Get the currently selected options from add-to-cart form. Works with all
     * form input elements.
     *
     * @return {array} options - Values of currently selected variants
     */
    _getCurrentOptions: function() {
      var result = [];

      this.singleOptions.forEach(function(option) {
        var type = option.getAttribute('type');
        var isRadioOrCheckbox = type === 'radio' || type === 'checkbox';

        if (!isRadioOrCheckbox || option.checked) {
          result.push({
            value: option.value,
            index: option.getAttribute('data-index')
          });
        }
      });

      return result;
    },

    /**
     * Find variant based on selected values.
     *
     * @param  {array} selectedValues - Values of variant inputs
     * @return {object || undefined} found - Variant object from product.variants
     */
    _getVariantFromOptions: function() {
      var selectedValues = this._getCurrentOptions();
      var variants = this.product.variants;

      var found = variants.find(function(variant) {
        return selectedValues.every(function(values) {
          return variant[values.index] === values.value;
        });
      });

      return found;
    },

    /**
     * Event handler for when a variant input changes.
     */
    _onSelectChange: function() {
      var variant = this._getVariantFromOptions();
      var product = this.product;

      this.container.dispatchEvent(
        new CustomEvent('variantChange', {
          detail: {
            variant: variant,
            product: product
          },
          bubbles: true,
          cancelable: true
        })
      );

      if (!variant) {
        return;
      }

      this._updateMasterSelect(variant, product);
      this._updateImages(variant, product);
      this._updatePrice(variant, product);
      this._updateSKU(variant, product);
      this.currentVariant = variant;

      if (this.enableHistoryState) {
        this._updateHistoryState(variant, product);
      }
    },


    /**
     * Trigger event when variant image changes
     *
     * @param  {object} variant - Currently selected variant
     * @return {event}  variantImageChange
     */
    _updateImages: function(variant, product) {
      var variantImage = variant.featured_image || {};
      var currentVariantImage = this.currentVariant.featured_image || {};

      if (
        !variant.featured_image ||
        variantImage.src === currentVariantImage.src
      ) {
        return;
      }

      this.container.dispatchEvent(
        new CustomEvent('variantImageChange', {
          detail: {
            variant: variant,
             product: product
          },
          bubbles: true,
          cancelable: true
        })
      );
    },

    /**
     * Trigger event when variant price changes.
     *
     * @param  {object} variant - Currently selected variant
     * @return {event} variantPriceChange
     */
    _updatePrice: function(variant, product) {
      if (
        variant.price === this.currentVariant.price &&
        variant.compare_at_price === this.currentVariant.compare_at_price &&
        variant.unit_price === this.currentVariant.unit_price
      ) {
        return;
      }

      this.container.dispatchEvent(
        new CustomEvent('variantPriceChange', {
          detail: {
            variant: variant,
            product: product
          },
          bubbles: true,
          cancelable: true
        })
      );
    },

    /**
     * Trigger event when variant sku changes.
     *
     * @param  {object} variant - Currently selected variant
     * @return {event} variantSKUChange
     */
    _updateSKU: function(variant, product) {
      if (variant.sku === this.currentVariant.sku) {
        return;
      }

      this.container.dispatchEvent(
        new CustomEvent('variantSKUChange', {
          detail: {
            variant: variant,
            product: product
          },
          bubbles: true,
          cancelable: true
        })
      );
    },

    /**
     * Update history state for product deeplinking
     *
     * @param  {variant} variant - Currently selected variant
     * @return {k}         [description]
     */
    _updateHistoryState: function(variant, product) {
      if (!history.replaceState || !variant) {
        return;
      }

    },

    /**
     * Update hidden master select of variant change
     *
     * @param  {variant} variant - Currently selected variant
     */
    _updateMasterSelect: function(variant, product) {
      var masterSelect = this.container.querySelector(this.originalSelectorId);

      if (!masterSelect) return;
      masterSelect.value = variant.id;
    }
  });

  return Variants;
})();






$(document).on("submit", "#bundleAddToCartFrmId", function(event) {
  event.preventDefault();
  
  //const cartContent = document.querySelector('cart-notification') || document.querySelector('cart-drawer');
  
  const setShopifyQue = function() {
        
    Shopify.queue = [];
    Shopify.isBundleDiscount = true;
    bundleProductIds.forEach(element => {
      var productId = element;
      var productVarientId = $("#bundle_product_price_" + productId).attr("data-varient-id");
      if (productVarientId != '' && $('#bundle_chk_' + productId).is(":checked")) {
        Shopify.queue.push({
          id: productVarientId,
          quantity: 1
        });

      } else {
        Shopify.isBundleDiscount = false;
      }

    });

  };


  const doBundleAddToCart = function() {
    setShopifyQue();
    Shopify.moveAlong = function() {
      if (Shopify.queue.length) {
        console.log('one');
        dT_AddToCartOther(Shopify.queue);
      }
    };

    Shopify.moveAlong();

  };


  Promise.resolve(doBundleAddToCart())
  .then(function() {
  });

});

const dT_AddToCartOther = function(products) {
   
  const cartContent = document.querySelector('cart-notification') || document.querySelector('cart-drawer');
  
  let formData = {
    items: products,
    sections: cartContent.getSectionsToRender().map((section) => section.id),
    sections_url: window.location.pathname
  };

  fetch(window.Shopify.routes.root + 'cart/add.js', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(formData)
  })
  .then(response => {
    return response.json();
  })
  .then((response) => {
    cartContent.classList.contains('is-empty') && cartContent.classList.remove('is-empty');
    cartContent.renderContents(response);
  })
  .catch((error) => {
    console.error('Error:', error);
  });

};