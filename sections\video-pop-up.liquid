{{ 'video-pop-up.css' | asset_url | stylesheet_tag }}
<script src="{{ 'video-pop-up.js' | asset_url }}" defer="defer"></script>
{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
 .shopify-section.reveal :where(.video-section__content){ -webkit-animation: fadeInUp 1s ease-in-out 0s forwards;
          animation: fadeInUp 1s ease-in-out 0s forwards; } 


 .video-banner .video-section__content .button:hover{    background-color: var(--gradient-base-accent-1);
    color: var(--gradient-base-background-1); }
@-webkit-keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 100%, 0);
  }
  to {
    opacity: 1;
    transform: none;
  }
}
{%- if section.settings.cover_image != blank -%}
.section-{{ section.id }}-cover_image {
        background-image: url('{{ section.settings.cover_image |  image_url: width: 1100   }}');
        background-size: cover;
        background-position: center center;
        background-repeat: no-repeat;
        width: 100%;
        object-fit: cover;
        object-position: center;
        background-attachment: unset;
        }

 

  {%- endif -%} 

  
{%- endstyle -%}

<div class="color-{{ section.settings.color_scheme }} gradient section-{{ section.id }}-cover_image video-banner ">
  <div class="video-section  {{ section.settings.custom_class_name }} isolate section-{{ section.id }}-padding content-style-{{ section.settings.content_style -}}">
    <div class="{%- if section.settings.page_full_width %}page-width{% else%}container{% endif -%} ">
      <div class="video-section__content">
      {%- unless section.settings.heading == blank -%}
        <div class="title-wrapper title-wrapper--no-top-margin">
          <h2 class="title {{ section.settings.heading_size }}">{{ section.settings.heading }}</h2>
        </div>
      {%- endunless -%}
      {%- unless section.settings.sub_heading == blank -%}
        <div class="sub-title-wrapper sub-title-wrapper--no-top-margin">
          <h2 class="sub-title {{ section.settings.heading_size }}">{{ section.settings.sub_heading }}</h2>
        </div>
      {%- endunless -%}
      {%- unless section.settings.text == blank -%}
        <div class="text-wrapper text-wrapper--no-top-margin">
          <p class="text {{ section.settings.heading_size }}">{{ section.settings.text }}</p>
        </div>
      {%- endunless -%}
      {%- unless section.settings.button_label == blank -%}
        <a{% if section.settings.button_link == blank %} role="link" aria-disabled="true"{% else %} href="{{ section.settings.button_link }}"{% endif %} class="button{% if section.settings.button_style_secondary %} button--secondary{% else %} button--primary{% endif %}">
        {{ section.settings.button_label | escape }}
        </a>
      {%- endunless -%}
      </div>
    </div>
    {% if section.settings.video_url != blank and section.settings.video_format == 'modal' %}    
    <div class="video-play-icon">
      <a href="#" class="watch-more">  {%- render 'icon-play' -%}</a>
    </div>
    <!-- The Modal -->
      <div id="modal" class="modal"></div>
        <div class="video-popup">  <a class="close">&times;</a>  
          <div class="video-wrapper">
            <div class="video-container">
            <iframe width="860" height="615" src="https://www.youtube.com/embed/{{ section.settings.video_url.id }}?autoplay=1" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
            </div>
          </div>
        </div>
    {% endif %}
    {% if section.settings.self_video_url != blank and section.settings.video_format == 'video' %}    
    <video loop muted autoplay playsinline class="autoplay-video">
    <source src="{{section.settings.self_video_url}}" type="video/mp4">
    </video>
    {% endif %}
  </div>
</div>

{% schema %}
  {
    "name": "Video popup",
    "class": "index-video-popup",
    "settings": [
      {
      "type":"checkbox",
      "id":"page_full_width",
       "default": false,
      "label": "t:sections.all.page_full_width.label"
    },
    {
      "type":"checkbox",
      "id":"page_full_width_spacing",
       "default": false,
      "label": "t:sections.all.page_full_width_spacing.label"
    },
      {
      "type": "text",
      "id": "heading",
      "default": "Video heading",
      "label": "t:sections.video-pop-up.settings.heading.label"
    },
    {
    "type": "text",
    "id": "sub_heading",
    "default": "Video sub heading",
    "label": "t:sections.video-pop-up.settings.sub_heading.label"
    },
    {
    "type": "text",
    "id": "text",
    "default": "Short description about this video",
    "label": "t:sections.video-pop-up.settings.text.label"
    },
      {
      "type": "text",
      "id": "button_label",
      "default": "Button label",
      "label": "t:sections.video-pop-up.settings.button_label.label"
      },
      {
      "type": "url",
      "id": "button_link",
      "label": "t:sections.video-pop-up.settings.button_link.label"
      },
      {
      "type": "checkbox",
      "id": "button_style_secondary",
      "default": false,
      "label": "t:sections.video-pop-up.settings.button_style_secondary.label"
      },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h4",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h3",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__3.label"
        }
      ],
      "default": "h4",
      "label": "t:sections.all.heading_size.label"
    },
{
      "type": "select",
      "id": "content_style",
      "options": [
        {
          "value": "default",
          "label": "t:sections.all.content_style.options__1.label"
        },
        {
          "value": "overlay",
          "label": "t:sections.all.content_style.options__2.label"
        }       
      ],
      "default": "overlay",
      "label": "t:sections.all.content_style.label"
    },
    {
      "type": "image_picker",
      "id": "cover_image",
      "label": "t:sections.video-pop-up.settings.cover_image.label"
    },
    {
      "type": "video_url",
      "id": "video_url",
      "accept": [
        "youtube",
        "vimeo"
      ],
      "default": "https://www.youtube.com/watch?v=_9VUPq3SxOc",
      "label": "t:sections.video-pop-up.settings.video_url.label",
      "placeholder": "t:sections.video-pop-up.settings.video_url.placeholder",
      "info": "t:sections.video-pop-up.settings.video_url.info"
    },
    {
      "type": "text",
      "id": "description",
      "label": "t:sections.video-pop-up.settings.description.label",
      "info": "t:sections.video-pop-up.settings.description.info"
    },
      {
     "type": "textarea",
     "id": "self_video_url",
     "label": "t:sections.video-pop-up.settings.self_video_url.label",
      "info": "t:sections.video-pop-up.settings.self_video_url.info",
      "default": "https://storage.googleapis.com/coverr-main/mp4/Palm_Trees.mp4"
       },
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "t:sections.video-pop-up.settings.full_width.label",
      "default": false
    },
       {
    "type": "text",
    "id": "custom_class_name",
    "label": "t:sections.video-pop-up.settings.custom_class_name.label"
    },
   {
   "type": "select",
   "id": "video_format",
   "label": "t:sections.video-pop-up.settings.video_format.label",
   "info": "t:sections.video-pop-up.settings.video_format.info",
    "options": [
    {
    "value": "video",
    "label": "Self Hosted Video"
    },
      
    {
    "value": "modal",
   "label": "Popup"
    }
    ]
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "t:sections.all.colors.accent_1.label"
        },
        {
          "value": "accent-2",
          "label": "t:sections.all.colors.accent_2.label"
        },
        {
          "value": "background-1",
          "label": "t:sections.all.colors.background_1.label"
        },
        {
          "value": "background-2",
          "label": "t:sections.all.colors.background_2.label"
        },
        {
          "value": "inverse",
          "label": "t:sections.all.colors.inverse.label"
        }
      ],
      "default": "background-1",
      "label": "t:sections.all.colors.label"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    }
],
  "presets": [
    {
      "name": "t:sections.video-pop-up.presets.name"
    }
  ]
}
{% endschema %}
