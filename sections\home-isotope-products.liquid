{{ 'component-loading-overlay.css' | asset_url | stylesheet_tag }}
{{ 'component-card.css' | asset_url | stylesheet_tag }}
{{ 'component-price.css' | asset_url | stylesheet_tag }}

<link rel="stylesheet" href="{{ 'component-slider.css' | asset_url }}" media="print" onload="this.media='all'">
<link rel="stylesheet" href="{{ 'template-collection.css' | asset_url }}" media="print" onload="this.media='all'">
<link rel="stylesheet" href="{{ 'quick-add.css' | asset_url }}" media="print" onload="this.media='all'">
<script src="{{ 'quick-add.js' | asset_url }}" defer="defer"></script>
<script src="{{ 'product-form.js' | asset_url }}" defer="defer"></script>
<noscript>{{ 'component-slider.css' | asset_url | stylesheet_tag }}</noscript>
<noscript>{{ 'template-collection.css' | asset_url | stylesheet_tag }}</noscript>
<script src="{{ 'isotope.pkgd.js' | asset_url }}" defer="defer"></script>

{% style %}
    .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.50 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.50 | round: 0 }}px;
  }
   @media screen and (min-width: 576px) and (max-width: 749px){
   .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }
   }
  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
.isotope-grid .product-icons{opacity: 1;}
{%- if section.settings.cover_image != blank -%}
.section-{{ section.id }}-isotope-products {background-image: url('{{ section.settings.cover_image |  image_url: width: 1920   }}');background-size: cover;background-position: center center;background-repeat: no-repeat;width: 100%;object-fit: cover;object-position: center;background-attachment: fixed;padding-top:50px;}
{%- endif -%}
{% endstyle %}

{%- liquid
assign margin = section.settings.margin | split: ','
assign margin_top = margin[0]
assign margin_bottom = margin[1]
assign padding = section.settings.padding | split: ','
assign padding_top = padding[0]
assign padding_bottom = padding[1]
if section.settings.enable_overlay
assign section_overlay = 'dt-sc-overlay'
endif

case section.settings.grid
when '1'
when '2'
assign column = '50%'
when '3'
assign column = '33.33%'
when '4'
assign column = '24.75%'
when '5'
assign column = '19.6%'
when '6'
assign column = '16.2%'
else
assign column = '33.33%'
endcase

%}
{% assign tags = section.settings.shop_by_tags_list | split: ',' %}
<div data-section-id ="{{ section.id }}" data-section-type="home-isotope-products" class="dt-sc-section-wrapper  section-{{ section.id }}-isotope-products" >
<div class="{% if section.settings.page_full_width %} page-full-width {% else %} page-width {% endif %} {% if section.settings.page_full_width_spacing %} page-full-width_spacing {% endif %}section-{{ section.id }}-padding isolate">
<div class="row {{ section.settings.custom_class_name }}">
{%- unless section.settings.title == blank -%}
<div class="title-wrapper-with-link title-wrapper--self-padded-mobile title-wrapper--no-top-margin content-align--{{ section.settings.column_alignment }}">
{%- if section.settings.sub_heading != blank -%}  
<h6 class="sub-heading">{{ section.settings.sub_heading | escape }}</h6>
{%- endif -%} 
{%- if section.settings.title != blank -%}  
<h2 class="title {{ section.settings.heading_size }}">
{{ section.settings.title | escape }}
</h2>
{%- endif -%} 
{%- if section.settings.description != blank -%}  
<p class="description">{{ section.settings.description }}</p>
{%- endif -%}   
{%- if section.settings.button_label != blank -%}
<a {% if section.settings.button_link == blank %} role="link" aria-disabled="true"{% else %} href="{{ section.settings.button_link }}"{% endif %} class="button{% if section.settings.button_style_secondary %} button--secondary{% else %} button--primary{% endif %}">{{ section.settings.button_label | escape }}</a>
{%- endif -%}
</div>
{%- endunless -%}
<div class="filters {{ section.id }} wow">      
<div class="ui-group products-filter">
<div class="button-group js-radio-button-group" data-filter-group="color">
<button class="button is-checked" data-filter="">{{ 'sections.featured_collection.all' | t }}</button>
{% for result in tags %}
<button class="button " data-filter=".{{ result | handle }}">{{ result }}</button>
{% endfor %}
</div>
</div>
</div>
<div class="section-{{ section.id }}-cover_image">

<div class="dT_vDynamicPWrap-{{ section.id }} dT_vProdWrap wow">

<div class="isotope-grid {{ section.id }} grid">          
{% for product in collections[section.settings.tab].products limit: section.settings.products_to_show %}
    <div id="Slide-{{ section.id }}-{{ forloop.index }}" class="isotope-selector  {% for tag in product.tags %} {{ tag | handle | uniq | join: ' ' }}{% endfor %} grid__item">     
    {% render 'isotope-grid-item',
      card_product: product,
      media_aspect_ratio: section.settings.image_ratio,
      show_secondary_image: section.settings.show_secondary_image,
      show_vendor: section.settings.show_vendor,
      show_rating: section.settings.show_rating,
      show_quick_add: true,
      show_new_tag: section.settings.show_new_tag,
      section_id: section.id
    %}
    </div>
{%- endfor -%}
</div>
</div>
</div>
</div>
</div>
</div>
<script>

$(document).ready(function(){
var $grid = $('.isotope-grid.{{ section.id }}').isotope({
itemSelector: '.isotope-selector',
masonry: {
gutter: {{ section.settings.gallery_spacing }}
}
});
var filters = {};
$('.filters.{{ section.id }}').on( 'click', '.button', function( event ) {
var $button = $( event.currentTarget );
var $buttonGroup = $button.parents('.button-group');
var filterGroup = $buttonGroup.attr('data-filter-group');
filters[ filterGroup ] = $button.attr('data-filter');
var filterValue = concatValues( filters );
$grid.isotope({ filter: filterValue });
});

$('.button-group').each( function( i, buttonGroup ) {
var $buttonGroup = $( buttonGroup );
$buttonGroup.on( 'click', 'button', function( event ) {
$buttonGroup.find('.is-checked').removeClass('is-checked');
var $button = $( event.currentTarget );
$button.addClass('is-checked');
});
});


function concatValues( obj ) {
var value = '';
for ( var prop in obj ) {
value += obj[ prop ];
}
return value;
}   
});


$(document).on('click', '.products-filter .button', function(){
$('.products-filter .button').removeClass('active');
$('.products-filter .button-group').toggleClass('expanded');
$(this).addClass('active');

});

$(document).click(function(event) {
if (!$(event.target).closest(".products-filter").length) {
$("body").find(".products-filter .button-group").removeClass("expanded");
}
});

</script>
{%- style -%}
#shopify-section-{{ section.id }}.isotope-products .isotope-selector { overflow: hidden; margin-bottom: {{ section.settings.gallery_spacing }}px; width: calc({{ column }} - calc({{ section.settings.gallery_spacing }}px - 10px));animation: none; }
#shopify-section-{{ section.id }}.isotope-products .ui-group.products-filter .button { margin: 0.2rem 10px;}
/* ---- isotope ---- */
/* clear fix */
#shopify-section-{{ section.id }}.isotope-products .isotope-grid {
overflow: hidden;
transition: var(--DTBaseTransition);
-webkit-transition: var(--DTBaseTransition);
}
/* clear fix */
#shopify-section-{{ section.id }}.isotope-products .isotope-grid:after {
content: '';
display: block;
clear: both;
}
#shopify-section-{{ section.id }}.isotope-products .filters { text-align: center; }
/* ui group */
#shopify-section-{{ section.id }}.isotope-products .ui-group {
display: inline-block; width: 100%; margin: 0 auto calc(7.5rem - 15px);
}
#shopify-section-{{ section.id }}.isotope-products .ui-group .button-group {
display: flex; width: 100%; justify-content: center;
}

@media only screen and (max-width: 1380px) {
#shopify-section-{{ section.id }}.isotope-products .isotope-selector { width: calc(24.7% - calc({{ section.settings.gallery_spacing }}px - 10px)); }
}
@media only screen and (max-width: 1199px) {
#shopify-section-{{ section.id }}.isotope-products .isotope-selector { width: calc(33.33% - calc({{ section.settings.gallery_spacing }}px - 10px)); }
}
@media only screen and (max-width:991px) {
#shopify-section-{{ section.id }}.isotope-products .isotope-selector {width: calc(50% - calc({{ section.settings.gallery_spacing }}px - 10px));}
}
@media only screen and (min-width:768px) {
#shopify-section-{{ section.id }}.isotope-products .button.is-checked,
#shopify-section-{{ section.id }}.isotope-products .button.is-checked.active {  background: var(--gradient-base-accent-3); border: 1px solid var(--gradient-base-accent-3); }
}
@media only screen and (max-width:767px) {
#shopify-section-{{ section.id }}.isotope-products .filters {
position: relative;
z-index: 3;
}
#shopify-section-{{ section.id }}.isotope-products .filters .ui-group {
position: relative;
width: 50%;
height: 100%;
display: grid;
height: 100%;
margin-bottom: 3rem;
}
 #shopify-section-{{ section.id }}.isotope-products .filters .ui-group .button-group {
display: grid;
width: 100%;
grid-template-columns: 1fr;
line-height: normal;
} 
#shopify-section-{{ section.id }}.isotope-products .filters .ui-group .button-group button {
order: 1;
width: 100%;
margin: 0;
transition: var(--DTBaseTransition);
}
#shopify-section-{{ section.id }}.isotope-products .filters .ui-group .button-group button:not(:last-child),
#shopify-section-{{ section.id }}.isotope-products .filters .ui-group .button-group button.is-checked.active {
border-bottom: 1px solid {{ settings.button_text_hover_color | color_modify: 'alpha', 0.35 }};
}
#shopify-section-{{ section.id }}.isotope-products .filters .ui-group .button-group button.active,
#shopify-section-{{ section.id }}.isotope-products .filters .ui-group .button-group button.is-checked {
order: 0;
z-index: 1;
}
#shopify-section-{{ section.id }}.isotope-products .filters .ui-group .button-group:after {
width: 15px;
height: 15px;
border: 2px solid var(--DT_Button_Text_Hover_Color);
border-right: 0;
border-bottom: 0;
content: "";
position: absolute;
right: 25px;
transform: rotate(-135deg) translateY(-50%);
-webkit-transform: rotate(-135deg) translateY(-50%);
top: 50%;
z-index: 1;
pointer-events: none;
transform-origin: top;
-webkit-transform-origin: top;
}

#shopify-section-{{ section.id }}.isotope-products .filters .ui-group .button-group.expanded:after {
transform: rotate(45deg) translateY(-50%);
-webkit-transform: rotate(45deg) translateY(-50%);
transform-origin: left;
-webkit-transform-origin: left;
}
#shopify-section-{{ section.id }}.isotope-products .filters .ui-group .button-group.expanded button:not(.active) {
position: relative;
top: auto;
}
}
@media only screen and (max-width:576px) {
#shopify-section-{{ section.id }}.isotope-products .isotope-selector {width: 100%;max-width:100%; }
#shopify-section-{{ section.id }}.isotope-products .filters .ui-group { width: 75%;height:100%; }
}

#shopify-section-{{ section.id }}.isotope-products .isotope-selector .card .card__media_1{position:relative;}
#shopify-section-{{ section.id }}.isotope-products .isotope-selector .card .card__media_1::after {
    bottom: 0;
    content: "";
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    box-shadow: 0 0 5px 0 var(--color-overlay) inset;
    -moz-box-shadow: 0 0 5px 0 var(--color-overlay) inset;
    -webkit-box-shadow: 0 0 5px 0 var(--color-overlay) inset;
    border: 15px solid var(--color-overlay);
    display: flex;
}
  
  #shopify-section-{{ section.id }}.isotope-products .isotope-selector .card .card-content-1 .card__media_1 .card-contant-wrapper-1 { 
    transition:all 0.3s linear;
    position: absolute;
    top: 0;
    display: flex;
    align-items: center;
    width: 100%;
    height: 100%;text-align: center;
    flex-direction: column;opacity:0;z-index: 1;
    justify-content: flex-end;padding:25px;}
 #shopify-section-{{ section.id }}.isotope-products .isotope-selector .card .card-content-1 .card__media_1 .card-contant-wrapper-1 ul.product-icons{ position: relative;  width: 100%; flex-direction: row-reverse;height: 100%; align-items: center;}
  #shopify-section-{{ section.id }}.isotope-products  .isotope-selector .card .card-content-1 .card__media_1 .card-contant-wrapper-1 ul.product-icons li a svg,
  #shopify-section-{{ section.id }}.isotope-products .isotope-selector .card .card-content-1 .card__media_1 .card-contant-wrapper-1 ul.product-icons button svg { width: 2rem; height: 2rem;fill: currentcolor;}
   #shopify-section-{{ section.id }}.isotope-products  .isotope-selector .card .card-content-1 .card__media_1 .card-contant-wrapper-1 ul.product-icons li a,
  #shopify-section-{{ section.id }}.isotope-products .isotope-selector .card .card-content-1 .card__media_1 .card-contant-wrapper-1 ul.product-icons button { background: transparent;color: var(--gradient-base-accent-1);
    border: 1px solid var(--gradient-base-accent-1);
    border-radius: 50%;
    width: 47px;
    height: 47px;
    display: flex;
    justify-content: center;
    align-items: center;cursor: pointer;}
   #shopify-section-{{ section.id }}.isotope-products .isotope-selector .card:hover .card-content-1 .card__media_1 .card-contant-wrapper-1 { 
     background: rgba(var(--color-base-background-1), 0.8);transition:all 0.3s linear;opacity:1;
   }
  #shopify-section-{{ section.id }}.isotope-products .isotope-selector .card .card-content-1 .card__media_1 .card-contant-wrapper-1 .card__heading a:hover {
    color: var(--gradient-base-accent-2);
}
  #shopify-section-{{ section.id }}.isotope-products  .isotope-selector .card .card-content-1 .card__media_1 .card-contant-wrapper-1 ul.product-icons li a:hover,
  #shopify-section-{{ section.id }}.isotope-products .isotope-selector .card .card-content-1 .card__media_1 .card-contant-wrapper-1 ul.product-icons button:hover{background: var(--gradient-base-accent-1);color: var(--gradient-background);}
   #shopify-section-{{ section.id }}.isotope-products .isotope-selector .card .card-content-1 .card__media_1 img{transition:all 1.5s ease;}
   #shopify-section-{{ section.id }}.isotope-products .isotope-selector .card:hover .card-content-1 .card__media_1 img{ /*transform: scale(1.2);*/transition:all 1.5s ease;}
   #shopify-section-{{ section.id }}.isotope-products  .isotope-selector .card .card-content-1 .card__media_1 .card-contant-wrapper-1 .price--on-sale .price__sale{ text-align: center;justify-content: center;}
   #shopify-section-{{ section.id }}.isotope-products  .isotope-selector .card .card-content-1 .card__media_1 .card-contant-wrapper-1 .quick-add__submit .loading-overlay__spinner{width: 47px;height: 47px;color: var(--gradient-base-background-1);}
   #shopify-section-{{ section.id }}.isotope-products  .isotope-selector .card .card-content-1 .card__media_1 .card-contant-wrapper-1 .quick-add__submit:hover .loading-overlay__spinner{color:var(--gradient-base-accent-1);}
  {%- endstyle -%}

{% schema %}
{
"name": "t:sections.isotope-products.name",
"class": "section index-section isotope-products",
"settings": [
{
"type":"checkbox",
"id":"page_full_width",
"default": false,
"label": "t:sections.all.page_full_width.label"
},
{
"type":"checkbox",
"id":"page_full_width_spacing",
"default": false,
"label": "t:sections.all.page_full_width_spacing.label"
},
{
"type": "range",
"id": "padding_top",
"min": 0,
"max": 120,
"step": 4,
"unit": "px",
"label": "t:sections.all.padding.padding_top",
"default": 36
},
{
"type": "range",
"id": "padding_bottom",
"min": 0,
"max": 120,
"step": 4,
"unit": "px",
"label": "t:sections.all.padding.padding_bottom",
"default": 36
},  
{
"type": "image_picker",
"id": "cover_image",
"label": "t:sections.isotope-products.settings.cover_image.label",
"info": "Size: 1920x1280"
},
{
"type": "text",
"id": "title",
"default": "Isotop product gallery",
"label": "t:sections.all.title.label"
},
{
"type": "select",
"id": "heading_size",
"options": [
{
"value": "h2",
"label": "t:sections.all.heading_size.options__1.label"
},
{
"value": "h1",
"label": "t:sections.all.heading_size.options__2.label"
},
{
"value": "h0",
"label": "t:sections.all.heading_size.options__3.label"
}
],
"default": "h1",
"label": "t:sections.all.heading_size.label"
},
{
"type": "text",
"id": "sub_heading",
"default": "Sub Heading",     
"label": "t:sections.all.sub_heading.label"
},
{
"type": "text",
"id": "description",
"default": "Use This Text To Share The Information Which You Like!.",    
"label": "t:sections.all.description.label"
},
{
"type": "text",
"id": "button_label",
"default": "Button label",
"label": "t:sections.all.button_label.label"
},
{
"type": "url",
"id": "button_link",
"label": "t:sections.all.button_link.label"
},
{
"type": "checkbox",
"id": "button_style_secondary",
"default": false,
"label": "t:sections.all.button_style_secondary.label"
},
{
      "type": "select",
      "id": "column_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.isotope-products.settings.column_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.isotope-products.settings.column_alignment.options__2.label"
        }
      ],
      "default": "center",
      "label": "t:sections.isotope-products.settings.column_alignment.label"
    },  
{
"type": "collection",
"id": "tab",
"label": "t:sections.isotope-products.settings.tab.label" 
},
{
"type": "range",
"id": "products_to_show",
"min": 2,
"max": 20,
"step": 1,
"default": 8,
"label": "t:sections.isotope-products.settings.products_to_show.label"
},
{
"type": "textarea",
"id": "shop_by_tags_list",
"label": "t:sections.isotope-products.settings.shop_by_tags_list.label",
"default":"Tab1, Tab2, Tab3",    
"info": "Separate by a comma, i.e \"XS, S, M, L, XL\"."
},
{
"type": "select",
"id": "grid",
"label": "t:sections.isotope-products.settings.grid.label",
"default": "4",
"options": [
{
"value": "1",
"label": "t:sections.isotope-products.settings.grid.options__1.label"
},
{
"value": "2",
"label": "t:sections.isotope-products.settings.grid.options__2.label"
},
{
"value": "3",
"label": "t:sections.isotope-products.settings.grid.options__3.label"
},
{
"value": "4",
"label": "t:sections.isotope-products.settings.grid.options__4.label"
},
{
"value": "5",
"label": "t:sections.isotope-products.settings.grid.options__5.label"
},
{
"value": "6",
"label": "t:sections.isotope-products.settings.grid.options__6.label"
}
]
},
{
"type": "range",
"id": "gallery_spacing",
"label": "t:sections.isotope-products.settings.gallery_spacing.label",
"min": 0,
"max": 100,
"step": 1,
"unit": "px",
"default": 30
},
{
"type": "header",
"content": "t:sections.all.custom_class_heading.content"
},
{
"type": "text",
"id": "custom_class_name",
"label": "t:sections.all.custom_class_name.label"
}
],
"presets": [
{
"name": "Isotope Products"
}
]
}
{% endschema %}