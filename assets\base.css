.shopify-section.slideshow.slidershow.reveal slideshow-component .swiper-slide :where(.banner__sub_heading,) {
  font-size: 1.25 rem;
}
.text-separate-color{
  color:var(--gradient-base-accent-2);
}
.purchase-code{
   background:var(--gradient-base-accent-2);
  font-size:20px;
  font-weight:500;
  color:#ffff;
  margin-left:8px;
  padding:0 10px;
}
.text-separate-color1{
  color:#FBBF18;
}
.no-js:not(html) {
  display: none !important;
}

html.no-js .no-js:not(html) {
  display: block !important;
}

.no-js-inline {
  display: none !important;
}

html.no-js .no-js-inline {
  display: inline-block !important;
}

html.no-js .no-js-hidden {
  display: none !important;
}
*,
*::before,
*::after {
  box-sizing: inherit;
}
#shopify-section-template--21701471240498__a9f7c4e0-0bee-4499-b8cd-1f36c7f56d21 .curtainex-grid-banner .grid-banner{
  background:transparent;
}

/* Color custom properties */
:root,
.color-background-1 {
  --color-foreground: var(--color-base-accent-1);
  --color-background: var(--color-base-background-1);
  --gradient-background: var(--gradient-base-background-1);
}

.color-background-2 {
  --color-foreground: var(--color-base-background-1);
  --color-background: var(--color-base-background-2);
  --gradient-background: var(--gradient-base-background-2);
}

.color-inverse {
  --color-foreground: var(--color-base-background-1);
  --color-background:var(--color-base-accent-1);
  --gradient-background: var(--gradient-base-accent-1);
}

.color-accent-1 {
  --color-foreground: var(--color-base-background-1);
  --color-background: var(--gradient-base-background-3);
  --gradient-background: var(--gradient-base-background-3);
}

.color-accent-2 {
  --color-foreground: var(--color-base-accent-1);
  --color-background: var(--color-base-background-2);
  --gradient-background: var(--gradient-base-background-2);
}

.color-foreground-outline-button {
  --color-foreground: var(--color-base-outline-button-labels);
}

.color-foreground-accent-1 {
  --color-foreground: var(--color-base-accent-1);
}

.color-foreground-accent-2 {
  --color-foreground: var(--color-base-accent-2);
}


:root{
  --caption-color:#FBBF18;
}





:root,
.color-background-1 {
  --color-link: var(--color-icon);
  --alpha-link: 0.85;
}

.color-background-2,
.color-inverse,
.color-accent-1,
.color-accent-2 {
  --color-link: var(--color-foreground);
  --alpha-link: 0.7;
}

:root,
.color-background-1 {
  /*   --color-link: var(--color-base-outline-button-labels); */
  --color-link: var(--color-icon);
  --alpha-link: 0.85;
}

.color-background-2,
.color-inverse,
.color-accent-1,
.color-accent-2 {
  --color-link: var(--color-foreground);
  --alpha-link: 0.7;
}

:root,
.color-background-1 {
  --color-button: var(--color-base-solid-button-labels);
  --color-button-text: var(--color-base-background-1);
  --color-hover-button: var(--color-base-outline-button-labels);
  --color-button-hover-text: var(--color-base-accent-1);
  --alpha-button-background: 1;
  --alpha-button-border: 1;
}

.button--secondary {
  --color-button: var(--color-base-outline-button-labels);
  --color-button-text: var(--color-base-accent-1);
  --color-hover-button: var(--color-base-solid-button-labels);
  --color-button-hover-text: var(--color-base-background-1);
  /*   --color-button: var(--color-background); */
  --alpha-button-background: 1;
}

/* .color-background-2 .button--secondary,
.color-accent-1 .button--secondary,
.color-accent-2 .button--secondary {
  --color-button: var(--color-background);
  --color-button-text: var(--color-foreground);
}
 */
.color-inverse .button--secondary {
  --color-button: var(--color-background);
  --color-button-text: var(--color-foreground);
}

.button--tertiary {
  --color-button: var(--color-base-outline-button-labels);
  --color-button-text: var(--color-base-outline-button-labels);
  --alpha-button-background: 0;
  --alpha-button-border: 0.2;
}

.color-background-2 .button--tertiary,
.color-inverse .button--tertiary,
.color-accent-1 .button--tertiary,
.color-accent-2 .button--tertiary {
  --color-button: var(--color-foreground);
  --color-button-text: var(--color-foreground);
}

:root,
.color-background-1 {
  --color-badge-background: var(--color-background);
  --color-badge-border: var(--color-foreground);
  --alpha-badge-border: 0.1;
}

.color-background-2,
.color-inverse,
.color-accent-1,
.color-accent-2 {
  --color-badge-background: var(--color-background);
  --color-badge-border: var(--color-background);
  --alpha-badge-border: 1;
}

:root,
.color-background-1,
.color-background-2 {
  --color-card-hover: var(--color-base-text);
}

.color-inverse {
  --color-card-hover: var(--color-base-background-1);
}

.color-accent-1,
.color-accent-2 {
  --color-card-hover: var(--color-base-solid-button-labels);
}

:root,
.color-icon-text {
  --color-icon: rgb(var(--color-base-text));
}

.color-icon-accent-1 {
  --color-icon: rgb(var(--color-base-accent-1));
}

.color-icon-accent-2 {
  --color-icon: rgb(var(--color-base-accent-2));
}

.color-icon-outline-button {
  --color-icon: rgb(var(--color-base-outline-button-labels));
}

.contains-card,
.card {
  --border-radius: var(--card-corner-radius);
  --border-width: var(--card-border-width);
  --border-opacity: var(--card-border-opacity);
  --shadow-horizontal-offset: var(--card-shadow-horizontal-offset);
  --shadow-vertical-offset: var(--card-shadow-vertical-offset);
  --shadow-blur-radius: var(--card-shadow-blur-radius);
  --shadow-opacity: var(--card-shadow-opacity);
}

.contains-content-container,
.content-container {
  --border-radius: var(--text-boxes-radius);
  --border-width: var(--text-boxes-border-width);
  --border-opacity: var(--text-boxes-border-opacity);
  --shadow-horizontal-offset: var(--text-boxes-shadow-horizontal-offset);
  --shadow-vertical-offset: var(--text-boxes-shadow-vertical-offset);
  --shadow-blur-radius: var(--text-boxes-shadow-blur-radius);
  --shadow-opacity: var(--text-boxes-shadow-opacity);
}

.contains-media,
.global-media-settings {
  --border-radius: var(--media-radius);
  --border-width: var(--media-border-width);
  --border-opacity: var(--media-border-opacity);
  --shadow-horizontal-offset: var(--media-shadow-horizontal-offset);
  --shadow-vertical-offset: var(--media-shadow-vertical-offset);
  --shadow-blur-radius: var(--media-shadow-blur-radius);
  --shadow-opacity: var(--media-shadow-opacity);
}
.close_icon_button {
  color: var(--color-icon);
  transition: all var(--duration-default) linear;
}
.close_icon_button:hover {
  color: rgb(var(--color-base-outline-button-labels));
}

html {
  box-sizing: border-box;
  font-size: calc(var(--font-body-scale) * 62.5%);
  height: 100%;
}

body {
  display: grid;
  grid-template-rows: auto auto 1fr auto;
  grid-template-columns: 100%;
  min-height: 100%;
  margin: 0;
  font-size: 1.5rem;
  line-height: calc(1 + 0.8 / var(--font-body-scale));
  font-family: var(--font-body-family);
  font-style: var(--font-body-style);
  font-weight: var(--font-body-weight);
}

@media screen and (min-width: 750px) {
  body {
    font-size: 1.6rem;
  }
}
.dt-sc-column {
  display: grid;
  width: 100%;
  row-gap: var(--grid-desktop-horizontal-spacing);
  column-gap: var(--grid-desktop-vertical-spacing);
  margin-bottom: 30px;
  margin-left: 0;
  padding: 0;
  grid-auto-flow: row dense;
}
.dt-sc-column.two-column {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
}
.dt-sc-column.three-column {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
}
.dt-sc-column.four-column {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
}
.dt-sc-column.five-column {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
}
.dt-sc-column.six-column {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
}
.dt-sc-column.seven-column {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
}
.dt-sc-column.eight-column {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
}
.dt-sc-column.nine-column {
  display: grid;
  grid-template-columns: repeat(9, 1fr);
}
.dt-sc-column.ten-column {
  display: grid;
  grid-template-columns: repeat(10, 1fr);
}
.dt-sc-column.eleven-column {
  display: grid;
  grid-template-columns: repeat(11, 1fr);
}
.dt-sc-column.twelve-column {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
}
.two-third-one-third {
  grid-template-columns: 2fr 1fr;
}
.three-fourth-one-fourth {
  grid-template-columns: 3fr 1fr;
}
.four-fifth-one-fifth {
  grid-template-columns: 4fr 1fr;
}
.one-third-two-third {
  grid-template-columns: 1fr 2fr;
}
.one-fourth-three-fourth {
  grid-template-columns: 1fr 3fr;
}
.one-fifth-four-fifth {
  grid-template-columns: 1fr 4fr;
}
.double-quarter-half {
  grid-template-columns: 1fr 1fr 2fr;
}
.double-quarter-one-half {
  grid-template-columns: 1fr 1fr 2fr;
}

.no-js .sub-menu-block {
  display: none;
}
.video-popup {
  display: none;
}
/* base */
header .header-contact svg.icon {
  width: 20px;
  height: 20px;
  font-size: 20px;
  top: 4px;
  position: relative;
  margin-right: 5px;
}

.page-width {
  max-width: calc(var(--page-width) + 8rem);
  margin: 0 auto;
  padding: 0 1.5rem;
}

.page-width-desktop {
  padding: 0;
  margin: 0 auto;
}

@media screen and (min-width: 750px) {
  .page-width {
    padding: 0 4rem;
  }

  .page-width--narrow {
    padding: 0 4rem;
  }

  .page-width-desktop {
    padding: 0;
  }

  .page-width-tablet {
    padding: 0 4rem;
  }
}

@media screen and (min-width: 990px) {
  .page-width--narrow {
    padding: 0;
  }

  .page-width-desktop {
    max-width: calc(var(--page-width) + 8rem);
    padding: 0 5rem;
  }
}
.page-full-width {
  max-width: 100%;
  width: 100%;
}
.page-full-width.page-full-width_spacing .row {
  margin: 0 var(--page-full-width-spacing);
}
@media screen and (max-width: 990px) {
  .page-full-width.page-full-width_spacing .row {
    margin: 0 calc(var(--page-full-width-spacing) / 2);
  }
}
@media screen and (max-width: 749px) {
  .page-full-width.page-full-width_spacing .row {
    margin: 0 1.5rem;
  }
}
@media screen and (max-width: 1240px) {
  .page-width {max-width: calc(var(--page-width-laptop) + 8rem);}
}
@media screen and (max-width: 1000px) {
  .page-width {max-width:calc(var(--page-width-tab) + 8rem);}
}
@media screen and (max-width: 750px) {
  .page-width {max-width:100%;}
}

.isolate {
  position: relative;
  z-index: 1;
  /*   float:left;
  width:100%; */
}

.section + .section {
  margin-top: var(--spacing-sections-mobile);
}

@media screen and (min-width: 750px) {
  .section + .section {
    margin-top: var(--spacing-sections-desktop);
  }
}

.element-margin-top {
  margin-top: 5rem;
}

@media screen and (min-width: 750px) {
  .element-margin {
    margin-top: calc(5rem + var(--page-width-margin));
  }
}

body,
.color-background-1,
.color-background-2,
.color-inverse,
.color-accent-1,
.color-accent-2 {
  color: rgba(var(--color-foreground), 1);
  background-color: rgb(var(--color-background));
}

.background-secondary {
  background-color: rgba(var(--color-foreground), 0.04);
}

.grid-auto-flow {
  display: grid;
  grid-auto-flow: column;
}

.page-margin,
.shopify-challenge__container {
  margin: 11rem auto;
}
.shopify-challenge__container .g-recaptcha {
  margin: 5rem 0;
}

.rte-width {
  max-width: 82rem;
  margin: 0 auto 2rem;
}

.list-unstyled {
  margin: 0;
  padding: 0;
  list-style: none;
}

.hidden {
  display: none !important;
}

.visually-hidden {
  position: absolute !important;
  overflow: hidden;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  border: 0;
  clip: rect(0 0 0 0);
  word-wrap: normal !important;
}

.visually-hidden--inline {
  margin: 0;
  height: 1em;
}

.overflow-hidden {
  overflow: hidden;
}

.skip-to-content-link:focus {
  z-index: 9999;
  position: inherit;
  overflow: auto;
  width: auto;
  height: auto;
  clip: auto;
}
/* Swap Splide arrows with gap */
#shopify-section-{{ section.id }} .splide__arrow--prev {
  right: 10px !important;  /* push further left */
  left: auto !important;
}

#shopify-section-{{ section.id }} .splide__arrow--next {
  left: 0px !important;   /* push further right */
  right: auto !important;
}



.full-width-link {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2;
}

::selection {
  background-color: rgba(var(--color-foreground), 0.2);
}

.text-body {
  font-size: 1.6rem;
  /*   letter-spacing: 0.06rem; */
  line-height: calc(1 + 0.8 / var(--font-body-scale));
  font-family: var(--font-body-family);
  font-style: var(--font-body-style);
  font-weight: var(--font-body-weight);
}

h1,
h2,
h3,
h4,
h5,
.h0,
.h1,
.h2,
.h3,
.h4,
.h5 {
  font-family: var(--font-heading-family);
  font-style: var(--font-heading-style);
  font-weight: var(--font-heading-weight);
  /*   letter-spacing: calc(var(--font-heading-scale) * 0.06rem); */
  color: var(--color-base-background-2);
  line-height: calc(1 + 0.3 / max(1, var(--font-heading-scale)));
  word-break: break-word;
}

.h0 {font-size: clamp(1.5rem, 0.7rem + 4vw, 5.5rem);}   
h1, .h1 {font-size: clamp(2rem, 1.7rem + 1.5vw, 3.5rem);}    
h2, .h2 {font-size: clamp(1.6rem, 1.52rem + 0.4vw, 2rem);}   
h3, .h3 {font-size: clamp(1.4rem, 1.32rem + 0.4vw, 1.8rem);}   
h4, .h4 {font-size: clamp(1.2rem, 1.12rem + 0.4vw, 1.6rem);} 
h5, .h5 {font-size: clamp(2rem, 1.92rem + 0.4vw, 2.4rem);}    
h6, .h6 {font-size: clamp(2.1rem, 1.96rem + 0.7vw, 2.8rem);} 

blockquote {
  font-style: italic;
  color:  var(--gradient-base-background-1);
  border-left: 0.2rem solid rgba(var(--color-foreground), 0.2);
  padding-left: 1rem;
}

/* @media screen and (min-width: 750px) {
  blockquote {
    padding-left: 1.5rem;
  }
} */

.caption {
  font-size: 1.4rem;
  letter-spacing: 0.07rem;
  line-height: calc(1 + 0.7 / var(--font-body-scale));
}

/* @media screen and (min-width: 750px) {
  .caption {
    font-size: 1.8rem;
  }
} */

.caption-with-letter-spacing {
  font-size: 1rem;
  letter-spacing: 0.13rem;
  line-height: calc(1 + 0.2 / var(--font-body-scale));
  text-transform: uppercase;
  font-weight: 700;
}

.caption-with-letter-spacing--medium {
  font-size: 1.8rem;
  letter-spacing: 1rem;
  color: var(--gradient-base-accent-3);
  font-weight: 600;
  font-family: var(--font-body-family);
}

.caption-with-letter-spacing--large {
  font-size: 1.4rem;
  letter-spacing: 0.18rem;
}

.caption-large,
.customer .field input,
.customer select,
.field__input,
.form__label,
.select__select {
  font-size: 1.6rem;
  line-height: calc(1 + 0.5 / var(--font-body-scale));
  letter-spacing: 0.04rem;
  font-weight: 400;
}

.color-foreground {
  color: rgb(var(--color-foreground));
}

table:not([class]) {
  table-layout: fixed;
  border-collapse: collapse;
  font-size: 1.4rem;
  border-style: hidden;
  box-shadow: 0 0 0 0.1rem rgba(var(--color-foreground), 0.2);
  /* draws the table border  */
}

table:not([class]) td,
table:not([class]) th {
  padding: 1em;
  border: 0.1rem solid rgba(var(--color-foreground), 0.2);
}

.hidden {
  display: none !important;
}

@media screen and (max-width: 749px) {
  .small-hide {
    display: none !important;
  }
}

@media screen and (min-width: 750px) and (max-width: 989px) {
  .medium-hide {
    display: none !important;
  }
}

@media screen and (min-width: 990px) {
  .large-up-hide {
    display: none !important;
  }
}
@media screen and (max-width: 480px) {
  .mobile-hide {
    display: none !important;
  }
}

.center {
  text-align: center;
}

.right {
  text-align: right;
}

.uppercase {
  text-transform: uppercase;
}

.light {
  opacity: 1;
}

/* a:empty, */
ul:empty,
dl:empty,
div:empty,
section:empty,
article:empty,
p:empty,
h1:empty,
h2:empty,
h3:empty,
h4:empty,
h5:empty,
h6:empty {
  display: none;
}

a {
  text-decoration: none;
  transition: all var(--duration-default) linear;
  color:rgb(var(--color-foreground));
}

.link,
.customer a {
  cursor: pointer;
  display: inline-block;
  border: none;
  box-shadow: none;
  text-decoration: none;
  /*   text-underline-offset: 0.3rem; */
  color: rgb(var(--color-base-solid-button-labels));
  background-color: transparent;
  font-size: 1.6rem;
  font-family: inherit;
  transition: all var(--duration-default) linear;
}

.link--text {
  color: rgb(var(--color-foreground));
}
.link:hover,
.customer a:hover {
  color: rgb(var(--color-base-outline-button-labels));
}



.link-with-icon {
  display: inline-flex;
  font-size: 1.4rem;
  font-weight: 600;
  letter-spacing: 0.1rem;
  /*   text-decoration: none; */
  margin-bottom: 4.5rem;
  white-space: nowrap;
}

.link-with-icon .icon {
  width: 1.5rem;
  margin-left: 1rem;
}

a:not([href]) {
  cursor: not-allowed;
}

.circle-divider::after {
  content: "\2022";
  margin: 0 1.3rem 0;
}

.circle-divider:last-of-type::after {
  display: none;
}

hr {
  border: none;
  height: 0.1rem;
  background-color: rgba(var(--color-foreground), 0.2);
  display: block;
  margin: 5rem 0;
}

@media screen and (min-width: 750px) {
  hr {
    margin: 7rem 0;
  }
}

.full-unstyled-link {
  /*   text-decoration: none; */
  color: currentColor;
  /*   display: block; */
  display: inline-block;
}

.placeholder {
  background-color: rgba(var(--color-foreground), 0.04);
  color: rgba(var(--color-foreground), 0.55);
  fill: rgba(var(--color-foreground), 0.55);
}

details > * {
  box-sizing: border-box;
}

.break {
  word-break: break-word;
}

.visibility-hidden {
  visibility: hidden;
}

@media (prefers-reduced-motion) {
  .motion-reduce {
    transition: none !important;
    animation: none !important;
  }
}

:root {
  --duration-short: 100ms;
  --duration-default: 300ms;
  --duration-long: 500ms;
}

.underlined-link,
.customer a {
  color: rgba(var(--color-link), var(--alpha-link));
  /*   text-underline-offset: 0.3rem;
  text-decoration-thickness: 0.1rem;
  transition: text-decoration-thickness ease 100ms; */
}

.underlined-link:hover,
.customer a:hover {
  color:rgb(var(--color-base-outline-button-labels));
  /*   text-decoration-thickness: 0.2rem; */
}

.icon-arrow {
  width: 1.5rem;
}

h3 .icon-arrow,
.h3 .icon-arrow {
  width: calc(var(--font-heading-scale) * 1.5rem);
}

/* arrow animation */
.animate-arrow .icon-arrow path {
  transform: translateX(-0.25rem);
  transition: transform var(--duration-short) ease;
}

.animate-arrow:hover .icon-arrow path {
  transform: translateX(-0.05rem);
}

/* base-details-summary */
summary {
  cursor: pointer;
  list-style: none;
  position: relative;
}

summary .icon-caret {
  position: absolute;
  height: 0.6rem;
  right: 1.5rem;
  top: calc(50% - 0.2rem);
}

summary::-webkit-details-marker {
  display: none;
}

.disclosure-has-popup {
  position: relative;
}

.disclosure-has-popup[open] > summary::before {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2;
  display: block;
  cursor: default;
  content: " ";
  background: transparent;
}

.disclosure-has-popup > summary::before {
  display: none;
}

.disclosure-has-popup[open] > summary + * {
  z-index: 100;
}

/* Sidebar carousel */

.optional-sidebar {
  display: flex;
  flex-direction: column;
  width: var(--sidebar-width);
     /* margin-top: 35px;  */
}
.optional-sidebar #accordian li {
  line-height: normal;
}
.product-list-style .quick-add.button-quick-add {
  display: none;
}
.font-body-bold {
  font-weight: var(--font-body-weight-bold);
}
ul.product-list-style .card__content {
  padding: 0 15px;
}
ul.product-list-style .product-icons,
ul.product-list-style .card__content .variant-option-color,
ul.product-list-style .card__content .variant-option-size {
  display: none;
}
ul.product-list-style li:not(:last-child) {
  margin-bottom: 30px;
}
ul.product-list-style .card .card__inner .card__media {
  border-radius: 0;
}
ul.product-list-style .card {
  display: grid;
  grid-template-columns: 1.5fr 2.5fr;
  gap: 0;
  padding: 0;
}
.filter-panel-menu #accordian ul ul{padding-left:20px;}
.optional-sidebar .filter-panel-menu li a:hover {
  color: rgb(var(--color-base-outline-button-labels));
}

.facets-container .button-show-more{padding-bottom:0;}
.widget.product-sidebar-type-carousel .swiper-sidebar-arrows.swiper-arrows {
  position: absolute;top: 135px;
    bottom: unset;
    right: 0;
    left: 0;
    transform: translateY(-50%);
    z-index: 1;
    margin: auto;
}
.widget.product-sidebar-type-carousel #swiper-sidebar-carousel{position:relative;}
.widget.product-sidebar-type-carousel
  .swiper-sidebar-arrows.swiper-arrows
  .swiper-button-prev
  svg,
.widget.product-sidebar-type-carousel
  .swiper-sidebar-arrows.swiper-arrows
  .swiper-button-next
  svg {
  width: 6px;
  height: 12px;
  fill: currentColor;
}

.widget.product-sidebar-type-carousel
  .swiper-sidebar-arrows.swiper-arrows
  .swiper-button-next span,
.widget.product-sidebar-type-carousel
  .swiper-sidebar-arrows.swiper-arrows
  .swiper-button-prev span {display:flex;}

.widget.product-sidebar-type-carousel
  .swiper-sidebar-arrows.swiper-arrows
  .swiper-button-prev,
.widget.product-sidebar-type-carousel
  .swiper-sidebar-arrows.swiper-arrows
  .swiper-button-next {
  width: 30px;
  height: 30px;
  margin: 0;
  border-radius: 50%; transition: all 0.3s linear;
}

.widget.product-sidebar-type-collection .card__information .card__heading{font-size:1.8rem;}


/*Sidebar menu */
.sidebar .StickySidebar > div[class*="filter-panel"] ul,
.sidebar > div[class*="filter-panel"] ul,
.widget[class*="product-sidebar"] ul {
  margin: 0;
  padding: 0;
}
#accordian li {
  position: relative;
  cursor: pointer;
}
#accordian a {
  margin-right: 30px;
}


.filter-panel-menu ul li a,
.product-sidebar-type-menu li a {
  display: block;
  padding: 0;
  position: relative;
  color: var(--gradient-base-accent-1);

}
.filter-panel-menu ul li:not(:last-child) a,
.product-sidebar-type-menu li:not(:last-child) a {
  margin: 0 0 10px;
}
#main-collection-filters .filter-panel-menu {margin: 35px 0 35px;}
.filter-panel-menu ul li a:hover{color:rgba(var(--color-base-outline-button-labels));}
#accordian ul ul {
  display: none;
  padding-left: 30px;
}
ul ul {
  list-style-type: circle;
}
#accordian a:not(:only-child):after {
  content: "+"; /*     font-family: fontawesome; */
  position: absolute;
  left: 100%;
  width: 30px;
  text-align: center;
  font-size: 22px;
  pointer-events: none;
  top: 50%;
  transform: translateY(-50%);
}
#accordian .active > a:not(:only-child):after {
  pointer-events: none;
  content: "-";
}
.filter-panel-menu ul ul li {
  font-size: 1.4rem;
  list-style: disc;
}
/*sidebar*/
.optional-sidebar .sidebar_title,
.filter-panel-menu .sidebar_title,
.blog-sidebar .sidebar_title{
  font-size: 2rem;
  margin: 0px 0 40px;
  font-weight: 400;position:relative;
}
.optional-sidebar .sidebar_title:after,
.filter-panel-menu .sidebar_title:after,
.blog-sidebar .sidebar_title:after{content:'';width:100%;height:2px;background:rgba(var(--color-foreground),.08);position:absolute;bottom:-8px;left:0;}
.optional-sidebar > * {
  margin-bottom: 35px;
}
.optional-sidebar ul,
.filter-panel ul{
  list-style: none;
  padding: 0;
  margin: 0;
}
.optional-sidebar ul.product-list-style .card {
  gap: 20px;
}
.optional-sidebar ul.product-list-style .card, 
.optional-sidebar ul.product-list-style .card__information, 
.optional-sidebar ul.product-list-style .card__information .price--on-sale .price__sale {
  text-align: left;justify-content: flex-start;
}
#FacetsWrapperDesktop .field__input {
  padding: 1rem;
  font-size: 1.4rem;
}
#FacetsWrapperDesktop .field__label {
  top: calc(0.8rem + var(--inputs-border-width));
}
.optional-sidebar span.close-sidebar svg,
.blog-sidebar span.close-sidebar svg{
  width: 1.5rem;height:1.5rem;fill:currentcolor;
}
.optional-sidebar span.close-sidebar,
.blog-sidebar span.close-sidebar{
  position: relative;
  width: 1.5rem;
  height: 1.5rem;
  margin:0;
  margin-bottom:10px!important; 
  z-index: 1;
  display: flex;
  cursor: pointer;
  transition: all 0.3s linear;
  margin-left: auto;
}
.optional-sidebar span.close-sidebar:hover,
.blog-sidebar span.close-sidebar:hover{color:rgba(var(--color-base-outline-button-labels));}

@media screen and (max-width: 991px) {
  .optional-sidebar {
    /*     display: flex;
    flex-direction: column; */
    width: var(--sidebar-width);
  }
}
@media screen and (min-width: 990px) {
  button.toggleFilter {
    display: none;
  }
  .optional-sidebar span.close-sidebar {
    display: none;
  }
}

@media screen and (max-width: 989px) {
  /*   .optional-sidebar {
    display: none;
  } */
  .facets-vertical.sidebar-left.open .optional-sidebar {
    left: 0;
  }
  .main-product-template .facets-vertical .optional-sidebar,
  .main-product-template .facets-vertical.sidebar-right .optional-sidebar {
    position: fixed !important;
    overflow-y: scroll !important;
    padding: 15px;
    max-width: 80%;
    top: 0;
    left: calc(var(--sidebar-width) * -1);
    height: 100%;
    background: var(--gradient-background);
    margin: 0;
    z-index: 17;
    transition: all 0.5s linear;
  }
  .main-product-template .facets-vertical.sidebar-right.open .optional-sidebar,
  .main-product-template .facets-vertical.sidebar-right.open .optional-sidebar {
    left: 0;
  }
  .main-product-template .facets-vertical.sidebar-right.open:after,
  .main-product-template .facets-vertical.sidebar-left.open:after {
    content: "";
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    z-index: 16;
    background-color: rgba(0, 0, 0, 0.7);
    transition:all 0.5s linear;
  }
  .widget.product-sidebar-type-carousel .swiper-sidebar-arrows.swiper-arrows {top:120px;}
}


@media screen and (min-width: 750px) {
  .disclosure-has-popup[open] > summary + * {
    z-index: 4;
  }

  .facets .disclosure-has-popup[open] > summary + * {
    z-index: 3;
  }
}

/* base-focus */
/*
  Focus ring - default (with offset)
*/
.facet-filters .facets__display {
  width: 214px;
}
.facet-filters .facet-filters__sort {
  
}
*:focus {
  outline: 0;
  box-shadow: none;
}

*:focus-visible {
  outline: 0.2rem solid rgba(var(--color-foreground), 0.5);
  outline-offset: 0.3rem;
  box-shadow: 0 0 0 0.3rem rgb(var(--color-background)),
    0 0 0.5rem 0.4rem rgba(var(--color-foreground), 0.3);
}
.facet-filters.sorting .facets__display {
  width: 100%;
  left: 0;
}
/* Fallback - for browsers that don't support :focus-visible, a fallback is set for :focus */
.focused,
.no-js *:focus {
  outline: 0.2rem solid rgba(var(--color-foreground), 0.5);
  outline-offset: 0.3rem;
  box-shadow: 0 0 0 0.3rem rgb(var(--color-background)),
    0 0 0.5rem 0.4rem rgba(var(--color-foreground), 0.3);
}

/* Negate the fallback side-effect for browsers that support :focus-visible */
.no-js *:focus:not(:focus-visible) {
  outline: 0;
  box-shadow: none;
}

/*
  Focus ring - inset
*/

.focus-inset:focus-visible {
  outline: 0.2rem solid rgba(var(--color-foreground), 0.5);
  outline-offset: -0.2rem;
  box-shadow: 0 0 0.2rem 0 rgba(var(--color-foreground), 0.3);
}

.focused.focus-inset,
.no-js .focus-inset:focus {
  outline: 0.2rem solid rgba(var(--color-foreground), 0.5);
  outline-offset: -0.2rem;
  box-shadow: 0 0 0.2rem 0 rgba(var(--color-foreground), 0.3);
}

.no-js .focus-inset:focus:not(:focus-visible) {
  outline: 0;
  box-shadow: none;
}

/*
  Focus ring - none
*/

/* Dangerous for a11y - Use with care */
.focus-none {
  box-shadow: none !important;
  outline: 0 !important;
}

.focus-offset:focus-visible {
  outline: 0.2rem solid rgba(var(--color-foreground), 0.5);
  outline-offset: 1rem;
  box-shadow: 0 0 0 1rem rgb(var(--color-background)),
    0 0 0.2rem 1.2rem rgba(var(--color-foreground), 0.3);
}

.focus-offset.focused,
.no-js .focus-offset:focus {
  outline: 0.2rem solid rgba(var(--color-foreground), 0.5);
  outline-offset: 1rem;
  box-shadow: 0 0 0 1rem rgb(var(--color-background)),
    0 0 0.2rem 1.2rem rgba(var(--color-foreground), 0.3);
}

.no-js .focus-offset:focus:not(:focus-visible) {
  outline: 0;
  box-shadow: none;
}

/* component-title */
.title,
.title-wrapper-with-link {
  margin: 3rem 0 2rem;
  text-transform: capitalize;
}

.title-wrapper-with-link .title {
  margin: 0;
  /* font-weight: 600; */
}

.title .link {
  font-size: inherit;
}

.title-wrapper {
  margin-bottom: 3rem;
}

.title-wrapper-with-link {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3rem;
  flex-wrap: wrap;
  text-transform: capitalize;
}
.title--primary {
  margin: 4rem 0;
}
.title-wrapper-with-link.content-align--left {
  align-items: flex-start;
  text-align: left;
}
.title-wrapper-with-link.content-align--center {
  align-items: center;
  text-align: center;
}
.title-wrapper--self-padded-tablet-down,
.title-wrapper--self-padded-mobile {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

@media screen and (min-width: 750px) {
  .title-wrapper--self-padded-mobile {
    padding-left: 0;
    padding-right: 0;
  }
}

@media screen and (min-width: 990px) {
  .title,
  .title-wrapper-with-link {
    margin: 5rem 0 0;
  }

  .title--primary {
    margin: 2rem 0;
  }

  .title-wrapper-with-link {
    align-items: center;
  }

  .title-wrapper-with-link .title {
    margin-bottom: 16px;
  }

  .title-wrapper--self-padded-tablet-down {
    padding-left: 0;
    padding-right: 0;
  }
}

.title-wrapper-with-link .link-with-icon {
  margin: 0;
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.title-wrapper-with-link .link-with-icon svg {
  width: 1.5rem;
}

/* .title-wrapper-with-link a {
  color: rgb(var(--color-link));
  margin-top: 0;
  flex-shrink: 0;
} */

.title-wrapper--no-top-margin {
  margin-top: 0;
}

 .title-wrapper--no-top-margin > .title,
.title-wrapper--no-top-margin > .sub-heading,
.title-wrapper--no-top-margin > .description {
  margin: 0;
} 
/* .title-wrapper-with-link > *:not(:last-child){ margin-bottom: 1rem;} */
.subtitle {
  font-size: 2.4rem;
  line-height: calc(1 + 0.8 / var(--font-body-scale));
  letter-spacing: 0.06rem;
  color: rgba(var(--color-foreground), 1);
}

.subtitle--small {
  font-size: 1.4rem;
  letter-spacing: 0.1rem;
}

.subtitle--medium {
  font-size: 1.8rem;
  letter-spacing: 0.08rem;
}

/* component-grid */
.grid {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 2rem;
  padding: 0;
  list-style: none;
  column-gap: var(--grid-mobile-horizontal-spacing);
  row-gap: var(--grid-mobile-vertical-spacing);
}

@media screen and (min-width: 750px) {
  .grid {
    column-gap: var(--grid-desktop-horizontal-spacing);
    row-gap: var(--grid-desktop-vertical-spacing);
  }
}

.grid:last-child {
  margin-bottom: 0;
}

.grid__item {
  width: calc(25% - var(--grid-mobile-horizontal-spacing) * 3 / 4);
  max-width: calc(50% - var(--grid-mobile-horizontal-spacing) / 2);
  flex-grow: 1;
  flex-shrink: 0;
}

@media screen and (min-width: 750px) {
  .grid__item {
    width: calc(25% - var(--grid-desktop-horizontal-spacing) * 3 / 4);
    max-width: calc(50% - var(--grid-desktop-horizontal-spacing) / 2);
  }
}

.grid--gapless.grid {
  column-gap: 0;
  row-gap: 0;
}

@media screen and (max-width: 749px) {
  .grid__item.slider__slide--full-width {
    width: 100%;
    max-width: none;
  }
  .subtitle,
  .subtitle--medium,
  .subtitle--small {
    font-size: 1.8rem;
  }
}

.grid--1-col .grid__item {
  max-width: 100%;
  width: 100%;
}

.grid--3-col .grid__item {
  width: calc(33.33% - var(--grid-mobile-horizontal-spacing) * 2 / 3);
}

@media screen and (min-width: 750px) {
  .grid--3-col .grid__item {
    width: calc(33.33% - var(--grid-desktop-horizontal-spacing) * 2 / 3);
  }
}

.grid--2-col .grid__item {
    width: calc(50% - var(--grid-desktop-horizontal-spacing) / 2);
    max-width: calc(50% - var(--grid-desktop-horizontal-spacing) / 2);
}

@media screen and (min-width: 990px) {
  .grid--2-col .grid__item {
    width: calc(50% - var(--grid-desktop-horizontal-spacing) / 2);
  }

  .grid--4-col-tablet .grid__item {
    width: calc(25% - var(--grid-desktop-horizontal-spacing) * 3 / 4);
  }

  .grid--3-col-tablet .grid__item {
    width: calc(33.33% - var(--grid-desktop-horizontal-spacing) * 2 / 3);
  }

  .grid--2-col-tablet .grid__item {
    width: calc(50% - var(--grid-desktop-horizontal-spacing) / 2);
  }
  .theme__default-footer_style
    .footer__blocks-wrapper.grid--4-col-tablet
    .grid__item {
    width: 100%;
    text-align: center;
  }
}

@media screen and (max-width: 989px) {
  .grid--1-col-tablet-down .grid__item {
    width: 100%;
    max-width: 100%;
  }

  .slider--tablet.grid--peek {
    margin: 0;
    width: 100%;
  }

  .slider--tablet.grid--peek .grid__item {
    box-sizing: content-box;
    margin: 0;
  }
}

@media screen and (min-width: 1200px) {
  .grid--6-col-desktop .grid__item {
    width: calc(16.66% - var(--grid-desktop-horizontal-spacing) * 5 / 6);
    max-width: calc(16.66% - var(--grid-desktop-horizontal-spacing) * 5 / 6);
  }

  .grid--5-col-desktop .grid__item {
    width: calc(20% - var(--grid-desktop-horizontal-spacing) * 4 / 5);
    max-width: calc(20% - var(--grid-desktop-horizontal-spacing) * 4 / 5);
    .card--card.card--media>.card__content{padding:1rem;}
    .card-wrapper .card__inner .quick-add.button-quick-add {
    position: absolute;
    bottom: 0%;
    right: 15px;
    opacity: 1;
    z-index: 2;
    transition: all .3s linear;
    flex-direction: column;
    pointer-events: all;
    top: 40px;
    margin: 0;
}

.card-wrapper .quick-add__submit.button {
    margin-top: 10px;
    padding: 0;
    min-width: 40px;
    min-height: 30px;
}
.card-wrapper .quick-add__submit.button span svg{
    width:13px;
}

  }

  .grid--4-col-desktop .grid__item {
    width: calc(25% - var(--grid-desktop-horizontal-spacing) * 3 / 4);
    max-width: calc(25% - var(--grid-desktop-horizontal-spacing) * 3 / 4);
    .card-wrapper .card__inner .quick-add.button-quick-add {
    position: absolute;
    bottom: 0%;
    right: 32px;
    opacity: 1;
    z-index: 2;
    transition: all .3s linear;
    flex-direction: column;
    pointer-events: all;
    top: 55px;
    margin: 0;
}

.card-wrapper .quick-add__submit.button {
    margin-top: 10px;
    padding: 0;
    min-width: 40px;
    min-height: 30px;
}


.card-wrapper .quick-add__submit.button span svg{
    width:13px;
}
  }

  .grid--3-col-desktop .grid__item {
    width: calc(33.33% - var(--grid-desktop-horizontal-spacing) * 2 / 3);
    max-width: calc(33.33% - var(--grid-desktop-horizontal-spacing) * 2 / 3);
  }

  .grid--2-col-desktop .grid__item {
    width: calc(50% - var(--grid-desktop-horizontal-spacing) / 2);
    max-width: calc(50% - var(--grid-desktop-horizontal-spacing) / 2);
  }
}
.card-wrapper .quick-add__submit.button span{
  height:20px;
}

@media screen and (min-width: 1925px) {
  .grid--6-col-desktop .grid__item {
    width: calc(16.66% - var(--grid-desktop-horizontal-spacing) * 5 / 6);
    max-width: calc(16.66% - var(--grid-desktop-horizontal-spacing) * 5 / 6);
  }

  .grid--5-col-desktop .grid__item {
    width: calc(20% - var(--grid-desktop-horizontal-spacing) * 4 / 5);
    max-width: calc(20% - var(--grid-desktop-horizontal-spacing) * 4 / 5);
    .card--card.card--media>.card__content{padding:2rem;}
    .card-wrapper .card__inner .quick-add.button-quick-add {
    position: absolute;
    bottom: 0%;
    right: 15px;
    opacity: 1;
    z-index: 2;
    transition: all .3s linear;
    flex-direction: column;
    pointer-events: all;
    top: 44px;
    margin: 0;
}

.card-wrapper .quick-add__submit.button {
    margin-top: 10px;
    padding: 0;
    min-width: 40px;
    min-height: 30px;
}
.card-wrapper .quick-add__submit.button span svg{
    width:13px;
}

  }

  .grid--4-col-desktop .grid__item {
    width: calc(25% - var(--grid-desktop-horizontal-spacing) * 3 / 4);
    max-width: calc(25% - var(--grid-desktop-horizontal-spacing) * 3 / 4);
    .card-wrapper .card__inner .quick-add.button-quick-add {
    position: absolute;
    bottom: 0%;
    right: 25px;
    opacity: 1;
    z-index: 2;
    transition: all .3s linear;
    flex-direction: column;
    pointer-events: all;
    top: 53px;
    margin: 0;
}

.card-wrapper .quick-add__submit.button {
    margin-top: 10px;
    padding: 0;
    min-width: 50px;
    min-height: 30px;
}
.card-wrapper .quick-add__submit.button span svg{
    width:15px;
}
  }

  .grid--3-col-desktop .grid__item {
    width: calc(33.33% - var(--grid-desktop-horizontal-spacing) * 2 / 3);
    max-width: calc(33.33% - var(--grid-desktop-horizontal-spacing) * 2 / 3);
  }

  .grid--2-col-desktop .grid__item {
    width: calc(50% - var(--grid-desktop-horizontal-spacing) / 2);
    max-width: calc(50% - var(--grid-desktop-horizontal-spacing) / 2);
  }
}


@media screen and (max-width: 1200px) {
  .grid--6-col-desktop .grid__item {
    width: calc(25% - var(--grid-desktop-horizontal-spacing) * 4 / 5);
    max-width: calc(25% - var(--grid-desktop-horizontal-spacing) * 4 / 5);
  }

  .grid--5-col-desktop .grid__item {
     width: calc(33.33% - var(--grid-desktop-horizontal-spacing) * 2 / 3);
    max-width: calc(33.33% - var(--grid-desktop-horizontal-spacing) * 2 / 3);
  }

  .grid--4-col-desktop .grid__item {
    width: calc(33.33% - var(--grid-desktop-horizontal-spacing) * 2 / 3);
    max-width: calc(33.33% - var(--grid-desktop-horizontal-spacing) * 2 / 3);
  }

  .grid--3-col-desktop .grid__item {
    width: calc(33.33% - var(--grid-desktop-horizontal-spacing) * 2 / 3);
    max-width: calc(33.33% - var(--grid-desktop-horizontal-spacing) * 2 / 3);
  }

  .grid--2-col-desktop .grid__item {
    width: calc(50% - var(--grid-desktop-horizontal-spacing) / 2);
    max-width: calc(50% - var(--grid-desktop-horizontal-spacing) / 2);
  }
}
/* @media screen and (max-width: 990px) and (min-width: 750px) {
  .grid--6-col-desktop .grid__item {
    width: calc(33.33% - var(--grid-desktop-horizontal-spacing) / 3);
    max-width: calc(33.33% - var(--grid-desktop-horizontal-spacing)  / 3);
    }

  .grid--4-col-desktop .grid__item {
    width: calc(50% - var(--grid-mobile-horizontal-spacing) * 2 / 2);
    max-width: calc(50% - var(--grid-mobile-horizontal-spacing) * 2 / 2);
  }
  .grid--3-col-desktop .grid__item {
     width: calc(50% - var(--grid-desktop-horizontal-spacing) / 2);
    max-width: calc(50% - var(--grid-desktop-horizontal-spacing) / 2);
    }
  .grid--2-col-tablet-down .grid__item {
    width: calc(50% - var(--grid-mobile-horizontal-spacing) / 2);
    max-width: calc(50% - var(--grid-mobile-horizontal-spacing) / 2);
  }
  
} */
@media screen and (max-width: 749px) {
  .grid--6-col-desktop .grid__item {
    width: calc(50% - var(--grid-mobile-horizontal-spacing) / 2);
    max-width: calc(50% - var(--grid-mobile-horizontal-spacing) / 2);
    }
  .grid--5-col-desktop .grid__item {
     width: calc(50% - var(--grid-mobile-horizontal-spacing) / 2);
    max-width: calc(50% - var(--grid-mobile-horizontal-spacing) / 2);
    }
  .grid--4-col-desktop .grid__item {
    width: calc(50% - var(--grid-mobile-horizontal-spacing) * 2 / 2);
    max-width: calc(50% - var(--grid-mobile-horizontal-spacing) * 2 / 2);
  }
  .grid--3-col-desktop .grid__item {
     width: calc(50% - var(--grid-mobile-horizontal-spacing) / 2);
    max-width: calc(50% - var(--grid-mobile-horizontal-spacing) / 2);
    }
  .grid--2-col-tablet-down .grid__item {
    width: calc(50% - var(--grid-mobile-horizontal-spacing) / 2);
    max-width: calc(50% - var(--grid-mobile-horizontal-spacing) / 2);
  }
  
}
@media screen and (max-width: 480px) {
   .grid--6-col-desktop .grid__item {
      width: calc(50% - var(--grid-mobile-horizontal-spacing) * 2 / 2);
    max-width: calc(50% - var(--grid-mobile-horizontal-spacing) * 2 / 2);
    }
  .grid--4-col-desktop .grid__item {
      width: calc(50% - var(--grid-mobile-horizontal-spacing) / 2);
    max-width: calc(50% - var(--grid-mobile-horizontal-spacing)  / 2);
  }
  .grid--2-col-tablet-down .grid__item {
       width: calc(50% - var(--grid-mobile-horizontal-spacing) * 2 / 2);
    max-width: calc(50% - var(--grid-mobile-horizontal-spacing) * 2 / 2);
  }
}
@media screen and (min-width: 990px) {
  .grid--1-col-desktop {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .grid--1-col-desktop .grid__item {
    width: 100%;
    max-width: 100%;
  }
}

@media screen and (max-width: 749px) {
  .grid--peek.slider--mobile {
    margin: 0;
    width: 100%;
  }

  .grid--peek.slider--mobile .grid__item {
    box-sizing: content-box;
    margin: 0;
  }

  .grid--peek .grid__item {
    min-width: 47%;
  }

  .grid--peek.slider .grid__item:first-of-type {
    margin-left: 0;
  }

  .grid--peek.slider .grid__item:last-of-type {
    margin-right: 0;
  }
.collection .slider--tablet.product-grid{    scroll-padding-left: 0rem;}
  /*   .grid--2-col-tablet-down .grid__item {
    width: calc(50% - var(--grid-mobile-horizontal-spacing) / 2);
    max-width: calc(50% - var(--grid-mobile-horizontal-spacing) / 2);
  } */
  .slider--tablet.grid--peek.grid--2-col-tablet-down .grid__item,
  .grid--peek .grid__item {
/*     width: calc(50% - var(--grid-mobile-horizontal-spacing) - 0rem); */
    width: calc(50% - var(--grid-mobile-horizontal-spacing) / 2);
    max-width: calc(50% - var(--grid-mobile-horizontal-spacing) / 2);
  }

  .slider--tablet.grid--peek.grid--1-col-tablet-down .grid__item,
  .slider--mobile.grid--peek.grid--1-col-tablet-down .grid__item
  /*,.grid--1-col-tablet-down .grid__item*/ {
    /*     width: calc(100% - var(--grid-mobile-horizontal-spacing) - 3rem); */
    width: 100%;
    max-width: 100%;
  }
}
/* @media screen and (max-width: 480px) {
  .grid--2-col-tablet-down .grid__item {
    width: calc(50% - var(--grid-mobile-horizontal-spacing) / 2);
    max-width: 50%;
  }
} */
@media screen and (min-width: 750px) and (max-width: 989px) {
  .slider--tablet.grid--peek .grid__item {
    width: calc(25% - var(--grid-desktop-horizontal-spacing) - 3rem);
  }

  .slider--tablet.grid--peek.grid--3-col-tablet .grid__item {
    width: calc(33.33% - var(--grid-desktop-horizontal-spacing) - 3rem);
  }

  .slider--tablet.grid--peek.grid--2-col-tablet .grid__item,
  .slider--tablet.grid--peek.grid--2-col-tablet-down .grid__item {
    width: calc(50% - var(--grid-desktop-horizontal-spacing));
    max-width: calc(50% - var(--grid-desktop-horizontal-spacing));
  }

  .slider--tablet.grid--peek .grid__item:first-of-type {
    margin-left: 1.5rem;
  }

  .slider--tablet.grid--peek .grid__item:last-of-type {
    margin-right: 1.5rem;
  }

  .grid--2-col-tablet-down .grid__item {
    width: calc(50% - var(--grid-desktop-horizontal-spacing) / 2);
    max-width: calc(50% - var(--grid-desktop-horizontal-spacing) / 2);
  }

  .grid--1-col-tablet-down.grid--peek .grid__item {
    /*     width: calc(50% - var(--grid-desktop-horizontal-spacing) - 3rem); */
    width: calc(50% - var(--grid-desktop-horizontal-spacing) * 3 / 4);
    max-width: calc(50% - var(--grid-desktop-horizontal-spacing) * 3 / 4);
  }
}

/* component-media */
img {
  max-width: 100%;
  object-fit: cover;
  height: 100%;
}
.media {
  display: block;
  background-color: rgba(var(--color-foreground), 0.1);
  position: relative;
  overflow: hidden;
}

.media--transparent {
  background-color: transparent;
}

.media > *:not(.zoom):not(.deferred-media__poster-button),
.media model-viewer {
  display: block;
  max-width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
}

.media > img {
  object-fit: cover;
  object-position: center center;
  transition: opacity 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.media--square {
  padding-bottom: 100%;
}

.media--portrait {
  padding-bottom: 125%;
}

.media--landscape {
  padding-bottom: 66.6%;
}

.media--cropped {
  padding-bottom: 56%;
}

.media--16-9 {
  padding-bottom: 56.25%;
}

.media--circle {
  padding-bottom: 100%;
  border-radius: 50%;
}

.media.media--hover-effect > img + img {
  opacity: 0;
}

@media screen and (min-width: 990px) {
  .media--cropped {
    padding-bottom: 63%;
  }
}

deferred-media {
  display: block;
}

/* component-button */
/* Button - default */

.button,
.shopify-challenge__button,
.customer button,
button.shopify-payment-button__button--unbranded,
.shopify-payment-button [role="button"],
.cart__dynamic-checkout-buttons [role="button"],
.cart__dynamic-checkout-buttons iframe {
  --shadow-horizontal-offset: var(--buttons-shadow-horizontal-offset);
  --shadow-vertical-offset: var(--buttons-shadow-vertical-offset);
  --shadow-blur-radius: var(--buttons-shadow-blur-radius);
  --shadow-opacity: var(--buttons-shadow-opacity);
  --border-offset: var(
    --buttons-border-offset
  ); /* reduce radius edge artifacts */
  --border-opacity: calc(1 - var(--buttons-border-opacity));
  border-radius: var(--buttons-radius-outset);
  position: relative;
  transition: all var(--duration-default) linear;
}

.button,
.button-label,
.shopify-challenge__button,
.customer button,
button.shopify-payment-button__button--unbranded {
  min-width: calc(12rem + var(--buttons-border-width) * 2);
  min-height: calc(5rem + var(--buttons-border-width) * 2);
  font-size: clamp(1.2rem, 1.12rem + 0.4vw, 1.6rem);
  letter-spacing: 0;
  line-height: normal;
  text-transform: capitalize;
  padding: 0 5rem;
  border-radius:60px;
}

.shopify-payment-button__button--branded {
  z-index: auto;
}

.cart__dynamic-checkout-buttons iframe {
  box-shadow: var(--shadow-horizontal-offset) var(--shadow-vertical-offset)
    var(--shadow-blur-radius)
    rgba(var(--color-base-text), var(--shadow-opacity));
}

.button,
.shopify-challenge__button,
.customer button {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border: 0;
  cursor: pointer;
  text-decoration: none;
  color: rgb(var(--color-button-text));
  transition: box-shadow var(--duration-short) ease;
  -webkit-appearance: none;
  white-space: nowrap;
  appearance: none;
  background-color: rgba(var(--color-button), var(--alpha-button-background));
  transition: all 0.3s linear;
  font-family: var(--font-heading-family);
  font-weight: 400;
  transition: all var(--duration-default) linear;
  border-radius:60px;
  letter-spacing: 0.9px;
  min-height: 5rem;
}
.button:hover,
.shopify-challenge__button:hover,
.customer button:hover {
  background-color: rgba(
    var(--color-hover-button),
    var(--alpha-button-background)
  );
  color: rgba(var(--color-button-hover-text));
}
.button:before,
.shopify-challenge__button:before,
.customer button:before,
.shopify-payment-button__button--unbranded:before,
.shopify-payment-button [role="button"]:before,
.cart__dynamic-checkout-buttons [role="button"]:before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: -1;
  border-radius: var(--buttons-radius-outset);
  box-shadow: var(--shadow-horizontal-offset) var(--shadow-vertical-offset)
    var(--shadow-blur-radius) rgba(var(--color-shadow), var(--shadow-opacity));
}

.button:after,
.shopify-challenge__button:after,
.customer button:after,
.shopify-payment-button__button--unbranded:after {
  content: "";
  position: absolute;
  top: var(--buttons-border-width);
  right: var(--buttons-border-width);
  bottom: var(--buttons-border-width);
  left: var(--buttons-border-width);
  z-index: 1;
  border-radius: var(--buttons-radius);
  box-shadow: 0 0 0 calc(var(--buttons-border-width) + var(--border-offset))
      rgba(var(--color-button-text), var(--border-opacity)),
    0 0 0 var(--buttons-border-width)
      rgba(var(--color-button), var(--alpha-button-background));
  transition: box-shadow var(--duration-short) ease;
}

/* .button:not([disabled]):hover::after,
.shopify-challenge__button:hover::after,
.customer button:hover::after,
.shopify-payment-button__button--unbranded:hover::after {
  --border-offset: 1.3px;
  box-shadow: 0 0 0 calc(var(--buttons-border-width) + var(--border-offset))
      rgba(var(--color-button-text), var(--border-opacity)),
    0 0 0 calc(var(--buttons-border-width) + 0px)
      rgba(var(--color-button-text), var(--alpha-button-background));
} */

.button--secondary:after {
  --border-opacity: var(--buttons-border-opacity);
}

.button:focus-visible,
.button:focus,
.button.focused,
.shopify-payment-button__button--unbranded:focus-visible,
.shopify-payment-button [role="button"]:focus-visible,
.shopify-payment-button__button--unbranded:focus,
.shopify-payment-button [role="button"]:focus {
  outline: 0;
  box-shadow: 0 0 0 0.3rem rgb(var(--color-background)),
    0 0 0 0.5rem rgba(var(--color-foreground), 0.5),
    0 0 0.5rem 0.4rem rgba(var(--color-foreground), 0.3);
}

.button:focus:not(:focus-visible):not(.focused),
.shopify-payment-button__button--unbranded:focus:not(:focus-visible):not(
    .focused
  ),
.shopify-payment-button
  [role="button"]:focus:not(:focus-visible):not(.focused) {
  box-shadow: inherit;
}

.button::selection,
.shopify-challenge__button::selection,
.customer button::selection {
  background-color: rgba(var(--color-button-text), 0.3);
}

.button--tertiary {
  font-size: 1.2rem;
  padding: 1rem 1.5rem;
  min-width: calc(9rem + var(--buttons-border-width) * 2);
  min-height: calc(3.5rem + var(--buttons-border-width) * 2);
}

.button--small {
  padding: 1.2rem 2.6rem;
}

/* Button - other */

.button:disabled,
.button[aria-disabled="true"],
.button.disabled,
.customer button:disabled,
.customer button[aria-disabled="true"],
.customer button.disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.button--full-width {
  display: flex;
  width: 100%;
}

.button.loading {
  color: transparent;
  position: relative;
}

@media screen and (forced-colors: active) {
  .button.loading {
    color: rgb(var(--color-foreground));
  }
}

.button.loading > .loading-overlay__spinner {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  position: absolute;
  height: 100%;
  display: flex;
  background: transparent;
}

.button.loading > .loading-overlay__spinner .path {
  stroke: rgb(var(--color-button-text));
}

/* Button - social share */

.share-button {
  display: block;
  position: relative;
}

.share-button details {
  width: fit-content;
}

.share-button__button {
  font-size: 1.4rem;
  display: flex;
  align-items: center;
  color: rgb(var(--color-base-accent-1));
  margin-left: 0;
  padding-left: 0;
}

details[open] > .share-button__fallback {
  animation: animateMenuOpen var(--duration-default) ease;
}


.share-button__button,
.share-button__fallback button {
  cursor: pointer;
  background-color: transparent;
  border: none;
}

.share-button__button .icon-share {
 margin-right: 1rem;
}
.share-button__button {
  font-size: 1.6rem;
  font-weight: 700;
  transition:all 0.3s linear;
}
.share-button__button:hover {
    color: rgba(var(--color-base-outline-button-labels));
}
.share-button__fallback {
  display: flex;
  align-items: center;
  position: absolute;
  top: 3rem;
  left:auto;
  right:0;
  background:var(--gradient-background);
  z-index: 3;
  width: 100%;
  min-width: max-content;
  border-radius: var(--inputs-radius);
  border: 0;
}

.share-button__fallback:after {
  pointer-events: none;
  content: "";
  position: absolute;
  top: var(--inputs-border-width);
  right: var(--inputs-border-width);
  bottom: var(--inputs-border-width);
  left: var(--inputs-border-width);
  border: 0.1rem solid transparent;
  border-radius: var(--inputs-radius);
  box-shadow: 0 0 0 var(--inputs-border-width)
    rgba(var(--color-foreground), var(--inputs-border-opacity));
  transition: box-shadow var(--duration-short) ease;
  z-index: 1;
}

.share-button__fallback:before {
  background: rgb(var(--color-background));
  pointer-events: none;
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  border-radius: var(--inputs-radius-outset);
  box-shadow: var(--inputs-shadow-horizontal-offset)
    var(--inputs-shadow-vertical-offset) var(--inputs-shadow-blur-radius)
    rgba(var(--color-base-text), var(--inputs-shadow-opacity));
  z-index: -1;
}

.share-button__fallback button {
  width: 4.4rem;
  height: 4.4rem;
  padding: 0;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  right: var(--inputs-border-width);
}

.share-button__fallback button:hover {
  color: rgba(var(--color-foreground), 0.75);
}

.share-button__fallback button:hover svg {
  transform: scale(1.07);
}

.share-button__close:not(.hidden) + .share-button__copy {
  display: none;
}

.share-button__close,
.share-button__copy {
  background-color: transparent;
  color: rgb(var(--color-foreground));
}

.share-button__copy:focus-visible,
.share-button__close:focus-visible {
  background-color: rgb(var(--color-background));
  z-index: 2;
}

.share-button__copy:focus,
.share-button__close:focus {
  background-color: rgb(var(--color-background));
  z-index: 2;
}

.field:not(:focus-visible):not(.focused)
  + .share-button__copy:not(:focus-visible):not(.focused),
.field:not(:focus-visible):not(.focused)
  + .share-button__close:not(:focus-visible):not(.focused) {
  background-color: inherit;
}

.share-button__fallback .field:after,
.share-button__fallback .field:before {
  content: none;
}

.share-button__fallback .field {
  border-radius: 0;
  min-width: auto;
  min-height: auto;
  transition: none;
}

.share-button__fallback .field__input:focus,
.share-button__fallback .field__input:-webkit-autofill {
  outline: 0.2rem solid rgba(var(--color-foreground), 0.5);
  outline-offset: 0.1rem;
  box-shadow: 0 0 0 0.1rem rgb(var(--color-background)),
    0 0 0.5rem 0.4rem rgba(var(--color-foreground), 0.3);
}

.share-button__fallback .field__input {
  box-shadow: none;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  filter: none;
  min-width: auto;
  min-height: auto;
}

.share-button__fallback .field__input:hover {
  box-shadow: none;
}

.share-button__fallback .icon {
  width: 1.5rem;
  height: 1.5rem;
}

.share-button__message:not(:empty) {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  margin-top: 0;
  padding: 0.8rem 0 0.8rem 1.5rem;
  margin: var(--inputs-border-width);
}

.share-button__message:not(:empty):not(.hidden) ~ * {
  display: none;
}

/* component-form */
.field__input,
.select__select,
.customer .field input,
.customer select {
  font-family: inherit;
  -webkit-appearance: none;
  appearance: none;
  font-size: 1.6rem;
  width: 100%;
  box-sizing: border-box;
  transition: all var(--duration-default) linear;
  border-radius: var(--inputs-radius);
  height: 5rem;
  min-height: calc(var(--inputs-border-width) * 2);
  min-width: calc(7rem + (var(--inputs-border-width) * 2));
  position: relative;
  border: var(--inputs-border-width) solid rgb(var(--color-foreground),0.2);
  border-radius:30px;
}

/* .field:before,
.select:before,
.customer .field:before,
.customer select:before,
.localization-form__select:before {
  pointer-events: none;
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  border-radius: var(--inputs-radius-outset);
  box-shadow: var(--inputs-shadow-horizontal-offset)
    var(--inputs-shadow-vertical-offset) var(--inputs-shadow-blur-radius)
    rgba(var(--color-base-text), var(--inputs-shadow-opacity));
  z-index: -1;
}

.field:after,
.select:after,
.customer .field:after,
.customer select:after,
.localization-form__select:after {
  pointer-events: none;
  content: "";
  position: absolute;
  top: var(--inputs-border-width);
  right: var(--inputs-border-width);
  bottom: var(--inputs-border-width);
  left: var(--inputs-border-width);
  border: 0.1rem solid transparent;
  border-radius: var(--inputs-radius);
  box-shadow: 0 0 0 var(--inputs-border-width)
    rgba(var(--color-foreground), var(--inputs-border-opacity));
  transition: box-shadow var(--duration-short) ease;
  z-index: 1;
} */

.select__select {
  font-family: var(--font-body-family);
  font-style: var(--font-body-style);
  font-weight: var(--font-body-weight);
  font-size: 1.6rem;
  color: rgba(var(--color-foreground), 1);
}

.field:hover.field:after,
.select:hover.select:after,
.select__select:hover.select__select:after,
.customer .field:hover.field:after,
.customer select:hover.select:after,
.localization-form__select:hover.localization-form__select:after {
  box-shadow: none;
  outline: 0;
  border-radius: var(--inputs-radius);
}

.field__input:focus-visible,
.select__select:focus-visible,
.customer .field input:focus-visible,
.customer select:focus-visible,
.localization-form__select:focus-visible.localization-form__select:after {
  box-shadow: 0 0 0 calc(0.1rem + var(--inputs-border-width))
    rgba(var(--color-foreground));
  outline: 0;
}
/* .field.field--with-error input {
    border: 1px solid red;
}
.field.field--with-error input:focus{
  box-shadow: 0 0 0 calc(0.1rem + 0rem) red;
} */
.field__input:focus,
.select__select:focus,
.customer .field input:focus,
.customer select:focus,
.localization-form__select:focus.localization-form__select:after {
  box-shadow: none;
  outline: 0;

}

.localization-form__select:focus {
  outline: 0;
  box-shadow: none;
}

.text-area,
.select {
  display: flex;
  position: relative;
  width: 100%;
}

/* Select */

.select .icon-caret,
.customer select + svg {
  height: 0.6rem;
  pointer-events: none;
  position: absolute;
  top: calc(50% - 0.2rem);
  right: calc(var(--inputs-border-width) + 1.5rem);
}

.select__select,
.customer select {
  cursor: pointer;
  line-height: calc(1 + 0.6 / var(--font-body-scale));
  padding: 1rem;
  margin: var(--inputs-border-width);
  min-height: calc(var(--inputs-border-width) * 2);
}

/* Field */

.field {
  position: relative;
  width: 100%;
  display: flex;
  transition: box-shadow var(--duration-short) ease;
}

.customer .field {
  display: flex;
}

.field--with-error {
  flex-wrap: wrap;
}

.field__input,
.customer .field input {
  flex-grow: 1;
  text-align: left;
  padding: 0 3rem;
  margin: var(--inputs-border-width);
  transition: all var(--duration-default) linear;
}

.field__label,
.customer .field label {
  font-size: 1.6rem;
  left: calc(var(--inputs-border-width) + 3rem);
  top: calc(2rem + var(--inputs-border-width));
  margin-bottom: 0;
  pointer-events: none;
  position: absolute;
  transition: all var(--duration-default) linear;
    font-size var(--duration-short) ease;
  color: var(--gradient-base-accent-1);
  letter-spacing: 0.1rem;
  line-height: 1.5;
  display: none;
}

/* :focus ~ .field__label,
.field__input:not(:placeholder-shown) ~ .field__label,
.field__input:-webkit-autofill ~ .field__label,
.customer .field input:focus ~ label,
.customer .field input:not(:placeholder-shown) ~ label,
.customer .field input:-webkit-autofill ~ label {
  font-size: 1rem;
  top: calc(var(--inputs-border-width) + 0.5rem);
  left: calc(var(--inputs-border-width) + 2rem);
  letter-spacing: 0.04rem;
}

.field__input:focus,
.field__input:not(:placeholder-shown),
.field__input:-webkit-autofill,
.customer .field input:focus,
.customer .field input:not(:placeholder-shown),
.customer .field input:-webkit-autofill {
  padding: 3.3rem;
  margin: var(--inputs-border-width);
}

.field__input::-webkit-search-cancel-button,
.customer .field input::-webkit-search-cancel-button {
  display: none;
}

.field__input::placeholder,
.customer .field input::placeholder {
  opacity: 0;
} */

.field__button {
  align-items: center;
  background-color: var(--gradient-base-accent-1);
  border: 0;
  color: var(--gradient-base-background-1);
  cursor: pointer;
  display: flex;
  height: 4.4rem;
  justify-content: center;
  overflow: hidden;
  padding: 0;
  position: absolute;
  right: 0;
  top: 0;
  width: 4.4rem;
  transition: all 0.3s linear;
  font-weight:500;
  text-transform:uppercase;
  font-family: var(--font-heading-family);
}

.field__button > svg {
  height: 1.5rem;
  width: 1.5rem;
  fill:transparent;
}

/* .field__input:-webkit-autofill ~ .field__button,
.field__input:-webkit-autofill ~ .field__label,
.customer .field input:-webkit-autofill ~ label {
  color: rgb(0, 0, 0);
} */

/* Text area */

.text-area {
  font-family: var(--font-body-family);
  font-style: var(--font-body-style);
  font-weight: var(--font-body-weight);
  min-height: 10rem;
  resize: none;
}

input[type="checkbox"] {
  display: inline-block;
  width: auto;
  margin-right: 0.5rem;
}

/* Form global */

.form__label {
  display: block;
  margin-bottom: 0.6rem;
}

.form__message {
  align-items: center;
  display: flex;
  font-size: 1.4rem;
  line-height: normal;
  margin-top: 1rem;
}

.form__message--large {
  font-size: 1.6rem;
}

.customer .field .form__message {
  font-size: 1.4rem;
  text-align: left;
}

.form__message .icon,
.customer .form__message svg {
  flex-shrink: 0;
  height: 1.3rem;
  margin-right: 0.5rem;
  width: 1.3rem;
}

.form__message--large .icon,
.customer .form__message svg {
  height: 1.5rem;
  width: 1.5rem;
  margin-right: 1rem;
}

.customer .field .form__message svg {
  align-self: start;
}

.form-status {
  margin: 0;
  font-size: 1.6rem;
}
.form-status *:focus-visible,
.form__message *:focus-visible {
  outline: none;
  outline-offset: unset;
  box-shadow: none;
}
.form-status-list {
  padding: 0;
  margin: 2rem 0 4rem;
}

.form-status-list li {
  list-style-position: inside;
}

.form-status-list .link::first-letter {
  text-transform: capitalize;
}

/* component-quantity */
.quantity {
  color: rgba(var(--color-foreground));
  position: relative;
  width: calc(
    13.8rem / var(--font-body-scale) + var(--inputs-border-width) * 2
  );
  display: flex;
  border-radius: var(--inputs-radius);
  min-height: calc((var(--inputs-border-width) * 2) + 3.8rem);
}

.quantity:after {
  pointer-events: none;
  content: "";
  position: absolute;
  top: var(--inputs-border-width);
  right: var(--inputs-border-width);
  bottom: var(--inputs-border-width);
  left: var(--inputs-border-width);
  border: 0.1rem solid rgba(var(--color-foreground));
  border-radius: var(--inputs-radius);
  box-shadow: 0 0 0 var(--inputs-border-width)
    rgba(var(--color-foreground), var(--inputs-border-opacity));
  transition: box-shadow var(--duration-short) ease;
  z-index: 1;
}

.quantity:before {
  background: rgb(var(--color-background));
  pointer-events: none;
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  border-radius: var(--inputs-radius-outset);
  box-shadow: var(--inputs-shadow-horizontal-offset)
    var(--inputs-shadow-vertical-offset) var(--inputs-shadow-blur-radius)
    rgba(var(--color-base-text), var(--inputs-shadow-opacity));
  z-index: -1;
}

.quantity__input {
  color: currentColor;
  font-size: 1.4rem;
  font-weight: 500;
  opacity: 0.85;
  text-align: center;
  background-color: transparent;
  border: 0;
  padding: 0 0.2rem;
  width: 100%;
  flex-grow: 1;
  -webkit-appearance: none;
  appearance: none;
  outline:none;
  box-shadow:none;
}

.quantity__button {
  width: calc(4.5rem / var(--font-body-scale));
  flex-shrink: 0;
  font-size: 1.8rem;
  border: 0;
  background-color: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgb(var(--color-foreground));
  padding: 0;
  border-radius: 0 var(--buttons-radius) var(--buttons-radius) 0;
  transition: all 0.3s linear;
  /* height: 5rem; */
}
.quantity__button:first-child {
  border-radius: var(--buttons-radius) 0 0 var(--buttons-radius);
}
.quantity__button:hover {
  background: var(--gradient-base-accent-1);
  color: var(--gradient-base-background-1);
}
.quantity__button:first-child {
  margin-left: calc(var(--inputs-border-width));
}

.quantity__button:last-child {
  margin-right: calc(var(--inputs-border-width));
}

.quantity__button svg {
  width: 1.4rem;
  pointer-events: none;
}

.quantity__button:focus-visible,
.quantity__input:focus-visible {
  /*   background-color: rgb(var(--color-background)); */
  z-index: 2;
}

.quantity__button:focus,
.quantity__input:focus {
  /*   background-color: rgb(var(--color-background)); */
  z-index: 2;
}

.quantity__button:not(:focus-visible):not(.focused),
.quantity__input:not(:focus-visible):not(.focused) {
  box-shadow: inherit;
  /*   background-color: inherit; */
}

.quantity__input:-webkit-autofill,
.quantity__input:-webkit-autofill:hover,
.quantity__input:-webkit-autofill:active {
  box-shadow: 0 0 0 10rem rgb(var(--color-background)) inset !important;
  -webkit-box-shadow: 0 0 0 10rem rgb(var(--color-background)) inset !important;
}

.quantity__input::-webkit-outer-spin-button,
.quantity__input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.quantity__input[type="number"] {
  -moz-appearance: textfield;
}

/* component-modal */
.modal__toggle {
  list-style-type: none;
}

.no-js details[open] .modal__toggle {
  position: absolute;
  z-index: 5;
}

.modal__toggle-close {
  display: none;
}

.no-js details[open] svg.modal__toggle-close {
  display: flex;
  z-index: 1;
  height: 1.7rem;
  width: 1.7rem;
}

.modal__toggle-open {
  display: flex;
}

.no-js details[open] .modal__toggle-open {
  display: none;
}

.no-js .modal__close-button.link {
  display: none;
}

.modal__close-button.link {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0rem;
  height: 4.4rem;
  width: 4.4rem;
  background-color: transparent;
}

.modal__close-button .icon {
  width: 1.7rem;
  height: 1.7rem;
}

.modal__content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgb(var(--color-background));
  z-index: 4;
  display: flex;
  justify-content: center;
  align-items: center;
}

.media-modal {
  cursor: zoom-out;
}

.media-modal .deferred-media {
  cursor: initial;
}

/* component-cart-count-bubble */
.cart-count-bubble:empty {
  display: none;
}

.cart-count-bubble {
  position: absolute;
  background-color: rgb(var(--color-base-outline-button-labels));
  color: var(--gradient-background);
  height: 1.4rem;
  width: 1.4rem;
  border-radius: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1rem;
  top: 1rem;
    right: 1.5rem;
  line-height: 1.6rem;
  z-index: 1;
  font-weight: 500;
}
/* .grid-count-bubble {  position: relative;  top: 1px;  left: 1px;} */
/* section-announcement-bar */
#shopify-section-announcement-bar {
  z-index: 1; 
}

.announcement-bar {

  color: rgb(var(--color-foreground));
}

.announcement-bar__link {
  display: block;
  width: 100%;
}

.announcement-bar__link:hover {
  color: rgb(var(--color-foreground));

}

.announcement-bar__link .icon-arrow {
  display: inline-block;
  pointer-events: none;
  margin-left: 0.8rem;
  vertical-align: middle;
  margin-bottom: 0.2rem;
}

.announcement-bar__link .announcement-bar__message {
  padding: 0;
}

.announcement-bar__message {
  text-align: center;
  padding: 0.8rem 2rem;
  margin: 0;
  letter-spacing: 0;
  font-size: 1.4rem;
  font-weight: 500;
}

/* section-header */
#shopify-section-header {
  /*   z-index: 3; */
}

.shopify-section-header-sticky {
  position: sticky;
  top: 0;
  z-index: 3;
}

.shopify-section-header-hidden {
  transform: translateY(-100%);
}

.shopify-section-header-hidden.menu-open {
  transform: translateY(0);
}

#shopify-section-header.animate {
  transition: transform 0.15s ease-out;
}

/* Main Header Layout */
.header-wrapper {
  display: block;
  position: relative;
  /*   background-color: rgb(var(--color-background)); */
}

.header-wrapper--border-bottom {
  border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.08);
}
.index-header {
  position: absolute;
  z-index: 2;
  width: 100%;
  left: 0;
  right: 0;
}
.header .header-row {
  display: grid;
  grid-template-areas: "left-icon heading icons";
  row-gap: 10px;
  grid-template-columns: auto 1.3fr 1fr;
  align-items: center;
  left: 0;
  right: 0;
  column-gap: 15px;
}
.header .localization-form:only-child {
  margin: 0;
}
.header .localization-form:only-child .button,
.header .localization-form:only-child .localization-form__select {
  margin: 0;
}
.header .disclosure__button {
  padding-top: 0;
  padding-bottom: 0;
  height: 3rem;
}
.header .localization-form:only-child {
  padding: 0;
}
@media screen and (max-width: 1200px) {
  .index-header {
    position: relative;
  }
}

.header *[tabindex="-1"]:focus {
  outline: none;
}

.header__heading {
  margin: 0;
  line-height: 0;
}

.header > .header__heading-link {
  line-height: 0;
}

.header__heading,
.header__heading-link {
  grid-area: heading;
  justify-self: center;
  align-items: center;
  display: flex;
}

.header__heading-link {
  word-break: break-word;
}

.header__heading-link:hover .h2 {
  color: rgb(var(--color-foreground));
}

.header__heading-link .h2 {
  line-height: 1;
  color: rgba(var(--color-foreground), 0.75);
}

.header__heading-logo {
  height: auto;
  /*   width: 100%; */
}

@media screen and (max-width: 1279px) {
  .header__heading,
  .header__heading-link {
    text-align: left;
    justify-self: start;
  }
}


@media screen and (min-width: 1280px) {
 
  .header__heading,
  .header__heading-link {
    justify-self: start;
  }
}

/* Header icons */
.header__icons {
  grid-area: icons;
  justify-self: end;
  display: flex;
  align-items: center;
  gap:1.2rem;
}

.header__icon:not(.header__icon--summary),
.header__icon span {
  display: flex;
  align-items: center;
  justify-content: center;
}

.header__icon {
  color: rgb(var(--color-foreground));
}

.header__icon span {
  height: 100%;
  /*   top: -2px; */
  position: relative;
}

.header__icon::after {
  content: none;
}

/* .header__icon:hover .icon, */
/* .header__icon:hover svg, */

.modal__close-button:hover .icon,
.search__button:hover .icon-search,
.icon-search:hover svg, .header__icon--wishlist:hover svg, .header__icon--compare:hover svg
{
  color: rgb(var(--color-base-outline-button-labels));
}
.header__icon:hover{color: rgb(var(--color-base-outline-button-labels));}
.header__icon .icon,
.header__icon svg,
.modal__close-button .icon,
.search__button .icon-search,
.icon-search svg, .header__icon--wishlist svg, .header__icon--compare svg {
  transition: all var(--duration-default) linear;
}
.header__icon .icon {
  height: 2.3rem;
  width: 2.3rem;
  fill: none;
  vertical-align: middle;
  transition: all var(--duration-default) linear;
}

.header__icon,
.header__icon--cart .icon {
  height: 2.3rem;
  width: 2.3rem;
}

.header__icon--cart {
  position: relative;
  /*   margin-right: -1.2rem; */
}
.header__icon--wishlist,
.header__icon--compare {
  position: relative;
}
@media screen and (max-width: 750px) {
  .header {
    padding-top: 20px;
    padding-bottom: 20px;
  }
}
@media screen and (max-width: 989px) {
  menu-drawer ~ .header__icons .header__icon--account {
    display: none;
  }
}
.header__icon--menu[aria-expanded="true"]::before {
  content: "";
  top: 100%;
  left: 0;
  height: calc(
    var(--viewport-height, 100vh) - (var(--header-bottom-position, 100%))
  );
  width: 100%;
  display: block;
  position: fixed;
  background: rgba(var(--color-background), 0.5);
  top: 0;
  z-index: 2;
  height: 100vh;
}

/* Search */
menu-drawer + .header__search {
  display: none;
}

.header > .header__search {
  grid-area: left-icon;
  justify-self: start;
}

.header:not(.header--has-menu) * > .header__search {
  display: none;
}

.header__search {
  display: inline-flex;
  line-height: 0;
}

.header--top-center > .header__search {
  display: none;
}

.header--top-center * > .header__search {
  display: inline-flex;
}
.header .search-box .search-modal__content {
  padding: 0;
}

.no-js .predictive-search {
  display: none;
}

details[open] > .search-modal {
  opacity: 1;
  animation: animateMenuOpen var(--duration-default) ease;
}

details[open] .modal-overlay {
  display: block;
}

details[open] .modal-overlay::after {
  position: absolute;
  content: "";
  background-color: rgb(var(--color-foreground), 0.5);
  top: 100%;
  left: 0;
  right: 0;
  height: 100vh;
}

.no-js details[open] > .header__icon--search {
  top: 1rem;
  right: 0.5rem;
}

.search-modal {
  opacity: 0;
  border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.08);
  min-height: calc(
    100% + var(--inputs-margin-offset) + (2 * var(--inputs-border-width))
  );
  height: 100%;
}

.search-modal__content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 0 5rem 0 1rem;
  line-height: calc(1 + 0.8 / var(--font-body-scale));
  position: relative;
}

.search-modal__content-bottom {
  bottom: calc((var(--inputs-margin-offset) / 2));
}

.search-modal__content-top {
  top: calc((var(--inputs-margin-offset) / 2));
}

.search-modal__form {
  width: 100%;
}

.search-modal__close-button {
  position: absolute;
  right: 0.3rem;
}

@media screen and (min-width: 750px) {
  .search-modal__close-button {
    right: 1rem;
  }

  .search-modal__content {
    padding: 0 6rem;
  }
}

@media screen and (min-width: 990px) {
  .search-modal__form {
    max-width: 47.8rem;
  }

  .search-modal__close-button {
    position: initial;
    margin-left: 0.5rem;
  }
}

/* Header menu drawer */


details:not([open]) > .header__icon--menu .icon-close,
details[open] > .header__icon--menu .icon-hamburger {
  visibility: hidden;
  opacity: 0;
  transform: scale(0.8);
}

.js details[open]:not(.menu-opening) > .header__icon--menu .icon-close {
  visibility: hidden;
}

.js details[open]:not(.menu-opening) > .header__icon--menu .icon-hamburger {
  visibility: visible;
  opacity: 1;
  transform: scale(1.07);
}

.js details > .header__submenu {
  opacity: 0;
  transform: translateY(-1.5rem);
}

details[open] > .header__submenu {
  animation: animateMenuOpen var(--duration-default) ease;
  animation-fill-mode: forwards;
  z-index: 1;
}

@media (prefers-reduced-motion) {
  details[open] > .header__submenu {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Header menu */
.header__inline-menu {
  /*   margin-left: -1.2rem; */
  grid-area: navigation;
  display: none;
  width: 100%;
}
.header__inline-menu.secondary-menu {
  grid-area: secondary-menu;
}

.header--top-center .header__inline-menu,
.header--top-center .header__heading-link {
  margin-left: 0;
}

@media screen and (min-width: 990px) {
  .header--top-center .header__inline-menu {
    justify-self: center;
    /*     margin-top: 15px;
    margin-bottom: 15px; */
    width: 100%;
  }
  /*   .header--top-center .header__inline-menu #AccessibleNav{justify-content: center;} */

  .header--top-center .header__inline-menu > .list-menu--inline {
    justify-content: center;
  }

  .header--middle-left .header__inline-menu {
    margin-left: 0;
    padding: 0;
  }
}

.header__menu {
  padding: 0 1rem;
}

.header__menu-item {
  padding: 1.2rem;
  /*   text-decoration: none; */
  color: rgba(var(--color-foreground), 0.75);
}

.header__menu-item:hover {
  color: rgb(var(--color-foreground));
}

/* .header__menu-item span {
  transition: text-decoration var(--duration-short) ease;
} */

/* .header__menu-item:hover span {
  text-decoration: underline;
  text-underline-offset: 0.3rem;
} */

/* details[open] > .header__menu-item {
  text-decoration: underline;
} */

/* details[open]:hover > .header__menu-item {
  text-decoration-thickness: 0.2rem;
} */

details[open] > .header__menu-item .icon-caret {
  transform: rotate(180deg);
}

.header__active-menu-item {
  /*   transition: text-decoration-thickness var(--duration-short) ease; */
  color: rgb(var(--color-foreground));
  /*   text-decoration: underline;
  text-underline-offset: 0.3rem; */
}

/* .header__menu-item:hover .header__active-menu-item {
  text-decoration-thickness: 0.2rem;
} */

.header__submenu {
  transition: opacity var(--duration-default) ease,
    transform var(--duration-default) ease;
}

.global-settings-popup,
.header__submenu.global-settings-popup {
  border-radius: var(--popup-corner-radius);
  border-color: rgba(var(--color-foreground), var(--popup-border-opacity));
  border-style: solid;
  border-width: var(--popup-border-width);
  box-shadow: var(--popup-shadow-horizontal-offset)
    var(--popup-shadow-vertical-offset) var(--popup-shadow-blur-radius)
    rgba(var(--color-shadow), var(--popup-shadow-opacity));
}

.header__submenu.list-menu {
  padding: 2.4rem 0;
}

.header__submenu .header__submenu {
  background-color: rgba(var(--color-foreground), 0.03);
  padding: 0.5rem 0;
  margin: 0.5rem 0;
}

.header__submenu .header__menu-item:after {
  right: 2rem;
}

.header__submenu .header__menu-item {
  justify-content: space-between;
  padding: 0.8rem 2.4rem;
}

.header__submenu .header__submenu .header__menu-item {
  padding-left: 3.4rem;
}

.header__menu-item .icon-caret {
  right: 0.8rem;
}

.header__submenu .icon-caret {
  flex-shrink: 0;
  margin-left: 1rem;
  position: static;
}

header-menu > details,
details-disclosure > details {
  position: relative;
}

@keyframes animateMenuOpen {
  from {
    opacity: 0;
    transform: translateY(-1.5rem);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}



.overflow-hidden-mobile,
.overflow-hidden-tablet {
  overflow: hidden;
}

@media screen and (min-width: 750px) {
  .overflow-hidden-mobile {
    overflow: hidden;
  }
}

/* @media screen and (min-width: 1024px) {
  .overflow-hidden-tablet {
    overflow: auto;
  }
} */

.badge {
  border: 1px solid transparent;
  border-radius: var(--badge-corner-radius);
  display: inline-block;
  font-size: 1.2rem;
  letter-spacing: 0.1rem;
  line-height: 1;
  padding: 0.6rem 1.3rem;
  text-align: center;
  background-color: rgb(var(--color-foreground));
  border-color: rgb(var(--color-foreground), var(--alpha-badge-border));
  color: rgb(var(--color-badge-background));
  word-break: break-word;
}

/* .gradient {
  background: rgb(var(--color-background));
  background: var(--gradient-background);
  background-attachment: fixed;
} */

@media screen and (forced-colors: active) {
  .icon {
    color: CanvasText;
    fill: CanvasText !important;
  }

  .icon-close-small path {
    stroke: CanvasText;
  }
}

.ratio {
  display: flex;
  position: relative;
  align-items: stretch;
}

.ratio::before {
  content: "";
  width: 0;
  height: 0;
  padding-bottom: var(--ratio-percent);
}

.content-container {
  border-radius: var(--text-boxes-radius);
  border: var(--text-boxes-border-width) solid
    rgba(var(--color-foreground), var(--text-boxes-border-opacity));
  position: relative;
}

.content-container:after {
  content: "";
  position: absolute;
  top: calc(var(--text-boxes-border-width) * -1);
  right: calc(var(--text-boxes-border-width) * -1);
  bottom: calc(var(--text-boxes-border-width) * -1);
  left: calc(var(--text-boxes-border-width) * -1);
  border-radius: var(--text-boxes-radius);
  box-shadow: var(--text-boxes-shadow-horizontal-offset)
    var(--text-boxes-shadow-vertical-offset)
    var(--text-boxes-shadow-blur-radius)
    rgba(var(--color-shadow), var(--text-boxes-shadow-opacity));
  z-index: -1;
}

.content-container--full-width:after {
  left: 0;
  right: 0;
  border-radius: 0;
}

@media screen and (max-width: 749px) {
  .content-container--full-width-mobile {
    border-left: none;
    border-right: none;
    border-radius: 0;
  }
  .content-container--full-width-mobile:after {
    display: none;
  }
}

.global-media-settings {
  position: relative;
  border: var(--media-border-width) solid
    rgba(var(--color-foreground), var(--media-border-opacity));
  border-radius: var(--media-radius);
  overflow: visible !important;
  background-color: rgb(var(--color-background));
}

.global-media-settings:after {
  content: "";
  position: absolute;
  top: calc(var(--media-border-width) * -1);
  right: calc(var(--media-border-width) * -1);
  bottom: calc(var(--media-border-width) * -1);
  left: calc(var(--media-border-width) * -1);
  border-radius: var(--media-radius);
  box-shadow: var(--media-shadow-horizontal-offset)
    var(--media-shadow-vertical-offset) var(--media-shadow-blur-radius)
    rgba(var(--color-shadow), var(--media-shadow-opacity));
  z-index: -1;
}

.global-media-settings--no-shadow {
  overflow: hidden !important;
}

.global-media-settings--no-shadow:after {
  content: none;
}

.global-media-settings img,
.global-media-settings iframe,
.global-media-settings model-viewer,
.global-media-settings video {
  border-radius: calc(var(--media-radius) - var(--media-border-width));
}

.content-container--full-width,
.global-media-settings--full-width,
.global-media-settings--full-width img {
  border-radius: 0;
  border-left: none;
  border-right: none;
}

/* check for flexbox gap in older Safari versions */
@supports not (inset: 10px) {
  .grid {
    margin-left: calc(-1 * var(--grid-mobile-horizontal-spacing));
  }

  .grid__item {
    padding-left: var(--grid-mobile-horizontal-spacing);
    padding-bottom: var(--grid-mobile-vertical-spacing);
  }

  @media screen and (min-width: 750px) {
    .grid {
      margin-left: calc(-1 * var(--grid-desktop-horizontal-spacing));
    }

    .grid__item {
      padding-left: var(--grid-desktop-horizontal-spacing);
      padding-bottom: var(--grid-desktop-vertical-spacing);
    }
  }

  .grid--gapless .grid__item {
    padding-left: 0;
    padding-bottom: 0;
  }

  @media screen and (min-width: 749px) {
    .grid--peek .grid__item {
      padding-left: var(--grid-mobile-horizontal-spacing);
    }
  }

  .product-grid .grid__item {
    padding-bottom: var(--grid-mobile-vertical-spacing);
  }

  @media screen and (min-width: 750px) {
    .product-grid .grid__item {
      padding-bottom: var(--grid-desktop-vertical-spacing);
    }
  }
}
/* product page thumbnail slick slider height issue when click color variant */
.height_fix {
  height: 350px !important;
}

/* Advanced product tagging*/
.adv-product-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 20px;
  padding: 0;margin:0;
}
.adv-product-list li {
  padding: 5px;
  list-style: none;
}
.adv-product-list .products a img {
  aspect-ratio: 1/1;
}
.adv-product-list .carousel-block-list.active {
  box-shadow: 0px 0px 15px var(--gradient-base-accent-1);
  border: 1px solid var(--gradient-base-background-2);
}
.adv-product-list .products .product-detail {
  padding: 10px 0 0;
}
.adv-product-list .products .product-detail .grid-link__title {
  text-align: center;
}
.adv-product-list .products .product-detail .grid-link__title a {
  display: block;
  font-size: 12px;
}
.adv-product-list li a{display:block;line-height: normal;}
.adv-product-list li a span {
  color: rgba(var(--color-foreground), 1);
  transition: all var(--duration-default) linear;
  font-size: 1.4rem;
  font-weight: 500;line-height: 20px;
}
.adv-product-list li:hover a span {
  color: rgb(var(--color-base-outline-button-labels));
}

@media screen and  (max-width:480px){
  .adv-product-list {grid-template-columns: repeat(auto-fill, minmax(85px, 1fr));grid-gap:8px;}
  .adv-product-list li{padding:5px 0;}
}

/* Html section in product page */
.html-sections-product svg {
  width: 30px;
  height: 30px;
  vertical-align: middle;
  margin-right: 10px;
}
.flag-icon {
  margin-left: 15px;
}
.disclosure .localization-form__select:after {
  display: none;
}
.button-quick-add {
  display: flex;
  justify-content: space-around;
  grid-row-start: 4;
}
.spr-form-label {
  font-size: 18px !important;
}
.spr-form-title {
  font-size: 20px !important;
}

/* product page new tag section */
.ribbon {
  background-color: #DA3F3F;
  z-index: 999;
  width: max-content;
  height: 24px;
  text-align: center;
  top: 20px;
  left: 20px;
  line-height: 25px;
  padding:0 14px;
  position: absolute;
  cursor: pointer;
  user-select: none;
  color: var(--gradient-background);
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 2.6px;
}
/*@media screen and (max-width: 750px) {
  .ribbon {
    width: 100px;
    left: 0;
  }
}
 .ribbon:before {
  border-color: #f2b500 #f2b500 #f2b500 transparent;
  left: -25px;
  border-width: 17px;
}

.ribbon:before,
.ribbon:after {
  content: "";
  position: absolute;
  height: 0;
  width: 0;
  border-style: solid;
  top: 5px;
  z-index: -10;
}

.ribbon:after {
  border-color: #f2b500 transparent #f2b500 #f2b500;
  right: -25px;
  border-width: 17px;
} */
.optional-sidebar .product-deal-count {
  display: none;
}
.product__info-container .product-deal-count {
  display: none;
}
/* svg {
  width: 1.6rem;
  height: 1.6rem;
} */
body.overflow-hidden-mobile .shopify-section-header-sticky {
  opacity: 0;
  z-index: 1;
}

.swiper-pagination-bullet:before {
  will-change: transform;
}

.dt-sc-rating {
  width: 100%;
  /*   float: left; */
  margin: 0 0 0px 0;
  letter-spacing: 5px;
  display: flex;
  justify-content: flex-start;
}
.dt-sc-rating div {
  display: block !important;
}
.dt-sc-rating div[class*="star-rating"] {
  position: relative;
  width: 100%;
}
.dt-sc-rating div[class*="star-rating"]:after {
  content: "\f005""\f005""\f005""\f005""\f005";
  display: block;
  position: relative;
  font-family: FontAwesome;
  color: #fec42d;
  font-size: 10px;
  font-style: normal;
}
.dt-sc-rating div[class*="no-star-rating"]:after {
  content: "\f006""\f006""\f006""\f006""\f006";
}
.dt-sc-rating div[class*="half-star-rating"]:after {
  content: "\f123""\f006""\f006""\f006""\f006";
}
.dt-sc-rating div[class*="one-star-rating"]:after {
  content: "\f005""\f006""\f006""\f006""\f006";
}
.dt-sc-rating div[class*="one-half-star-rating"]:after {
  content: "\f005""\f123""\f006""\f006""\f006";
}
.dt-sc-rating div[class*="two-star-rating"]:after {
  content: "\f005""\f005""\f006""\f006""\f006";
}
.dt-sc-rating div[class*="two-half-star-rating"]:after {
  content: "\f005""\f005""\f123""\f006""\f006";
}
.dt-sc-rating div[class*="three-star-rating"]:after {
  content: "\f005""\f005""\f005""\f006""\f006";
}
.dt-sc-rating div[class*="three-half-star-rating"]:after {
  content: "\f005""\f005""\f005""\f123""\f006";
}
.dt-sc-rating div[class*="four-star-rating"]:after {
  content: "\f005""\f005""\f005""\f005""\f006";
}
.dt-sc-rating div[class*="four-half-star-rating"]:after {
  content: "\f005""\f005""\f005""\f005""\f123";
}

/* sidebar */
.no-sidebar .blog-content__area {
  width: 100%;
}
@media screen and (min-width: 990px) {
  .blog-sidebar.facets-vertical {
    width: var(--sidebar-width);
  }
}
@media screen and (min-width: 990px) {
aside.facets-wrapper.sidebar-sticky {
    width: var(--sidebar-width);
  }
}
/* @media screen and (min-width: 1024px) {
  .blog-content__area {
    width: calc(100% - Calc(380px + var(--grid-desktop-vertical-spacing)));
  }
} */
.blog-sidebar .filter-panel-menu ul li ul li:not(:last-child) a {
    margin: 0 0 5px;
    display: inline-block;
}
.preloader-overflow {
  overflow: hidden;
  height: 100%;
  width: 100%;
}
a#to-top.dt-sc-to-top {
  padding: 0;
  bottom: -50px;
  right: 30px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  text-align: center;
  line-height: 50px;
  z-index: 3;
  position: fixed;
  background: var(--gradient-base-accent-2);
  color: var(--gradient-base-background-1);
  overflow: hidden;
  -webkit-transition: all 1s ease;
  transition: all 1s ease;
}
.overflow-hidden-tablet a#to-top.dt-sc-to-top{z-index:2;}
a#to-top.dt-sc-to-top.show {
  bottom: 60px;
}
a#to-top.dt-sc-to-top:hover {
  background: var(--gradient-base-accent-1);
  color: var(--gradient-base-background-1);
}
a#to-top.dt-sc-to-top:hover svg {
  fill: var(--gradient-base-background-1);
}
a#to-top.dt-sc-to-top svg {
  width: 20px;
  height: 10px;
  transition: all var(--duration-default) linear;
  fill: var(--gradient-base-background-1);
  transform: rotate(270deg);
  -webkit-transform: rotate(270deg);
}
@media screen and (max-width: 750px) {
a#to-top.dt-sc-to-top.show {
  bottom: 70px;
}
}
.video_overlay {
  opacity: 1;
  visibility: hidden;
  display: none;
}
.video_overlay.open {
  display: flex !important;
  align-items: center;
  justify-content: center;
  opacity: 1;
  visibility: visible;
  background-color: rgba(0, 0, 0, 0.9);
  cursor: pointer;
  height: 100%;
  top: 0;
  left: 0;
  position: fixed;
  width: 100%;
  z-index: 98;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-transition: opacity 0.2s, visibility 0.2s;
  transition: opacity 0.2s, visibility 0.2s;
}
#video_player {
  max-width: 80%;
  max-height: 80%;
  height: 360px;
  width: 640px;
  margin: auto;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.6);
}
.video-section .video_modal {
  opacity: 0;
  visibility: hidden;
  position: fixed;
  z-index: 1;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  transition: var(--DTBaseTransition);
}
#product-with-hidden-thumbs .thumbnail-slider {
  display: none;
}
/* quick view */
.quick-add-modal__content-info .optional-sidebar,
.quick-add-modal__content-info .dt-sc-enquiry-form,
.quick-add-modal__content-info .sticky-bar-form,
.quick-add-modal__content-info .html-sections-product,
.quick-add-modal__content-info .fake_counter_p,
.quick-add-modal__content-info .product-additional__information,
.quick-add-modal__content-info .product-deal-count,
.quick-add-modal__content-info .advance-product-style,
.quick-add-modal__content-info button.toggleFilter {
  display: none !important;
}
.quick-add-modal__content-info .main-product_info {
  width: 100%;
}
@media screen and (max-width: 749px) {
.quick-add-modal__content-info .main-product_info {
  width: unset;
}
}
.mobile-menu-overlay {
  background-color: rgba(0, 0, 0, 0.5);
  cursor: pointer;
  height: 100%;
  top: 0;
  left: 0;
  opacity: 0;
  position: fixed;
  visibility: hidden;
  width: 100%;
  z-index: 98;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-transition: opacity 0.2s, visibility 0.2s;
  transition: opacity 0.2s, visibility 0.2s;
}
body.overflow-hidden-mobile .customer-purchased {
  display: none;
}
body.enquiry-overlay .shopify-section-header-sticky {
  opacity: 0;
}
svg.placeholder-svg {
  width: 100%;
  height: 100%;
  /* background:rgba(var(--color-base-accent-3), 0.5); */
}
.spr-summary-actions a.spr-summary-actions-newreview {
  font-weight: 600 !important;
  min-width: calc(12rem + var(--buttons-border-width) * 2);
  min-height: calc(4rem + var(--buttons-border-width) * 2);
  font-size: 1.4rem;
  letter-spacing: 0;
  line-height: normal;
  text-transform: uppercase;
  padding: 0 5rem;
  background-color: rgba(var(--color-button),var(--alpha-button-background));
  color: rgb(var(--color-button-text));
  display: flex;
  justify-content: center;
  align-items: center; white-space: nowrap;
}
.spr-summary-actions a.spr-summary-actions-newreview:hover {
    background-color: rgba(var(--color-hover-button),var(--alpha-button-background));
    color: rgba(var(--color-button-hover-text));
}
/*Review form*/
[class*=" spr-icon-"]:before {
    color: #f2b500;
}
.spr-review-content-body{    font-size: 16px !important; line-height:normal !important; }
.spr-review-footer.rte a{    font-size: 1.4rem !important; font-weight:600;border-bottom: 1px dotted;}
.spr-review-reportreview{    font-size: 1.4rem !important; font-weight:600;border-bottom: 1px dotted;}

/* body#compare main#MainContent {
  min-height: 700px;
  display: flex;
  align-items: center;
  flex-direction:column;
}
body#wishlist main#MainContent {
  min-height: 700px;
  display: flex;
  align-items: center;
}
@media screen and (max-width: 1920px) {
  body#compare main#MainContent {
    min-height: 100%;
    display: flex;
    align-items: center;
  }
  body#wishlist main#MainContent {
    min-height: unset;
    display: unset;
    align-items: unset;
  }
} */
.title-wrapper--no-top-margin>.sub-heading {
    font-size: 1.8rem;
    font-family: var(--font-heading-family);
    line-height:normal;
    font-weight:400;
}
/*animation*/
@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  70% {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translate3d(0, 100%, 0);
  }
  100% {
    opacity: 1;
    transform: none;
  }
}
@keyframes fadeInDown {
  0% {
    opacity: 0;
    transform: translate3d(0, -100%, 0);
  }
  100% {
    opacity: 1;
    transform: none;
  }
}
@keyframes fadeInLeft {
  0% {
    opacity: 0;
    transform: translateX(-50%);
  }
  100% {
    opacity: 1;
    transform: none;
  }
}
@keyframes fadeInRight {
  0% {
    opacity: 0;
    transform: translateX(50%);
  }
  100% {
    opacity: 1;
    transform: none;
  }
}
@keyframes zoomIn {
  0% {
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3);
  }
  50% {
    opacity: 1;
  }
}
@keyframes zoomOut {
  0% {
    opacity: 0;
    transform: scale3d(1.3, 1.3, 1.3);
  }
  50% {
    opacity: 1;
  }
}
:root {
    --anim-time: 1.5s;
}
/* .shopify-section.reveal>div:not(.header-wrapper){animation: fadeInRight var(--anim-time) ease both;} */
svg.placeholder_svg {
    background: rgba(var(--color-base-accent-3), 0.5); height:100%; width:100%;
}



/******************/
/*Product-details-description*/
/*****************/
.main-product-template .product-tab .dt-sc-column{grid-template-columns: repeat( auto-fit, minmax(250px, 1fr) ); margin-bottom:0;}
.main-product-template .product-tab li.tooltip img {  width: 50px;  margin-right: 0px;}
.main-product-template .product-tab li.tooltip {  position: relative;}
.main-product-template .product-tab .tooltip .tooltiptext  {   width: max-content;  min-width: 120px; background-color: var(--DTPrimaryColor); color: var(--DTBodyBGColor);  text-align: center; font-size: 14px; border-radius: 6px; padding: 0px 15px; position: absolute; z-index: 1; top: -150%; left: 50%; opacity: 0; transform: translateX(-50%); transition: all .35s cubic-bezier(0.52, 1.25, 0.53, 1.25); pointer-events: none; }
.main-product-template .product-tab .tooltip .tooltiptext:after {  content: "";  width: 10px;  height: 10px;  position: absolute;  bottom: -3px;  background: var(--DTPrimaryColor);  left: 0;  right: 0;  z-index: -1;  margin: 0 auto;  transform: rotate(45deg);}
.main-product-template .product-tab .tooltip:hover .tooltiptext { opacity: 1;top: -70%;}
.main-product-template .product-tab h5{ font-size:Calc(var(--DTFontSize_H4) - 2px); margin-top:0;}
.main-product-template .product-tab  p{font-size:var(--DTFontSizeBase); line-height: var(--DT_Body_Line_Height);}
.main-product-template .product-tab img{width:100%; height:100%; object-fit:cover;}
.main-product-template .product-tab ul li{list-style: inside; padding:5px 0; font-size:var(--DTFontSizeBase);}
.main-product-template .product-tab .product-description-img{margin-top:20px; position: relative; display: flex;}
.main-product-template .product-tab .product-image{ gap: 10px;  margin-bottom: 0;}
.main-product-template .product-page-row .swiper-container-fade .swiper-slide { height: auto; }
/*Product-detail page styles 1*/
.pdd-product-description-styl1 .product-descriptionsection ul{padding-left: 30px;  margin-top: 25px;}
.pdd-product-description-styl1 .product-image .custom-half-width{display: grid; grid-template-columns: repeat(2,1fr); gap: 10px;}
.pdd-product-description-styl1.dt-sc-column.three-fourth-one-fourth .product-image.dt-sc-column { grid-template-columns: repeat(1,1fr);}
.pdd-product-description-styl1 .product-descriptionsection .product-description2 { margin-bottom: 25px;}

/*Product-detail page styles 2*/
.pdd-product-description-styl2 .product-descriptionsection1 ul{padding-left: 30px;}
.pdd-product-description-styl2 .product-descriptionsection1.dt-sc-column.two-column,
.pdd-product-description-styl2 .product-descriptionsection2.dt-sc-column.two-column { align-items: center; gap:30px;}
.pdd-product-description-styl2 .product-descriptionsection1-img, .ret-product-description-styl2 .product-descriptionsection2-img {height:100%;}
.pdd-product-description-styl2 .product-descriptionsection1.dt-sc-column.two-column {margin-bottom: 30px;}
/*Product-detail page styles 3*/
.pdd-product-description-styl3 p{ width: 85%; }
.pdd-product-description-styl3 .product-descriptionsection1.dt-sc-column.two-column,
.pdd-product-description-styl3 .product-descriptionsection2.dt-sc-column.two-column { align-items: center; gap:40px;}
.pdd-product-description-styl3 .product-descriptionsection-img{  margin: 0 0 40px !important;}
/*Product-detail page styles 4*/
.pdd-product-description-styl4 .product-descriptionsection-img.dt-sc-column.three-column { margin: 40px  0; gap: 0; grid-template-columns: repeat(3,1fr);}
.pdd-product-description-styl4 .product-descriptionsection-desc.dt-sc-column.four-column { gap: 0;}

.product-information-style1-table table td, table th { border: none; white-space: nowrap;}
.product-information-style1-table table tr:nth-child(odd){background:{{ settings.tertiary_color | color_modify: 'alpha', 0.4}}; font-weight:bold;}
.product-information-style1-table table th{min-width:90px;}


@media (max-width:1540px) {
.pdd-product-description-styl3 .product-descriptionsection-desc.dt-sc-column.two-column{ grid-template-columns:1fr;}
.pdd-product-description-styl3 p{width: 100%;}
.pdd-product-description-styl2 .product-descriptionsection1.dt-sc-column.two-column,
.pdd-product-description-styl2 .product-descriptionsection2.dt-sc-column.two-column {  align-items: unset;}
}

@media (max-width:576px) {
.main-product-template .product-tab .dt-sc-column{grid-template-columns: 1fr;}
.main-product-template .product-tab h5{font-size:Calc(var(--DTFontSize_H4) - 6px);}
.pdd-product-description-styl4 .product-descriptionsection-img.dt-sc-column.three-column { grid-template-columns: repeat(1,1fr);}
}
address{font-style:normal;}
.overflow-hidden-mobile #shopify-section-header {
    z-index: 0;
}
input[type=password]:not(:placeholder-shown) {
    font-family: 'pass', 'Roboto', Helvetica, Arial, sans-serif;
}
/* custom overlay */
 @media (max-width: 750px) {
  .hot-spot-overlay-active .dt-custom-overlay{
    background: rgba(0,0,0,0.7);
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    display: block !important;
    text-align: center;
}
  }
/*animation*/
.slideshow__text.banner__box{overflow:hidden;}
.shopify-section.slideshow.slidershow slideshow-component .swiper-slide :where(.banner__heading, .banner__sub_heading,.banner__text, .banner__buttons){opacity:0; transition: all 0.3s linear .6s;}
.shopify-section.slideshow.slidershow.reveal slideshow-component .swiper-slide :where(.banner__heading, .banner__sub_heading,.banner__text, .banner__buttons){opacity:1; transition: all 0.3s linear .6s; }
.shopify-section.slideshow.slidershow.reveal slideshow-component .swiper-slide-active :where(.banner__sub_heading) {
    animation: fadeInLeft var(--anim-time) ease both .2s; 
}
.shopify-section.slideshow.slidershow.reveal slideshow-component .swiper-slide-active :where(.banner__heading) {
    animation: fadeInLeft var(--anim-time) ease both .6s; 
}
.shopify-section.slideshow.slidershow.reveal slideshow-component .swiper-slide-active :where(.banner__text) {
    animation: fadeInLeft var(--anim-time) ease both 1s; 
}
.shopify-section.slideshow.slidershow.reveal slideshow-component .swiper-slide-active :where(.banner__buttons) {
    animation: fadeInLeft var(--anim-time) ease both 1.4s;  
}
.card-wrapper .card__inner .quick-add__submit svg {
    width: 17px;
} 
.collection .title-wrapper-with-link {
    margin-bottom: 5rem;
}
span.text_color {
    color: rgb(var(--color-base-solid-button-labels));
}

/*swiper loading zoom issue code*/
@media screen and (min-width: 1200px){
  .swiper:not(.swiper-initialized) .swiper-wrapper .desk-col-2,  .swiper:not(.swiper-initialized) .swiper-wrapper.desk-col-2{
      width: calc(50% - var(--grid-desktop-horizontal-spacing) / 2);
      max-width: calc(50% - var(--grid-desktop-horizontal-spacing) / 2);
      margin-right: 30px;
    }
    .swiper:not(.swiper-initialized) .swiper-wrapper .desk-col-3,  .swiper:not(.swiper-initialized) .swiper-wrapper.desk-col-3{
      width: calc(33.33% - calc(var(--grid-desktop-horizontal-spacing) - var(--grid-desktop-horizontal-spacing)) * 2 / 3);
      max-width: calc(33.33% - calc(var(--grid-desktop-horizontal-spacing) - var(--grid-desktop-horizontal-spacing)) * 2 / 3);
      margin-right: 30px;
    }
      .swiper:not(.swiper-initialized) .swiper-wrapper .desk-col-3-half,  .swiper:not(.swiper-initialized) .swiper-wrapper.desk-col-3-half{
      width: calc(28.57% - calc(var(--grid-desktop-horizontal-spacing) - var(--grid-desktop-horizontal-spacing)) * 2 / 6);
      max-width: calc(28.57% - calc(var(--grid-desktop-horizontal-spacing) - var(--grid-desktop-horizontal-spacing)) * 2 / 6);
      margin-right: 30px;
    }
    .swiper:not(.swiper-initialized) .swiper-wrapper .desk-col-4,  .swiper:not(.swiper-initialized) .swiper-wrapper.desk-col-4{
      width: calc(25% - calc(var(--grid-desktop-horizontal-spacing) - var(--grid-desktop-horizontal-spacing)) * 3 / 4);
        max-width: calc(25% - calc(var(--grid-desktop-horizontal-spacing) - var(--grid-desktop-horizontal-spacing)) * 3 / 4);
      margin-right: 30px;
    }
      .swiper:not(.swiper-initialized) .swiper-wrapper .desk-col-4-half,  .swiper:not(.swiper-initialized) .swiper-wrapper.desk-col-4-half{
      width: calc(22.22% - calc(var(--grid-desktop-horizontal-spacing) - var(--grid-desktop-horizontal-spacing)) * 3 / 8);
        max-width: calc(22.22% - calc(var(--grid-desktop-horizontal-spacing) - var(--grid-desktop-horizontal-spacing)) * 3 / 8);
      margin-right: 30px;
    }
    .swiper:not(.swiper-initialized) .swiper-wrapper .desk-col-5,  .swiper:not(.swiper-initialized) .swiper-wrapper.desk-col-5{
      width: calc(20% - calc(var(--grid-desktop-horizontal-spacing) - var(--grid-desktop-horizontal-spacing)) * 4 / 5);
        max-width: calc(20% - calc(var(--grid-desktop-horizontal-spacing) - var(--grid-desktop-horizontal-spacing)) * 4 / 5);
      margin-right: 30px;
    }
      .swiper:not(.swiper-initialized) .swiper-wrapper .desk-col-5-half,  .swiper:not(.swiper-initialized) .swiper-wrapper.desk-col-5-half{
      width: calc(18.18% - calc(var(--grid-desktop-horizontal-spacing) - var(--grid-desktop-horizontal-spacing)) * 2 / 5);
        max-width: calc(18.18% - calc(var(--grid-desktop-horizontal-spacing) - var(--grid-desktop-horizontal-spacing)) * 2 / 5);
      margin-right: 30px;
    }
   .swiper:not(.swiper-initialized) .swiper-wrapper .desk-col-6,  .swiper:not(.swiper-initialized) .swiper-wrapper.desk-col-6{
      width: calc(15.66% - calc(var(--grid-desktop-horizontal-spacing) - var(--grid-desktop-horizontal-spacing)) * 5 / 6);
        max-width: calc(15.66% - calc(var(--grid-desktop-horizontal-spacing) - var(--grid-desktop-horizontal-spacing)) * 5 / 6);
      margin-right: 30px;
    }
    }
  @media screen and (max-width: 1199px){
     .swiper:not(.swiper-initialized) .swiper-wrapper .lap-col-3, .swiper:not(.swiper-initialized) .swiper-wrapper.lap-col-3{
      width: calc(33.33% - calc(var(--grid-desktop-horizontal-spacing) - var(--grid-desktop-horizontal-spacing)) * 2 / 3);
      max-width: calc(33.33% - calc(var(--grid-desktop-horizontal-spacing) - var(--grid-desktop-horizontal-spacing)) * 2 / 3);
      margin-right: 30px;
    }
    .swiper:not(.swiper-initialized)  .swiper-wrapper .lap-col-3-half, .swiper:not(.swiper-initialized)  .swiper-wrapper.lap-col-3-half{
      width: calc(28.57% - calc(var(--grid-desktop-horizontal-spacing) - var(--grid-desktop-horizontal-spacing)) * 1 / 3);
      max-width: calc(28.57% - calc(var(--grid-desktop-horizontal-spacing) - var(--grid-desktop-horizontal-spacing)) * 1 / 3);
      margin-right: 30px;
    }
   .swiper:not(.swiper-initialized) .swiper-wrapper .lap-col-4, .swiper:not(.swiper-initialized) .swiper-wrapper.lap-col-4{
      width: calc(25% - calc(var(--grid-desktop-horizontal-spacing) - var(--grid-desktop-horizontal-spacing)) * 3 / 4);
        max-width: calc(25% - calc(var(--grid-desktop-horizontal-spacing) - var(--grid-desktop-horizontal-spacing)) * 3 / 4);
      margin-right: 30px;
    }
    .swiper:not(.swiper-initialized)  .swiper-wrapper .lap-col-4-half,  .swiper:not(.swiper-initialized)  .swiper-wrapper.lap-col-4-half{
      width: calc(22.22% - calc(var(--grid-desktop-horizontal-spacing) - var(--grid-desktop-horizontal-spacing)) * 3 / 4);
        max-width: calc(22.22% - calc(var(--grid-desktop-horizontal-spacing) - var(--grid-desktop-horizontal-spacing)) * 3 / 4);
      margin-right: 30px;
    }
   .swiper:not(.swiper-initialized) .swiper-wrapper .lap-col-5, .swiper:not(.swiper-initialized) .swiper-wrapper.lap-col-5{
      width: calc(20% - calc(var(--grid-desktop-horizontal-spacing) - var(--grid-desktop-horizontal-spacing)) * 4 / 5);
        max-width: calc(20% - calc(var(--grid-desktop-horizontal-spacing) - var(--grid-desktop-horizontal-spacing)) * 4 / 5);
      margin-right: 30px;
    }
    }