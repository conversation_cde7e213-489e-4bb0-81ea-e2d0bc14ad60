{{ 'template-collection.css' | asset_url | stylesheet_tag }}
{{ 'component-card.css' | asset_url | stylesheet_tag }}
{{ 'component-price.css' | asset_url | stylesheet_tag }}

<link rel="stylesheet" href="{{ 'component-search.css' | asset_url }}" media="print" onload="this.media='all'">
{%- if settings.enable_quickadd or settings.enable_quickview -%}
  <link rel="stylesheet" href="{{ 'quick-add.css' | asset_url }}" media="print" onload="this.media='all'">
  <script src="{{ 'quick-add.js' | asset_url }}" defer="defer"></script>
  <script src="{{ 'product-form.js' | asset_url }}" defer="defer"></script>
{%- endif -%}
<noscript>{{ 'component-search.css' | asset_url | stylesheet_tag }}</noscript>

{%- if section.settings.enable_filtering or section.settings.enable_sorting -%}
  {{ 'component-facets.css' | asset_url | stylesheet_tag }}
  <script src="{{ 'facets.js' | asset_url }}" defer="defer"></script>
{%- endif -%}
<script src="{{ 'sticky-sidebar.js' | asset_url }}" defer="defer"></script>
<style>
  .template-search__header {
    margin-bottom: 3rem;
  }

  .template-search__search {
    margin: 0 auto 3.5rem;
    max-width: 47.8rem;
  }

  .template-search__search .search {
    margin-top: 3rem;
  }

  .template-search--empty {
    padding-bottom: 18rem;
  }

  @media screen and (min-width: 750px) {
    .template-search__header {
      margin-bottom: 5rem;
    }
  }

  .search__button .icon {
    height: 1.8rem;
  }
.template-search facet-filters-form.facets.facets-vertical-sort{flex-direction: row;justify-content: space-between;}
.template-search .facets-vertical-form{margin:0;}
.custom-product-grid {opacity: 1;display: flex;justify-content: space-between; transition: all var(--duration-default) linear;transform: translateY(0);padding:0 0 0 25px;margin:0;}
.custom-product-grid.panel-disabled { cursor: not-allowed; pointer-events: none; opacity: 0; transform: translateY(-5px); }
.custom-product-grid li { opacity: 1; min-width: 36px; min-height: 36px; padding: 5px 15px; cursor: pointer; display: flex; align-items: center; justify-content: center; }
.custom-product-grid li { position: relative; margin: 0 5px; background: var(--gradient-base-background-2);border-radius: var(--buttons-radius); }
.custom-product-grid li:after { content: '';display:none; width: auto; height: auto; white-space: nowrap; /*letter-spacing: -4px;*/ transform: translateX(-2.5px); -webkit-transform: translateX(-2.5px); color: var(--gradient-base-accent-1); transition: all var(--duration-default) linear; }
.custom-product-grid li:before { content: ""; position: absolute; right: 0; top: 0; bottom: 0; width: 0; transition: all var(--duration-default) linear; background: var(--gradient-base-accent-2);border-radius: var(--buttons-radius); }
.custom-product-grid li[data-cols="grid--1-col-desktop"]:after {content:  "|" }
.custom-product-grid li[data-cols="grid--2-col-desktop"]:after {content:  "||" }
.custom-product-grid li[data-cols="grid--3-col-desktop"]:after {content:  "|||" }
.custom-product-grid li[data-cols="grid--4-col-desktop"]:after {content:  "||||" }
.custom-product-grid li[data-cols="grid--5-col-desktop"]:after {content:  "|||||" }
.custom-product-grid li[data-cols="grid--6-col-desktop"]:after {content:  "||||||" }

.custom-product-grid li.active:after { color: var(--gradient-base-accent-1); } 
  .custom-product-grid li.active .icon-columns{ color:var(--gradient-base-background-1); }
.custom-product-grid li.active:before { left: 0; width: 100%;  background-color:var(--gradient-base-accent-2); }
.custom-product-grid li .icon-columns{z-index:1;width: 2rem;height: 2rem;}

@media screen and (max-width: 1540px) {
.custom-product-grid li[data-cols="grid--5-col-desktop"]{display:none;}
}
   /*popular search*/
.template-search__search ul.search-tags { display: flex; flex-wrap: wrap; margin: 0; width: 100%; align-items: center; justify-content: center;  padding: 0;}
.template-search__search ul.search-tags li{    list-style: none;  margin: 14px 0px 0; font-weight: 500;}
.template-search__search ul.search-tags li a{  display: block; padding: 0px 10px; text-transform: capitalize; position: relative; transition: all .3s linear; font-size: 1.6rem; font-weight: 400; color:rgb(var(--color-foreground));}
.template-search__search ul.search-tags li a:hover{color:rgb(var(--color-base-outline-button-labels));}
 .template-search__search main-search .field{display:flex; flex-direction:column;}
 .template-search__search main-search .predictive-search #predictive-search-results{  width: 100%;  box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px; padding: 20px;  background: rgb(var(--color-background));}
 .template-search__search main-search .predictive-search{position:absolute; top: calc(100% + 0.1rem);left: -0.1rem;}

</style>

{%- liquid
  assign sort_by = search.sort_by | default: search.default_sort_by
  assign terms = search.terms | escape
  assign search_url = '?q=' | append: terms | append: '&options%5Bprefix%5D=last&sort_by=' | append: sort_by
-%}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}

<div class="template-search{% unless search.performed and search.results_count > 0 %} template-search--empty{% endunless %} section-{{ section.id }}-padding ">
<div class="{% if section.settings.page_full_width %} page-full-width {% else %} page-width {% endif %} {% if section.settings.page_full_width_spacing %} page-full-width_spacing {% endif %}">
  <div class="row">
  <div class="template-search__header page-width center">
    {%- if search.performed -%}
      <h1 class="h2">{{ 'templates.search.title' | t }}</h1>
    {%- else -%}
      <h1 class="h2">{{ 'general.search.search' | t }}</h1>
    {%- endif -%}
    <div class="template-search__search">
      {%- if settings.predictive_search_enabled -%}
          <predictive-search data-loading-text="{{ 'accessibility.loading' | t }}">
        {%- endif -%}
            <main-search>
              <form action="{{ routes.search_url }}" method="get" role="search" class="search">
                 <input type="hidden" name="type" value="product">
                <div class="field">
                  <input
                    class="search__input field__input"
                    id="Search-In-Template"
                    type="search"
                    name="q"
                    value="{{ search.terms | escape }}"
                    placeholder="{{ 'general.search.search' | t }}"
                    {%- if settings.predictive_search_enabled -%}
                      role="combobox"
                      aria-expanded="false"
                      aria-owns="predictive-search-results"
                      aria-controls="predictive-search-results"
                      aria-haspopup="listbox"
                      aria-autocomplete="list"
                      autocorrect="off"
                      autocomplete="off"
                      autocapitalize="off"
                      spellcheck="false"
                    {%- endif -%}
                  >
                  <label class="field__label" for="Search-In-Template">{{ 'general.search.search' | t }}</label>
                  <input name="options[prefix]" type="hidden" value="last">

                  {%- if settings.predictive_search_enabled -%}
                    <div class="predictive-search predictive-search--search-template" tabindex="-1" data-predictive-search>
                      <div class="predictive-search__loading-state">
                        <svg aria-hidden="true" focusable="false" class="spinner" viewBox="0 0 66 66" xmlns="http://www.w3.org/2000/svg">
                          <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
                        </svg>
                      </div>
                    </div>

                    <span class="predictive-search-status visually-hidden" role="status" aria-hidden="true"></span>
                  {%- endif -%}

                  <button type="reset" class="reset__button field__button{% if search.terms == blank %} hidden{% endif %}" aria-label="{{ 'general.search.reset' | t }}">
                    <svg class="icon icon-close" aria-hidden="true" focusable="false">
                      <use xlink:href="#icon-reset">
                    </svg>
                  </button>
                  <button type="submit" class="search__button field__button" aria-label="{{ 'general.search.search' | t }}">
                    <svg class="icon icon-search" aria-hidden="true" focusable="false">
                      <use xlink:href="#icon-search">
                    </svg>
                  </button>
                </div>
              </form>
            </main-search>
        {% render 'search-tags-collections', headerType: section.settings.logo_position, line: 'line-70' %}                  
        {%- if settings.predictive_search_enabled -%}
          </predictive-search>
        {%- endif -%}

    </div>
    {%- if search.performed -%}
      {%- unless section.settings.enable_filtering or section.settings.enable_sorting -%}
        {%- if search.results_count > 0 -%}
          <p role="status">{{ 'templates.search.results_with_count_and_term' | t: terms: search.terms, count: search.results_count }}</p>
        {%- endif -%}
      {%- endunless -%}
      {%- if search.results_count == 0 and search.filters == empty -%}
        <p role="status">{{ 'templates.search.no_results' | t: terms: search.terms }}</p>
      {%- endif -%}
    {%- endif -%}
  </div>
  {%- if search.performed -%}
    {%- if section.settings.enable_sorting and section.settings.filter_type == 'vertical' and search.filters != empty -%}
      <facet-filters-form class="facets facets-vertical-sort small-hide no-js-hidden">
             <div class="product-count-vertical light" role="status">
            <h2 class="product-count__text text-body">
              <span id="ProductCountDesktop">
                {%- if search.results_count -%}
                  {{ 'templates.search.results_with_count' | t: terms: search.terms, count: search.results_count }}
                {%- elsif search.products_count == search.all_products_count -%}
                  {{ 'products.facets.product_count_simple' | t: count: search.products_count }}
                {%- else -%}
                  {{ 'products.facets.product_count' | t: product_count: search.products_count, count: search.all_products_count }}
                {%- endif -%}
              </span>
            </h2>
            <div class="loading-overlay__spinner">
              <svg aria-hidden="true" focusable="false" role="presentation" class="spinner" viewBox="0 0 66 66" xmlns="http://www.w3.org/2000/svg">
                <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
              </svg>
            </div>
          </div> 

              <ul class="custom-product-grid">
              <li  data-cols="list-view-filter" class="list-view-button layout-mode{% if section.settings.columns_desktop == '1' %}active{% endif %} ">
               <svg class="icon-columns" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="15.25" y="4.25" width="1.5" height="12.5" rx="0.75" transform="rotate(90 15.25 4.25)" fill="currentcolor"></rect>
                <rect x="15.25" y="8.25" width="1.5" height="12.5" rx="0.75" transform="rotate(90 15.25 8.25)" fill="currentcolor"></rect>
                <rect x="15.25" y="12.25" width="1.5" height="12.5" rx="0.75" transform="rotate(90 15.25 12.25)" fill="currentcolor"></rect>
                </svg>
              </li>
              <li data-cols="grid--2-col-desktop" class="{% if section.settings.columns_desktop == '2' %}active{% endif %}">
                <svg class="icon-columns" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="6.25" y="2.75" width="1.5" height="12.5" rx="0.75" fill="currentcolor"></rect>
                <rect x="10.25" y="2.75" width="1.5" height="12.5" rx="0.75" fill="currentcolor"></rect>
                </svg>
              </li>
              <li data-cols="grid--3-col-desktop" class="{% if section.settings.columns_desktop == '3' %}active{% endif %}">
                <svg class="icon-columns" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="4.25" y="2.75" width="1.5" height="12.5" rx="0.75" fill="currentcolor"></rect>
                <rect x="8.25" y="2.75" width="1.5" height="12.5" rx="0.75" fill="currentcolor"></rect>
                <rect x="12.25" y="2.75" width="1.5" height="12.5" rx="0.75" fill="currentcolor"></rect>
                </svg>
              </li>
              <li data-cols="grid--4-col-desktop" class="{% if section.settings.columns_desktop == '4' %}active{% endif %}">
                <svg class="icon-columns" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="2.25" y="2.75" width="1.5" height="12.5" rx="0.75" fill="currentcolor"></rect>
                <rect x="6.25" y="2.75" width="1.5" height="12.5" rx="0.75" fill="currentcolor"></rect>
                <rect x="10.25" y="2.75" width="1.5" height="12.5" rx="0.75" fill="currentcolor"></rect>
                <rect x="14.25" y="2.75" width="1.5" height="12.5" rx="0.75" fill="currentcolor"></rect>
                </svg>
              </li>
              <li data-cols="grid--5-col-desktop" class="{% if section.settings.columns_desktop == '5' %}active{% endif %}">
               <svg class="icon-columns" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="0.25" y="2.75" width="1.5" height="12.5" rx="0.75" fill="currentcolor"></rect>
                <rect x="4.25" y="2.75" width="1.5" height="12.5" rx="0.75" fill="currentcolor"></rect>
                <rect x="8.25" y="2.75" width="1.5" height="12.5" rx="0.75" fill="currentcolor"></rect>
                <rect x="12.25" y="2.75" width="1.5" height="12.5" rx="0.75" fill="currentcolor"></rect>
                <rect x="16.25" y="2.75" width="1.5" height="12.5" rx="0.75" fill="currentcolor"></rect>
                </svg>
              </li>
              </ul>
      
              <form class="facets-vertical-form" id="FacetSortForm">
              <div class="facet-filters sorting caption">
                 <div class="facet-filters__field">                                    
                    
                    <div class="sorting">
                <div class="facet-filters__field">
                  <details class="disclosure-has-popup facets__disclosure facet-filters__sort">
                    <summary class="facets__summary">
                       <span class="button button--tertiary button--small">{{ 'products.facets.sort_by_label' | t }}  {% render 'icon-caret' %}</span>                        
                     
                    </summary>
                    <div class="facets__display">
                      <ul class="facets__list list-unstyled" role="list">
                        {%- for option in search.sort_options -%}
                          <li class="list-menu__item facets__item">
                            <label for="Filter-{{ option.value | escape }}-{{ forloop.index }}" class="facet-checkbox">
                              <input type="radio" name="sort_by" value="{{ option.value | escape }}" id="Filter-{{ option.value | escape }}-{{ forloop.index }}" {% if option.value == sort_by %}checked="checked"{% endif %} class="visually-hidden" />
                              <span class="label">{{ option.name | escape }}</span>
                            </label>
                          </li>
                        {%- endfor -%}
                      </ul>
                    </div>
                  </details>                         
              </div>
                  </div>
                </div>
                
              </div>
            </form>
    
            
           
          </facet-filters-form>
    {%- endif -%}
    <div class="{% if section.settings.filter_type == 'vertical' %} facets-vertical  {{ section.settings.sidebar_settings }}{% endif %}">
      {%- if search.filters != empty -%}
        {%- if section.settings.enable_filtering or section.settings.enable_sorting -%}
          <aside aria-labelledby="verticalTitle" class="facets-wrapper{% unless section.settings.enable_filtering %} facets-wrapper--no-filters{% endunless %}{% if section.settings.filter_type != 'vertical' %} page-width{% endif %} {% unless section.settings.filter_type == 'horizontal' %} sidebar-sticky {% endunless %}" id="main-search-filters" data-id="{{ section.id }}">
            {% render 'facets', results: search, enable_filtering: section.settings.enable_filtering, enable_sorting: section.settings.enable_sorting, filter_type: section.settings.filter_type %}
          </aside>
        {%- endif -%}
      {%- endif -%}
      <div class="product-grid-container" id="ProductGridContainer">
        {%- if search.results.size == 0 and search.filters != empty -%}
          <div class="template-search__results collection collection--empty{% if section.settings.filter_type != 'vertical' %} page-width{% endif %}" id="product-grid" data-id="{{ section.id }}">
            <div class="loading-overlay gradient"></div>
            <div class="title-wrapper center">
              <h2 class="title title--primary">
                {{ 'sections.collection_template.empty' | t }}<br>
                {{ 'sections.collection_template.use_fewer_filters_html' | t: link: search_url, class: "underlined-link link" }}
              </h2>
            </div>
          </div>
        {%- else -%}
          {% paginate search.results by 24 %}
            <div class="template-search__results collection{% if section.settings.filter_type != 'vertical' %} page-width{% endif %}" id="product-grid" data-id="{{ section.id }}">
              <div class="loading-overlay gradient"></div>
       {% liquid 
  if  section.settings.columns_desktop == 2
  assign column_width = "grid grid--2-col-desktop  grid--2-col-tablet-down"
  elsif section.settings.columns_desktop == 3
  assign column_width = "grid grid--3-col-desktop  grid--2-col-tablet-down"
  elsif section.settings.columns_desktop == 4
  assign column_width = "grid grid--4-col-desktop  grid--2-col-tablet-down"
  elsif section.settings.columns_desktop == 5
  assign column_width = "grid grid--5-col-desktop  grid--2-col-tablet-down"
  elsif section.settings.columns_desktop == 6
  assign column_width = "grid grid--6-col-desktop  grid--2-col-tablet-down"
  endif 
  %}
                
              <ul id="product-grid" data-id="{{ section.id }}" class="grid-view-filter view-mode grid product-grid  grid--{{ section.settings.columns_mobile }}-col-mobile {{ column_width }}" role="list">
                {%- for item in search.results -%}
                  {% assign lazy_load = false %}
                  {%- if forloop.index > 2 -%}
                    {%- assign lazy_load = true -%}
                  {%- endif -%}
                  {%- case item.object_type -%}
                  {%- when 'product' -%}
                  <li class="grid__item card_style-{{ settings.card_style }}">
                        {%- capture product_settings -%}{%- if section.settings.product_show_vendor -%}vendor,{%- endif -%}title,price{%- endcapture -%}
                      {%  if settings.card_style == 'standard' %}

                      {%- assign placeholder_image_index = forloop.index0 | modulo: 4 | plus: 1 -%}
                      {%- assign placeholder_image = 'product-apparel-' | append: placeholder_image_index -%}
                      {% render 'card-product',
                      card_product: item,
                      media_aspect_ratio: section.settings.image_ratio,
                      show_secondary_image: section.settings.show_secondary_image,
                      show_vendor: section.settings.show_vendor,
                      show_rating: section.settings.show_rating,
                      show_quick_add: settings.enable_quickadd,
                      show_quick_view: settings.enable_quickview,
                      show_new_tag: section.settings.show_new_tag,
                      placeholder_image: placeholder_image,
                      section_id: section.id
                      %}
                          {%  elsif settings.card_style == 'button_width_icons' %}
                      
                      {% render 'card-product-2',
                      card_product: item,
                      media_aspect_ratio: section.settings.image_ratio,
                      show_secondary_image: section.settings.show_secondary_image,
                      show_vendor: section.settings.show_vendor,
                      show_rating: section.settings.show_rating,
                      show_quick_add: settings.enable_quickadd,
                      show_quick_view: settings.enable_quickview,
                      show_new_tag: section.settings.show_new_tag,
                      section_id: section.id
                      %}
                         {%  elsif settings.card_style == 'card_with_icons' %}
                    
                      {% render 'card-product-3',
                      card_product: item,
                      media_aspect_ratio: section.settings.image_ratio,
                      show_secondary_image: section.settings.show_secondary_image,
                      show_vendor: section.settings.show_vendor,
                      show_rating: section.settings.show_rating,
                      show_quick_add: settings.enable_quickadd,
                      show_quick_view: settings.enable_quickview,
                      show_new_tag: section.settings.show_new_tag,
                      section_id: section.id
                      %}
                      {%  elsif settings.card_style == 'card_with_buttons' %}   
                        
                      {% render 'card-product-4',
                      card_product: item,
                      media_aspect_ratio: section.settings.image_ratio,
                      show_secondary_image: section.settings.show_secondary_image,
                      show_vendor: section.settings.show_vendor,
                      show_rating: section.settings.show_rating,
                      show_quick_add: settings.enable_quickadd,
                      show_quick_view: settings.enable_quickview,
                      show_new_tag: section.settings.show_new_tag,
                      section_id: section.id
                      %}
                     {%  else %}    
                      {% render 'card-product-5',
                      card_product: item,
                      media_aspect_ratio: section.settings.image_ratio,
                      show_secondary_image: section.settings.show_secondary_image,
                      show_vendor: section.settings.show_vendor,
                      show_rating: section.settings.show_rating,
                      show_quick_add: settings.enable_quickadd,
                      show_quick_view: settings.enable_quickview,
                      show_new_tag: section.settings.show_new_tag,
                      section_id: section.id
                      %}
                      {%  endif %}
                  </li>
              {%- endcase -%}
                {%- endfor -%}
              </ul>
              {%- if paginate.pages > 1 -%}
                {% render 'pagination', paginate: paginate %}
              {%- endif -%}
            </div>
          {% endpaginate %}
        {%- endif -%}
      </div>
    </div>
  {%- endif -%}
  </div>
 </div>
 </div>
{% schema %}
{
  "name": "t:sections.main-search.name",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 5,
      "step": 1,
      "default": 4,
      "label": "t:sections.main-search.settings.columns_desktop.label"
    },
    {
      "type": "header",
      "content": "t:sections.main-search.settings.header__1.content"
    },
    {
      "type":"checkbox",
      "id":"page_full_width",
       "default": false,
      "label": "t:sections.all.page_full_width.label"
    },
    {
      "type":"checkbox",
      "id":"page_full_width_spacing",
       "default": false,
      "label": "t:sections.all.page_full_width_spacing.label"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.main-search.settings.image_ratio.options__1.label"
        },
        {
          "value": "portrait",
          "label": "t:sections.main-search.settings.image_ratio.options__2.label"
        },
        {
          "value": "square",
          "label": "t:sections.main-search.settings.image_ratio.options__3.label"
        }
      ],
      "default": "adapt",
      "label": "t:sections.main-search.settings.image_ratio.label"
    },
    {
      "type": "checkbox",
      "id": "show_secondary_image",
      "default": false,
      "label": "t:sections.main-search.settings.show_secondary_image.label"
    },
    {
      "type": "checkbox",
      "id": "show_vendor",
      "default": false,
      "label": "t:sections.main-search.settings.show_vendor.label"
    },
    {
      "type": "checkbox",
      "id": "show_rating",
      "default": false,
      "label": "t:sections.main-search.settings.show_rating.label",
      "info": "t:sections.main-search.settings.show_rating.info"
    },
    {
      "type": "header",
      "content": "t:sections.main-search.settings.header__1.content"
    },
    {
      "type": "checkbox",
      "id": "enable_filtering",
      "default": true,
      "label": "t:sections.main-search.settings.enable_filtering.label",
      "info": "t:sections.main-search.settings.enable_filtering.info"
    },
    {
      "type": "select",
      "id": "filter_type",
      "options": [
        {
          "value": "horizontal",
          "label": "t:sections.main-search.settings.filter_type.options__1.label"
        },
        {
          "value": "vertical",
          "label": "t:sections.main-search.settings.filter_type.options__2.label"
        },
        {
          "value": "drawer",
          "label": "t:sections.main-search.settings.filter_type.options__3.label"
        }
      ],
      "default": "horizontal",
      "label": "t:sections.main-search.settings.filter_type.label",
      "info": "t:sections.main-search.settings.filter_type.info"
    },
    {
    "type": "select",
    "id": "sidebar_settings",
    "options": [
    {
    "value": "sidebar-left",
    "label": "t:sections.main-search.settings.sidebar_settings.options__1.label"
    },
    {
    "value": "sidebar-right",
    "label": "t:sections.main-search.settings.sidebar_settings.options__2.label"
    },
    {
    "value": "no-sidebar",
    "label": "t:sections.main-search.settings.sidebar_settings.options__3.label"
    }
    ],
    "default": "sidebar-left",
    "label": "t:sections.main-search.settings.sidebar_settings.label"
    },
    {
      "type": "checkbox",
      "id": "enable_sorting",
      "default": true,
      "label": "t:sections.main-search.settings.enable_sorting.label"
    },
    {
"type": "checkbox",
"id": "enable_grid_list",
"default": true,
"label": "t:sections.main-search.settings.enable_grid_list.label"
},
    {
      "type": "header",
      "content": "t:sections.main-search.settings.header__2.content"
    },
    {
      "type": "checkbox",
      "id": "article_show_date",
      "default": true,
      "label": "t:sections.main-search.settings.article_show_date.label"
    },
    {
      "type": "checkbox",
      "id": "article_show_author",
      "default": false,
      "label": "t:sections.main-search.settings.article_show_author.label"
    },
    {
      "type": "header",
      "content": "t:sections.main-search.settings.header_mobile.content"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "default": "2",
      "label": "t:sections.main-search.settings.columns_mobile.label",
      "options": [
        {
          "value": "1",
          "label": "t:sections.main-search.settings.columns_mobile.options__1.label"
        },
        {
          "value": "2",
          "label": "t:sections.main-search.settings.columns_mobile.options__2.label"
        }
      ]
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    }
  ]
}
{% endschema %}
