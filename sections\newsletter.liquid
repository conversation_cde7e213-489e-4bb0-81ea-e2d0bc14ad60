{{ 'component-newsletter.css' | asset_url | stylesheet_tag }}
{{ 'newsletter-section.css' | asset_url | stylesheet_tag }}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
  {%- if section.settings.newletter_bg -%}
       #section-{{ section.id }}.newsletter .newsletter__wrapper{position:relative;}
    #section-{{ section.id }}.newsletter .newsletter__wrapper:before{content:'';background:url({{ section.settings.newletter_bg | image_url: width: 1920}});position:absolute;top:0;background-repeat: no-repeat;
    background-size: cover; width:100%;height:100%;z-index:-1; left:0;} 
  {%- endif -%}
{%- endstyle -%}

<div id= "section-{{ section.id }}" class=" {% if section.settings.page_full_width %} page-full-width {% else %} page-width {% endif %} {% if section.settings.page_full_width_spacing %} page-full-width_spacing {% endif %} isolate newsletter center {{ section.settings.custom_class_name }} ">
<div class="row">
  <div class="newsletter__wrapper color-{{ section.settings.color_scheme }} gradient content-container section-{{ section.id }}-padding">
    <div class="newletter_wrapper-block">
     <div class="newsletter__wrapper-left">
     {%- for block in section.blocks -%}
      {%- case block.type -%}        
        {%- when 'heading' -%}
          <h2 class="{{ block.settings.heading_size }} " {{ block.shopify_attributes }}>{{ block.settings.heading | escape }}</h2>
        {%- when 'paragraph' -%}
          <div class="newsletter__subheading rte {{ block.settings.text_style }}" {{ block.shopify_attributes }}>{{ block.settings.text }}</div>        
     {%- endcase -%}
    {%- endfor -%}
     </div>

     <div class="newsletter__wrapper-right">
    {%- for block in section.blocks -%}
      {%- case block.type -%}
        {%- when '@app' -%}
          {% render block %}
        {%- when 'email_form' -%}
          <div {{ block.shopify_attributes }}>
            {% form 'customer', class: 'newsletter-form' %}
              <input type="hidden" name="contact[tags]" value="newsletter">
              <div class="newsletter-form__field-wrapper">
                <div class="field">
                  <input
                    id="NewsletterForm--{{ section.id }}"
                    type="email"
                    name="contact[email]"
                    class="field__input"
                    value="{{ form.email }}"
                    aria-required="true"
                    autocorrect="off"
                    autocapitalize="off"
                    autocomplete="email"
                    {% if form.errors %}
                      autofocus
                      aria-invalid="true"
                      aria-describedby="Newsletter-error--{{ section.id }}"
                    {% elsif form.posted_successfully? %}
                      aria-describedby="Newsletter-success--{{ section.id }}"
                    {% endif %}
                    placeholder="{{ 'newsletter.label' | t }}"
                    required
                  >
                  <label class="field__label visually-hidden" for="NewsletterForm--{{ section.id }}">
                    {{ 'newsletter.label' | t }}
                  </label>
                  <button type="submit" class="newsletter-form__button field__button" name="commit" id="Subscribe" aria-label="{{ 'newsletter.button_label' | t }}">
                     {% render 'newsletter-arrow' %}
                  </button>
                </div>
              </div>
              {%- if form.errors -%}
                  <small class="newsletter-form__message form__message" id="Newsletter-error--{{ section.id }}">{% render 'icon-error' %}{{ form.errors.translated_fields['email'] | capitalize }} {{ form.errors.messages['email'] }}</small>
                {%- endif -%}
              {%- if form.posted_successfully? -%}
                <h3 class="newsletter-form__message newsletter-form__message--success form__message" id="Newsletter-success--{{ section.id }}" tabindex="-1" autofocus>{% render 'icon-success' %}{{ 'newsletter.success' | t }}</h3>
              {%- endif -%}
            {% endform %}
        </div>
      {%- endcase -%}
    {%- endfor -%}
  </div>  
</div>    
  </div>
</div>
</div>
{% schema %}
{
  "name": "t:sections.newsletter.name",
  "tag": "section",
  "class": "section",
  "settings": [
     {
      "type":"checkbox",
      "id":"page_full_width",
       "default": false,
      "label": "t:sections.all.page_full_width.label"
    },
    {
      "type":"checkbox",
      "id":"page_full_width_spacing",
       "default": false,
      "label": "t:sections.all.page_full_width_spacing.label"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "t:sections.all.colors.accent_1.label"
        },
        {
          "value": "accent-2",
          "label": "t:sections.all.colors.accent_2.label"
        },
        {
          "value": "background-1",
          "label": "t:sections.all.colors.background_1.label"
        },
        {
          "value": "background-2",
          "label": "t:sections.all.colors.background_2.label"
        },
        {
          "value": "inverse",
          "label": "t:sections.all.colors.inverse.label"
        }
      ],
      "default": "background-1",
      "label": "t:sections.all.colors.label"
    },
     {
      "type": "image_picker",
      "id": "newletter_bg",
      "label": "t:sections.newsletter.settings.newletter_bg.label"
    },
    {
      "type": "paragraph",
      "content": "t:sections.newsletter.settings.paragraph.content"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 40
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 52
    },
    {
    "type": "header",
    "content": "t:sections.all.custom_class_heading.content"
    },
    {
    "type": "text",
    "id": "custom_class_name",
    "label": "t:sections.all.custom_class_name.label"
    }
  ],
  "blocks": [
    {
      "type": "heading",
      "name": "t:sections.newsletter.blocks.heading.name",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "default": "Subscribe to our emails",
          "label": "t:sections.newsletter.blocks.heading.settings.heading.label"
        },
        {
          "type": "select",
          "id": "heading_size",
          "options": [
            {
              "value": "h2",
              "label": "t:sections.all.heading_size.options__1.label"
            },
            {
              "value": "h1",
              "label": "t:sections.all.heading_size.options__2.label"
            },
            {
              "value": "h0",
              "label": "t:sections.all.heading_size.options__3.label"
            }
          ],
          "default": "h1",
          "label": "t:sections.all.heading_size.label"
        }
      ]
    },
     {
      "type": "paragraph",
      "name": "t:sections.newsletter.blocks.paragraph.name",
      "limit": 2,
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "default": "<p>Be the first to know when we launch.</p>",
          "label": "t:sections.newsletter.blocks.paragraph.settings.paragraph.label"
        },
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "body",
              "label": "t:sections.newsletter.blocks.paragraph.settings.text_style.options__1.label"
            },
            {
              "value": "subtitle",
              "label": "t:sections.newsletter.blocks.paragraph.settings.text_style.options__2.label"
            }
          ],
          "default": "body",
          "label": "t:sections.newsletter.blocks.paragraph.settings.text_style.label"
        }
      ]
    },
    {
      "type": "email_form",
      "name": "t:sections.newsletter.blocks.email_form.name",
      "limit": 1
    },
    {
      "type": "@app"
    }
  ],
  "presets": [
    {
      "name": "t:sections.newsletter.presets.name",
      "blocks": [
        {
          "type": "heading"
        },
        {
          "type": "paragraph"
        },
        {
          "type": "email_form"
        }
      ]
    }
  ]
}
{% endschema %}
