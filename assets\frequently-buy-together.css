.frequently-buy-togeather-products .dt-sc-thumb-image {
    display: grid;
    grid-template-columns: 1fr;
    gap: 3rem;
}

.frequently-buy-togeather-products .dt-sc-thumb-image a.item-image {
  width: 100%;
  height: 200px;
}

.dt-frequently-bought-together {
  /* background: rgba(var(--color-foreground), 0.05); */
  padding: 3rem;
  display: inline-block;
  width: 100%;
  /* border-radius: var(--DTRadius); */
  margin: 0 0 40px;
}
.dt-frequently-bought-together .section-title h3 {
  margin: 0 0 30px;
  text-align: left;
  font-size: 2.4rem;
  font-weight:600;
}

.dt-frequently-bought-together .dt-sc-thumb-image > div {
  text-align: left;
  position: relative; 
  display: flex;
  flex-direction: row;
}

.dt-frequently-bought-together .frequently-buy-togeather-products .dT_bundleProductToggle {
  width: max-content;
  padding: 0 5rem;
}
.dt-frequently-bought-together .dt-sc-thumb-image .item-image img {
  width: 100%;
}
.dt-frequently-bought-together .dt-sc-thumb-image a.item-image {
  display: flex;
  overflow: hidden;
  border-radius: var(--DTRadius);
}
.dt-frequently-bought-together .dt-sc-thumb-image > div:not(:last-child) > div:first-child {
  position: relative;
}

.dt-frequently-bought-together .dt-sc-thumb-image > div:not(:last-child) > div:first-child:after {
  content: "+";
  color: var(--gradient-base-accent-2);
  font-weight: 500;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  right: -31px;
  font-size: 25px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  line-height: 25px;
  display:none;
}

.dt-frequently-bought-together .dt-sc-thumb-image li:not(:last-child) {
  margin-bottom: 15px;
}

.dt-frequently-bought-together .dt-sc-thumb-image li > div[id*="sale-price"],
.dt-frequently-bought-together .dt-sc-thumb-image li > div[class*="price-sale"] {
  font-size: var(--DTFontSize_H6);
}

.dt-frequently-bought-together .dt-sc-thumb-image [type="checkbox"] {
  margin: auto;
}
.dt-frequently-bought-together .dt-sc-product-details {
  display: flex;
  align-items: center;
  margin-bottom: 0;
  flex-flow: column wrap;
  line-height: normal;
}
.dt-frequently-bought-together .dt-sc-product-details span {
  font-weight: 600;
  padding: 0 3px;
  color: var(--DTPrimaryColor);
}
.frequently-buy-togeather-products .dt-sc-thumb-image .main-product,
.frequently-buy-togeather-products .dt-sc-thumb-image .group-product{position:relative;margin-left:20px;text-align:left;align-self: center;}

.dt-frequently-bought-together .bundle-product-cart-total {
  font-weight: 600;
  font-size: var(--DTFontSize_H6);
}
.dt-frequently-bought-together .dt-sc-select-btn {
  width: 100%;
  padding: 10px 15px;
  border-radius: var(--DTRadius);
  background-color: var(--gradient-base-accent-2);
  border: 1px solid var(--DTColor_Border);
}
.dt-frequently-bought-together .varient-options {
  z-index: 2;
  left: 0;
  display: none;
  margin-bottom: 15px;
  padding: 15px !important;
  width: 100% !important;
  box-shadow: 0 1px 5px #0000001a;
  border-radius: var(--DTRadius);
  background-color: var(--gradient-base-background-1);
  position: absolute;
}
.dt-frequently-bought-together .product-form__controls-group {
  display: flex;
  flex-flow: column;
}
.dt-frequently-bought-together .product-form__controls-group ~ * {
  display: none;
}
.dt-frequently-bought-together .product-form__controls-group .selector-wrapper {
  display: inline-flex;
  text-align: left;
  margin-bottom: 15px;
}
.dt-frequently-bought-together .product-form__controls-group .selector-wrapper:last-child {
  margin-bottom: 0px;
}
.dt-frequently-bought-together .selector-wrapper label {
  display: inline-flex;
  font-weight: 600;
  width: 50%;
  font-size: 14px;
  align-items: center;
}
.dt-frequently-bought-together .selector-wrapper .select2.select2-container {
  width: 50% !important;
}
.dt-frequently-bought-together .selector-wrapper select {
      padding: 5px 8px;
    width: 100%;
    background-color: transparent;
    border-radius: var(--buttons-radius);
    color: var(--gradient-base-accent-1);
    border: var(--inputs-border-width) solid rgb(var(--color-foreground),.2);
    margin: 0;
    appearance:none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='icon icon-caret' width='8.828' height='5.414' viewBox='0 0 8.828 5.414'%3E%3Cpath id='Path_20' data-name='Path 20' d='M4,10,7,7,4,4' transform='translate(11.414 -2.586) rotate(90)' fill='none' stroke='currentcolor' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5'%3E%3C/path%3E%3C/svg%3E");
    background-repeat: no-repeat, repeat;
    background-position: right 0.7em top 50%, 0 0;
    background-size: 0.65em auto, 100%;
    cursor: pointer;
}
.dt-frequently-bought-together .dt-sc-btn.btn-product-add-to-cart,
.bundle-product-add-to-cart,
.bundle-product-offer-note {
  display: inline-block;
  margin: 0px;
  width: 100%;
}
.dt-frequently-bought-together .selector-wrapper select:focus-visible {
  box-shadow: none;
  outline: none;
}
.dt-frequently-bought-together .products-grouped-info {
  text-align: center;
}
.dt-frequently-bought-together .bundle-product-additional-offer {
  
  margin: 30px auto 0;
  display: flex;
  flex-direction: column;
   align-items: center;
  justify-content: center;
}

.dt-frequently-bought-together .bundle-product-additional-offer > *:not(:last-child) {
  /* margin-bottom: 2rem; */
}

.dt-frequently-bought-together .bundle-product-cart-total {
  display: flex;
}
.dt-frequently-bought-together .bundle-product-cart-total > * + * {
  margin-left: 10px;
}
.dt-frequently-bought-together .bundle-product-add-to-cart > button {
  margin-top: 0;
  width: 100%;
}
.dt-frequently-bought-together .dt-frequently-bought-together .bundle-product-offer-note {
  width: 100%;
  font-size: 16px;
  color: var(--gradient-base-accent-1);
}
.dt-frequently-bought-together span.dT_totalBundleOriginalPrice {
  text-decoration: line-through;
  opacity: 0.5;
  color: inherit;
  font-weight: 400;
  display:none !important;
}
.dt-frequently-bought-together .group-product .old-price,
.main-product .old-price {
  font-size: 80%;
  opacity: 0.5;
  text-decoration: line-through;
}
.dt-frequently-bought-together .group-product .old-price,
.group-product .special-price {
  margin: 0 5px;
}

.dt-frequently-bought-together .dt-sc-thumb-image ul {
  position: relative;
  line-height: normal;
  list-style: none;
  padding: 0;
}
.dt-frequently-bought-together .dt-sc-thumb-image ul li .dt-sc-btn {
  margin: auto;
  display: inline-block;
}

.dt-frequently-bought-together .checkbox_style {
  display: inline-block;
  position: relative;
  cursor: pointer;
  font-size: 0;
  height: 0;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.dt-frequently-bought-together .checkbox_style input[type="checkbox"] {
  float: left;
  width: 36px;
  height: 6px;
  cursor: pointer;
  background-color: #fff;
  -webkit-appearance: none;
  border-radius: 8px;
  position: relative;
}
.dt-frequently-bought-together .checkbox_style input[type="checkbox"]:before {
  content: "";
  width: 17px;
  height: 17px;
  box-shadow: 0 0 0 calc(var(--buttons-border-width) + var(--border-offset))
      rgba(var(--color-button-text), var(--border-opacity)),
    0 0 0 var(--buttons-border-width)
      rgba(var(--color-button), var(--alpha-button-background));
  background-color: var(--gradient-background);
  border-radius: 50%;
  position: absolute;
  top: -6px;
  z-index: 1;
  left: -2px;
  transition: var(--DTBaseTransition);
}
.dt-frequently-bought-together .checkbox_style input[type="checkbox"]:after {
  content: "";
  height: 100%;
  width: 14px;
  position: absolute;
  left: 0;
  border-radius: 17px;
  background-color: rgba(186, 141, 105, 0.5);
  transition: var(--DTBaseTransition);
}
.dt-frequently-bought-together .checkbox_style input[type="checkbox"]:checked:before {
  left: calc(100% - 16px);
  background-color: var(--gradient-base-background-3);
}
.dt-frequently-bought-together .checkbox_style input[type="checkbox"]:checked:after {
  width: 100%;
  border-radius: 8px;
}
.dt-frequently-bought-together .dt-sc-thumb-image li.fbt-product-title {
    font-weight: 500;
}


@media only screen and (max-width: 991px) and (min-width: 767px) {
  .dt-frequently-bought-together {
    padding: 3rem;
  }
  .dt-frequently-bought-together .dt-sc-thumb-image > div:not(:last-child) > div:first-child:after {
    right: -26px;
    width: 20px;
    height: 20px;
    line-height: 20px;
    font-size: 16px;
  }
  .dt-frequently-bought-together .dt-sc-thumb-image ul li .dt-sc-btn,
  .bundle-product-add-to-cart > button {
    padding: 10px 20px;
    font-size: 16px;
  }
  
  .dt-frequently-bought-together {
    margin: 7rem 0 0;
  }
}

@media (max-width: 767px) {
  .dt-frequently-bought-together {
    padding: 3rem;
  }
  .dt-frequently-bought-together .dt-sc-thumb-image {
    flex-wrap: wrap;
  }
  .dt-frequently-bought-together .dt-sc-thumb-image ul {
    margin: 0;
  }
  .dt-frequently-bought-together .dt-sc-thumb-image > div {
    display: grid;
    grid-template-columns: 200px 1fr;
    width: 100%;
    align-items: center;
    text-align: left;
    gap: 30px;
  }
  .frequently-buy-togeather-products .dt-sc-thumb-image .main-product,
.frequently-buy-togeather-products .dt-sc-thumb-image .group-product{margin:0;}

  .dt-frequently-bought-together .dt-sc-thumb-image > div:not(:last-child) > div:first-child::after {
    display: none;
  }
  .dt-frequently-bought-together .dt-sc-thumb-image a.item-image {
    margin-bottom: 0;
  }
  .dt-frequently-bought-together .dt-sc-thumb-image ul li .dt-sc-btn,
  .bundle-product-add-to-cart > button {
    padding: 10px 20px;
    font-size: 16px;
  }
  .bundle-product-additional-offer {
    margin-top: 2rem;
    padding: 1.5rem 3rem;
  }
  .dt-frequently-bought-together .dt-sc-thumb-image {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
  }

}

@media (max-width: 576px) {
  .dt-frequently-bought-together {
    padding: 1.5rem;
  }
  .dt-frequently-bought-together .dt-sc-thumb-image > div {
    display: grid;
    grid-template-columns: 115px 1fr;
    gap: 15px;
  }
  .dt-frequently-bought-together .section-title h3 {
    font-size: 2rem;
    margin: 0 0 15px;
  }
  .bundle-product-additional-offer {
    padding: 1.5rem 1.5rem;
  }
  .dt-frequently-bought-together .dt-sc-thumb-image li:not(:last-child) {
    margin-bottom: 10px;
  }
  .dt-frequently-bought-together .dt-sc-thumb-image ul li .dt-sc-btn,
  .bundle-product-add-to-cart > button {
    padding: 8px 16px;
    font-size: 14px;
  }
  .dt-frequently-bought-together .dt-sc-thumb-image {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
  }
}
@media (max-width: 430px) {
  .dt-frequently-bought-together .dt-sc-thumb-image ul {
    text-align: center;
  }
  .dt-frequently-bought-together .dt-sc-thumb-image > div {
    grid-template-columns: repeat(1, 1fr);
  }
}
.dt-frequently-bought-together.content-container{    border: 1px solid rgba(var(--color-foreground), var(--text-boxes-border-opacity));}
.dt-frequently-bought-together .dt-sc-thumb-image li:first-child {
    display: none;
}