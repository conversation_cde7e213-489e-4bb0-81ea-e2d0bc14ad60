{% comment %}
    Renders an article card for a given blog with settings to either show the image or not.

    Accepts:
    - blog: {Object} Blog object
    - article: {Object} Article object
    - media_aspect_ratio: {String} The setting changes the aspect ratio of the article image, if shown
    - media_height: {String} The setting changes the height of the article image. Overrides media_aspect_ratio.
    - show_image: {String} The setting either show the article image or not. If it's not included it will show the image by default
    - show_date: {String} The setting either show the article date or not. If it's not included it will not show the image by default
    - show_author: {String} The setting either show the article author or not. If it's not included it will not show the author by default
    - show_badge: {String} The setting either show the blog badge or not.
    - lazy_load: {Boolean} Image should be lazy loaded. Default: true (optional)

    Usage:
    {% render 'article-card' blog: blog, article: article, show_image: section.settings.show_image %}
{% endcomment %}

{%- if article and article != empty -%}
  {%- liquid
    assign ratio = 1
    if media_aspect_ratio != nil
      assign ratio = media_aspect_ratio
    endif
  -%}
  <div class="card-wrapper underline-links-hover">
    {% comment %} <article aria-labelledby="Article-{{ article.id }}"> {% endcomment %}
    <div class="card article-card  
      card--{{ settings.card_style }}
      {% if media_height and media_height != 'adapt' %} article-card__image--{{ media_height }}{% endif %}
      {% if article.image and show_image %} card--media{% else %} card--text{% endif %}
      {% if settings.card_style == 'card' %} color-{{ settings.card_color_scheme }} gradient{% endif %}
      {% if settings.card_style == 'card' and media_height == nil and article.image == empty or show_image == false %} ratio{% endif %}"
      style="--ratio-percent: {{ 1 | divided_by: ratio | times: 100 }}%;"
    >
     <div class="card__inner {% if settings.card_style == 'standard' %} color-{{ settings.card_color_scheme }} gradient{% endif %}{% if article.image and show_image or settings.card_style == 'standard' %} ratio{% endif %}" style="--ratio-percent: {{ 1 | divided_by: ratio | times: 100 }}%;">
        {%- if show_image == true and article.image -%}
           <div class="article-card__image-wrapper card__media">
             <div class="article-card__image media media--hover-effect {{ settings.overlay_color_scheme }}" {% if section.settings.media_height == 'adapt' %} style="padding-bottom: {{ 1 | divided_by: article.image.aspect_ratio | times: 100 }}%;"{% endif %}>
              {% comment %}theme-check-disable ImgLazyLoading{% endcomment %}
                <a href="{{ article.url }}" class="full-unstyled-link"><img
                srcset="{%- if article.image.src.width >= 165 -%}{{ article.image.src | image_url: width: 165 }} 165w,{%- endif -%}
                  {%- if article.image.src.width >= 360 -%}{{ article.image.src | image_url: width: 360 }} 360w,{%- endif -%}
                  {%- if article.image.src.width >= 533 -%}{{ article.image.src | image_url: width: 533 }} 533w,{%- endif -%}
                  {%- if article.image.src.width >= 720 -%}{{ article.image.src | image_url: width: 720 }} 720w,{%- endif -%}
                  {%- if article.image.src.width >= 1000 -%}{{ article.image.src | image_url: width: 1000 }} 1000w,{%- endif -%}
                  {%- if article.image.src.width >= 1500 -%}{{ article.image.src | image_url: width: 1500 }} 1500w,{%- endif -%}
                  {{ article.image.src | image_url }} {{ article.image.src.width }}w"
                src="{{ article.image.src | image_url: width: 1500 }}"
                sizes="(min-width: {{ settings.page_width }}px) {{ settings.page_width | minus: 100 | divided_by: 2 }}px, (min-width: 750px) calc((100vw - 130px) / 2), calc((100vw - 50px) / 2)"
                alt="{{ article.image.src.alt | escape }}"
                class="motion-reduce  loading-image article__image"
                {% unless lazy_load == false %}loading="lazy"{% endunless %}
                width="{{ article.image.width }}"
                height="{{ article.image.height }}" /*style="--aspect-ratio: 3 / 1;"*/
              ></a>
              {% comment %}theme-check-enable ImgLazyLoading{% endcomment %}
            </div>
             <div class="read-more-btn">
                 <a href="{{ article.url }}" class="blog__button button ">Read More</a> 
              </div>
           </div>
        {%- endif -%}       
       </div>
      
      <div class="card__content  for-arrow-alignment">
        <div class="card__information">
           <div class="article-card__info caption-with-letter-spacing h5">
            
          {%- if show_date -%}
          <span class="article-date">{% render 'icon-calender' %} {{ article.published_at | date: "%b %d" }}</span>
          {%- endif -%}
          {%- if show_author -%}
          <span class="article-author">{% render 'user-icon' %} {{ article.author }}</span>
          {%- endif -%}
          {%- if article.comments_count > 0 and blog.comments_enabled? -%}
          {%- if show_comment -%}
          <span class="comment-icon">{% render 'comment-icon' %} {{ 'blogs.article.comments' | t: count: article.comments_count }}</span>
          {%- endif -%}
          {%- endif -%}
          </div>
          <h3 class="card__heading{% if show_excerpt %} h2{% endif %}">
            <a href="{{ article.url }}" class="full-unstyled-link">
              {{ article.title | truncate: 50 | escape }}
            </a>
          </h3>   
          {%- if show_excerpt -%}
            {%- if article.excerpt.size > 0 or article.content.size > 0 -%}
              <p class="article-card__excerpt rte-width">
                {%- if article.excerpt.size > 0 -%}
                  {{ article.excerpt | strip_html | truncatewords: 12 }}
                {%- else -%}
                  {{ article.content | strip_html | truncatewords: 12 }}
                {%- endif -%}
              </p>
            {%- endif -%}
            {%- endif -%}
        </div>
        
        
        {%- if show_badge -%}
          <div class="card__badge {{ settings.badge_position }}">
            <span class="badge color-background-1">{{ 'blogs.article.blog' | t }}</span>
          </div>
        {%- endif -%}  
      </div>
    </div>
  </div>
{%- endif -%}
