<div id="shopify-section-announcement-bar" class="shopify-section">
<div class="announcement-bar-wrapper marquee color-background-1 gradient  ">
<div class="marquee__content">
 <div class="marquee_annoucement list-inline"><div class="announcement-bar " role="region" aria-label="Announcement"><p class="announcement-bar__message h5">
                Free Shipping on Orders over $140
              </p></div><div class="announcement-bar " role="region" aria-label="Announcement"><p class="announcement-bar__message h5">
                40% OFF Store Wide Use Code : BOOM
              </p></div><div class="announcement-bar " role="region" aria-label="Announcement"><p class="announcement-bar__message h5">
                One Day Delivery Available in US
              </p></div></div>
   <div class="marquee_annoucement list-inline"><div class="announcement-bar  " role="region" aria-label="Announcement"><p class="announcement-bar__message h5">
                Free Shipping on Orders over $140
              </p></div><div class="announcement-bar  " role="region" aria-label="Announcement"><p class="announcement-bar__message h5">
                40% OFF Store Wide Use Code : BOOM
              </p></div><div class="announcement-bar  " role="region" aria-label="Announcement"><p class="announcement-bar__message h5">
                One Day Delivery Available in US
              </p></div></div>
  <div class="marquee_annoucement list-inline"><div class="announcement-bar  " role="region" aria-label="Announcement"><p class="announcement-bar__message h5">
                Free Shipping on Orders over $140
              </p></div><div class="announcement-bar  " role="region" aria-label="Announcement"><p class="announcement-bar__message h5">
                40% OFF Store Wide Use Code : BOOM
              </p></div><div class="announcement-bar  " role="region" aria-label="Announcement"><p class="announcement-bar__message h5">
                One Day Delivery Available in US
              </p></div></div>
</div>
</div>
   
<style data-shopify="">@media screen and (max-width: 990px) {
   }
  .announcement-bar-wrapper{display: flex;width: 100%;justify-content: space-between;}

  .marquee {
  margin: 0 auto;
  width: 100%;
  height: 35px;
  white-space: nowrap;
  overflow: hidden;
  box-sizing: border-box;
  position: relative;
}
  .marquee:before {
  left: 0;
  background: linear-gradient(to right, white 5%, transparent 100%);
}
.marquee:after {
  right: 0;
  background: linear-gradient(to left, white 5%, transparent 100%);
}
.marquee:before, .marquee:after {
   position: absolute;
  top: 0;
  width: 300px;
  height: 30px;
  content: "";
  z-index: 1;
}

.marquee__content {
  width: 300% !important;
  display: flex;
  line-height: 30px;
  justify-content: center;
  -webkit-animation: marquee 30s linear infinite forwards;
          animation: marquee 30s linear infinite forwards;
}
.marquee__content:hover {
  -webkit-animation-play-state: paused;
          animation-play-state: paused;
}

.list-inline {
  display: flex;
  justify-content: space-around;
  /* reset list */
  list-style: none;
  padding: 0;
  margin: 0;
}

@-webkit-keyframes marquee {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-66.6%);
  }
}

@keyframes marquee {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-66.6%);
  }
}

@media screen and (min-width: 1200px){
.announcement-bar__message{margin:0 280px;}
}
@media screen (max-width:1199px) and (min-width: 768px){
.announcement-bar__message{margin:0 100px;}
}  
@media screen and (max-width: 767px){
.announcement-bar__message{margin:0 50px;}
}  

body.overflow-hidden #shopify-section-announcement-bar, body.overflow-hidden-mobile #shopify-section-announcement-bar{ z-index: 0;}
  body.overflow-hidden-tablet #shopify-section-announcement-bar { z-index: 0;  display: none;}</style>
</div>