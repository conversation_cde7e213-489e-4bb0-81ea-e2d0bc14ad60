    <div class="details-modal" id="login-modal__window">
      <div class="detial-login">
      <div class="details-modal-close">
        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" >
          <path fill-rule="evenodd" clip-rule="evenodd" d="M13.7071 1.70711C14.0976 1.31658 14.0976 0.683417 13.7071 0.292893C13.3166 -0.0976311 12.6834 -0.0976311 12.2929 0.292893L7 5.58579L1.70711 0.292893C1.31658 -0.0976311 0.683417 -0.0976311 0.292893 0.292893C-0.0976311 0.683417 -0.0976311 1.31658 0.292893 1.70711L5.58579 7L0.292893 12.2929C-0.0976311 12.6834 -0.0976311 13.3166 0.292893 13.7071C0.683417 14.0976 1.31658 14.0976 1.70711 13.7071L7 8.41421L12.2929 13.7071C12.6834 14.0976 13.3166 14.0976 13.7071 13.7071C14.0976 13.3166 14.0976 12.6834 13.7071 12.2929L8.41421 7L13.7071 1.70711Z" />
        </svg>
      </div>
               
 {%- form 'customer_login', novalidate: 'novalidate' -%}
      {%- if form.errors -%}
        <h2 class="form__message" tabindex="-1" autofocus>
          <span class="visually-hidden">{{ 'accessibility.error' | t }} </span>
          <svg aria-hidden="true" focusable="false" role="presentation" viewBox="0 0 13 13">
            <circle cx="6.5" cy="6.50049" r="5.5" stroke="white" stroke-width="2"/>
            <circle cx="6.5" cy="6.5" r="5.5" fill="#EB001B" stroke="#EB001B" stroke-width="0.7"/>
            <path d="M5.87413 3.52832L5.97439 7.57216H7.02713L7.12739 3.52832H5.87413ZM6.50076 9.66091C6.88091 9.66091 7.18169 9.37267 7.18169 9.00504C7.18169 8.63742 6.88091 8.34917 6.50076 8.34917C6.12061 8.34917 5.81982 8.63742 5.81982 9.00504C5.81982 9.37267 6.12061 9.66091 6.50076 9.66091Z" fill="white"/>
            <path d="M5.87413 3.17832H5.51535L5.52424 3.537L5.6245 7.58083L5.63296 7.92216H5.97439H7.02713H7.36856L7.37702 7.58083L7.47728 3.537L7.48617 3.17832H7.12739H5.87413ZM6.50076 10.0109C7.06121 10.0109 7.5317 9.57872 7.5317 9.00504C7.5317 8.43137 7.06121 7.99918 6.50076 7.99918C5.94031 7.99918 5.46982 8.43137 5.46982 9.00504C5.46982 9.57872 5.94031 10.0109 6.50076 10.0109Z" fill="white" stroke="#EB001B" stroke-width="0.7">
          </svg>
          {{ 'templates.contact.form.error_heading' | t }}
        </h2>
        {{ form.errors | default_errors }}
      {%- endif -%}
   
  <h1 id="customer--login" tabindex="-1">
        {{ 'customer.login_page.title' | t }}
  </h1>
    
      <div class="field field--with-error">  
          <label for="CustomerEmail">
          {{ 'customer.login_page.email' | t }}
        </label>
        <input
          type="email"
          name="customer[email]"
          id="CustomerEmail"
          autocomplete="email"
          autocorrect="off"
          autocapitalize="off"
          {% if form.errors contains 'form' %}
            aria-invalid="true"
          {% endif %}
          placeholder="{{ 'customer.login_page.email' | t }}" required
        >
      
      </div>

      {%- if form.password_needed -%}
        <div class="field field--with-error">   
         <label for="CustomerPassword">
            {{ 'customer.login_page.password' | t }}
          </label>
          <input
            type="password"
            value=""
            name="customer[password]"
            id="CustomerPassword"
            autocomplete="current-password"
            {% if form.errors contains 'form' %}
              aria-invalid="true"
            {% endif %}
            placeholder="{{ 'customer.login_page.password' | t }}" required
          >
        </div>
        
      {%- endif -%}

      <div class="link-wrapper">
      <a href="{{ routes.account_login_url }}#recover">
          {{ 'customer.login_page.forgot_password' | t }}
        </a> 
      <a href="{{ routes.account_register_url }}">
        {{ 'customer.login_page.create_account' | t }}
      </a>
      </div>          
       <button class="button button--primary">
        {{ 'customer.login_page.sign_in' | t }}
      </button>        
    {%- endform -%}
            </div>  
    </div>


              <style>
 .detial-login h1#customer--login{margin:0; margin-bottom: 2rem;}              
 .details-modal {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 3;
    display: none;
    opacity: 0;
    visibility: hidden;
    background: rgba(var(--color-foreground), 0.6);
}
   .detial-login {
    position: relative;
    z-index: 3;
    background: white;
    max-width: 510px;
    margin: auto;
    max-height: 100%;
    width: 100%;
    padding: 40px 30px;
    text-align: center;
}
.details-modal.login-modal-active {
  display:flex;
  opacity: 1;
  visibility: visible;
}
.detial-login .link-wrapper a{
  position:relative;
}                
.detial-login .link-wrapper a:hover{
   color: rgb(var(--color-base-outline-button-labels));
}                
.details-modal .details-modal-close {
  align-items: center;
  color: #111827;
  display: flex;
  height: 3rem;
  justify-content: center;
  cursor: pointer;
  position: absolute;
  right: 10px;
  top: 10px;
  width: 3rem;
  z-index:5;
}
.details-modal .details-modal-close svg {
  display: block; transition:all 0.3s linear;
}
.details-modal .details-modal-title {
  color: #111827;
  padding: 1.5em 2em;
  pointer-events: all;
  position: relative;
  width: calc(100% - 4.5em);
}
.details-modal .details-modal-title h1 {
  font-size: 1.25rem;
  font-weight: 600;
  line-height: normal;
}
.details-modal .details-modal-content {
  border-top: 1px solid #e0e0e0;
  padding: 2em;
  pointer-events: all;
  overflow: auto;
}

.details-modal-overlay {
  transition: opacity 0.2s ease-out;
  pointer-events: none;
  background: rgba(15, 23, 42, 0.8);
  position: fixed;
  opacity: 0;
  bottom: 0;
  right: 0;
  left: 0;
  top: 0;
}
details[open] .details-modal-overlay {
  pointer-events: all;
  opacity: 0.5;
}
.detial-login .field input{
      line-height: 50px;
    height: 50px;
    padding: 0 20px;
    border: 1px solid #e5e5e5;
    width: 100%;
    font-family: var(--font-body-family);
}
 .detial-login .field label{display:none;}               
.detial-login .field {margin-bottom:2rem;}   
.detial-login  .link-wrapper{display:flex; justify-content:space-between; margin-top:2rem; margin-bottom: 2rem;}  
.detial-login .field input:focus-visible { outline: none; box-shadow: none; outline-offset: none;}   
.details-modal .details-modal-close:hover svg{fill:rgb(var(--color-base-outline-button-labels));}                
 </style>
  