{% if section.settings.display_back_in_stock %}
<div class="notify-block dT_ProdNotify" data-section-type="notify"  id="notify-block-{{section.id}}" {% if product.variants[0].available %} style="display:none" {% endif %}>
    <div id="sold-out">
    {% form 'contact' %}
    {% if form.posted_successfully? %}
    <p class="accent-text"> {{ section.settings.notify_text }}</p>
    {% else %}
      
    <a id="notify-me" href="#">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200">
       <circle class="hand-tap-2" cx="89.43" cy="69.55" r="29.61"/>
       <circle class="hand-tap-1" cx="89.43" cy="69.55" r="19.46"/>
       <path class="hand-double" d="M139.93,107.76l-41.12-6.93V70.28A9.25,9.25,0,0,0,89.56,61h0a9.25,9.25,0,0,0-9.25,9.25v57.36L71,116.85c-3.61-3.61-8.44-3.89-13.08,0,0,0-7.24,5.84-3.83,9.25l34,34h42.63a9.25,9.25,0,0,0,9.07-7.43l6.82-34.09A9.28,9.28,0,0,0,139.93,107.76Z"/>
     </svg>
      <strong>{{ 'products.notify.text1' | t }}</strong></a>
      <p class="notify-text">{{ 'products.notify.text2' | t }} {{ product.title }} {{ 'products.notify.text3' | t }}.</p>
    {% endif %}
    {% if form.errors %}
    <div class="error feedback accent-text">
      <p>{{ section.settings.notify_error }}</p>
    </div>
    {% endif %}
    {% unless form.posted_successfully? %}
    <div id="notify-me-wrapper" class="clearfix" style="display:none">
      {% if customer %}
      <input type="hidden" name="contact[email]" value="{{ customer.email }}" />
      {% else %}
      <input required="required" type="email" name="contact[email]" placeholder="<EMAIL>" class="styled-input{% if form.errors contains 'email' %} error{% endif %}" value="{{ contact.fields.email }}" />
      {% endif %}
      <input type="hidden" name="contact[body]" value="Please notify me when {{ product.title | escape }} becomes available." />
      <input class="dt-sc-btn styled-submit" type="submit" value="Send" />
    </div>
    {% endunless %}
    {% endform %}
  </div>
</div>
{% endif %}
<style>
  .hand-tap-2,
  .hand-tap-1{
    fill: transparent;
	stroke: rgb(var(--color-link));
	stroke-width: 3px;
	stroke-linecap: round;
	stroke-linejoin: round;
  }
  .hand-tap-2{
    opacity: .25;
  }
  .hand-tap-1{
    opacity: .5;
  }
  .hand-double{
	fill: #fff;
	stroke: rgb(var(--color-link));
	stroke-width: 3px;
	stroke-linecap: round;
	stroke-linejoin: round;
}
.hand-double {
	animation: tap-double 1.25s ease-out backwards;
	animation-iteration-count:infinite;
}

.hand-tap-2,
.hand-tap-1 {
	animation: tap-circle 1.25s ease-out backwards;
	animation-iteration-count:infinite;
}  
  .hand-tap-2 {
	animation-delay: 0.2s;
}

@keyframes tap-double {
	0% {
		transform: rotateX(0deg);
	}
	10% {
		transform: rotateX(12.5deg);
	}
	25% {
		transform: rotateX(25deg);
	}
	35% {
		transform: rotateX(10deg);
	}
	50% {
		transform: rotateX(25deg);
	}
}
  @keyframes tap-circle {
	0% {
		transform: scale(0);
		opacity: 0;
	}
	75% {
		transform: scale(1.05);
		opacity: .6;
	}
	80% {
		transform: scale(1);
		opacity: .5;
	}
}
  #notify-me svg{display:inline-block;width:40px;height:40px;}
  #notify-me{color:rgb(var(--color-link)); display: flex;align-items: center;}
  #sold-out p.notify-text {margin: 0px;}
</style>