.lush06-support-block .support-block-list__item.grid__item .support-block-card__image-wrapper{
  border:none;
}
.lush06-support-block .support-block-list__item, .lush06-support-block .grid--4-col-desktop .support-block-list__item{
  background:transparent;
}
.support-block-demo .section-template--21701471240498__dc0a4aad-bf52-40df-bcae-8dff429e05c1-padding.isolate .slider-mobile-gutter .support-block-list{border:none;}
.support-block.support-block-demo.color-background-1.gradient{
  background:rgb(225, 104, 11);
}
.lush06-support-block .support-block.no-heading .title {display:none;}
.lush06-support-block .support-block .title-wrapper-with-link {margin-top:0;}
.lush06-support-block .grid{column-gap:0;justify-content:space-between;}
.lush06-support-block .support-block-list__item, .lush06-support-block .grid--4-col-desktop .support-block-list__item{width:fit-content;max-width:fit-content;}
@media screen and (max-width: 749px) {
.lush06-support-block .support-block .title-wrapper-with-link {margin-bottom:3rem;}
}

.lush06-support-block .support-block-card__image-wrapper--third-width {width:33%;}
.lush06-support-block .support-block-card__image-wrapper--half-width {width:50%;}
.lush06-support-block .support-block-list__item.center .support-block-card__image-wrapper:not(.support-block-card__image-wrapper--full-width), .lush06-support-block .support-block-list__item:only-child {margin-left:auto; margin-right:auto;}
.lush06-support-block .support-block-list {margin-top:0; margin-bottom:0; padding:0;}
.lush06-support-block .support-block-list__item:only-child {max-width:72rem;}
.lush06-support-block .support-block-list__item--empty {display:none;}
.lush06-support-block .support-block:not(.background-none) .support-block-card {height:100%;}
.lush06-support-block .support-block-list .support-title {line-height:calc(1 + 0.5 / max(1, var(--font-heading-scale))); font-size:2rem; font-weight:500;}
.lush06-support-block .support-block-list .support-title {margin:0;}
.lush06-support-block .support-block-list p {margin:0;}
.lush06-support-block .support-block-card-spacing {padding-top:2.5rem; margin-left:2.5rem; margin-right:2.5rem;}
.lush06-support-block .support-block-card__info >:nth-child(2) {margin-top:1rem;}
.lush06-support-block .support-block-card__info .rte {margin:0;}
.lush06-support-block .support-block-list__item.center .media--adapt, .lush06-support-block .support-block-list__item .media--adapt .support-block-card__image {width:auto;}
.lush06-support-block .support-block-list__item.center .media--adapt img {left:50%; transform:translateX(-50%);}

@media screen and (max-width: 749px) {
.lush06-support-block .support-block-list {margin:0; width:100%;}
.lush06-support-block .support-block-list:not(.slider) {padding-left:1.5rem; padding-right:1.5rem;}
}

@media screen and (min-width: 750px) {
.lush06-support-block .support-block-list.slider, .lush06-support-block .support-block-list.grid--4-col-desktop {padding:0;}
.lush06-support-block .support-block-list__item, .lush06-support-block .grid--4-col-desktop .support-block-list__item {padding-bottom:0;}
.lush06-support-block .background-none .grid--2-col-tablet .support-block-list__item {margin-top:4rem;}
}

.lush06-support-block .background-none .support-block-card-spacing {padding:0; margin:0;}
.lush06-support-block .background-none .support-block-card__info {padding-top:0; padding-left:0; padding-right:0;}
.lush06-support-block .background-none .slider .support-block-card__info {padding-bottom:0;}
.lush06-support-block .background-none .support-block-card__image-wrapper + .support-block-card__info {padding-top:2.5rem;}
.lush06-support-block .background-none .slider .support-block-card__info {padding-left:0.5rem;}
.lush06-support-block .background-none .slider .support-block-card__image-wrapper + .support-block-card__info {padding-left:1.5rem;}
.lush06-support-block .background-none .support-block-list:not(.slider) .center .support-block-card__info {padding-left:2.5rem; padding-right:2.5rem;}

@media screen and (max-width: 749px) {
.lush06-support-block .background-none .slider .support-block-card__info {padding-bottom:1rem;}
.lush06-support-block .support-block.background-none .slider.slider--mobile {margin-bottom:0rem;}
}

@media screen and (min-width: 750px) {
.lush06-support-block .background-none .support-block-card__image-wrapper {margin-left:1.5rem; margin-right:1.5rem;}
.lush06-support-block .background-none .support-block-list .support-block-card__info, .lush06-support-block .background-none .support-block-list:not(.slider) .center .support-block-card__info {padding-left:1.5rem; padding-right:1.5rem;}
}

.lush06-support-block .support-block-card {position:relative; box-sizing:border-box;}
.lush06-support-block .support-block-card > .support-block-card__image-wrapper--full-width:not(.support-block-card-spacing) {border-top-left-radius:calc(var(--text-boxes-radius) - var(--text-boxes-border-width)); border-top-right-radius:calc(var(--text-boxes-radius) - var(--text-boxes-border-width)); overflow:hidden;}
.lush06-support-block .support-block.background-none .support-block-card {border-radius:0;}
.lush06-support-block .support-block-card__info .link {text-decoration:none; font-size:inherit; margin-top:1.5rem;}
.lush06-support-block .support-block-card__info .icon-wrap {margin-left:0.8rem; white-space:nowrap;}

@media screen and (min-width: 990px) {
.lush06-support-block .support-block-list__item--empty {display:list-item;}
}

.lush06-support-block .support-block-card .support-block-card__image-wrapper img {width:100%; height:100%; object-fit:contain;}
.lush06-support-block .support-block-card .support-block-card__image-wrapper {display:flex; justify-content:center; align-items:center;}
/* .lush06-support-block .support-block-list__item .support-block-card {padding:2.5rem;} */
.lush06-support-block .support-block-list__item.grid__item .support-block-card__info {padding-top:0rem;}
.lush06-support-block .support-block-list__item.grid__item .support-block-card__image-wrapper {padding-bottom:0rem;}
.lush06-support-block .support-block-list__item.list__item .support-block-card {display:inline-flex; align-items:center;}
.lush06-support-block .support-block-list__item.list__item .support-block-card.veritcal_top {align-items:flex-start;}
.lush06-support-block .support-block-list__item.list__item .support-block-card.veritcal_bottom {align-items:flex-end;}
.lush06-support-block .support-block-list__item.list__item .support-block-card .support-block-card__info {padding:0 20px;color: #fff;font-size:22px;}
.lush06-support-block .support-block-list__item {list-style:none;}
.lush06-support-block .support-block-card .support-block-card__image-wrapper .fa {font-size:34px;}
.lush06-support-block .support-block-list__item.list__item .support-title {margin:0;}
support-slider {cursor:grab;}

@media screen and (max-width: 1199px) {
.lush06-support-block .support-block-list.grid--4-col-desktop .grid__item {width:calc(25% - var(--grid-desktop-horizontal-spacing) * 3 / 4); max-width:calc(25% - var(--grid-desktop-horizontal-spacing) * 3 / 4);}
.lush06-support-block .support-block-list__item .support-block-card {padding:0;}
.lush06-support-block .support-block-list.slider, .support-block-list.grid--4-col-desktop {padding:2.5rem 0;}
}

@media screen and (max-width: 990px) {
.lush06-support-block .support-block-list.grid--1-col-tablet-down.grid--peek .grid__item {width:calc(50% - var(--grid-desktop-horizontal-spacing) / 2); max-width:calc(50% - var(--grid-desktop-horizontal-spacing) / 2);}
.lush06-support-block .support-block-list .support-title {font-size:1.8rem;}
}

@media screen and (max-width: 749px) {
.lush06-support-block .support-block-list.grid--1-col-tablet-down.grid--peek .grid__item {width:calc(50% - var(--grid-mobile-horizontal-spacing) / 2); max-width:calc(50% - var(--grid-mobile-horizontal-spacing) / 2);}
}

@media screen and (max-width: 575px) {
.lush06-support-block .support-block-list__item.list__item .support-block-card .support-block-card__info {text-align:center;}
.lush06-support-block .support-block-list__item.list__item .support-block-card {flex-direction:column;}
.lush06-support-block .support-block-list__item.list__item .support-block-card{display:flex;}
.lush06-support-block .support-block-list .support-title {font-size:1.6rem;}
.lush06-support-block .support-block-list.slider, .support-block-list.grid--4-col-desktop {padding:2.5rem 0;}
.lush06-support-block .support-block-card__info .rte p{margin:20px 0;}
}

@media screen and (max-width: 480px) {
  .lush06-support-block .support-block-list.grid--1-col-tablet-down.grid--peek .grid__item {
    width: calc(100% - var(--grid-mobile-horizontal-spacing) / 2);
    max-width: calc(100% - var(--grid-mobile-horizontal-spacing) / 2);
}
  .lush06-support-block .support-block-card__info .rte p{margin:0;}
}

@media screen and (max-width: 1440px) {
.lush06-support-block .support-block-list .support-title {font-size:1.6rem;}
}

.lush06-support-block .slider__slide .slideshow__text-wrapper.page-width {max-width:100%;}
slider-component {--desktop-margin-left-first-item:max(5rem, calc((100vw - var(--page-width) + 10rem - var(--grid-desktop-horizontal-spacing)) / 2)); position:relative; display:block;}
slider-component.slider-component-full-width {--desktop-margin-left-first-item:1.5rem;}

@media screen and (max-width: 749px) {
slider-component.page-width {padding:0 1.5rem;}
}

@media screen and (min-width: 749px) and (max-width:990px) {
slider-component.page-width {padding:0 5rem;}
}

@media screen and (max-width: 989px) {
.lush06-support-block .no-js slider-component .slider {padding-bottom:3rem;}
}

.lush06-support-block .slider__slide {--focus-outline-padding:0.5rem; --shadow-padding-top:calc(var(--shadow-vertical-offset) * -1 + var(--shadow-blur-radius)); --shadow-padding-bottom:calc(var(--shadow-vertical-offset) + var(--shadow-blur-radius)); scroll-snap-align:start; flex-shrink:0; padding-bottom:0;}

@media screen and (max-width: 749px) {
.lush06-support-block .slider.slider--mobile {position:relative; flex-wrap:inherit; overflow-x:auto; scroll-snap-type:x mandatory; scroll-behavior:smooth; scroll-padding-left:0rem; -webkit-overflow-scrolling:touch; margin-bottom:1rem;}
.lush06-support-block .slider.slider--mobile .slider__slide {margin-bottom:0; padding-top:max(var(--focus-outline-padding), var(--shadow-padding-top)); padding-bottom:max(var(--focus-outline-padding), var(--shadow-padding-bottom));}
.lush06-support-block .slider.slider--mobile.contains-card--standard .slider__slide:not(.collection-list__item--no-media) {padding-bottom:var(--focus-outline-padding);}
.lush06-support-block .slider.slider--mobile.contains-content-container .slider__slide {--focus-outline-padding:0rem;}
}

@media screen and (min-width: 750px) {
.lush06-support-block .slider.slider--tablet-up {position:relative; flex-wrap:inherit; overflow-x:auto; scroll-snap-type:x mandatory; scroll-behavior:smooth; -webkit-overflow-scrolling:touch;}
.lush06-support-block .slider.slider--tablet-up .slider__slide {margin-bottom:0;}
}

@media screen and (max-width: 989px) {
.lush06-support-block .slider.slider--tablet {position:relative; flex-wrap:inherit; overflow-x:auto; scroll-snap-type:x mandatory; scroll-behavior:smooth; -webkit-overflow-scrolling:touch; margin-bottom:1rem;}
.lush06-support-block .slider.slider--tablet .slider__slide {margin-bottom:0; padding-top:max(var(--focus-outline-padding), var(--shadow-padding-top)); padding-bottom:max(var(--focus-outline-padding), var(--shadow-padding-bottom));}
.lush06-support-block .slider.slider--tablet.contains-card--standard .slider__slide:not(.collection-list__item--no-media) {padding-bottom:var(--focus-outline-padding);}
.lush06-support-block .slider.slider--tablet.contains-content-container .slider__slide {--focus-outline-padding:0rem;}
}

.lush06-support-block .slider--everywhere {position:relative; flex-wrap:inherit; overflow-x:auto; scroll-snap-type:x mandatory; scroll-behavior:smooth; -webkit-overflow-scrolling:touch; margin-bottom:1rem;}
.lush06-support-block .slider.slider--everywhere .slider__slide {margin-bottom:0; scroll-snap-align:center;}

@media screen and (min-width: 990px) {
.lush06-support-block .slider-component-desktop.page-width {max-width:none;}
.lush06-support-block .slider--desktop {position:relative; flex-wrap:inherit; overflow-x:auto; scroll-snap-type:x mandatory; scroll-behavior:smooth; -webkit-overflow-scrolling:touch; margin-bottom:1rem;}
.lush06-support-block .slider.slider--desktop .slider__slide {margin-bottom:0; padding-top:max(var(--focus-outline-padding), var(--shadow-padding-top)); padding-bottom:max(var(--focus-outline-padding), var(--shadow-padding-bottom));}
.lush06-support-block .slider--desktop .slider__slide:first-child {margin-left:var(--desktop-margin-left-first-item); scroll-margin-left:var(--desktop-margin-left-first-item);}
.lush06-support-block .slider.slider--desktop .slider__slide:last-child {margin-right:5rem;}
.lush06-support-block .slider-component-full-width .slider--desktop .slider__slide:first-child {margin-left:1.5rem; scroll-margin-left:1.5rem;}
.lush06-support-block .slider-component-full-width .slider--desktop .slider__slide:last-child {margin-right:1.5rem;}
.lush06-support-block .slider--desktop.grid--5-col-desktop .grid__item {width:calc( (100% - var(--desktop-margin-left-first-item)) / 5 - var(--grid-desktop-horizontal-spacing) * 2);}
.lush06-support-block .slider--desktop.grid--4-col-desktop .grid__item {width:calc( (100% - var(--desktop-margin-left-first-item)) / 4 - var(--grid-desktop-horizontal-spacing) * 3);}
.lush06-support-block .slider--desktop.grid--3-col-desktop .grid__item {width:calc( (100% - var(--desktop-margin-left-first-item)) / 3 - var(--grid-desktop-horizontal-spacing) * 4);}
.lush06-support-block .slider--desktop.grid--2-col-desktop .grid__item {width:calc( (100% - var(--desktop-margin-left-first-item)) / 2 - var(--grid-desktop-horizontal-spacing) * 5);}
.lush06-support-block .slider--desktop.grid--1-col-desktop .grid__item {width:calc( (100% - var(--desktop-margin-left-first-item)) - var(--grid-desktop-horizontal-spacing) * 9);}
.lush06-support-block .slider.slider--desktop.contains-card--standard .slider__slide:not(.collection-list__item--no-media) {padding-bottom:var(--focus-outline-padding);}
.lush06-support-block .slider.slider--desktop.contains-content-container .slider__slide {--focus-outline-padding:0rem;}
}

@media (prefers-reduced-motion) {
.lush06-support-block .slider {scroll-behavior:auto;}
}

@media screen and (min-width:1440px) {
.lush06-support-block .slideshow__text .banner__heading {font-size:calc(var(--font-heading-scale) * 6.3rem);}
}

.lush06-support-block .slider {scrollbar-color:rgb(var(--color-foreground)) rgba(var(--color-foreground), 0.04); -ms-overflow-style:none; scrollbar-width:none;}
.lush06-support-block .slider::-webkit-scrollbar {height:0.4rem; width:0.4rem; display:none;}
.lush06-support-block .no-js .slider {-ms-overflow-style:auto; scrollbar-width:auto;}
.lush06-support-block .no-js .slider::-webkit-scrollbar {display:initial;}
.lush06-support-block .slider::-webkit-scrollbar-thumb {background-color:rgb(var(--color-foreground)); border-radius:0.4rem; border:0;}
.lush06-support-block .slider::-webkit-scrollbar-track {background:rgba(var(--color-foreground), 0.04); border-radius:0.4rem;}
.lush06-support-block .slider-counter {display:flex; justify-content:center; min-width:4.4rem;}
.lush06-support-block .slider-counter span {display:none;}

@media screen and (min-width: 750px) {
.lush06-support-block .slider-counter--dots {margin:0 0rem;}
}

.lush06-support-block .slider-counter__link {padding:1rem;}

@media screen and (max-width: 749px) {
.lush06-support-block .slider-counter__link {padding:0.7rem;}
}

.lush06-support-block .slider-counter__link--dots .dot {width:1rem; height:1rem; border-radius:50%; border:0.1rem solid rgba(var(--color-foreground), 0.5); padding:0; display:block;}
.lush06-support-block .slider-counter__link--active.slider-counter__link--dots .dot {background-color:rgb(var(--color-foreground));}

@media screen and (forced-colors: active) {
.lush06-support-block .slider-counter__link--active.slider-counter__link--dots .dot {background-color:CanvasText;}
}

.lush06-support-block .slider-counter__link--dots:not(.slider-counter__link--active):hover .dot {border-color:rgb(var(--color-foreground));}
.lush06-support-block .slider-counter__link--dots .dot, .slider-counter__link--numbers {transition:transform 0.2s ease-in-out;}
.lush06-support-block .slider-counter__link--active.slider-counter__link--numbers, .slider-counter__link--dots:not(.slider-counter__link--active):hover .dot, .slider-counter__link--numbers:hover {transform:scale(1.1);}
.lush06-support-block .slider-counter__link--numbers {color:rgba(var(--color-foreground), 0.5); text-decoration:none;}
.lush06-support-block .slider-counter__link--numbers:hover {color:rgb(var(--color-foreground));}
.lush06-support-block .slider-counter__link--active.slider-counter__link--numbers {text-decoration:underline; color:rgb(var(--color-foreground));}
.lush06-support-block .slider-buttons {display:flex; align-items:center; justify-content:center;}
.lush06-support-block .slider-counter--numbers .slider-buttons {position:absolute; top:50%; width:100%; height:var(--swiper-navigation-size); margin-top:calc(0px - (var(--swiper-navigation-size)/ 2)); z-index:2; cursor:pointer; display:flex; align-items:center; justify-content:center; color:red; transform:translateY(-50%);}
.lush06-support-block .slider-counter--numbers button.slider-button.slider-button--prev {left:10px; right:auto; position:absolute;}
.lush06-support-block .slider-counter--numbers button.slider-button.slider-button--next {right:10px; left:auto; position:absolute;}
.lush06-support-block .slider-counter--dots .slideshow__controls.slider-buttons {position:absolute; bottom:40px; margin:auto; left:0; right:0; z-index:4;}
.lush06-support-block .slider-counter--counter .slideshow__controls.slider-buttons {position:absolute; bottom:40px; margin:auto; left:0; right:0; z-index:2;}

@media screen and (min-width: 990px) {
.lush06-support-block .slider:not(.slider--everywhere):not(.slider--desktop) + .slider-buttons {display:none;}
}

@media screen and (max-width: 989px) {
.lush06-support-block .slider--desktop:not(.slider--tablet) + .slider-buttons {display:none;}
}

@media screen and (max-width: 1540px) {
.support-block-card__info .rte>p:first-child {
    margin: 25px 0;
}
}
@media screen and (max-width: 576px) {
.support-block-card__info .rte>p:first-child {
    margin: 25px 0 0;
}
}
@media screen and (min-width: 750px) {
.lush06-support-block .slider--mobile + .slider-buttons {display:none;}
}

.lush06-support-block .slider-button {color:var(--gradient-base-accent-1); background:transparent; border:none; cursor:pointer; width:34px; height:34px; display:flex; align-items:center; justify-content:center; transition:all 0.3s linear; z-index:3; border-radius:50%;}
.lush06-support-block .slider-button:not([disabled]):hover {color:var(--gradient-base-background-1); background:var(--gradient-base-accent-1);}
.lush06-support-block .slider-button .icon {height:0.6rem;}
.lush06-support-block .slider-button[disabled] .icon {color:rgba(var(--color-foreground), 0.5); cursor:not-allowed;}
.lush06-support-block .slider-button--next .icon {transform:rotate(270deg); width:15px; height:15px;}
.lush06-support-block .slider-button--prev .icon {transform:rotate(90deg); width:15px; height:15px;}
.lush06-support-block .slider-button--next:not([disabled]):hover .icon {transform:rotate(-90deg) scale(1.1);}
.lush06-support-block .slider-button--prev:not([disabled]):hover .icon {transform:rotate(90deg) scale(1.1);}


@media screen and (min-width: 1200px) and (max-width: 1340px){
  .lush06-support-block .grid{
    justify-content: center;
  }
  .lush06-support-block .support-block-list.slider, .lush06-support-block .support-block-list.grid--4-col-desktop{
    row-gap: 0;
  }
}