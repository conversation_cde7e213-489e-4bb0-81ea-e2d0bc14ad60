.lush-product-tab .tabs {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 4rem;
}


.lush-product-tab .tabs .tablinks {
  color: var(--color-base-accent-1);
  cursor: pointer;
  padding: 0rem 1rem;
  margin: 0 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background-color: var(--gradient-base-background-1);
  border: 1px solid var(--gradient-base-background-1);
  border-right: none;
  box-shadow: none;
  transition: all 0.3s linear;
  font-weight: 700;
  text-transform: uppercase;
}

.lush-product-tab .tabs .tablinks:last-child {
  border-right: none;
}

/* .tabs .tablinks:hover, */
.lush-product-tab .tabs .tablinks.active {
  color: var(--gradient-base-accent-3);
  /*     box-shadow: inset 0 -3px 0 var(--gradient-base-accent-1); */
  /* border-left: none;
  border-right: none; */
  /* border-bottom: 2px solid #000000;
  border-top: 2px solid #000000; */
  box-shadow: none;
}

.lush-product-tab .tabs .tablinks:hover {
  color: var(--gradient-base-accent-3);
  box-shadow: none;
}

.lush-product-tab .tabs_container {
  position: relative;
  width: 100%;
}

.lush-product-tab .list-style-none {
  list-style: none !important;
}

.lush-product-tab .text-align-left {
  text-align: left !important;
  align-items: flex-start;
}

.lush-product-tab .text-align-center {
  text-align: center !important;
  align-items: center;
  margin-bottom:2.2rem;
}
.lush-product-tab .tabs .product-tab:before {
    content: "";
    position: absolute;
    width: 2px;
    height: 23px;
    background: var(--gradient-base-accent-1);
    right: 0;
    left: 5px;
    margin: auto;
    z-index: 1;
    top: 0;
    bottom: 0;
}
.lush-product-tab .product-tab-wrapper .collection .tabs .product-tab .tablinks{text-transform: capitalize;margin:0;padding:10px;}
.lush-product-tab .tabs_container .product-tab-carousel {
  width: 100%;
  transition: all 0.3s linear;

}

.lush-product-tab .tabs_container .product-tab-carousel:not(:first-child, :only-child),
.lush-product-tab .tabs_container .dt-sc-tabs-content:not(:first-child, :only-child) {
  /*   position: absolute;  */
  left: 0;
  top: 0;
}

.lush-product-tab .tabs_container .product-tab-carousel:not(.active),
.lush-product-tab .tabs_container .dt-sc-tabs-content:not(.active) {
  opacity: 0;
  pointer-events: none;
}

.lush-product-tab .product-tab-wrapper .collection .grid {
  justify-content: space-between;
  margin: 0;
  width: 100%;
  padding: 0;
  gap: 0;
}

.lush-product-tab .product-tab-wrapper .collection .grid>.grid__item {
  max-width: 100%;
  width: 25%;
}

.lush-product-tab .product-tab-wrapper .collection .grid>.grid__item[class*="tab-template--"]:not(:only-child) {
  max-width: 75%;
  width: 75%;
}

.lush-product-tab .product-tab-wrapper .collection .grid__item:only-child {
  max-width: 100%;
  width: 100%;
}

.lush-product-tab .product-tab-wrapper .collection .grid>.grid__item:not(:only-child) .tabs_container .dt-sc-column.four-column {
  grid-template-columns: repeat(2, 1fr);
}

.lush-product-tab .product-tab-wrapper .collection .grid>.grid__item:not(:only-child) .tabs_container .dt-sc-column.five-column,
.lush-product-tab .product-tab-wrapper .collection .grid>.grid__item:not(:only-child) .tabs_container .dt-sc-column.six-column {
  grid-template-columns: repeat(3, 1fr);
}

.lush-product-tab .product-tab-wrapper .collection .grid__item,
.lush-product-tab .product-tab-wrapper .template-search .grid__item {
  padding: 0;
}

.lush-product-tab .product-tab-wrapper .collection .grid__item>.media {
  height: 100%;
  background: none;
  width: 100%;
}

.lush-product-tab .product-tab-wrapper .collection .grid__item>.media>img {
  height: 100%;
  position: absolute;
}

.lush-product-tab .product-tab-wrapper .collection .grid__item>.media .image-block-heading {
  display: flex;
  flex-wrap: wrap;
  padding: 4rem;
  z-index: 1;
}

.lush-product-tab .product-tab-wrapper .collection .grid__item>.media .image-block-heading.vertical_top {
  align-content: flex-start;
}

.lush-product-tab .product-tab-wrapper .collection .grid__item>.media .image-block-heading.vertical_middle {
  align-content: center;
}

.lush-product-tab .product-tab-wrapper .collection .grid__item>.media .image-block-heading.vertical_bottom {
  align-content: flex-end;
}

.lush-product-tab .product-tab-wrapper .collection .grid__item>.media .image-block-heading>* {
  width: 100%;
  margin: 0;
}

.lush-product-tab .product-tab-wrapper .collection .grid__item>.media .image-block-heading>*:not(.button) {
  margin-bottom: 1rem;
}

.lush-product-tab .product-tab-wrapper .collection .grid__item>.media .image-block-heading>.button {
  width: auto;
  margin-bottom: 0;
  margin-top: 1.5rem;
}


.lush-product-tab .product-tab-wrapper .collection .grid__item>.media .image-block-heading>.image-block-sub-heading {
  font-family: var(--font-body-family);
  line-height: normal;
  font-size: 1.6rem;
  text-transform: uppercase;
  font-weight: normal;
  letter-spacing: 1px;
}


.lush-product-tab .product-tab-wrapper .collection>.grid.image-with-text__grid.image-with-text__grid--reverse {
  flex-direction: row-reverse;
}

.lush-product-tab .product-tab-wrapper .collection>.grid.image-with-text__grid.image-with-text__grid--reverse .grid__item>.media {
  float: right;
}

.lush-product-tab .tabs_container .dt-sc-tabs-content-Details:not(.active) {
  opacity: 0;
  pointer-events: none;
  display: none;
}


@media screen and (min-width: 1201px) and (max-width: 1440px) {

.lush-product-tab .product-tab-wrapper .collection .grid>.grid__item[class*="tab-template--"]:not(:only-child) .tabs_container .dt-sc-column {
    grid-template-columns: repeat(2, 1fr);
  }

}

@media screen and (max-width: 1200px) {

  /*   .product-tab .collection .grid, */
.lush-product-tab .product-tab-wrapper .collection .grid[class*="tab-template--"]:not(:only-child) {
    display: grid;
    grid-template-columns: 1fr;
  }

.lush-product-tab .product-tab-wrapper .collection .grid>.grid__item,
.lush-product-tab .product-tab-wrapper .collection .grid>.grid__item[class*="tab-template--"]:not(:only-child) {
    max-width: 100%;
    width: 100%;
  }

.lush-product-tab .product-tab-wrapper .collection .grid>.grid__item:only-child .tabs_container .dt-sc-column.three-column,
.lush-product-tab .product-tab-wrapper .collection .grid>.grid__item:only-child .tabs_container .dt-sc-column.four-column {
    grid-template-columns: repeat(2, 1fr);
  }

.lush-product-tab .product-tab-wrapper .collection .grid>.grid__item:only-child .tabs_container .dt-sc-column.five-column,
.lush-product-tab .product-tab-wrapper .collection .grid>.grid__item:only-child .tabs_container .dt-sc-column.six-column {
    grid-template-columns: repeat(2, 1fr);
  }

  #dtx-quickview-content .product .product__title {
    font-size: calc(.4 * var(--heading_font_size));
  }

}

@media screen and (min-width: 1201px) {
.lush-product-tab .home-product-tab-1.product-tab-wrapper .collection .grid__item:first-child {
    /* padding-right: 50px; */
  }
}

@media screen and (max-width: 750px) {

.lush-product-tab .product-tab-wrapper .collection .grid>.grid__item[class*="tab-template--"]:not(:only-child) .tabs_container .dt-sc-column,
.lush-product-tab .product-tab-wrapper .collection .grid>.grid__item:not(:only-child) .tabs_container .dt-sc-column,
.lush-product-tab .product-tab-wrapper .collection .grid>.grid__item:only-child .tabs_container .dt-sc-column.five-column,
.lush-product-tab .product-tab-wrapper .collection .grid>.grid__item:only-child .tabs_container .dt-sc-column.six-column {
    grid-template-columns: repeat(2, 1fr);
  }

.lush-product-tab .product-tab-wrapper .collection .grid__item>.media .image-block-heading {
    padding: 3rem;
  }
}


@media screen and (max-width: 575px) {

.lush-product-tab .product-tab-wrapper .collection .grid>.grid__item[class*="tab-template--"]:not(:only-child) .tabs_container .dt-sc-column,
.lush-product-tab .product-tab-wrapper .collection .grid>.grid__item:not(:only-child) .tabs_container .dt-sc-column,
.lush-product-tab .product-tab-wrapper .collection .grid>.grid__item:only-child .tabs_container .dt-sc-column.five-column,
.lush-product-tab .product-tab-wrapper .collection .grid>.grid__item:only-child .tabs_container .dt-sc-column.six-column {
    grid-template-columns: 1fr;
  }

.lush-product-tab .product-tab-wrapper .collection .grid__item>.media .image-block-heading {
    /* padding: 2.5rem;*/
  }

  /*   .tabs .tablinks { width: 100%;  } */

  /*   .product-tab .collection .grid > .grid__item { padding-bottom: 100%; position: relative; } */
  /*   .product-tab .collection > .grid.image-with-text__grid.image-with-text__grid--reverse .grid__item > .media { position: absolute; } */

.lush-product-tab .product-tab-wrapper .collection .grid>.grid__item:only-child .tabs_container .dt-sc-column.three-column,
.lush-product-tab .product-tab-wrapper .collection .grid>.grid__item:only-child .tabs_container .dt-sc-column.four-column {
    grid-template-columns: repeat(1, 1fr);
  }
.lush-product-tab .tabs .product-tab:before{ display:none; }

}

.lush-product-tab .tabs .product-tab {
  display: flex;
  padding: 0;
  position:relative;
}

.lush-product-tab .tabs_container .product-tab-carousel .swiper-container {
  overflow: hidden;
}

.lush-product-tab .tabs_container .product-tab-carousel .swiper-container .slider-button:after {
  font-size: 20px;
}

.lush-product-tab .tabs_container .product-tab-carousel .swiper-container .slider-button {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 30px;
  height: 30px;
  background: transparent;
  transition: all 0.3s linear;
}



.lush-product-tab .tabs_container .product-tab-carousel .swiper-container .swiper-button-next {
    position: absolute;
    top: -68px;
    right: -22%;
    left: 0;
    margin: auto;
}

.lush-product-tab .tabs_container .product-tab-carousel .swiper-container .swiper-button-prev {
    position: absolute;
    right: 0;
    left: -22%;
    margin: auto;
    top: -68px;
}

/* @media screen and (min-width:1925px){
  .lush-product-tab .tabs_container .product-tab-carousel .swiper-container .swiper-button-next {
    position: absolute;
    top: -58px;
    right: -16%;
    left: 0;
    margin: auto;
}

.lush-product-tab .tabs_container .product-tab-carousel .swiper-container .swiper-button-prev {
    position: absolute;
    right: 0;
    left: -16%;
    margin: auto;
    top: -68px;
}
} */





.lush-product-tab .tabs_container .product-tab-carousel .swiper-container ul {
  padding: 0;
  margin: 0;
}

.lush-product-tab .product-tab-wrapper .collection .grid>.grid__item[class*=tab-template--]:not(:only-child) .tabs .section-main-heading .section-main-title {
  font-size: 3.6rem;
  margin: 0;
  letter-spacing: 1px;
}

/* .home-product-tab-1.product-tab-wrapper .collection .grid__item:first-child{ height: 645px;} */
.lush-product-tab .home-product-tab-2.product-tab-wrapper .collection .grid__item:first-child {
  padding-left: 50px;
}

.lush-product-tab .product-tab-wrapper .collection .grid__item>.media .collection_count {
  top: 72%;
  z-index: 2;
  left: 43px;
}

.lush-product-tab .product-tab-wrapper .collection .grid__item>.media .text_count {
  font-size: 100px;
  font-weight: 700;
  color: var(--gradient-base-accent-1);
}

.lush-product-tab .product-tab-wrapper .collection .grid__item>.media .text_count_text {
  font-size: 2.2rem;
  font-weight: 400;
  color: var(--gradient-base-accent-1);
  text-shadow: var(--gradient-base-accent-1) 1px 0 10px;
}


@media screen and (max-width: 990px) {
.lush-product-tab .product-tab-wrapper .collection .grid>.grid__item[class*=tab-template--]:not(:only-child) .tabs .section-main-heading .section-main-title {
    font-size: 30px;
  }

.lush-product-tab .tabs .product-tab {
    padding: 0;
  }
}

@media screen and (max-width: 1440px) {
.lush-product-tab .product-tab-wrapper .collection .grid>.grid__item[class*=tab-template--]:not(:only-child) .tabs .section-main-heading .section-main-title {
    font-size: 30px;
  }
}

@media screen and (min-width: 1540px) {
.lush-product-tab .product-tab-wrapper .collection .grid__item>.media .image-block-heading>.image-block-main-heading {
    font-size: 4.0rem;
  }
}

.lush-product-tab .product-tab-wrapper .collection .grid__item>.media .image-block-heading {
  top: 6%;
}

.lush-product-tab .product-tab-wrapper .collection .grid__item>.media .image-block-heading>.image-block-main-heading {
  letter-spacing: 1px;
  margin-top: 10px;
}

/* .tabs_container .product-tab-carousel .swiper-container ul{padding-left:10px;} */
.lush-product-tab .tabs .tablinks:hover {
  color: var(--gradient-base-accent-3);
}

@media screen and (max-width:876px) {
.lush-product-tab .product-tab-wrapper .collection .grid>.grid__item[class*=tab-template--]:not(:only-child) .tabs .section-main-heading .section-main-title {
    font-size: 30px;
    text-align: center;
  }

.lush-product-tab .tabs {
    align-items: center;
  }

.lush-product-tab .tabs .product-tab {
    padding: 0;
    gap: 20px;
  }

.lush-product-tab .tabs .tablinks {
    padding: 1rem 1rem;
  }
}

@media screen and (max-width: 575px) {
.lush-product-tab .tabs .product-tab {
    flex-direction: column;
    gap:0;
  }

.lush-product-tab .tabs {
    margin-bottom: 30px;
  }
  .lush-product-tab .tabs .product-tab{width:100%;}
}

@media screen and (max-width: 1440px) {
.lush-product-tab .tabs_container .product-tab-carousel .swiper-container .slider-button {
    top: -58px;
    /* left: auto; */
  }

.lush-product-tab .tabs_container .product-tab-carousel .swiper-container .swiper-button-next {
    right: -30%;
    left: 0;
    margin: auto;
}
.lush-product-tab .tabs_container .product-tab-carousel .swiper-container .swiper-button-prev {
    left: 0px;
    right: 30%;
    margin: auto;
}
}

@media screen and (max-width: 1200px) {
.lush-product-tab .home-product-tab-1.product-tab-wrapper .collection .grid__item:first-child {
    height:100%;
    padding: 0;
  }

.lush-product-tab .home-product-tab-2.product-tab-wrapper .collection .grid__item:first-child {
    height: 540px;
    padding: 0;
  }
  .lush-product-tab .tabs_container .product-tab-carousel .swiper-container .swiper-button-next {
    right: -35%;
    left: 0;
    margin: auto;
}
.lush-product-tab .tabs_container .product-tab-carousel .swiper-container .swiper-button-prev {
    left: 0px;
    right: 35%;
    margin: auto;
}
}

@media screen and (max-width: 1024px) {
.lush-product-tab .tabs_container .product-tab-carousel .swiper-container .slider-button {
    top: -68px;
  }

.lush-product-tab .tabs_container .product-tab-carousel .swiper-container .swiper-button-next {
    right: 10px;
    left: unset;
    margin: auto;
}
.lush-product-tab .tabs_container .product-tab-carousel .swiper-container .swiper-button-prev {
    left: 10px;
    right: unset;
    margin: auto;
}
}


@media screen and (max-width: 990px) {
.lush-product-tab .tabs_container .product-tab-carousel .swiper-container .slider-button{border:transparent; background: var(--gradient-base-background-1);}  
.lush-product-tab .tabs_container .product-tab-carousel .swiper-container .slider-button:hover{border:transparent;/* background:rgb(var(--color-base-outline-button-labels)); */color:var(--gradient-base-background-1);}
/* .lush-product-tab .tabs_container .product-tab-carousel .swiper-container .swiper-button-prev:hover i, .lush-product-tab .tabs_container .product-tab-carousel .swiper-container .swiper-button-next:hover i{color:var(--gradient-base-background-1);}   */
.lush-product-tab .tabs_container .product-tab-carousel .swiper-container .slider-button {
    top: -68px;
  }

.lush-product-tab .tabs_container .product-tab-carousel .swiper-container .swiper-button-next {
    right: 10px;
    left: unset;
    margin: auto;
}
.lush-product-tab .tabs_container .product-tab-carousel .swiper-container .swiper-button-prev {
    left: 10px;
    right: unset;
    margin: auto;
}


}


@media screen and (max-width: 780px) {
.lush-product-tab .tabs_container .product-tab-carousel .swiper-container .slider-button {
    top: -68px;
  }

.lush-product-tab .tabs_container .product-tab-carousel .swiper-container .swiper-button-next {
    left: unset;
    right:10px;
    margin: auto;
}
.lush-product-tab .tabs_container .product-tab-carousel .swiper-container .swiper-button-prev {
    left: 10px;
    right: unset;
    margin: auto;
}
}
.lush-product-tab .tabs_container .product-tab-carousel .swiper-container .slider-button svg {transition: 0.3s;}
.lush-product-tab .tabs_container .product-tab-carousel .swiper-container .slider-button:hover svg {
    transform: scale(1.3);
}