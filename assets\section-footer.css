.footer {
  border-top: 0.1rem solid rgba(var(--color-foreground), 0.08);
  position: relative;
  z-index: 0;
}
.footer:not(.color-background-1) {
  border-top: none;
}
.footer-block svg.placeholder-svg{width:249px;height:66px;}
.footer__content-top {
  padding-bottom: 0rem;
  display: block;
  position: relative;
}
footer .banner__media.media {
  position: unset;
}
@media screen and (max-width: 749px) {
  .footer .grid {
    display: block;
  }

  .footer-block.grid__item {
    padding: 0;
    margin: 0rem 0;
    width: 100%;
    max-width:100%;
  }

  .footer-block.grid__item:first-child {
    margin-top: 0;
  }

  .footer__content-top {
    padding-bottom: 0rem;
  }
}

@media screen and (min-width: 750px) {
  .footer__content-top .grid {
    row-gap: 3.5rem;
    margin-bottom: 0;
  }
}

.footer__content-bottom {
  position: relative;
}

.footer__content-bottom:only-child {
  border-top: 0;
}

.footer__content-bottom-wrapper {
  display: flex;
  width: 100%;
  margin-top: 0; padding: 15px 0;
}

@media screen and (max-width: 749px) {
  .footer__content-bottom {
    flex-wrap: wrap;
    padding-left: 0;
    padding-right: 0;
    row-gap: 1.5rem;
  }

  .footer__content-bottom-wrapper {
    flex-wrap: wrap;
    row-gap: 1.5rem;
  }
}

.footer__localization:empty + .footer__column--info {
  align-items: center;
}

@media screen and (max-width: 749px) {
  .footer__localization:empty + .footer__column {
    padding-top: 1.5rem;
  }
}
.footer__column {
  width: 100%;
  align-items: flex-end;
}

.footer__column--info {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}

@media screen and (min-width: 990px) {
  .footer__column--info {
    padding-left: 0;
    padding-right: 0;
    flex-direction: row;
  }
}

.footer-block:only-child:last-child {
  text-align:left;
  max-width: 76rem;
  margin: 0 auto;
}
.footer-block__newsletter .newsletter-form__field-wrapper .field__input{
  background:transparent;
   border: var(--inputs-border-width) solid rgb(var(--color-foreground));
   color: var(--gradient-base-background-1);
  border-radius: 30px;
}
.footer-block__newsletter .newsletter-form__field-wrapper .field__input:focus{
   border: var(--inputs-border-width) solid var(--gradient-base-accent-2);
}
@media screen and (max-width: 990px){
  .footer__blocks-wrapper .footer-block{
    display: block;
    flex-direction: column;
    align-items: center;
  }
  .footer-block:only-child:last-child{
    text-align:left;
    margin-right: auto;
        margin-left: 0;
  }
  .footer .footer-text .footer-block__details-content {
    margin-top: 15px;
    width: 100%;
    text-align: left;
}
  .footer-block__details-content-newsletter .news-letter {
    margin: 0 auto 0 0;
    text-align: left;
    max-width: 80%;
}
  .footer-block__details-content .footer-block__newsletter{
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }
  .footer-block__newsletter .newsletter-form {
    align-items: flex-start;
    max-width: 45rem;
    position: relative;
    padding-left: 0;
}
}

@media screen and (max-width:575px){
  .footer-block__details-content-newsletter .news-letter{max-width:70%;}
}
@media screen and (max-width:480px){
  .footer-block__details-content-newsletter .news-letter{max-width:80%;}
}
@media screen and (max-width:425px){
  .footer-block__details-content-newsletter .news-letter{max-width:100%;}
}


@media screen and (min-width: 750px) {
  .footer-block {
    display: block;
    margin-top: 0;
  }
}

.footer-block:empty {
  display: none;
}

.footer-block--newsletter {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: flex-end;
  margin-top: 0rem;
}

.footer-block--newsletter:only-child {
  margin-top: 0;
}

.footer-block--newsletter > * {
  flex: 1 1 100%;
}

@media screen and (max-width: 749px) {
  .footer-block.footer-block--menu:only-child {
    text-align: left;
  }
}

@media screen and (min-width: 750px) {
  .footer-block--newsletter {
    flex-wrap: nowrap;
    justify-content: space-between;
  }
}

.footer-block__heading {
  margin-bottom: 2rem;
  margin-top: 0;
  font-size: calc(var(--font-heading-scale) * 1.8rem);
  font-weight: 400;
  z-index: 99;
}

@media screen and (min-width: 990px) {
  .footer-block__heading {
    font-size: clamp(2rem, 1.92rem + 0.4vw, 2.4rem);
    font-weight: 400;
  }
}

.footer__list-social:empty,
.footer-block--newsletter:empty {
  display: none;
}

.footer__list-social.list-social.left:only-child {
  justify-content: flex-start;
}
.footer__list-social.list-social.center:only-child {
  justify-content: center;
}
.footer__list-social.list-social.right:only-child {
  justify-content: flex-end';
}
.footer__list-social.list-social .list-social__item{padding: 0 1.3rem;}


.footer-block__details-content.footer-block--newsletter.left .list-social {
  justify-content: flex-start;
}
.footer-block__details-content.footer-block--newsletter.center .list-social {
  justify-content: center;
}
.footer-block__details-content.footer-block--newsletter.right .list-social {
  justify-content: flex-end;
}

.newsletter-form__field-wrapper {
  max-width: 70rem;
}

@media screen and (min-width: 750px) {
  .footer-block__newsletter:not(:only-child) {
    text-align: left;
  }

  .footer-block__newsletter:not(:only-child) .footer__newsletter {
    justify-content: flex-start;
    margin: 0;
  }

  .footer-block__newsletter:not(:only-child)
    .newsletter-form__message--success {
    left: auto;
  }
}

.footer-block__newsletter + .footer__list-social {
  margin-top: 3rem;
}

@media screen and (max-width: 749px) {
  .footer__list-social.list-social {
    justify-content: center;
  }
}

@media screen and (min-width: 750px) {
  .footer-block__newsletter + .footer__list-social {
    margin-top: 0;
  }
}

.footer__localization {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-content: center;
  flex-wrap: wrap;
  padding: 1rem 1rem 0;
}

.footer__localization:empty {
  display: none;
}

.localization-form {
  display: flex;
  flex-direction: column;
  flex: auto 1 0;
  padding: 1rem;
  margin: 0 auto;
}

.localization-form:only-child {
  display: inline-flex;
  flex-wrap: wrap;
  flex: initial;
  padding: 1rem 0;
}

.localization-form:only-child .button,
.localization-form:only-child .localization-form__select {
  margin: 1rem 1rem 0.5rem;
  flex-grow: 1;
}

.footer__localization h2 {
  margin: 1rem 1rem 0.5rem;
  color: rgba(var(--color-foreground), 0.75);
}

.header__icons   localization-form .disclosure__list-wrapper {
  top: 100%;
  bottom: unset;
}
@media screen and (min-width: 750px) {
  .footer__localization {
    padding: 0.4rem 0;
    justify-content: flex-start;
  }

  .localization-form {
    padding: 1rem 2rem 1rem 0;
  }

  .localization-form:first-of-type {
    padding-left: 0;
  }

  .localization-form:only-child {
    justify-content: start;
    width: auto;
    margin: 0 1rem 0 0;
  }

  .localization-form:only-child .button,
  .localization-form:only-child .localization-form__select {
    margin: 1rem 0;
  }

  .footer__localization h2 {
    margin: 1rem 0 0;
  }
}

@media screen and (max-width: 989px) {
  noscript .localization-form:only-child,
  .footer__localization noscript {
    width: 100%;
  }
}

.localization-form .button {
  padding: 1rem;
}

.localization-form__currency {
  display: inline-block;
}

@media screen and (max-width: 749px) {
  .localization-form .button {
    word-break: break-all;
  }
}

.localization-form__select {
  border-radius: var(--inputs-radius-outset);
  position: relative;
  margin-bottom: 1.5rem;
  padding-left: 1rem;
  text-align: left;
  min-height: calc(4rem + var(--inputs-border-width) * 2);
  min-width: calc(7rem + var(--inputs-border-width) * 2);
}

.disclosure__button.localization-form__select {
  padding: calc(2rem + var(--inputs-border-width));
  background: rgb(var(--color-background));
}

noscript .localization-form__select {
  padding-left: 0rem;
}

@media screen and (min-width: 750px) {
  noscript .localization-form__select {
    min-width: 20rem;
  }
}

.localization-form__select .icon-caret {
  position: absolute;
  content: "";
  height: 0.6rem;
  right: calc(var(--inputs-border-width) + 1.5rem);
  top: calc(50% - 0.2rem);
}

.localization-selector.link {
  text-decoration: none;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  color: rgb(var(--color-foreground));
  width: 100%;
  padding-right: 4rem;
  padding-bottom: 1.5rem;
}

noscript .localization-selector.link {
  padding-top: 1.5rem;
  padding-left: 1.5rem;
}

.disclosure .localization-form__select {
  padding-top: 1.5rem;
}

.localization-selector option {
  color: #000000;
}

.localization-selector + .disclosure__list-wrapper {
  margin-left: 0;
  opacity: 1;
  animation: animateLocalization var(--duration-default) ease;
}

.theme__default-footer_style .footer__blocks-wrapper li.office-mail a {
  display: unset !important;
}
.footer__copyright {
  text-align: center;
  margin: 0;
  }
.footer__copyright .copyright__content p{margin:0;font-size:16px;}

@media screen and (min-width: 750px) {
  .footer__copyright {
    text-align: right;
  }
}

@keyframes appear-down {
  0% {
    opacity: 0;
    margin-top: -1rem;
  }
  100% {
    opacity: 1;
    margin-top: 0;
  }
}

.footer-block__details-content {
  margin-bottom: 1rem;
  justify-content: center;
}
.footer-block__details-content > p,
  .footer-block__details-content > li {
    padding: 0;line-height: normal;
  }
.footer-block__details-content > p,
.footer-block__details-content > li:not(:last-child),
.footer-block__address .contact-info li:not(:last-child) {
    margin: 16px 0;
  }

@media screen and (min-width: 750px) {
  .footer-block__details-content {
    margin-bottom: 0;
  }

  

  .footer-block:only-child li {
    display: inline;
  }
}
.footer-block__details-content > li:not(:last-child) {
  position: relative;
}

.footer-block__details-content .list-menu__item--link,
.copyright__content a {
  color: rgba(var(--color-icon), 1);
  transition: all 0.3s linear;
  display: inline-flex; line-height: normal;
  padding:0;
}

.footer-block__details-content .list-menu__item--active {
  transition: text-decoration-thickness var(--duration-short) ease;
  color: rgb(var(--color-foreground));
}

.footer-block__details-content .list-menu__item--link:hover,
.copyright__content a:hover {
  color: rgb(var(--color-base-outline-button-labels));
}
@media screen and (min-width: 750px) {
  .footer-block__details-content .list-menu__item--active:hover {
    text-decoration-thickness: 0.2rem;
  }
}


@media screen and (min-width: 750px) {
  .footer-block__details-content .list-menu__item--link {
    padding: 6px;
  }

}

@media screen and (max-width: 749px) {
  .footer-block-image {
    text-align: center;
  }
}

.footer-block-image > img {
  height: auto;
}

.footer-block__details-content .placeholder-svg {
  max-width: 20rem;
}

.copyright__content a {
  color: currentColor;
  text-decoration: none;
}

@keyframes animateLocalization {
  0% {
    opacity: 0;
    transform: translateY(-1rem);
  }

  100% {
    opacity: 1;
    transform: translateY(0rem);
  }
}

.footer .disclosure__link {
  padding: 0.95rem 3.5rem 0.95rem 2rem;
  color: rgba(var(--color-foreground), 0.75);
}

.footer .disclosure__link:hover {
  color: rgb(var(--color-foreground));
}

.footer .disclosure__link--active {
  text-decoration: underline;
}

/* check for flexbox gap in older Safari versions */
@supports not (inset: 10px) {
  @media screen and (max-width: 749px) {
    .footer .grid {
      margin-left: 0;
    }
  }

  @media screen and (min-width: 750px) {
    .footer__content-top .grid {
      margin-left: -3rem;
    }

    .footer__content-top .grid__item {
      padding-left: 3rem;
    }
  }
}
footer ul.footer-social-icons li {
    list-style: none;
}
footer ul.footer-social-icons li:not(:first-child) {
    margin-left: 30px;
}
footer ul.footer-social-icons{
    display: flex;
    flex-wrap: wrap;
    padding:0px;
}
footer ul.contact-info li svg {  margin-right: 10px;}
footer ul.contact-info address{font-style:normal} 
.theme__default-footer_style .footer__blocks-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.theme__default-footer_style .footer__column--info {
  align-items: center;
}

@media screen and (max-width: 990px) {
   .footer-block:first-child{padding-top:0;}
 .footer-block.footer-block--menu.footer-links h2.footer-block__heading{position:relative; cursor: pointer; display: flex;align-items: center;margin:0;transition: all 0.3s linear;justify-content:space-between;}
 .footer-block.footer-block--menu.footer-links h2.footer-block__heading:after {
    content: "";
    background: #ffff;
    width: 15px;
    height: 2px;
    right: 0;
    margin: auto;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    transition: all 0.3s linear;
}
 .footer-block.footer-block--menu.footer-links h2.footer-block__heading:before {
    content: "";
    height: 15px;
    width: 2px;
    position: absolute;
    right: 7px;
    opacity: 1;
    background: #ffff;
    margin: auto;
    transition: all 0.3s linear;
    top: 50%;
    transform: translateY(-50%);
}
 .footer-block.footer-block--menu.footer-links.open h2.footer-block__heading:before{opacity:0}
  
  .footer-block.footer_address h2.footer-block__heading{position:relative; cursor: pointer; display: flex;align-items: center;margin:0;transition: all 0.3s linear;justify-content:center;}
 .footer-block.footer_address h2.footer-block__heading:after { content: ""; background:var(--gradient-base-background-1); width: 15px; height: 2px; right: 0; margin: auto; position: absolute; top: 10px;transition: all 0.3s linear;}
 .footer-block.footer_address h2.footer-block__heading:before { content: ""; height: 15px; width: 2px; position: absolute; right: 6px;opacity:1; background:var(--gradient-base-background-1); margin: auto;transition: all 0.3s linear;}
 .footer-block.footer_address.open h2.footer-block__heading:before{opacity:0;}
 .footer-block.footer_address .footer-block {margin-top:15px;}
  
  .footer-block__details-content{text-align:left;}
  .footer-block__details-content.center p {text-align: center;}
  .footer-block__details-content.footer_menu {margin-top:15px;}

  .footer-links .footer_menu{display:none;}
  .footer-links.open .footer_menu { display: block; transition: all .5s ease-in-out;text-align:left;}
  .footer_address .footer-block  {display:none;}
  .footer_address.open .footer-block{ display: block; transition: all .5s ease-in-out;}
  .shopify-section.reveal .footer_menu>li>a {   animation: fadeInDown 1s ease-out .05s both;}
  .shopify-section.reveal .footer_address .footer-block>ul>li { animation: fadeInDown 1s ease-out .05s both;}
  footer ul.footer-social-icons {
    display: flex;
    flex-wrap: wrap;
    padding: 0px;
    justify-content: flex-start;
}
  }
.footer.theme__default-footer_style .footer-block{
      align-items: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
  width:100%; max-width:100%;
}
