<div class="social-proof-sales">
  {% render 'icon-fire' %}
  <div class="flash_total_qty"></div>
  <div class="flash_in_hour"></div>
</div>
<script type="text/javascript">
  flashSoldBar();
  function flashSoldBar() {  
    var minQty = {{- block.settings.minqty | escape -}};
    var maxQty = {{- block.settings.maxqty | escape -}};
  
    var minTime = '6';
    var maxTime = '24';
    minQty = Math.ceil(minQty);
    maxQty = Math.floor(maxQty);
    minTime = Math.ceil(minTime);
    maxTime = Math.floor(maxTime);
    var qty = Math.floor(Math.random() * (maxQty - minQty + 1)) + minQty;
    qty = parseInt(qty);

    if (qty <= minQty) {
      qty = minQty;
    }

    if (qty > maxQty) {
      qty = maxQty;
    }
    jQuery(".flash_total_qty").html(qty+'<span>{{ block.settings.item_sold }}</span><span>{{ block.settings.products }}</span>');
    var time = Math.floor(Math.random() * (maxTime - minTime + 1)) + minTime;
    time = parseInt(time);
    if (time <= minTime) {
      time = minTime;
    }

    if (time > maxTime) {
      time = maxTime;
    }

    jQuery(".flash_in_hour").html(time+" {{ block.settings.hours }}");
    }
    var flashinterval = window.setInterval(function(){
    flashSoldBar();
    }, 5000);

</script>

<style>
  .social-proof-sales { display: flex; flex-wrap: wrap; margin: 0;  font-weight:500;   color: #FF4610; font-size: 1.4rem;  line-height: 20px;  align-items: center; }
  .social-proof-sales  .flash_total_qty {  margin-left: 8px;}
  .flash_total_qty span { margin: 0 3px;  }  
  .social-proof-sales svg.icon.icon-fire {  position: relative;  top: 0;  bottom: 0;  margin: auto; height:25px; width:25px;   }

</style>