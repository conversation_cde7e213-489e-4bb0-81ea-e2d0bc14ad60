
{{ 'grid-banner-typ3.css' | asset_url | stylesheet_tag }}

{%- liquid

case section.settings.grid-column
when '1'
assign column = 'one-column'
when '2'
assign column = 'two-column'
when '3'
assign column = 'three-column'
when '4'
assign column = 'four-column'
when '5'
assign column = 'five-column'
when '6'
assign column = 'six-column'
else
assign column = 'three-column'
endcase
if section.settings.swiper_enable
assign enable_slider = true  
endif
%}

{%- style -%}
.grid-typ3 .section-{{ section.id }}-padding {padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;}
@media screen and (min-width: 750px) {
.grid-typ3 .section-{{ section.id }}-padding {padding-top: {{ section.settings.padding_top }}px;padding-bottom: {{ section.settings.padding_bottom }}px;}
}
{%- for block in section.blocks -%} 
.grid-typ3 .grid-banner-section.list .grid-banner-wrapper.reverse-columns{ flex-direction: row-reverse;}
{% endfor %}
.grid-typ3 .grid-banner .section-{{ section.id }}-padding .grid-banner-section{grid-gap:{{section.settings.column_gap}}px;}
.grid-typ3 .grid-banner .section-{{ section.id }}-padding .grid-banner-section.overlay .grid-banner-wrapper{ height: {{section.settings.overlay_height}}px;}
.grid-typ3 .grid-banner .section-{{ section.id }}-padding .grid-banner-section.one-column:not(.grid) .grid-banner-wrapper:not(:last-child){margin-bottom:{{section.settings.column_gap}}px;}
.grid-typ3 .grid-banner .section-{{ section.id }}-padding .grid-banner-section {column-gap:{{section.settings.column_gap| times: 0.50 | round: 0}}px; row-gap:{{section.settings.column_gap | times: 0.50 | round: 0}}px;}

@media screen and (min-width: 750px) {
.grid-typ3 .grid-banner .section-{{ section.id }}-padding .grid-banner-section {column-gap:{{section.settings.column_gap}}px; row-gap:{{section.settings.column_gap}}px;}
}
@media screen and (max-width: 1540px) {
.grid-typ3 .grid-banner .section-{{ section.id }}-padding .grid-banner-section.overlay .grid-banner-wrapper{ height: {{section.settings.overlay_height_laptop}}px;}
}
@media screen and (max-width: 1199px) {
.grid-typ3 .grid-banner .section-{{ section.id }}-padding .grid-banner-section.overlay .grid-banner-wrapper{ height: {{section.settings.overlay_height_tab}}px;}
}
@media screen and (max-width: 767px) {
.grid-typ3 .grid-banner .section-{{ section.id }}-padding .grid-banner-section.overlay .grid-banner-wrapper{ height: {{section.settings.overlay_height_mobile}}px;}
}
.grid-typ3 .grid-banner-section.overlay .grid-banner-wrapper .grid-banner-content .grid-banner-inner.background-none{background:none;}
{%- endstyle -%}
<div class="grid-typ3 color-{{ section.settings.color_scheme }} gradient" >
<div class="grid-banner {% if section.settings.title == blank %} no-heading{% endif %} {{ section.settings.custom_class_name }} ">
<div class="{% if section.settings.page_full_width %} page-full-width {% else %} page-width {% endif %} {% if section.settings.page_full_width_spacing %} page-full-width_spacing {% endif %}section-{{ section.id }}-padding isolate">
<div class="row"> 
<div class="grid-banner-wrapper">
{%- unless section.settings.title == blank -%}
<div class="title-wrapper-with-link title-wrapper--self-padded-mobile title-wrapper--no-top-margin content-align--{{ section.settings.column_alignment }}">
   {%- if section.settings.sub_heading != blank -%}  
   <h6 class="sub-heading">{{ section.settings.sub_heading | escape }}</h6>
   {%- endif -%} 
   {%- if section.settings.title != blank -%}  
    <h2 class="title {{ section.settings.heading_size }}">
      {{ section.settings.title | escape }}
    </h2>
    {%- endif -%} 
    {%- if section.settings.description != blank -%}  
    <p class="description">{{ section.settings.description }}</p>
    {%- endif -%}   
    {%- if section.settings.button_label != blank -%}
      <a {% if section.settings.button_link == blank %} role="link" aria-disabled="true"{% else %} href="{{ section.settings.button_link }}"{% endif %} class="button{% if section.settings.button_style_secondary %} button--secondary{% else %} button--primary{% endif %}">{{ section.settings.button_label | escape }}</a>
    {%- endif -%}
    </div>
{%- endunless -%}
 {% unless enable_slider %}
<div class="grid-banner-section {{ column }} overlay ">
    {% else %}
<swiper-slider class="team-section-slider  overlay ">
<div data-slider-options='{"loop": "{%- if section.settings.centered_slide -%}1{%- else -%}2{%- endif -%}","desktop": "{{ section.settings.desktop_column }}", "tablet": "{{ section.settings.tablet_column }}","mobile": "{{ section.settings.mobile_column }}","auto_play": "{{ section.settings.auto_play }}"}'>
<div class="swiper" data-swiper-slider>
  <div class="swiper-wrapper">
   {%- endunless -%}
 {%- for block in section.blocks -%} 
 <div class="{% unless enable_slider %}grid-banner-wrapper{% else %} swiper-slide{% endunless %} {% if block.settings.reverse_column %}reverse-columns{% endif %}">   
   <div class="grid-banner-block-image">
    {%- if block.settings.enable_title_link %} <a href="{{ block.settings.block_button_link }}" class="grid-banner-image">{% endif %}
    {% if block.settings.block_image != blank %}
      <img
                class="grid-banner-image"
                srcset="{%- if block.settings.block_image.width >= 375 -%}{{ block.settings.block_image | image_url: width: 375 }} 375w,{%- endif -%}
                {%- if block.settings.block_image.width >= 550 -%}{{ block.settings.block_image | image_url: width: 550 }} 550w,{%- endif -%}
                {%- if block.settings.block_image.width >= 750 -%}{{ block.settings.block_image | image_url: width: 750 }} 750w,{%- endif -%}
                {%- if block.settings.block_image.width >= 1100 -%}{{ block.settings.block_image | image_url: width: 1100 }} 1100w,{%- endif -%}
                {%- if block.settings.block_image.width >= 1500 -%}{{ block.settings.block_image | image_url: width: 1500 }} 1500w,{%- endif -%}
                {%- if block.settings.block_image.width >= 1780 -%}{{ block.settings.block_image | image_url: width: 1780 }} 1780w,{%- endif -%}
                {%- if block.settings.block_image.width >= 2000 -%}{{ block.settings.block_image | image_url: width: 2000 }} 2000w,{%- endif -%}
                {%- if block.settings.block_image.width >= 3000 -%}{{ block.settings.block_image | image_url: width: 3000 }} 3000w,{%- endif -%}
                {%- if block.settings.block_image.width >= 3840 -%}{{ block.settings.block_image | image_url: width: 3840 }} 3840w,{%- endif -%}
                {{ block.settings.block_image | image_url: width: 1500 }} {{ block.settings.block_image.width }}w"
                sizes="100vw"
                src="{{ block.settings.block_image | image_url: width: 1500 }}"
                loading="lazy"              
                alt="{{ block.settings.block_image.alt | escape }}"
                width="{{ block.settings.block_image.width }}"
                height="{{ block.settings.block_image.width | divided_by: block.settings.block_image.aspect_ratio | round }}"
              >  
      {% else %}
       {%- assign placeholder_image_index = forloop.index0 | modulo: 4 | plus: 1 -%}
      {%- assign placeholder_image = 'hero-apparel-' | append: placeholder_image_index -%}
       {{ placeholder_image | placeholder_svg_tag: 'placeholder-svg' }} 
      {% endif %}
    </a>
   </div>    
    <div class="grid-banner-content {% unless block.settings.show_content %}hidden {% endunless %}">
       <div class="grid-banner-inner banner--content-align  color-{{ block.settings.block_color_scheme }} gradient {% if block.settings.background-none %}  background-none {% endif %} ">
          {% if block.settings.block_title != blank %}
              {% if block.settings.block_sub_title != blank %}
              <h4 class="sub-title">{{ block.settings.block_sub_title }}</h4>
              {% endif %}
              <h2 class="main-title {{ block.settings.block_heading_size }}">
              {% if block.settings.enable_title_link %}<a href="{{ block.settings.block_button_link }}">{% endif %}
              {{ block.settings.block_title }}
              {% if block.settings.enable_title_link %}</a>{% endif %}
              </h2>
              {% endif %}
              {% if block.settings.block_description != blank %}
              <h4 class="description">{{ block.settings.block_description }}</h4>
              {% endif %}      
              {% if block.settings.block_button_link != blank %}
              <a href="{{ block.settings.block_button_link }}" class="banner-button button button--primary">{{ block.settings.block_button_text }}</a>
           {% endif %}
       </div> 
    </div> 
 </div>  
   {% endfor %}   
{% unless enable_slider %}
</div> 
{% else %}
 </div>
  {% if section.settings.swiper_navigation != blank %}
  <div class="swiper-button-next"><span></span></div>
  <div class="swiper-button-prev"><span></span></div>
  {% endif %}
</div>
{% if section.settings.swiper_pagination != blank %}
<div class="swiper-pagination"></div>
{% endif %}
  </div>
</swiper-slider>
{%- endunless -%}

</div>
</div>
</div>
</div>  
</div>
<!-- Script-Start -->

<script type="text/javascript">
$(document).ready(function(){
$( ".gridPlay" ).each(function( index ) {
$(this).on('click', function(){
$(this).css('display','none');
$(this).next('.gridPause').css('display','flex');
$(this).closest('.dt-sc-grid-banner').find('video').trigger('play');
});
});
$( ".gridPause" ).each(function( index ) {
$(this).on('click', function(){
$(this).css('display','none');
$(this).prev('.gridPlay').css('display','flex');
$(this).closest('.dt-sc-grid-banner').find('video').trigger('pause');
});
});
});
</script>

<!-- Script-End -->
{% schema %}
{
"name": "grid-banner-typ3",
"class": "section section-grid-banner",
  "max_blocks":2,
"tag": "section",
"settings": [
{
"type":"checkbox",
"id":"page_full_width",
 "default": false,
"label": "t:sections.all.page_full_width.label"
},
{
"type":"checkbox",
"id":"page_full_width_spacing",
 "default": false,
"label": "t:sections.all.page_full_width_spacing.label"
},
{
"type": "text",
"id": "title",
"default": "Grid banner",
"label": "t:sections.all.title.label"
},
{
"type": "select",
"id": "heading_size",
"options": [
  {
    "value": "h2",
    "label": "t:sections.all.heading_size.options__1.label"
  },
  {
    "value": "h1",
    "label": "t:sections.all.heading_size.options__2.label"
  },
  {
    "value": "h0",
    "label": "t:sections.all.heading_size.options__3.label"
  }
],
"default": "h1",
"label": "t:sections.all.heading_size.label"
},
{
"type": "text",
"id": "sub_heading",
"label": "t:sections.all.sub_heading.label"
},
{
"type": "text",
"id": "description",
"label": "t:sections.all.description.label"
},
{
"type": "text",
"id": "button_label",
"default": "Button label",
"label": "t:sections.all.button_label.label"
},
{
"type": "url",
"id": "button_link",
"label": "t:sections.all.button_link.label"
},
{
"type": "checkbox",
"id": "button_style_secondary",
"default": false,
"label": "t:sections.all.button_style_secondary.label"
},
{
"type": "select",
"id": "column_alignment",
"options": [
  {
    "value": "left",
    "label": "t:sections.grid-banner-typ3.settings.column_alignment.options__1.label"
  },
  {
    "value": "center",
    "label": "t:sections.grid-banner-typ3.settings.column_alignment.options__2.label"
  }
],
"default": "left",
"label": "t:sections.grid-banner-typ3.settings.column_alignment.label"
},
{
"type": "select",
"id": "grid-column",
"options": [
{
"value": "1",
"label": "t:sections.grid-banner-typ3.settings.grid-column.options__1.label"
},
{
"value": "2",
"label": "t:sections.grid-banner-typ3.settings.grid-column.options__2.label"
},
{
"value": "3",
"label": "t:sections.grid-banner-typ3.settings.grid-column.options__3.label"
}
],
"default": "2",
"label": "t:sections.grid-banner-typ3.settings.grid-column.label" 
},
{
"type": "text",
"id": "column_gap",
"label": "t:sections.grid-banner-typ3.settings.column_gap.label"
},
{
"type": "header",
"content": "t:sections.grid-banner-typ3.settings.overlay_image_height_settings.content"
},
{
"type": "range",
"id": "overlay_height",
"min": 0,
"max": 1000,
"step": 100,
"unit": "px",
"default": 400,
"label": "t:sections.grid-banner-typ3.settings.overlay_height.label"
},
{
"type": "range",
"id": "overlay_height_laptop",
"min": 0,
"max": 1000,
"step": 100,
"unit": "px",
"default": 300,
"label": "t:sections.grid-banner-typ3.settings.overlay_height_laptop.label"
},
{
"type": "range",
"id": "overlay_height_tab",
 "min": 0,
"max": 1000,
"step": 100,
"unit": "px",
"default": 300,
"label": "t:sections.grid-banner-typ3.settings.overlay_height_tab.label"
},
{
"type": "range",
"id": "overlay_height_mobile",
"min": 0,
"max": 1000,
"step": 100,
"unit": "px",
"default": 200,
"label": "t:sections.grid-banner-typ3.settings.overlay_height_mobile.label"
},
{
"type": "select",
"id": "color_scheme",
"options": [
  {
    "value": "accent-1",
    "label": "t:sections.all.colors.accent_1.label"
  },
  {
    "value": "accent-2",
    "label": "t:sections.all.colors.accent_2.label"
  },
  {
    "value": "background-1",
    "label": "t:sections.all.colors.background_1.label"
  },
  {
    "value": "background-2",
    "label": "t:sections.all.colors.background_2.label"
  },
  {
    "value": "inverse",
    "label": "t:sections.all.colors.inverse.label"
  }
],
"default": "background-1",
"label": "t:sections.all.colors.label"
},
{
"type": "header",
"content": "t:sections.all.padding.section_padding_heading"
},
{
"type": "range",
"id": "padding_top",
"min": 0,
"max": 120,
"step": 4,
"unit": "px",
"label": "t:sections.all.padding.padding_top",
"default": 36
},
{
"type": "range",
"id": "padding_bottom",
"min": 0,
"max": 120,
"step": 4,
"unit": "px",
"label": "t:sections.all.padding.padding_bottom",
"default": 36
},
{
"type": "header",
"content": "t:sections.all.custom_class_heading.content"
},
{
"type": "text",
"id": "custom_class_name",
"label": "t:sections.all.custom_class_name.label"
}
],
"blocks": [
{
"type": "text",
"name": "t:sections.grid-banner-typ3.blocks.text.name",
"settings": [
{
"type": "image_picker",
"id": "block_image",
"label": "t:sections.grid-banner-typ3.blocks.text.settings.block_image.label",
"info": "Size: 1280x820 [Act as poster image for video type]"
},
  {
"type": "select",
"id": "block_color_scheme",
"options": [
  {
    "value": "accent-1",
    "label": "t:sections.all.colors.accent_1.label"
  },
  {
    "value": "accent-2",
    "label": "t:sections.all.colors.accent_2.label"
  },
  {
    "value": "background-1",
    "label": "t:sections.all.colors.background_1.label"
  },
  {
    "value": "background-2",
    "label": "t:sections.all.colors.background_2.label"
  },
  {
    "value": "inverse",
    "label": "t:sections.all.colors.inverse.label"
  }
],
"default": "background-1",
"label": "t:sections.all.colors.label"
},
  {
"type": "checkbox",
"id": "background-none",
"label": "t:sections.grid-banner-typ3.blocks.text.settings.background-none.label",
"default": true
},
{
"type": "checkbox",
"id": "show_content",
"label": "t:sections.grid-banner-typ3.blocks.text.settings.show_content.label",
"default": true
},
{
"type": "text",
"id": "block_title",
"label": "t:sections.grid-banner-typ3.blocks.text.settings.block_title.label",
"default": "Title"
},
  {
"type": "select",
"id": "block_heading_size",
"options": [
  {
    "value": "h2",
    "label": "t:sections.all.heading_size.options__1.label"
  },
  {
    "value": "h1",
    "label": "t:sections.all.heading_size.options__2.label"
  },
  {
    "value": "h0",
    "label": "t:sections.all.heading_size.options__3.label"
  }
],
"default": "h1",
"label": "t:sections.all.heading_size.label"
},
{
"type": "text",
"id": "block_sub_title",
"label": "t:sections.grid-banner-typ3.blocks.text.settings.block_sub_title.label",
"default": "Sub title"
},
{
"type": "text",
"id": "block_description",
"label": "t:sections.grid-banner-typ3.blocks.text.settings.block_description.label",
"default": "Short description"
},
{
"type": "text",
"id": "block_button_text",
"label": "t:sections.grid-banner-typ3.blocks.text.settings.block_button_text.label"
},
{
"type": "url",
"id": "block_button_link",
"label": "t:sections.grid-banner-typ3.blocks.text.settings.block_button_link.label"
}   
]
}
],
"presets": [
 {
      "name": "grid-banner-typ3",
       "blocks": [
        {
          "type": "text"
        },
        {
          "type": "text"
        }
      ]
    }
  ]
}

{% endschema %}        