.testimonial-video-wrapper .testimonial__heading { margin: 2rem 0 6rem; padding: 0; }
.testimonial-video-wrapper .testimonial-container .testimonial-author { margin: 0; font-weight: 600; font-family: var(--font-heading-family); }
.testimonial-video-wrapper .testimonial-container .testimonial-author a{ padding-right:30px; }
.testimonial-video-wrapper .testimonial-image .img { width: 117px; height: 121px; overflow: hidden; display: block; left: 0; right:0; transition: all 0.3s linear ;}
.testimonial-video-wrapper .testimonial-content blockquote{ padding: 0; margin-top:30px;background:rgb(var(--color-background));transition:all 0.3s linear; border: none; }
.testimonial-video-wrapper .testimonial-content blockquote cite { margin-top:0rem;display: flex;flex-direction: column; }
.testimonial-content blockquote cite span{ padding: 0; margin: 0; }
.testimonial-video-wrapper .testimonial-content blockquote cite span:before { display:none; }
.testimonial-video-wrapper .testimonial-content blockquote > p{ font-style: italic; font-size: 1.8rem; line-height: 34px;margin-top:0;  }
.slider-button--prev .icon { transform: rotate(90deg);}
.testimonial-video-wrapper .testimonials .swiper-button-next svg, .testimonials .swiper-button-prev svg {  display: none;}
.testimonial-video-wrapper blockquote {   margin: 0;  padding: 30px 0 30px 55px; position: relative;}
.testimonial-video-wrapper .testimonials .swiper-controls { position: relative; width: 115px; bottom: 0; right: auto; left: auto; margin: auto;}
.testimonial-video-wrapper .testimonials .swiper-container.testimonialsSwiper{margin-bottom:50px;}
.testimonial-video-wrapper .testimonial-content blockquote.content-center{align-items: center;}
.testimonial-video-wrapper .testimonial-content blockquote{display: flex;flex-direction: column;}
.testimonial-video-wrapper .testimonial-content blockquote.content-center p,
.testimonial-video-wrapper .testimonial-content blockquote.content-center span{text-align:center;}
.testimonial-video-wrapper .testimonial-container .testimonial-image.content-center{display: flex;justify-content: center;}
.testimonial-video-wrapper .testimonial-container .testimonial-image.content-left {justify-content:flex-start;display:flex;}


@media screen and (max-width: 990px) {
  .testimonial-video-wrapper .testimonial-container { grid-template-columns: 1fr 1fr;padding:0;}  
  .testimonial-video-wrapper .testimonial-container blockquote:before { left: 0; }
  .testimonial-video-wrapper .testimonial-image .img { max-width: 200px; }
}

@media screen and (max-width: 750px) {
  .testimonial-video-wrapper .testimonial-container { grid-template-columns: 1fr; }
  .testimonial-video-wrapper .testimonial-container blockquote { margin-top: 3rem;padding:0;  }
  .testimonial-video-wrapper .testimonial-image { justify-content: center; }
  .testimonial-video-wrapper .testimonial-image .img { max-width: 175px; }
}

@media screen and (max-width: 575px) {  
  .testimonial-image .img { max-width: 150px; }
}

/* video banner */
.testimonial-video-wrapper .watch-more {
  display: inline-block;
  color: var( --color-foreground);
  font-size: 14px;
  text-decoration: none;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  background: var( --gradient-background);
  border-radius: 50%;
  transition: all 0.3s linear;
  -webkit-animation: ripple 1s linear infinite;
  animation: ripple 1s linear infinite;
}
 @keyframes ripple {
    0% {
      box-shadow: 
        0 0 0 0 rgba(255, 255, 255 , 0.05), 
        0 0 0 10px rgba(255, 255, 255 , 0.05), 
        0 0 0 30px rgba(255, 255, 255 , 0.05), 
        0 0 0 50px rgba(255, 255, 255 , 0.05);
    }
    100% {
      box-shadow: 
        0 0 0 10px rgba(255, 255, 255 , 0.05), 
        0 0 0 30px rgba(255, 255, 255 , 0.05), 
        0 0 0 50px rgba(255, 255, 255 , 0.05), 
        0 0 0 80px rgba(255, 255, 255 , 0);
    }
  }
.video-play-icon {
    display: flex;
    justify-content: center;
    align-items: center;
}
.watch-more:hover, .watch-more:focus, .watch-more:active {
  color: var(--color-icon);
  background: var(--gradient-base-background-1);
}
svg.icon.icon-play {
    height: 20px;
    width: 20px;
}
body.gradient.overlay-active {
    overflow-y: hidden;
}
/* The Modal (background) */
.modal {
  display: none;
  position: fixed;
  z-index: 1;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.4);
}

.video-popup {
  display: none;
  z-index: 2;
  position: absolute;
  top: 20%;
  left: 50%;
  transform: translateX(-50%);
  border: 1px solid #ccc;
  padding: 10px 20px;
  background-color: #fff;
  border-radius: 4px;
}
.video-popup.visible {
  display: block;
}
.video-popup .close {
  position: absolute;
  right: 8px;
  top: -3px;
  font-weight: 900;
  font-size: 28px;
  color: black;
  padding: 5px 10px;
  border-bottom: none;
  cursor: pointer;
}

.video-wrapper {
  width: 800px;
  margin: 30px auto;
}
@media only screen and (max-width: 560px) {
  .video-wrapper {
    width: 350px;
  }
}
.video-wrapper .video-container {
  position: relative;
  padding-bottom: 55.25%;
  height: 0;
  overflow: hidden;
}
.video-wrapper .video-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.video-section {
    /* backdrop-filter: brightness(0.5); */
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}
/* .video-section__content .title{color:var(--gradient-background);} */
.video-banner .video-section__content p {
    font-family: var(--font-body-family);
    font-weight: 400;
    font-size: 1.8rem;
    padding: 0;
    line-height: calc(1 + 0.3 / max(1, var(--font-heading-scale)));
}
@media screen and (min-width: 991px) {
.video-banner .video-section__content p { padding: 0 18rem;}
.testimonial-video-wrapper .video-banner {
    height: 900px;
  
}  
}
.video-section__content{    text-align: center;}
.video_overlay .inner .close-icon:before  { content: ''; display: block; height: 22px; width: 22px; margin:auto; -webkit-mask:url("data:image/svg+xml;utf8,<svg  xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='0 0 100 100'  xml:space='preserve'> <path d='M57,50l35.2-35.2c1.9-1.9,1.9-5.1,0-7c-1.9-1.9-5.1-1.9-7,0L50,43L14.8,7.7c-1.9-1.9-5.1-1.9-7,0c-1.9,1.9-1.9,5.1,0,7 L43,50L7.7,85.2c-1.9,1.9-1.9,5.1,0,7c1,1,2.2,1.5,3.5,1.5s2.5-0.5,3.5-1.5L50,57l35.2,35.2c1,1,2.2,1.5,3.5,1.5s2.5-0.5,3.5-1.5 c1.9-1.9,1.9-5.1,0-7L57,50z'/></svg>"); 
                           background: currentColor; -webkit-mask-repeat: no-repeat; -webkit-mask-position: center; -webkit-mask-size: 14px;position:relative;top:4px; }
.video_overlay .inner .close-icon{width:30px;height:30px;background:rgba(var(--color-button-text));color:rgba(var(--color-button));}


/* testimonial video */
.testimonial-video-wrapper{display: flex;justify-content: space-between;flex-wrap:wrap;}
.testimonial-video-wrapper .left-block,
.testimonial-video-wrapper .right-block{width:calc(50% - var(--grid-desktop-vertical-spacing));}
@media screen and (max-width: 767px) {
.testimonial-video-wrapper .left-block, .testimonial-video-wrapper .right-block{width:100%;}
.testimonial-video-wrapper .right-block{margin-top: var(--grid-desktop-vertical-spacing);} 
.testimonial-video-wrapper .left-block .video-section {height:450px;}
}
.testimonials-with-video swiper-slider {
    cursor: grab;
}
.testimonial-video-wrapper .right-block swiper-slider .swiper-pagination{position:relative;}
.video_overlay .inner .close-icon:hover{background:rgba(var(--color-base-outline-button-labels)); color:rgb(var(--color-background));}
.video_overlay .inner .close-icon{transition:all 0.3s linear;}