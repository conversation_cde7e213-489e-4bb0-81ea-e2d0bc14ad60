{%- if settings.cart_drawer_collection != blank -%}
  <div class="cart-drawer__collection {% if cart == empty %}is-empty{% endif %}">
    <h4>{{ 'sections.cart.suggested_products' | t }}</h4>
    <featured-swiper-slider class="{% if section.settings.full_width %} slider-component-full-width{% endif %} page-width {% if section.settings.full_width == false %} page-width-desktop{% endif %}">
      <div data-slider-options='{"loop": "1","desktop": "1", "laptop": "1", "tablet": "1","mobile": "1","auto_play": "3"}'>
        <div class="swiper" data-swiper-slider>
          <div
            id="Slider-{{ section.id }}"
            class="product-grid contains-card{% if settings.card_style == 'standard' %} contains-card--standard{% endif %} swiper-wrapper"
            role="list"
            aria-label="{{ 'sections.featured_collection.slider' | t }}"
          >
            {%- for product in settings.cart_drawer_collection.products limit: 5 -%}
              <div id="Slide-{{ section.id }}-{{ forloop.index }}" class="swiper-slide">
                {% render 'card-product-for-drawer',
                  card_product: product,
                  media_aspect_ratio: section.settings.image_ratio,
                  show_secondary_image: section.settings.show_secondary_image,
                  show_rating: section.settings.show_rating,
                  show_quick_add: settings.enable_quickadd,
                  section_id: section.id
                %}
              </div>
            {%- else -%}
              {%- for i in (1..4) -%}
                <div class="grid__item">
                  {% render 'card-product', show_vendor: section.settings.show_vendor %}
                </div>
              {%- endfor -%}
            {%- endfor -%}
          </div>
          <div class="swiper-pagination"></div>
        </div>
      </div>
    </featured-swiper-slider>
  </div>
{%- endif -%}
