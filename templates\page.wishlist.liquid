{{ 'page-wishlist.css' | asset_url | stylesheet_tag }}
{%  style %}
 .breadcrumb a{color: rgba(var(--color-foreground),1);}  
 .breadcrumb a:hover{ color: rgb(var(--color-base-outline-button-labels));}
 .breadcrumb{padding-top:{{settings.breadcrumb_padding_top}}px;padding-bottom:{{settings.breadcrumb_padding_bottom}}px;margin-bottom:{{settings.breadcrumb_margin_bottom}}px;position: relative;z-index: 1;}  
 .breadcrumb .breadcrumb_title{margin:0; font-weight: 500;font-size: 4rem;}
 .breadcrumb a, .breadcrumb span{display: inline-block;margin-top:1rem;font-size:1.8rem;font-weight:400; padding: 0 0.4rem;} 
 .breadcrumb.text-center{text-align:center;}  
 .breadcrumb.text-start{text-align:left;}  
 .breadcrumb.text-end{text-align:right;}
 .breadcrumb:before { position: absolute; content: "";  display: block;  width: 100%;  height: 100%;  left: 0;  top: 0;  z-index: -1;background:url({{ settings.breadcrumb_image | image_url: width: 1920 }});opacity:.{{settings.image_overlay_opacity}};}  
/*  span.breadcrumb__sep:after {
    content: '\f105';
    color: var(--color-icon);
    font-family: 'FontAwesome';
} */
  .page-width.wishlist{padding:100px 0;}
   span.breadcrumb__sep svg {
    width: 1rem;
    height: 1rem;
    fill: rgba(var(--color-base-accent-1),0.6);
}
 @media screen and (max-width: 990px) {  
 .breadcrumb{padding-top:calc( {{settings.breadcrumb_padding_top}}px / 2 );padding-bottom:calc( {{settings.breadcrumb_padding_bottom}}px / 2 );margin-bottom:calc( {{settings.breadcrumb_margin_bottom}}px / 2 );}   
 }  
  @media screen and (max-width: 480px) { 
    .breadcrumb .breadcrumb_title{font-size: 2.6rem;}
    .breadcrumb a, .breadcrumb span{font-size:1.4rem;}
  }
  .dtx-grid-empty.dtx-grid-show{    
  text-align: center;
    display: flex;
    align-items: center;
    flex-direction: column;
  }
{% endstyle %}
<nav class="breadcrumb text-{{ settings.breadcrumb_style }}"  aria-label="breadcrumbs {{ request.page_type }}">
   <div class="page-width">
    <div class="row"> 
    <h1 class="breadcrumb_title">{{ page.title }}</h1> 
    <a href="/" title="{{ 'general.breadcrumbs.home_link_title' | t }}">{{ 'general.breadcrumbs.home' | t }}</a>

    <span aria-hidden="true" class="breadcrumb__sep">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M 7.75 1.34375 L 6.25 2.65625 L 14.65625 12 L 6.25 21.34375 L 7.75 22.65625 L 16.75 12.65625 L 17.34375 12 L 16.75 11.34375 Z"></path></svg>
    </span>
    <span>{{ page.title }}</span>
    </div>
   </div>
</nav>
<div class="page-width wishlist">
  <dtx-wishlist-grid grid_type="wishList">
    <div class="dtx-grid-empty">
      <h2>{{ 'products.wishlist.no_records' | t }}</h2>
      <a href="{{ routes.all_products_collection_url }}" class=" button button--primary">
        {{ 'general.continue_shopping' | t }}
      </a>
    </div>

    <table class="dtx-table">
      
      <tbody class="slick-wrapper">
        <tr id="row_{item.product_handle}" class="grid_template" style="display: none;">
            
          <td class="product-thumbnail">
            <a href="{item.product_url}">
              <img loading="lazy" src="{item.product_image}" alt="{item.product_title}" width="" height="">
            </a>
          </td>
          <td class="product-name">
            <a href="{item.product_url}">{item.product_title}</a>
          </td>
          <td class="product-price-cart">
            {item.money_price}
          </td>
          <td class="product-wishlist-cart">
            <a href="{item.product_url}" data-product-variant-id="{item.variant_id}" class="dt-sc-btn button product-cart">
              Select Options
            </a>
          </td>
          <td>
            <dtx-remove-wish-item grid_type="wishList">
              <a href="javascript:void(0);" data-product_handle="{item.product_handle}" class="remove-item  product-cart">
                 <span> Remove </span> {% render 'icon-remove' %}
              </a>
            </dtx-remove-wish-item>
          </td>
        </tr>
      </tbody>
    </table>
  </dtx-wishlist-grid>
</div>
