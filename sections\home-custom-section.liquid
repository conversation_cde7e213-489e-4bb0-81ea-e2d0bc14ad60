{%- liquid
    if section.blocks.size > 0 
    assign listWidth = section.settings.content_width | split: '/'
    assign leftColumn = listWidth[0]
    assign rightColumn = listWidth[1]  
    
    assign listWidth = section.settings.lap_content_width | split: '/' 
    assign lap_leftColumn = listWidth[0]
    assign lap_rightColumn = listWidth[1] 
     endif 
-%}

{%- style -%}
    .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.50 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.50 | round: 0 }}px;
  }
   @media screen and (min-width: 576px) and (max-width: 749px){
   .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }
   }
  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
  .home-custom-section .home-custom-section-wrapper {
    position: relative;
    display: flex;
    flex-wrap: nowrap;    
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    background: none;
    align-self: normal;
    min-width: 0;
    word-wrap: break-word;
    background-clip: border-box;
    padding: 0;
    overflow: hidden; gap: var(--grid-desktop-horizontal-spacing); justify-content: space-between;
  }
  .home-custom-section .home-custom-section-wrapper .dt-sc-image-gallery { display: grid; grid-template-columns: repeat(2, 1fr);
    gap: var(--DTGutter_Width); gap: {{section.settings.column_gap}}px;
      }
  {% for block in section.blocks %}
  .home-custom-section .home-custom-section-wrapper .dt-sc-custom-block.{{block.type}}-{{block.id}} .dt-sc-heading:empty { display: none; }
  {% if block.settings.fullwidth_images %}
  .home-custom-section .home-custom-section-wrapper .dt-sc-custom-block.{{block.type}}-{{block.id}} .dt-sc-image-gallery { grid-template-columns: 1fr; }
  {% endif %}
  {% endfor %}
  .home-custom-section .home-custom-section-wrapper .dt-sc-custom-block { display: flex; flex-wrap: wrap; align-self: flex-start; }
  .home-custom-section .home-custom-section-wrapper .dt-sc-custom-block > * { width: 100%; }
  .home-custom-section .home-custom-section-wrapper .dt-sc-custom-block.dt-sc-reverse-columns { flex-direction: column-reverse; }
  .home-custom-section .home-custom-section-wrapper .dt-sc-custom-block.dt-sc-reverse-columns .dt-sc-heading {
    margin-top: 50px; margin-bottom: 0; }
  .home-custom-section .home-custom-section-wrapper .dt-sc-custom-block .dt-sc-heading > *:not(:last-child){margin:0 0 10px;}
  .home-custom-section .home-custom-section-wrapper .dt-sc-custom-block .dt-sc-heading{margin-bottom:20px;}
  .home-custom-section .home-custom-section-wrapper .dt-sc-custom-block .dt-sc-heading.text-center{text-align:center;}
  .home-custom-section .home-custom-section-wrapper .dt-sc-custom-block .dt-sc-heading.text-start{text-align:left;}
  .home-custom-section .home-custom-section-wrapper .dt-sc-custom-block .dt-sc-heading.text-end{text-align:right;}
  .home-custom-section{position:relative;overflow:hidden;}
  .home-custom-section .home-custom-section-wrapper .dt-sc-heading  h4.dt-sc-main-heading{font-weight:600;}
  .home-custom-section:before{
      content:'';
    {% if section.settings.bg_image != blank %}
      background:url('{{ section.settings.bg_image |  image_url: width: 1920 }}');
      {% endif %}
     width:100%;
     height:100%;position:absolute;background-size: cover;background-repeat: no-repeat;}
   }
  
 
  
  @media (min-width: 1541px) {
    .home-custom-section .home-custom-section-wrapper .dt-sc-custom-block:first-child {
      width: calc({{leftColumn}}% - (calc({{section.settings.column_gap}}px)/2)); }
    .home-custom-section .home-custom-section-wrapper .dt-sc-custom-block:last-child {
      width: calc({{rightColumn}}% - (calc({{section.settings.column_gap}}px)/2)); }
    .home-custom-section .home-custom-section-wrapper .dt-sc-custom-block:only-child {
      width: 100%;
    }
    
  }
  @media (max-width: 1540px) {
    .home-custom-section .home-custom-section-wrapper .dt-sc-custom-block:first-child {
      width: calc({{lap_leftColumn}}% - (calc({{section.settings.column_gap}}px)/2)); }
    .home-custom-section .home-custom-section-wrapper .dt-sc-custom-block:last-child {
      width: calc({{lap_rightColumn}}% - (calc({{section.settings.column_gap}}px)/2)); }
    .home-custom-section .home-custom-section-wrapper .dt-sc-custom-block:only-child {
      width: 100%;
    }
    
  }
  @media only screen and (max-width: 1199px) {
    .home-custom-section .home-custom-section-wrapper { display: grid; gap: {{section.settings.column_gap}}px; }
    .home-custom-section .home-custom-section-wrapper .dt-sc-custom-block:first-child,
    .home-custom-section .home-custom-section-wrapper .dt-sc-custom-block:last-child {  width: 100%; }
    
    .home-custom-section .home-custom-section-wrapper .dt-sc-image-gallery { grid-template-columns: 1fr 1fr; }
    .home-custom-section .home-custom-section-wrapper .dt-sc-image-gallery img{width:100%; height:100%;}
  }
  @media only screen and (max-width: 480px) {
     .home-custom-section .home-custom-section-wrapper .dt-sc-image-gallery { grid-template-columns: 1fr; }
  }
{%- endstyle -%}

<div class="home-custom-section color-{{ section.settings.color_scheme }} gradient{% if section.settings.title == blank %} no-heading{% endif %} {{ section.settings.custom_class_name }} ">
 <div class="{% if section.settings.page_full_width %} page-full-width {% else %} page-width {% endif %} {% if section.settings.page_full_width_spacing %} page-full-width_spacing {% endif %} section-{{ section.id }}-padding isolate">
 <div class="row"> 
     {%- unless section.settings.title == blank -%}
      <div class="title-wrapper-with-link title-wrapper--self-padded-mobile title-wrapper--no-top-margin content-align--{{ section.settings.column_alignment }}">
         {%- if section.settings.sub_heading != blank -%}  
         <h6 class="sub-heading">{{ section.settings.sub_heading | escape }}</h6>
         {%- endif -%} 
         {%- if section.settings.title != blank -%}  
          <h2 class="title {{ section.settings.heading_size }}">
            {{ section.settings.title | escape }}
          </h2>
          {%- endif -%} 
          {%- if section.settings.description != blank -%}  
          <p class="description">{{ section.settings.description }}</p>
          {%- endif -%}   
          {%- if section.settings.button_label != blank -%}
            <a {% if section.settings.button_link == blank %} role="link" aria-disabled="true"{% else %} href="{{ section.settings.button_link }}"{% endif %} class="button{% if section.settings.button_style_secondary %} button--secondary{% else %} button--primary{% endif %}">{{ section.settings.button_label | escape }}</a>
          {%- endif -%}
          </div>
    {%- endunless -%}
    <div class="home-custom-section-wrapper">
    {% for block in section.blocks %}
        <div class="dt-sc-custom-block {{block.type}}-{{block.id}} {% if block.settings.reverse_column %}dt-sc-reverse-columns{% endif %}">
          {% if block.settings.block_heading != blank or block.settings.block_sub_heading != blank or block.settings.block_description != blank or block.settings.block_button_text or block.settings.block_button_link != blank %}
          <div class="dt-sc-heading {{ block.settings.heading_position }}">
            {%- if block.settings.block_sub_heading != blank -%}
            <h6 class="dt-sc-sub-heading">{{block.settings.block_sub_heading}}</h6>
            {%- endif -%}
            {%- if block.settings.block_heading != blank -%}
            <h4 class="dt-sc-main-heading">{{block.settings.block_heading}}</h4>
            {%- endif -%}
            {%- if block.settings.block_description != blank -%}
            <p class="dt-sc-heading-description">{{block.settings.block_description}}</p>
            {%- endif -%}
            {%- if block.settings.html != blank -%}
            <div class="dt-sc-heading-html rte">{{block.settings.html}}</div>
            {%- endif -%}
            {%- if block.settings.block_button_link != blank and block.settings.block_button_text != blank -%}
            <a href="{{block.settings.block_button_link}}" class="button">{{block.settings.block_button_text}}</a>
            {%- endif -%}
          </div>
          {% endif %}
          
          <div class="dt-sc-image-gallery">
            {% if block.settings.block_img_1 != blank   %}
            <img
                      class="dt-sc-block-image"
                      srcset="{%- if block.settings.block_img_1.width >= 275 -%}{{ block.settings.block_img_1 | image_url: width: 275 }} 275w,{%- endif -%}
                        {%- if block.settings.block_img_1.width >= 550 -%}{{ block.settings.block_img_1 | image_url: width: 550 }} 550w,{%- endif -%}
                        {%- if block.settings.block_img_1.width >= 710 -%}{{ block.settings.block_img_1 | image_url: width: 710 }} 710w,{%- endif -%}
                        {%- if block.settings.block_img_1.width >= 1420 -%}{{ block.settings.block_img_1 | image_url: width: 1420 }} 1420w,{%- endif -%}
                        {{ block.settings.block_img_1 | image_url }} {{ block.settings.block_img_1.width }}w"
                      src="{{ block.settings.block_img_1 | image_url: width: 550 }}"
                      sizes="(min-width: 990px) {% if section.blocks.size <= 2 %}710px{% else %}550px{% endif %},
                        (min-width: 750px) {% if section.blocks.size == 1 %}710px{% else %}550px{% endif %},
                        calc(100vw - 30px)"
                      alt="{{ block.settings.block_img_1.alt }}"
                      height="{{ block.settings.block_img_1.height }}"
                      width="{{ block.settings.block_img_1.width }}"
                      loading="lazy"
                    >
             {% else %}
            {{ 'image' | placeholder_svg_tag: 'placeholder_svg' }}
            {% endif %}

            {% if block.settings.block_img_2 != blank   %}
            <img
                      class="dt-sc-block-image"
                      srcset="{%- if block.settings.block_img_2.width >= 275 -%}{{ block.settings.block_img_2 | image_url: width: 275 }} 275w,{%- endif -%}
                        {%- if block.settings.block_img_2.width >= 550 -%}{{ block.settings.block_img_2 | image_url: width: 550 }} 550w,{%- endif -%}
                        {%- if block.settings.block_img_2.width >= 710 -%}{{ block.settings.block_img_2 | image_url: width: 710 }} 710w,{%- endif -%}
                        {%- if block.settings.block_img_2.width >= 1420 -%}{{ block.settings.block_img_2 | image_url: width: 1420 }} 1420w,{%- endif -%}
                        {{ block.settings.block_img_2 | image_url }} {{ block.settings.block_img_2.width }}w"
                      src="{{ block.settings.block_img_2 | image_url: width: 550 }}"
                      sizes="(min-width: 990px) {% if section.blocks.size <= 2 %}710px{% else %}550px{% endif %},
                        (min-width: 750px) {% if section.blocks.size == 1 %}710px{% else %}550px{% endif %},
                        calc(100vw - 30px)"
                      alt="{{ block.settings.block_img_2.alt }}"
                      height="{{ block.settings.block_img_2.height }}"
                      width="{{ block.settings.block_img_2.width }}"
                      loading="lazy"
                    >
             {% else %}
            {{ 'image' | placeholder_svg_tag: 'placeholder_svg' }}
            {% endif %}
          </div>
          
        </div>
        {% endfor %}
  </div>
 </div>
 </div>
</div>
{% schema %}
{
  "name": "t:sections.custom-section.name",
  "class": "section section-custom-section",
  "max_blocks":2,
  "tag": "section",
  "settings": [
    {
      "type":"checkbox",
      "id":"page_full_width",
       "default": false,
      "label": "t:sections.all.page_full_width.label"
    },
    {
      "type":"checkbox",
      "id":"page_full_width_spacing",
       "default": false,
      "label": "t:sections.all.page_full_width_spacing.label"
    },
    {
    "type": "image_picker",
    "id": "bg_image",
    "label": "t:sections.custom-section.settings.bg_image.label"
    },
    {
      "type": "text",
      "id": "title",
      "default": "Home Custom Section",
      "label": "t:sections.all.title.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__3.label"
        }
      ],
      "default": "h1",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "text",
      "id": "sub_heading",
      "default": "Sub Heading", 
      "label": "t:sections.all.sub_heading.label"
    },
     {
      "type": "text",
      "id": "description",
      "default": "Use This Text To Share The Information Which You Like!.",   
      "label": "t:sections.all.description.label"
    },
    {
      "type": "text",
      "id": "button_label",
      "default": "Button label",
      "label": "t:sections.all.button_label.label"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "t:sections.all.button_link.label"
    },
    {
      "type": "checkbox",
      "id": "button_style_secondary",
      "default": false,
      "label": "t:sections.all.button_style_secondary.label"
      },
    {
      "type": "select",
      "id": "column_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.custom-section.settings.column_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.custom-section.settings.column_alignment.options__2.label"
        }
      ],
      "default": "center",
      "label": "t:sections.custom-section.settings.column_alignment.label"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "t:sections.all.colors.accent_1.label"
        },
        {
          "value": "accent-2",
          "label": "t:sections.all.colors.accent_2.label"
        },
        {
          "value": "background-1",
          "label": "t:sections.all.colors.background_1.label"
        },
        {
          "value": "background-2",
          "label": "t:sections.all.colors.background_2.label"
        },
        {
          "value": "inverse",
          "label": "t:sections.all.colors.inverse.label"
        }
      ],
      "default": "background-1",
      "label": "t:sections.all.colors.label"
    },
    {
    "type": "header",
    "content": "t:sections.custom-section.settings.content_width_settings.content"
    }, 
    {
    "type": "text",
    "id": "content_width",
    "label": "t:sections.custom-section.settings.content_width.label",
    "default":"50/50"
    },
    {
    "type": "text",
    "id": "lap_content_width",
    "label": "t:sections.custom-section.settings.lap_content_width.label",
    "default":"40/60"
    },
    {
    "type": "text",
    "id": "column_gap",
    "label": "t:sections.custom-section.settings.column_gap.label",
    "default":"30"
    },
    
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
    {
    "type": "header",
    "content": "t:sections.all.custom_class_heading.content"
    },
    {
    "type": "text",
    "id": "custom_class_name",
    "label": "t:sections.all.custom_class_name.label"
    }
  ],
  "blocks": [
{
"type": "content",
"name": "t:sections.custom-section.blocks.content.name",    
"settings": [
{
"type": "text",
"id": "block_heading",
"label": "t:sections.custom-section.blocks.content.settings.block_heading.label",
"default": "Main Heading"
},
{
"type": "text",
"id": "block_sub_heading",
"label": "t:sections.custom-section.blocks.content.settings.block_sub_heading.label",
"default": "Sub Heading"
},  
{
"type": "text",
"id": "block_description",
"label": "t:sections.custom-section.blocks.content.settings.block_description.label",
"default": "Use this text to share the information which you like!."
},    
{
"type": "html",
"id": "html",
"label": "t:sections.custom-section.blocks.content.settings.html.label",
"default": "<p>Enter text with html tags</p>"
},    
{
"type": "text",
"id": "block_button_text",
"label": "t:sections.custom-section.blocks.content.settings.block_button_text.label"
},
{
"type": "url",
"id": "block_button_link",
"label": "t:sections.custom-section.blocks.content.settings.block_button_link.label"
},
{
"type": "select",
"id": "heading_position",
"label": "t:sections.custom-section.blocks.content.settings.heading_position.label",
"default": "text-center",
"options": [
{
"value": "text-center",
"label": "t:sections.custom-section.blocks.content.settings.heading_position.options__1.label"
},
{
"value": "text-start",
"label": "t:sections.custom-section.blocks.content.settings.heading_position.options__2.label"
},
{
"value": "text-end",
"label": "t:sections.custom-section.blocks.content.settings.heading_position.options__3.label"
}
]
},
{
"type": "header",
"content": "t:sections.custom-section.blocks.content.settings.image_settings.content"
},
{
"type": "image_picker",
"id": "block_img_1",
"label": "t:sections.custom-section.blocks.content.settings.block_img_1.label"
},  
{
"type": "image_picker",
"id": "block_img_2",
"label": "t:sections.custom-section.blocks.content.settings.block_img_2.label"  
}
]
}
  ],
    "presets": [
    {
      "name": "t:sections.custom-section.presets.name",
      "blocks": [
        {
          "type": "content"
        },
        {
          "type": "content"
        }
     ]
    }
  ]
}

{% endschema %}  