{"general": {"password_page": {"login_form_heading": "Enter store using password:", "login_password_button": "Enter using password", "login_form_password_label": "Password", "login_form_password_placeholder": "Your password", "login_form_error": "Wrong password!", "login_form_submit": "Enter", "admin_link_html": "Are you the store owner? <a href=\"/admin\" class=\"link underlined-link\">Log in here</a>", "powered_by_shopify_html": "This shop will be powered by {{ shopify }}"}, "social": {"alt_text": {"share_on_facebook": "Share on Facebook", "share_on_twitter": "Tweet on Twitter", "share_on_pinterest": "Pin on Pinterest"}, "links": {"twitter": "TW", "facebook": "FB", "pinterest": "Pinterest", "instagram": "IN", "tumblr": "Tumblr", "snapchat": "Snapchat", "youtube": "YouTube", "vimeo": "Vimeo", "tiktok": "TikTok"}}, "social_proof": {"sold": {"label": "Items sold"}, "products": {"label": "in last"}, "hours": {"label": "hours"}}, "continue_shopping": "Continue shopping", "pagination": {"label": "Pagination", "page": "Page {{ number }}", "next": "Next page", "previous": "Previous page", "load_more": "Load more", "no_more": "No more product", "count_text": "You're viewing {{current_count}} of {{total_count}} products"}, "search": {"search": "Search", "search_title": "Search in store", "category_title": "Popular searches:", "recently_purchased_title": "Featured Products"}, "cart": {"view": "View my cart ({{ count }})", "item_added": "Item added to your cart"}, "share": {"close": "Close share", "copy_to_clipboard": "Copy link", "share_url": "Link", "success_message": "Link copied to clipboard", "share_label": "Share"}, "slider": {"of": "of", "next_slide": "Slide right", "previous_slide": "Slide left"}, "breadcrumbs": {"home": "Home", "home_link_title": "Back to the frontpage", "all_collections": "All collections", "products": "Product", "collection": "Collection", "all": "All"}}, "newsletter": {"label": "email", "success": "Thanks for subscribing", "button_label": "Subscribe"}, "accessibility": {"skip_to_text": "Skip to content", "skip_to_product_info": "Skip to product information", "close": "Close", "unit_price_separator": "per", "vendor": "Vendor:", "error": "Error", "refresh_page": "Choosing a selection results in a full page refresh.", "link_messages": {"new_window": "Opens in a new window.", "external": "Opens external website."}, "loading": "Loading...", "total_reviews": "total reviews", "star_reviews_info": "{{ rating_value }} out of {{ rating_max }} stars", "collapsible_content_title": "Collapsible content"}, "blogs": {"article": {"older_post": "Previous Story", "newer_post": "Next Story", "blog": "Blog", "read_more_title": "Read more: {{ title }}", "comments": {"one": "{{ count }} comment", "other": "{{ count }} comments"}, "moderated": "Please note, comments need to be approved before they are published.", "comment_form_title": "Leave a Reply", "comments_desc": "Nunc vehicula quam semper odio varius tincidunt. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posue.", "name": "Name", "email": "Email", "message": "Your message", "comment_text": "Save my name, email, and website in this browser.", "post": "Submit Now", "back_to_blog": "Back to blog", "share": "Share this article", "success": "Your comment was posted successfully! Thank you!", "success_moderated": "Your comment was posted successfully. We will publish it in a little while, as our blog is moderated."}}, "onboarding": {"product_title": "Example product title", "collection_title": "Your collection's name"}, "products": {"notify": {"text1": "Click Here", "text2": "to be notified by email when", "text3": "becomes available"}, "product": {"add_to_cart": "Add to cart", "choose_options": "Choose options", "choose_product_options": "Choose options for {{ product_name }}", "description": "Description", "inventory_in_stock": "In stock", "inventory_in_stock_show_count": "{{ quantity }} in stock", "inventory_low_stock": "Low stock", "inventory_low_stock_show_count": "Hurry! Only {{ quantity }} left in stock", "inventory_out_of_stock": "Out of stock", "inventory_out_of_stock_continue_selling": "In stock", "reviews": "Reviews", "on_sale": "OFF", "sale": "Sale", "total": "Sub total", "product_variants": "Product variants", "stock_status": "Availability", "in_stock": "In stock", "sku": "SKU", "vendor": "<PERSON><PERSON><PERSON>", "type": "Product Type", "value_unavailable": "{{ option_value }} - Unavailable", "fbt": "Frequently bought together", "bought_together": "Add all to cart", "quick_buy": "Quick buy", "wishlist": "Wishlist", "compare": "Comapre", "more_payment_option": "More payment Option", "media": {"gallery_viewer": "Gallery Viewer", "load_image": "Load image {{ index }} in gallery view", "load_model": "Load 3D Model {{ index }} in gallery view", "load_video": "Play video {{ index }} in gallery view", "image_available": "Image {{ index }} is now available in gallery view", "open_media": "Open media {{ index }} in modal", "play_model": "Play 3D Viewer", "play_video": "Play video"}, "quantity": {"label": "Quantity", "input_label": "Quantity for {{ product }}", "increase": "Increase quantity for {{ product }}", "decrease": "Decrease quantity for {{ product }}"}, "pickup_availability": {"view_store_info": "View store information", "check_other_stores": "Check availability at other stores", "pick_up_available": "Pickup available", "pick_up_available_at_html": "Pickup available at <span class=\"color-foreground\">{{ location_name }}</span>", "pick_up_unavailable_at_html": "Pickup currently unavailable at <span class=\"color-foreground\">{{ location_name }}</span>", "unavailable": "Couldn't load pickup availability", "refresh": "Refresh"}, "price": {"from_price_html": "{{ price }}", "regular_price": "Regular price", "sale_price": "Sale price", "unit_price": "Unit price"}, "share": "Share this product", "sold_out": "Sold out", "unavailable": "Unavailable", "video_exit_message": "{{ title }} opens full screen video in same window.", "view_full_details": "View full details", "xr_button": "View in your space", "xr_button_label": "View in your space, loads item in augmented reality window", "include_taxes": "Tax included.", "shipping_policy_html": "<a href=\"{{ link }}\">Shipping</a> calculated at checkout."}, "wishlist": {"wishlist_name": "Wishlist", "addToWishList": "Add to wishlist", "viewMyWishList": "View wishlist", "addingWihlist": "Adding to wishlist", "product_name": "Product", "image": "Image", "price": "Price", "remove": "Remove", "continue": "Continue shopping", "no_records": "Nothing found in wishlist!", "purchase": "Purchase"}, "compare": {"no_records": "Nothing found to compare!", "compare": "Compare", "add_to_compare": "Add to compare", "view_compare": "View compare", "limit": "You will not be allowed to compare more than 4 products at a time"}, "modal": {"label": "Media gallery"}, "facets": {"apply": "Apply", "clear": "Clear", "clear_all": "Remove all", "from": "From", "filter_and_sort": "Filter and sort", "filter_by_label": "Filter:", "filter_button": "Filter", "filters_selected": {"one": "{{ count }} selected", "other": "{{ count }} selected"}, "filter_selected_accessibility": "{{ type }} ({{ count }} filters selected)", "show_more": "Show more", "show_less": "Show less", "max_price": "The highest price is {{ price }}", "product_count": {"one": "{{ product_count }} of {{ count }} product", "other": "{{ product_count }} of {{ count }} products"}, "product_count_simple": {"one": "{{ count }} product", "other": "{{ count }} products"}, "reset": "Reset", "sort_button": "Sort", "sort_by_label": "Sort by:", "to": "To", "clear_filter": "Remove filter", "count_text": "Showing 1-{{current_count}} of {{total_count}} Results"}}, "templates": {"404": {"title": "The Page you're looking for can't found", "subtext": "404", "button_text": "Back To Home", "description": "You didn't break the internet, but we can't find what you are looking for."}, "search": {"no_results": "No results found for “{{ terms }}”. Check the spelling or use a different word or phrase.", "page": "Page", "products": "Products", "results_pages_with_count": {"one": "{{ count }} page", "other": "{{ count }} pages"}, "results_suggestions_with_count": {"one": "{{ count }} suggestion", "other": "{{ count }} suggestions"}, "results_products_with_count": {"one": "{{ count }} product", "other": "{{ count }} products"}, "results_with_count": {"one": "{{ count }} result", "other": "{{ count }} results"}, "results_with_count_and_term": {"one": "{{ count }} result found for “{{ terms }}”", "other": "{{ count }} results found for “{{ terms }}”"}, "title": "Search results", "search_for": "Search for “{{ terms }}”", "suggestions": "Suggestions", "pages": "Pages"}, "cart": {"cart": "<PERSON><PERSON>", "free_shipping_spend": "Your cart is empty. Get free shipping when you spend", "free_shipping_success": "You are eligible for", "free_shipping_success_text": "free shipping!", "free_shipping_text_1": "You're only ", "free_shipping_text_2": "away from FREE shipping"}, "contact": {"form": {"title": "Contact form", "name": "Name", "email": "Email", "phone": "Phone number", "comment": "Comment", "contact_text": "Save my name, email, and website in this browser.", "send": "Send", "post_success": "Thanks for contacting us. We'll get back to you as soon as possible.", "error_heading": "Please adjust the following:"}}}, "sections": {"header": {"announcement": "Announcement", "menu": "<PERSON><PERSON>", "cart_count": {"one": "{{ count }} item", "other": "{{ count }} items"}}, "cart": {"title": "Shopping cart", "caption": "Cart items", "remove_title": "Remove {{ title }}", "subtotal": "Subtotal :", "new_subtotal": "New subtotal", "note": "Add note", "note_text": "Enter the text here...", "gift_wrap": "Gift wrap", "shipping": "Shipping", "discount_option": "Discount", "checkout": "Check out", "empty": "Your cart is empty", "suggested_products": "You may also like", "cart_error": "There was an error while updating your cart. Please try again.", "cart_quantity_error_html": "You can only add {{ quantity }} of this item to your cart.", "taxes_and_shipping_policy_at_checkout_html": "Taxes and <a href=\"{{ link }}\">shipping</a> calculated at checkout", "taxes_included_but_shipping_at_checkout": "Tax included and shipping calculated at checkout", "taxes_included_and_shipping_policy_html": "Tax included. <a href=\"{{ link }}\">Shipping</a> calculated at checkout.", "taxes_and_shipping_at_checkout": "Taxes and shipping calculated at checkout", "headings": {"product": "Product", "price": "Price", "total": "Total", "quantity": "Quantity"}, "view_cart": "View Cart", "update": "Update", "login": {"title": "Have an account?", "paragraph_html": "<a href=\"{{ link }}\" class=\"link underlined-link\">Log in</a> to check out faster."}}, "footer": {"payment": "Payment methods"}, "featured_blog": {"view_all": "View all", "onboarding_title": "Blog post", "onboarding_content": "Give your customers a summary of your blog post"}, "featured_collection": {"view_all": "View all", "view_all_label": "View all products in the {{ collection_name }} collection", "slider": "Slide<PERSON>", "all": "All"}, "collection_list": {"view_all": "View all", "shop_now": "Shop Now"}, "collection_template": {"empty": "No products found", "title": "Collection", "use_fewer_filters_html": "Use fewer filters or <a class=\"{{ class }}\" href=\"{{ link }}\">remove all</a>", "views_label": "View as", "load_more": "Load more", "no_more": "No more product"}, "video": {"load_video": "Load video: {{ description }}"}, "slideshow": {"load_slide": "Load slide", "previous_slideshow": "Previous slide", "next_slideshow": "Next slide", "pause_slideshow": "Pause slideshow", "play_slideshow": "Play slideshow", "carousel": "Carousel", "slide": "Slide"}, "page": {"title": "Page title"}}, "localization": {"country_label": "Country/region", "language_label": "Language", "update_language": "Update language", "update_country": "Update country/region"}, "customer": {"account": {"title": "Account", "details": "Account details", "view_addresses": "View addresses", "return": "Return to Account details"}, "account_fallback": "My Account", "activate_account": {"title": "Activate account", "subtext": "Create your password to activate your account.", "password": "Password", "password_confirm": "Confirm password", "submit": "Activate account", "cancel": "Decline invitation"}, "addresses": {"title": "Addresses", "default": "<PERSON><PERSON><PERSON>", "add_new": "Add a new address", "edit_address": "Edit address", "first_name": "First name", "last_name": "Last name", "company": "Company", "address1": "Address 1", "address2": "Address 2", "city": "City", "country": "Country/region", "province": "Province", "zip": "Postal/ZIP code", "phone": "Phone", "set_default": "Set as default address", "add": "Add address", "update": "Update address", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "delete_confirm": "Are you sure you wish to delete this address?"}, "log_in": "Sign in", "log_out": "Log out", "login_page": {"cancel": "Cancel", "create_account": "Sign Up", "login_account": "Sign In", "email": "Email", "forgot_password": "Forgot your password?", "return_to_store": "Return to store", "guest_continue": "Continue", "guest_title": "Continue as a guest", "password": "Password", "title": "Sign In", "sign_in": "Sign in", "submit": "Submit"}, "order": {"title": "Order {{ name }}", "date_html": "Placed on {{ date }}", "cancelled_html": "Order Cancelled on {{ date }}", "cancelled_reason": "Reason: {{ reason }}", "billing_address": "Billing Address", "payment_status": "Payment Status", "shipping_address": "Shipping Address", "fulfillment_status": "Fulfillment Status", "discount": "Discount", "shipping": "Shipping", "tax": "Tax", "product": "Product", "sku": "SKU", "price": "Price", "quantity": "Quantity", "total": "Total", "fulfilled_at_html": "Fulfilled {{ date }}", "track_shipment": "Track shipment", "tracking_url": "Tracking link", "tracking_company": "Carrier", "tracking_number": "Tracking number", "subtotal": "Subtotal", "total_duties": "Duties"}, "orders": {"title": "Order history", "order_number": "Order", "order_number_link": "Order number {{ number }}", "date": "Date", "payment_status": "Payment status", "fulfillment_status": "Fulfillment status", "total": "Total", "none": "You haven't placed any orders yet."}, "recover_password": {"title": "Reset your password", "subtext": "We will send you an email to reset your password", "success": "We've sent you an email with a link to update your password."}, "register": {"title": "Sign Up", "first_name": "First name", "last_name": "Last name", "email": "Email", "password": "Password", "submit": "Create"}, "reset_password": {"title": "Reset account password", "subtext": "Enter a new password for {{ email }}", "password": "Password", "password_confirm": "Confirm password", "submit": "Reset password"}}, "gift_cards": {"issued": {"title": "Here's your {{ value }} gift card for {{ shop }}!", "subtext": "Your gift card", "gift_card_code": "Gift card code", "shop_link": "Continue shopping", "remaining_html": "Remaining {{ balance }}", "add_to_apple_wallet": "Add to Apple Wallet", "qr_image_alt": "QR code — scan to redeem gift card", "copy_code": "Copy code", "expired": "Expired", "copy_code_success": "Code copied successfully", "print_gift_card": "Print"}}, "upsell": {"general": {"heading": "Please choose options for all selected products", "total": "Total", "add_all_to_cart": "Add all to cart", "get_discount_label1": "Get a", "get_discount_label2": "discount buying these products together", "select_option": "choose option"}}, "enquiry": {"form": {"heading": "Let us know about your query!", "sub_heading": "Contact Us", "name": "Name", "email": "Email", "phone": "Phone", "by_email": "Mail", "by_phone": "Phone", "by_both": "Both", "message_heading": "Communication mode that you prepare", "message": "Message"}}, "promo": {"label": {"new": "New", "sale": "Sale", "offer": "Offer"}}, "shopify": {"links": {"powered_by_shopify": " All Right Reserved"}, "checkout": {"thank_you": {"login": "Sign In"}}}, "recipient": {"form": {"checkbox": "I want to send this as a gift", "email_label": "Recipient email", "email_label_optional_for_no_js_behavior": "Recipient email (optional)", "email": "Email", "name_label": "Recipient name (optional)", "name": "Name", "message_label": "Message (optional)", "message": "Message", "max_characters": "{{ max_chars }} characters max", "send_on": "YYYY-MM-DD", "send_on_label": "Send on (optional)"}}, "pagefly": {"products": {"product": {"regular_price": "Regular price", "sold_out": "Sold out", "unavailable": "Unavailable", "on_sale": "Sale", "quantity": "Quantity", "add_to_cart": "Add to cart", "back_to_collection": "Back to {{ title }}", "view_details": "View details"}}, "article": {"tags": "Tags:", "all_topics": "All topics", "by_author": "by {{ author }}", "posted_in": "Posted in", "read_more": "Read more", "back_to_blog": "Back to {{ title }}"}, "comments": {"title": "Leave a comment", "name": "Name", "email": "Email", "message": "Message", "post": "Post comment", "moderated": "Please note, comments must be approved before they are published", "success_moderated": "Your comment was posted successfully. We will publish it in a little while, as our blog is moderated.", "success": "Your comment was posted successfully! Thank you!", "comments_with_count": {"one": "{{ count }} comment", "other": "{{ count }} comments"}}, "password_page": {"login_form_message": "Enter store using password:", "login_form_password_label": "Password", "login_form_password_placeholder": "Your password", "login_form_submit": "Enter", "signup_form_email_label": "Email", "signup_form_success": "We will send you an email right before we open!", "password_link": "Enter using password"}}}