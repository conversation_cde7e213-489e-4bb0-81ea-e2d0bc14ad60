<link rel="stylesheet" href="{{ 'magnific-popup.css' | asset_url }}" media="print" onload="this.media='all'">
<noscript>{{ 'magnific-popup.css' | asset_url | stylesheet_tag }}</noscript>
<script src="{{ 'magnific-popup.js' | asset_url }}" defer="defer"></script>
<script src="{{ 'isotope.pkgd.js' | asset_url }}" defer="defer"></script>
<script src="{{ 'home-isotope-gallery.js' | asset_url }}" defer="defer"></script>
{% style %}
    .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.50 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.50 | round: 0 }}px;
  }
   @media screen and (min-width: 576px) and (max-width: 749px){
   .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }
   }
  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }


{%- if section.settings.background_image != blank -%}
.section-{{ section.id }}-isotope-gallery {background-image: url('{{ section.settings.background_image |  image_url: width: 1920   }}');background-size: cover;background-position: center center;background-repeat: no-repeat;width: 100%;object-fit: cover;object-position: center;background-attachment: fixed;padding-top:50px;}
{%- endif -%}
  .isotope-gallery .isotope-grid {
    height: 100%;
}
.isotope-selector a {
    display: flex;
    height: 100%;
}
.isotope-selector a img {
    height: 100%;
}
  
{% endstyle %}


{% assign tags = section.settings.shop_by_tags_list | split: ',' %}
<div class="isotope-gallery dt-sc-section-wrapper color-{{ section.settings.color_scheme }} gradient section-{{ section.id }}-isotope-gallery" id="isotope-gallery-{{ section.id }}" data-spacing="{{ section.settings.gallery_spacing }}">
     <div class="{% if section.settings.page_full_width %} page-full-width {% else %} page-width {% endif %} {% if section.settings.page_full_width_spacing %} page-full-width_spacing {% endif %}section-{{ section.id }}-padding isolate">
    <div class="row {{ section.settings.custom_class_name }}">
    {%- unless section.settings.title == blank -%}
    <div class="title-wrapper-with-link title-wrapper--self-padded-mobile title-wrapper--no-top-margin content-align--{{ section.settings.column_alignment }}">
    {%- if section.settings.sub_heading != blank -%}  
    <h6 class="sub-heading">{{ section.settings.sub_heading | escape }}</h6>
    {%- endif -%} 
    {%- if section.settings.title != blank -%}  
    <h2 class="title {{ section.settings.heading_size }}">
    {{ section.settings.title | escape }}
    </h2>
    {%- endif -%} 
    {%- if section.settings.description != blank -%}  
    <p class="description">{{ section.settings.description }}</p>
    {%- endif -%}   
    {%- if section.settings.button_label != blank -%}
    <a {% if section.settings.button_link == blank %} role="link" aria-disabled="true"{% else %} href="{{ section.settings.button_link }}"{% endif %} class="button{% if section.settings.button_style_secondary %} button--secondary{% else %} button--primary{% endif %}">{{ section.settings.button_label | escape }}</a>
    {%- endif -%}
    </div>
    {%- endunless -%}
      <div  id="filters-tab-{{ section.id }}" class="filters {{ section.id }} wow {% if section.settings.animation_effect_section != 'none' %} {{ section.settings.animation_effect_section }} {% endif %}">
        <div class="ui-group gallery-filter">
          <div class="button-group js-radio-button-group" data-filter-group="color">
            <button class="button is-checked" data-filter="">{{ 'sections.featured_collection.all' | t }}</button>
            {% for result in tags %}
            <button class="button " data-filter=".{{ result | handle }}">{{ result }}</button>
            {% endfor %}
          </div>
        </div>
      </div>
      <div id="isotope-grid-{{ section.id }}" class="isotope-grid  {{ section.id }}  {% if section.settings.animation_effect_section != 'none' %} {{ section.settings.animation_effect_section }} {% endif %}">
        {% for block in section.blocks %}
        <div class="grid-sizer isotope-selector {{ block.settings.tag | replace: ',', ' ' }}">
          {% if block.settings.link != blank %}
          <a href="{{ block.settings.link }}">
            {% else %}
            <a href="#">
            {% endif %}
            {% if block.settings.image != blank %}
          {{ block.settings.image | image_url: width: 1920 | image_tag }} 
              {%- else -%}
           {{ 'image' | placeholder_svg_tag }}  
            {% endif %}
            {% if block.settings.link != blank %}
          </a>
          {% endif %}
          <div class="image-overlay ">
            <div class="isotope_links">
              <a href="{{ block.settings.link }}" class="select_options"><i class="fa fa-link"></i></a>
               {% if block.settings.image != blank %}
              <a href="{{ block.settings.image | image_url: width: 1024 }}" class="view-btn gallery"><i class="fa fa-search-plus"></i></a>
              {% endif %}
            </div>
            <div class="isotope_cntnt">
              {% if block.settings.title != blank %}
              <h4 class="isotope-title">{{ block.settings.title }}</h4>
              {% endif %}
              {% if block.settings.text != blank %}
              <p class="isotope-description">{{ block.settings.text }}</p>
              {% endif %}
            </div>
          </div>
        </div>
        {% endfor %}
      </div>
    </div>
  </div>
</div>

{%- liquid
  case section.settings.grid
  when '1'
  assign column = '50%'
  when '2'
  assign column = '50%'
  when '3'
  assign column = '33.33%'
  when '4'
  assign column = '24.75%'
  when '5'
  assign column = '19.6%'
  when '6'
  assign column = '16.2%' 
  endcase
%}

{%- style -%}
    #shopify-section-{{ section.id }}.isotope-gallery   .grid-sizer {width: {{column}};}
    #shopify-section-{{ section.id }}.isotope-gallery .isotope-selector {margin-bottom: {{ section.settings.gallery_spacing }}px; position: relative; overflow: hidden;  width: calc({{ column }} - calc({{ section.settings.gallery_spacing }}px)); }
    #shopify-section-{{ section.id }}.isotope-gallery .isotope_links { left: 0; right: 0; top:0; bottom: 0; margin: auto; width: 100px; position: absolute;
      height: 100%; transform: translateY(0); -webkit-transform: translateY(0); display: flex; justify-content: space-between; align-items: center; z-index: 1; }
    #shopify-section-{{ section.id }}.isotope-gallery .isotope_links .view-btn,
    #shopify-section-{{ section.id }}.isotope-gallery .isotope_links .select_options { width:45px;height:45px;line-height:45px;text-align:center;display:inline-block;
      position:relative; display: flex; align-items: center; justify-content: center;
      background: var(--gradient-base-accent-1);
      color: var(--gradient-base-background-1);
        background: {{ section.settings.isotope_icon_bg_color }}; color: {{ section.settings.isotope_icon_color }};
        }
    #shopify-section-{{ section.id }}.isotope-gallery .isotope_links .view-btn:hover,
    #shopify-section-{{ section.id }}.isotope-gallery .isotope_links .select_options:hover {
        background: {{ section.settings.isotope_icon_hover_bg_color }}; color: {{ section.settings.isotope_icon_hover_color }};  background: var(--gradient-base-background-1); color:  var(--gradient-base-accent-1);
        }
    #shopify-section-{{ section.id }}.isotope-gallery .isotope_links .select_options{right:100%;bottom:0;}
    #shopify-section-{{ section.id }}.isotope-gallery .isotope_links .view-btn{left:100%;bottom:0;}
    #shopify-section-{{ section.id }}.isotope-gallery .isotope-selector .image-overlay {
      background: {{ settings.overlay_color | color_modify: 'alpha', 0.5 }};
      background: {{ section.settings.isotope_overlay_color | color_modify: 'alpha', 0.5 }};
      position: absolute;
      opacity: 0; display: block; bottom: 0; left: 0; right: 0; top: 0; margin: auto; width: 100%; height: 100%; overflow: hidden;transition:all 0.3s linear;}
    #shopify-section-{{ section.id }}.isotope-gallery .isotope-selector .isotope_cntnt { position: absolute; left: 0; bottom: 0;right:0;margin:auto;text-align:center; width: 100%;height:100%;
      transition:all 0.3s linear;overflow:hidden; }
     #shopify-section-{{ section.id }}.isotope-gallery .isotope-selector .isotope_cntnt > * { position: absolute; margin:0; transition:all 0.3s linear; } 
    #shopify-section-{{ section.id }}.isotope-gallery .isotope-selector .isotope_cntnt .isotope-title { top: 0; left: 0; color: var(--DTColor_Heading); color: {{ section.settings.isotope_title_color }}; }
    #shopify-section-{{ section.id }}.isotope-gallery .isotope-selector .isotope_cntnt .isotope-description { bottom: 0; right: 0; color: var(--DTColor_Body); color: {{ section.settings.isotope_description_color }}; }
    #shopify-section-{{ section.id }}.isotope-gallery .isotope-selector:hover .isotope_links .select_options{right:0;bottom:0;}
    #shopify-section-{{ section.id }}.isotope-gallery .isotope-selector:hover .isotope_links .view-btn{left:0;bottom:0;}
    #shopify-section-{{ section.id }}.isotope-gallery .isotope-selector:hover .image-overlay{opacity:1;}
    #shopify-section-{{ section.id }}.isotope-gallery .isotope-selector:hover .isotope_cntnt .isotope-title { top: 10px; left: 15px; }
    #shopify-section-{{ section.id }}.isotope-gallery .isotope-selector:hover .isotope_cntnt .isotope-description { bottom: 10px; right: 15px; }
    .isotope-gallery .mfp-content figure .mfp-img { padding: 0px; }
    .isotope-gallery .mfp-content .mfp-figure { position: relative; }
    .isotope-gallery .mfp-content .mfp-figure:after { top: auto; height: auto; }
    .isotope-gallery .mfp-content .mfp-close { cursor: pointer !important; }
    .isotope-gallery .mfp-iframe-holder .mfp-close,
    .isotope-gallery .mfp-image-holder .mfp-close { position: absolute; right: 0; top:0; width: 30px; height: 30px; margin: 0!important; }
/*     .isotope-gallery  .mfp-close:before { margin:10px; } */
     .isotope-gallery .mfp-image-holder .mfp-close:before{ content: ''; display: block; height: 18px; width: 18px; margin:auto; -webkit-mask:url("data:image/svg+xml;utf8,<svg  xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' viewBox='0 0 100 100'  xml:space='preserve'> <path d='M57,50l35.2-35.2c1.9-1.9,1.9-5.1,0-7c-1.9-1.9-5.1-1.9-7,0L50,43L14.8,7.7c-1.9-1.9-5.1-1.9-7,0c-1.9,1.9-1.9,5.1,0,7 L43,50L7.7,85.2c-1.9,1.9-1.9,5.1,0,7c1,1,2.2,1.5,3.5,1.5s2.5-0.5,3.5-1.5L50,57l35.2,35.2c1,1,2.2,1.5,3.5,1.5s2.5-0.5,3.5-1.5 c1.9-1.9,1.9-5.1,0-7L57,50z' /></svg>"); 
                           background: currentColor; -webkit-mask-repeat: no-repeat; -webkit-mask-position: center; -webkit-mask-size: 15px; }
   .isotope-gallery .mfp-image-holder button.mfp-close{background:rgba(var(--color-button));color:rgba(var(--color-button-text));border:none;}
  .isotope-gallery .mfp-image-holder button.mfp-close:hover{color:rgba(var(--color-button));background:rgba(var(--color-button-text));}
    .isotope-gallery .mfp-content { height: auto; overflow: visible; }

  
    /* ---- isotope ---- */
    /* clear fix */
    #shopify-section-{{ section.id }}.isotope-gallery .isotope-grid {
      /* overflow: hidden; */
      transition: all 0.3s linear;
        -webkit-transition: all 0.3s linear;
          }
    #shopify-section-{{ section.id }}.isotope-gallery .isotope-grid:after {
      content: '';
      display: block;
      clear: both;
    }
    #shopify-section-{{ section.id }}.isotope-gallery .filters { text-align: center; }
    /* ui group */
    #shopify-section-{{ section.id }}.isotope-gallery .ui-group {
      display: inline-block; width: 100%; margin: 0 auto calc(5rem - 15px);
    }
    #shopify-section-{{ section.id }}.isotope-gallery .ui-group .button-group {
      display: flex; width: 100%; justify-content: center;
    }
    #shopify-section-{{ section.id }}.isotope-gallery .button:hover { background: var(--gradient-base-background-3);color:rgb(var(--color-base-outline-button-labels)); }
    #shopify-section-{{ section.id }}.isotope-gallery .button.active {
      background: var(--gradient-base-background-3);
      color: var(--gradient-base-accent-1);
        }
    @media only screen and (max-width: 1380px) {
      #shopify-section-{{ section.id }}.isotope-gallery .isotope-selector { width: calc(24.7% - calc({{ section.settings.gallery_spacing }}px)); }
    }
    @media only screen and (max-width: 1199px) {
      #shopify-section-{{ section.id }}.isotope-gallery .isotope-selector { width: calc(33.33% - calc({{ section.settings.gallery_spacing }}px)); }
    }
    @media only screen and (max-width:991px) {
      #shopify-section-{{ section.id }}.isotope-gallery .isotope-selector {width: calc(50% - calc({{ section.settings.gallery_spacing }}px));}
    }
    @media only screen and (min-width:768px) {
      #shopify-section-{{ section.id }}.isotope-gallery .button.is-checked,
      #shopify-section-{{ section.id }}.isotope-gallery .button.is-checked.active { background: var(--gradient-base-background-3);color: var(--gradient-base-accent-1);    color: var(--gradient-background); }
    }
    @media only screen and (max-width:767px) {
      #shopify-section-{{ section.id }}.isotope-gallery .filters {
        position: relative;
        z-index: 3;
      }
      #shopify-section-{{ section.id }}.isotope-gallery .filters .ui-group {
        position: relative;
        width: 50%;
        height: 100%;
        display: grid;
        margin-bottom: 3rem;
      }
      #shopify-section-{{ section.id }}.isotope-gallery .filters .ui-group .button-group {
        display: grid;
        width: 100%;
        grid-template-columns: 1fr;
        line-height: normal;
      }
      #shopify-section-{{ section.id }}.isotope-gallery .filters .ui-group .button-group button {
        order: 1;
        width: 100%;
        margin: 0;
        transition: var(--DTBaseTransition);
          }
      #shopify-section-{{ section.id }}.isotope-gallery .filters .ui-group .button-group button:not(:last-child),
      #shopify-section-{{ section.id }}.isotope-gallery .filters .ui-group .button-group button.is-checked.active {
        border-bottom: 1px solid {{ settings.button_text_hover_color | color_modify: 'alpha', 0.35 }};
      }
      #shopify-section-{{ section.id }}.isotope-gallery .filters .ui-group .button-group button.active,
      #shopify-section-{{ section.id }}.isotope-gallery .filters .ui-group .button-group button.is-checked {
        order: 0;
        z-index: 1;
      }
      #shopify-section-{{ section.id }}.isotope-gallery .filters .ui-group .button-group:after {
        width: 15px;
        height: 15px;
        border: 2px solid var(--DT_Button_Text_Hover_Color);
          border-right: 0;
          border-bottom: 0;
          content: "";
          position: absolute;
          right: 25px;
          transform: rotate(-135deg) translateY(-50%);
          -webkit-transform: rotate(-135deg) translateY(-50%);
          top: 50%;
          z-index: 1;
          pointer-events: none;
          transform-origin: top;
          -webkit-transform-origin: top;
          }
      
      #shopify-section-{{ section.id }}.isotope-gallery .filters .ui-group .button-group.expanded:after {
        transform: rotate(45deg) translateY(-50%);
        -webkit-transform: rotate(45deg) translateY(-50%);
        transform-origin: left;
        -webkit-transform-origin: left;
      }
    
      #shopify-section-{{ section.id }}.isotope-gallery .filters .ui-group .button-group.expanded button:not(.active) {
        position: relative;
        top: auto;
      }
      .isotope-gallery .mfp-content {
        max-height: 50vh;
      }
    }
    @media only screen and (max-width:576px) {
      #shopify-section-{{ section.id }}.isotope-gallery .isotope-selector {width: 100%; }
      #shopify-section-{{ section.id }}.isotope-gallery .filters .ui-group { width: 75%; }
    }
  {%- endstyle -%}


{% schema %}
{
"name": "t:sections.isotope-gallery.name",
"class": "section index-section isotope-gallery",
"settings": [
{
"type":"checkbox",
"id":"page_full_width",
"default": false,
"label": "t:sections.all.page_full_width.label"
},
{
"type":"checkbox",
"id":"page_full_width_spacing",
"default": false,
"label": "t:sections.all.page_full_width_spacing.label"
},
{
"type": "range",
"id": "padding_top",
"min": 0,
"max": 120,
"step": 4,
"unit": "px",
"label": "t:sections.all.padding.padding_top",
"default": 36
},
{
"type": "range",
"id": "padding_bottom",
"min": 0,
"max": 120,
"step": 4,
"unit": "px",
"label": "t:sections.all.padding.padding_bottom",
"default": 36
},
{
"type": "image_picker",
"id": "background_image",
"label": "Background image",
"info": "Size: 1920x1280"
},
{
"type": "text",
"id": "title",
"default": "Isotope gallery",
"label": "t:sections.all.title.label"
},
{
"type": "select",
"id": "heading_size",
"options": [
  {
    "value": "h2",
    "label": "t:sections.all.heading_size.options__1.label"
  },
  {
    "value": "h1",
    "label": "t:sections.all.heading_size.options__2.label"
  },
  {
    "value": "h0",
    "label": "t:sections.all.heading_size.options__3.label"
  }
],
"default": "h1",
"label": "t:sections.all.heading_size.label"
},
{
"type": "text",
"id": "sub_heading",
"default": "Sub Heading",   
"label": "t:sections.all.sub_heading.label"
},
{
"type": "text",
"id": "description",
"default": "Use This Text To Share The Information Which You Like!.",  
"label": "t:sections.all.description.label"
},
{
"type": "text",
"id": "button_label",
"default": "Button label",
"label": "t:sections.all.button_label.label"
},
{
"type": "url",
"id": "button_link",
"label": "t:sections.all.button_link.label"
},
{
"type": "checkbox",
"id": "button_style_secondary",
"default": false,
"label": "t:sections.all.button_style_secondary.label"
},
{
"type": "select",
"id": "column_alignment",
"options": [
  {
    "value": "left",
    "label": "t:sections.isotope-gallery.settings.column_alignment.options__1.label"
  },
  {
    "value": "center",
    "label": "t:sections.isotope-gallery.settings.column_alignment.options__2.label"
  }
],
"default": "center",
"label": "t:sections.isotope-gallery.settings.column_alignment.label"
},
{
"type": "select",
"id": "color_scheme",
"options": [
  {
    "value": "accent-1",
    "label": "t:sections.all.colors.accent_1.label"
  },
  {
    "value": "accent-2",
    "label": "t:sections.all.colors.accent_2.label"
  },
  {
    "value": "background-1",
    "label": "t:sections.all.colors.background_1.label"
  },
  {
    "value": "background-2",
    "label": "t:sections.all.colors.background_2.label"
  },
  {
    "value": "inverse",
    "label": "t:sections.all.colors.inverse.label"
  }
],
"default": "background-1",
"label": "t:sections.all.colors.label"
},  
{
"type": "textarea",
"id": "shop_by_tags_list",
"label": "t:sections.isotope-gallery.settings.shop_by_tags_list.label",
"default": "Tab1, Tab2",  
"info": "Separate by a comma, i.e \"Sale Item, Featured, Offers, Trending\"."
},
{
"type": "select",
"id": "grid",
"label": "t:sections.isotope-gallery.settings.grid.label",
"default": "4",
"options": [
{
"value": "1",
"label": "t:sections.isotope-gallery.settings.grid.options__1.label"
},
{
"value": "2",
"label": "t:sections.isotope-gallery.settings.grid.options__2.label"
},
{
"value": "3",
"label": "t:sections.isotope-gallery.settings.grid.options__3.label"
},
{
"value": "4",
"label": "t:sections.isotope-gallery.settings.grid.options__4.label"
},
{
"value": "5",
"label": "t:sections.isotope-gallery.settings.grid.options__5.label"
},
{
"value": "6",
"label": "t:sections.isotope-gallery.settings.grid.options__6.label"
}
]
},
{
"type": "range",
"id": "gallery_spacing",
"label": "t:sections.isotope-gallery.settings.gallery_spacing.label",
"min": 0,
"max": 100,
"step": 1,
"unit": "px",
"default": 30
},
{
"type": "header",
"content": "t:sections.isotope-gallery.settings.gallery_color_settings.content"
},
{
"type": "color",
"id": "isotope_overlay_color",
"label": "t:sections.isotope-gallery.settings.isotope_overlay_color.label"
},
{
"type": "color",
"id": "isotope_title_color",
"label": "t:sections.isotope-gallery.settings.isotope_title_color.label"
},
{
"type": "color",
"id": "isotope_description_color",
"label": "t:sections.isotope-gallery.settings.isotope_description_color.label"
},
{
"type": "color",
"id": "isotope_icon_bg_color",
"label": "t:sections.isotope-gallery.settings.isotope_icon_bg_color.label"
},
{
"type": "color",
"id": "isotope_icon_color",
"label": "t:sections.isotope-gallery.settings.isotope_icon_color.label"
},
{
"type": "color",
"id": "isotope_icon_hover_bg_color",
"label": "t:sections.isotope-gallery.settings.isotope_icon_hover_bg_color.label"
},
{
"type": "color",
"id": "isotope_icon_hover_color",
"label": "t:sections.isotope-gallery.settings.isotope_icon_hover_color.label"
},
{
"type": "header",
"content": "t:sections.all.custom_class_heading.content"
},
{
"type": "text",
"id": "custom_class_name",
"label": "t:sections.all.custom_class_name.label"
}
],
"blocks": [
{
"type": "tab",
"name": "t:sections.isotope-gallery.blocks.tab.name",
"settings": [
{
"type": "image_picker",
"id": "image",
"label": "t:sections.isotope-gallery.blocks.tab.settings.image.label"
},
{
"type": "text",
"id": "tag",
"label": "t:sections.isotope-gallery.blocks.tab.settings.tag.label",
"info": "Separate by a comma with downcase & replace space with hyphen symbol, i.e \"sale-item, sale-off\"."
},
{
"type": "text",
"id": "title",
"label": "t:sections.isotope-gallery.blocks.tab.settings.title.label"
},
{
"type": "textarea",
"id": "text",
"label": "t:sections.isotope-gallery.blocks.tab.settings.text.label"
},
{
"type": "url",
"id": "link",
"label": "t:sections.isotope-gallery.blocks.tab.settings.link.label"
}
]
}
],
"presets": [
{
"name": "t:sections.isotope-gallery.presets.name",
 "blocks": [
        {
          "type": "tab"
        },
        {
          "type": "tab"
        },
        {
          "type": "tab"
        }
 ] 
}
]
}
{% endschema %}