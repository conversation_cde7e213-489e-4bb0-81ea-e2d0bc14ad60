/* Custom Image Gallery Mega Menu */
.image-gallery-menu .sub-menu-block {
  position: fixed !important;
  top: var(--header-height, 80px) !important;
  left: 0 !important;
  width: 100vw !important;
  z-index: 999 !important;
  background: transparent !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  padding: 20px !important;
  border: none !important;
  transform: translateY(0) !important;
}

.image-gallery-container {
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: wrap !important;
  gap: 20px !important;
  justify-content: flex-start !important;
  align-items: flex-start !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
}

.image-gallery-item {
  flex: 0 0 calc(20% - 16px) !important;
  min-width: 200px !important;
  background: white !important;
  border-radius: 8px !important;
  overflow: hidden !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
  transition: transform 0.3s ease !important;
}

.image-gallery-item:hover {
  transform: translateY(-5px) !important;
}

.image-gallery-item img {
  width: 100% !important;
  height: 150px !important;
  object-fit: cover !important;
}

.image-gallery-item h3 {
  padding: 15px !important;
  margin: 0 !important;
  font-size: 14px !important;
  font-weight: 600 !important;
  color: #333 !important;
  text-align: center !important;
}

/* High Priority Flex Layout for Category Grid - Desktop Only - Cannot be overwritten */
@media screen and (min-width: 750px) {
  .category-showcase .category-grid,
  .category-showcase .category-grid.category-grid--grid,
  .category-showcase .category-grid.category-grid--hexagonal,
  .category-showcase .category-grid.category-grid--masonry,
  .category-showcase .category-grid.category-grid--floating,
  .category-showcase .category-grid.category-grid--custom-rows,
  .category-showcase .category-grid[data-custom-pattern] {
    display: flex !important;
    flex-wrap: wrap !important;
    align-items: flex-start !important;
    justify-content: flex-start !important;
    max-width: 1099px !important;
    margin: 0 auto !important;
  }
}