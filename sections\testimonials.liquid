{{ 'section-testimonials.css' | asset_url | stylesheet_tag }}
<link rel="stylesheet" href="{{ 'component-swiper.css' | asset_url }}" media="print" onload="this.media='all'">
<noscript>{{ 'component-swiper.css' | asset_url | stylesheet_tag }}</noscript>

{%- style -%}
    .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.50 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.50 | round: 0 }}px;
  }
   @media screen and (min-width: 576px) and (max-width: 749px){
   .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }
   }
  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
/*  {%- for block in section.blocks -%}  
 .testimonial-image.mask-{{ forloop.index }}-image .img { 
  mask-image: url({{ block.settings.mask_image | image_url: width: 275  }});
  mask-size: contain; 
  mask-repeat: no-repeat;
  -webkit-mask-image: url({{ block.settings.mask_image | image_url: width: 275  }});
  -webkit-mask-size: contain; 
  -webkit-mask-repeat: no-repeat;
	 transition: all 0.3s linear ;
}
    {%- endfor -%} */
 {% if section.settings.quote_icon %}
.testimonials .testimonial-container .testimonial-content blockquote:before {
   content:'';
      position:relative;
      width:60px;
      height:100px;
      background-color:currentcolor;
       mask-image: url({{ section.settings.quote_icon | img_url: width: 260 }});
      -webkit-mask-image: url({{ section.settings.quote_icon | img_url: width: 260 }});
   mask-size: auto; mask-repeat: no-repeat; mask-position: center;
     -webkit-mask-size: auto; -webkit-mask-repeat: no-repeat; -webkit-mask-position: center;
  }
  
  
     @media screen and (min-width: 768px) {
.testimonials .testimonial-container .testimonial-content blockquote:before {
   
      content:'';
      position:relative;
      width:100px;
      height:100px;
      background-color:currentcolor;
     
    }
     }
   {% endif %}
{%- endstyle -%}

{%- liquid
if section.settings.enable_overlay
assign section_overlay = 'dt-sc-overlay'
endif
assign margin = section.settings.margin | split: ','
assign margin_top = margin[0]
assign margin_bottom = margin[1]
assign padding = section.settings.padding | split: ','
assign padding_top = padding[0]
assign padding_bottom = padding[1]
 %}


<div class="testimonials {{ section.settings.custom_class_name }} color-{{ section.settings.color_scheme }} gradient{% if section.settings.title == blank %} no-heading{% endif %}">
<div class="{% if section.settings.page_full_width %} page-full-width {% else %} page-width {% endif %} {% if section.settings.page_full_width_spacing %} page-full-width_spacing {% endif %}section-{{ section.id }}-padding isolate">
<div class="row">    
  {%- unless section.settings.title == blank -%}
      <div class="title-wrapper-with-link title-wrapper--self-padded-mobile title-wrapper--no-top-margin content-align--{{ section.settings.column_alignment }}">
         {%- if section.settings.sub_heading != blank -%}  
         <h6 class="sub-heading">{{ section.settings.sub_heading | escape }}</h6>
         {%- endif -%} 
         {%- if section.settings.title != blank -%}  
          <h2 class="title {{ section.settings.heading_size }}">
            {{ section.settings.title | escape }}
          </h2>
          {%- endif -%} 
          {%- if section.settings.description != blank -%}  
          <p class="description">{{ section.settings.description }}</p>
          {%- endif -%}   
          {%- if section.settings.button_label != blank -%}
            <a {% if section.settings.button_link == blank %} role="link" aria-disabled="true"{% else %} href="{{ section.settings.button_link }}"{% endif %} class="button{% if section.settings.button_style_secondary %} button--secondary{% else %} button--primary{% endif %}">{{ section.settings.button_label | escape }}</a>
          {%- endif -%}
          </div>
    {%- endunless -%}
     {%- if section.blocks.size > 0 -%}
       <swiper-slider>
      <div data-slider-options='{"loop": "{%- if section.settings.centered_slide -%}1{%- else -%}2{%- endif -%}","desktop": "{{ section.settings.desktop_column }}", "tablet": "{{ section.settings.tablet_column }}","mobile": "{{ section.settings.mobile_column }}","auto_play": "{{ section.settings.auto_play }}"}'>
     <div class="swiper testimonialsSwiper" id="quote-{{section.id}}-slider" data-swiper-slider>
      <div class="swiper-wrapper">
        {%- for block in section.blocks -%}
        <div class="testimonial-container swiper-slide color-{{ block.settings.block_color_scheme }} gradient ">
          {% if block.settings.image != blank %}    
          <div class="testimonial-image  content-{{ section.settings.testimonial_text_alignment }}"> 
            <img loading="lazy" width="" height=""
                 src="{{ block.settings.image | image_url: width: 550 }}"
                 alt="{{ block.settings.title }}"
                 class="img {%- if section.settings.rounded_image -%}rounded_image{%- endif -%}">   
          </div>
          {% else %}
          <div class="testimonial-image">
          <!--  {{ 'image' | placeholder_svg_tag: 'placeholder_svg img' }} -->
          </div>  
          {% endif %}
          <div class="testimonial-content">
            <blockquote class="content-{{ section.settings.testimonial_text_alignment }}">
                {%  render 'dt-sc-rating-widget', block: block, alignment: section.settings.content_alignment %} 
              {%- if block.settings.quote != blank -%}{{ block.settings.quote }}{%- endif -%}
              {%- if block.settings.author != blank -%}
              <cite>
                <p class="testimonial-author">{{ block.settings.author }}</p>
                <span>{{ block.settings.job_title }}</span>
              </cite>
              {%- endif -%}
            </blockquote>
          </div>
        </div>
        {%- endfor -%}
      </div>
    {% if section.settings.swiper_navigation != blank %}
        <div class="swiper-button-next"><span></span></div>
        <div class="swiper-button-prev"><span></span></div>
        {% endif %}
     {% if section.settings.swiper_pagination != blank %}
    <div class="swiper-pagination"></div>
    {% endif %}   
    </div>
    </div>
    </swiper-slider>
    {%- endif -%}
  </div>
 </div>
</div>


{% schema %}
{
  "name": "t:sections.testimonials.name",
  "class": "section section-testimonials",
  "tag": "section",
  "settings": [
    {
      "type":"checkbox",
      "id":"page_full_width",
       "default": false,
      "label": "t:sections.all.page_full_width.label"
    },
    {
      "type":"checkbox",
      "id":"page_full_width_spacing",
       "default": false,
      "label": "t:sections.all.page_full_width_spacing.label"
    },
     {
      "type": "text",
      "id": "title",
      "default": "Testimonial",
      "label": "t:sections.all.title.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__3.label"
        }
      ],
      "default": "h1",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "text",
      "id": "sub_heading",
      "default":"Sub Heading", 
      "label": "t:sections.all.sub_heading.label"
    },
     {
      "type": "text",
      "id": "description",
      "default":"Use this text to share the information which you like!.",     
      "label": "t:sections.all.description.label"
    },
    {
      "type": "text",
      "id": "button_label",
      "default": "Button label",
      "label": "t:sections.all.button_label.label"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "t:sections.all.button_link.label"
    },
    {
      "type": "checkbox",
      "id": "button_style_secondary",
      "default": false,
      "label": "t:sections.all.button_style_secondary.label"
      },
    {
      "type": "select",
      "id": "column_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.testimonials.settings.column_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.testimonials.settings.column_alignment.options__2.label"
        }
      ],
      "default": "center",
      "label": "t:sections.testimonials.settings.column_alignment.label"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "t:sections.all.colors.accent_1.label"
        },
        {
          "value": "accent-2",
          "label": "t:sections.all.colors.accent_2.label"
        },
        {
          "value": "background-1",
          "label": "t:sections.all.colors.background_1.label"
        },
        {
          "value": "background-2",
          "label": "t:sections.all.colors.background_2.label"
        },
        {
          "value": "inverse",
          "label": "t:sections.all.colors.inverse.label"
        }
      ],
      "default": "background-1",
      "label": "t:sections.all.colors.label"
    },
    {
      "type": "header",
      "content": "t:sections.testimonials.settings.testimonial_settings.content"
    },
    {
      "type": "select",
      "id": "testimonial_text_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.testimonials.settings.testimonial_text_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.testimonials.settings.testimonial_text_alignment.options__2.label"
        }
      ],
      "default": "left",
      "label": "t:sections.testimonials.settings.testimonial_text_alignment.label"
    },
    {
          "type": "image_picker",
          "id": "quote_icon",
          "label": "t:sections.testimonials.settings.quote_icon.label",
          "info":"t:sections.testimonials.settings.quote_icon.info"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
    {
    "type": "header",
    "content": "t:sections.all.custom_class_heading.content"
    },
    {
    "type": "text",
    "id": "custom_class_name",
    "label": "t:sections.all.custom_class_name.label"
    },
    {
      "type": "header",
      "content": "t:sections.all.swiper.swiper_slider_title"
    },
    {
      "type": "range",
      "id": "desktop_column",
      "min": 1,
      "max": 10,
      "step": 1,
      "label": "t:sections.all.swiper.desktop_column",
      "default": 3
    },
    {
      "type": "range",
      "id": "tablet_column",
      "min": 1,
      "max": 5,
      "step": 1,
      "label": "t:sections.all.swiper.tablet_column",
      "default": 2
    },
    {
      "type": "range",
      "id": "mobile_column",
      "min": 1,
      "max": 3,
      "step": 1,
      "label": "t:sections.all.swiper.mobile_column",
      "default": 1
    },
    {
      "type": "checkbox",
      "id": "centered_slide",
      "default": false,
      "label": "t:sections.all.swiper.centered_slide"
    },
    {
      "type": "checkbox",
      "id": "swiper_pagination",
      "default": false,
      "label": "t:sections.all.swiper.swiper_pagination"
    },
    {
      "type": "checkbox",
      "id": "swiper_navigation",
      "default": false,
      "label": "t:sections.all.swiper.swiper_navigation"
    },
    {
      "type": "range",
      "id": "auto_play",
      "min": 0,
      "max": 5,
      "step": 1,
      "label": "t:sections.all.swiper.auto_play",
      "default": 0
    }
  ],
  "blocks": [
    {
      "type": "text",
      "name": "t:sections.testimonials.blocks.text.name",
      "settings": [
           {
      "type": "select",
      "id": "block_color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "t:sections.all.colors.accent_1.label"
        },
        {
          "value": "accent-2",
          "label": "t:sections.all.colors.accent_2.label"
        },
        {
          "value": "background-1",
          "label": "t:sections.all.colors.background_1.label"
        },
        {
          "value": "background-2",
          "label": "t:sections.all.colors.background_2.label"
        },
        {
          "value": "inverse",
          "label": "t:sections.all.colors.inverse.label"
        }
      ],
      "default": "background-2",
      "label": "t:sections.testimonials.blocks.text.settings.block_color_scheme.label"
    },
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.testimonials.blocks.text.settings.image.label"
        },
        {
          "type": "text",
          "id": "author",
          "default": "Column",
          "label": "t:sections.testimonials.blocks.text.settings.author.label"
        },
        {
          "type": "text",
          "id": "job_title",
          "default": "john",
          "label": "t:sections.testimonials.blocks.text.settings.job_title.label"
        },
        {
          "type": "richtext",
          "id": "quote",
          "default": "<p>Pair text with an image to focus on your chosen product, collection, or blog post. Add details on availability, style, or even provide a review.</p>",
          "label": "t:sections.testimonials.blocks.text.settings.quote.label"
        },
        {
        "type": "select",
        "id": "star",
        "label": "rating",
        "default": "3",
        "options": [
        {
        "value": "0",
        "label": "0"
        },
        {
        "value": "0.5",
        "label": "0.5"
        },
        {
        "value": "1",
        "label": "1"
        },
        {
        "value": "1.5",
        "label": "1.5"
        },
        {
        "value": "2",
        "label": "2"
        },
        {
        "value": "2.5",
        "label": "2.5"
        },
        {
        "value": "3",
        "label": "3"
        },
        {
        "value": "3.5",
        "label": "3.5"
        },
        {
        "value": "4",
        "label": "4"
        },
        {
        "value": "4.5",
        "label": "4.5"
        },
        {
        "value": "5",
        "label": "5"
        }
        ]
        },
        {
          "type": "text",
          "id": "link_label",
          "label": "t:sections.testimonials.blocks.text.settings.link_label.label"
        },
        {
          "type": "url",
          "id": "link",
          "label": "t:sections.testimonials.blocks.text.settings.link.label"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.testimonials.presets.name",
      "blocks": [
        {
          "type": "text"
        },
         {
          "type": "text"
        },
         {
          "type": "text"
        }
      ]
    }
  ]
}
{% endschema %}
