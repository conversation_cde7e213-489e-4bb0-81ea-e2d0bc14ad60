{% comment %}theme-check-disable UndefinedObject{% endcomment %}
{%- assign pick_up_availabilities = product_variant.store_availabilities | where: 'pick_up_enabled', true -%}

{%- if pick_up_availabilities.size > 0 -%}
  <pickup-availability-preview class="pickup-availability-preview">
    {%- liquid
      assign closest_location = pick_up_availabilities.first

      if closest_location.available
        render 'icon-tick'
      endif
    -%}

    <div class="pickup-availability-info">
      {%- if closest_location.available -%}
        <p class="caption-large">{{ 'products.product.pickup_availability.pick_up_available_at_html' | t: location_name: closest_location.location.name }}</p>
        <p class="caption">{{ closest_location.pick_up_time }}</p>
        <button id="ShowPickupAvailabilityDrawer" class="pickup-availability-button link link--text underlined-link" aria-haspopup="dialog">
          {%- if pick_up_availabilities.size == 1 -%}
            {{ 'products.product.pickup_availability.view_store_info' | t }}
          {%- else -%}
            {{ 'products.product.pickup_availability.check_other_stores' | t }}
          {%- endif -%}
        </button>
      {%- else -%}
        <p class="caption-large">{{ 'products.product.pickup_availability.pick_up_unavailable_at_html' | t: location_name: closest_location.location.name }}</p>
        {%- if pick_up_availabilities.size > 1 -%}
          <button id="ShowPickupAvailabilityDrawer" class="pickup-availability-button link link--text underlined-link" aria-haspopup="dialog">{{ 'products.product.pickup_availability.check_other_stores' | t }}</button>
        {%- endif -%}
      {%- endif -%}
    </div>
  </pickup-availability-preview>
<div class="pickup-drawer">
  <pickup-availability-drawer class="gradient" tabindex="-1" role="dialog" aria-modal="true" aria-labelledby="PickupAvailabilityHeading">
   <div class="pickup-drawer">
    <div class="pickup-availability-header">
      <h2 class="h3 pickup-availability-drawer-title" id="PickupAvailabilityHeading">{{ product_variant.product.title | escape }}</h2>
      <button class="pickup-availability-drawer-button" type="button" aria-label="{{ 'accessibility.close' | t }}">{%- render 'icon-close' -%}</button>
    </div>

    {%- unless product_variant.product.has_only_default_variant -%}
      <p class="pickup-availability-variant">
        {%- for product_option in product_variant.product.options_with_values -%}
          {{ product_option.name | escape }}:&nbsp;
          {%- for value in product_option.values -%}
            {%- if product_option.selected_value == value -%}
              <span>{{ value | escape }}</span>
            {%- endif -%}
          {%- endfor -%}
          {%- unless forloop.last -%},&nbsp;{%- endunless forloop.last -%}
        {%- endfor -%}
      </p>
    {%- endunless -%}

    <ul class="pickup-availability-list list-unstyled" role="list" data-store-availability-drawer-content>
      {%- for availability in pick_up_availabilities -%}
        <li class="pickup-availability-list__item">
          <h3 class="h5">{{ availability.location.name | escape }}</h3>
          <p class="pickup-availability-preview caption-large">
            {%- if availability.available -%}
              {% render 'icon-tick' %} {{ 'products.product.pickup_availability.pick_up_available' | t }}, {{ availability.pick_up_time | downcase }}
            {%- endif -%}
          </p>

          {%- assign address = availability.location.address -%}
          <address class="pickup-availability-address">
            {{ address | format_address }}

            {%- if address.phone.size > 0 -%}
              <p>{{ address.phone }}</p>
            {%- endif -%}
          </address>
        </li>
      {%- endfor -%}
    </ul>
   </div>
  </pickup-availability-drawer>
</div>
{%- endif -%}
