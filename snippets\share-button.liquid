<script src="{{ 'share.js' | asset_url }}" defer="defer"></script>
<share-button id="Share-{{ section.id }}" class="share-button quick-add-hidden" {{ block.shopify_attributes }}>
  <button class="share-button__button hidden">
    {% render 'icon-share' %}
    {{ block.settings.share_label | escape }}
  </button>
  <details id="Details-{{ section.id }}">
    <summary class="share-button__button">
      {% render 'icon-share' %}
      {{ block.settings.share_label | escape }}
    </summary>
    <div class="share-button__fallback motion-reduce">
      <div class="field">
        <span id="ShareMessage-{{ section.id }}" class="share-button__message hidden" role="status"> </span>
        <input
          type="text"
          class="field__input"
          id="ShareUrl-{{ section.id }}"
          value="{{ share_url }}"
          placeholder="{{ 'general.share.share_url' | t }}"
          onclick="this.select();"
          readonly
        >
        <label class="field__label" for="ShareUrl-{{ section.id }}">{{ 'general.share.share_url' | t }}</label>
      </div>
      <button class="share-button__close hidden no-js-hidden">
        {% render 'icon-close' %}
        <span class="visually-hidden">{{ 'general.share.close' | t }}</span>
      </button>
      <button class="share-button__copy no-js-hidden">
        {% render 'icon-clipboard' %}
        <span class="visually-hidden">{{ 'general.share.copy_to_clipboard' | t }}</span>
      </button>
    </div>   
  </details>
</share-button>