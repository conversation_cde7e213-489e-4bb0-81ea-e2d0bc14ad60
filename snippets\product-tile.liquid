<div class="product-tile-container wishlist-tile-container" data-product-id="{{ product.id }}" data-product-handle="{{ product.handle }}">
  <div class="product-tile wishlist-tile">
    <div class="product-tile--tile-media">
      <a class="tile-media--media-link" href="{{ product.url }}">
        <div class="media-link--image">
          <img class="image--main" src="{{ product.featured_image | product_img_url: 'grande' }}" alt="{{ product.title }}">
        </div>
      </a>
    </div>
    <div class="product-tile--tile-content flex">
      <div class="tile-content--text">
        <p class="text--title black">{{ product.title }}</p>
        {% if product.compare_at_price > product.price %}
          <p class="text--price">
            <span class="price--compare strike-through">{{ product.compare_at_price | money }}</span> <span class="price--sale">{{ product.price | money }}</span>
          </p>
        {% else %}  
          <p class="text--price">{{ product.price | money }}</p>
        {% endif %}
      </div>
      <div class="product-tile--tile-actions flex">
        {% include 'wishlist-button'%}
        <a class="action--quick-cart tile-actions--btn flex cart-btn" href="/cart/add?id={{ product.selected_or_first_available_variant.id }}&quantity=1">Add To Cart</a>
      </div>
    </div>
  </div>  
</div>
