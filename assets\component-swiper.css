swiper-component {
  --desktop-margin-left-first-item: max(5rem, calc((100vw - var(--page-width) + 10rem - var(--grid-desktop-horizontal-spacing)) / 2));
  position: relative;
  display: block;
}

swiper-component.swiper-component-full-width {
  --desktop-margin-left-first-item: 1.5rem;
}

@media screen and (max-width: 749px) {
  swiper-component.page-width {
    padding: 0 1.5rem;
  }

  .slider.slider--mobile {
    position: relative;
    flex-wrap: inherit;
    overflow-x: auto;
    scroll-snap-type: x mandatory;
    scroll-behavior: smooth;
    scroll-padding-left: 1.5rem;
    -webkit-overflow-scrolling: touch;
    margin-bottom: 1rem;
    overflow:hidden;
  }
}

@media screen and (min-width: 749px) and (max-width: 990px) {
  swiper-component.page-width {
    padding: 0 5rem;
  }
}

@media screen and (max-width: 989px) {
  .no-js swiper-component .slider {
    padding-bottom: 3rem;
  }
}
@media screen and (min-width: 750px) {
swiper-component .grid--1-col-desktop.slider.slider--mobile .slider__slide {
  width: calc(100% - var(--grid-mobile-horizontal-spacing) - 0rem);
  max-width: calc(100% - var(--grid-desktop-horizontal-spacing) * 4 / 5);
  height:100%;
}
swiper-component .grid--2-col-desktop.slider.slider--mobile .slider__slide {
  width: calc(50% - var(--grid-mobile-horizontal-spacing) - 3rem);
  max-width: calc(50% - var(--grid-desktop-horizontal-spacing) * 4 / 5);
}
swiper-component .grid--3-col-desktop.slider.slider--mobile .slider__slide {
  width: calc(33% - var(--grid-mobile-horizontal-spacing) - 3rem);
  max-width: calc(33% - var(--grid-desktop-horizontal-spacing) * 4 / 5);
}
}
.slider__slide {
  --focus-outline-padding: 0.5rem;
  --shadow-padding-top: calc(var(--shadow-vertical-offset) * -1 + var(--shadow-blur-radius));
  --shadow-padding-bottom: calc(var(--shadow-vertical-offset) + var(--shadow-blur-radius));
  scroll-snap-align: start;
  flex-shrink: 0;
  padding-bottom: 0;
}

  .slider.slider--mobile .slider__slide {
    margin-bottom: 0;
    padding-top: max(var(--focus-outline-padding), var(--shadow-padding-top));
    padding-bottom: max(var(--focus-outline-padding), var(--shadow-padding-bottom));
  }

  .slider.slider--mobile.contains-card--standard .slider__slide:not(.collection-list__item--no-media) {
    padding-bottom: var(--focus-outline-padding);
  }

  .slider.slider--mobile.contains-content-container .slider__slide {
    --focus-outline-padding: 0rem;
  }


.slider--everywhere {
  position: relative;
  flex-wrap: inherit;
  overflow-x: auto;
  scroll-snap-type: x mandatory;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  margin-bottom: 1rem;
}

.slider.slider--everywhere .slider__slide {
  margin-bottom: 0;
  scroll-snap-align: center;
}


@media (prefers-reduced-motion) {
  .slider {
    scroll-behavior: auto;
  }
}

/* Scrollbar */

.slider {
  scrollbar-color: rgb(var(--color-foreground)) rgba(var(--color-foreground), 0.04);
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.slider::-webkit-scrollbar {
  height: 0.4rem;
  width: 0.4rem;
  display: none;
}

.no-js .slider {
  -ms-overflow-style: auto;
  scrollbar-width: auto;
}

.no-js .slider::-webkit-scrollbar {
  display: initial;
}

.slider::-webkit-scrollbar-thumb {
  background-color: rgb(var(--color-foreground));
  border-radius: 0.4rem;
  border: 0;
}

.slider::-webkit-scrollbar-track {
  background: rgba(var(--color-foreground), 0.04);
  border-radius: 0.4rem;
}

.slider-counter {
  display: flex;
  justify-content: center;
  min-width: 4.4rem;
}

@media screen and (min-width: 750px) {
  .slider-counter--dots {
    margin: 0 1.2rem;
  }
}

.slider-counter__link {
  padding: 1rem;
}

@media screen and (max-width: 749px) {
  .slider-counter__link {
    padding: 0.7rem;
  }
}

.slider-counter__link--dots .dot {
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  border: 0.1rem solid rgba(var(--color-foreground), 0.5);
  padding: 0;
  display: block;
}

.slider-counter__link--active.slider-counter__link--dots .dot {
  background-color: rgb(var(--color-foreground));
}

@media screen and (forced-colors: active) {
  .slider-counter__link--active.slider-counter__link--dots .dot {
    background-color: CanvasText;
  }
}

.slider-counter__link--dots:not(.slider-counter__link--active):hover .dot {
  border-color: rgb(var(--color-foreground));
}

.slider-counter__link--dots .dot,
.slider-counter__link--numbers {
  transition: transform 0.2s ease-in-out;
}

.slider-counter__link--active.slider-counter__link--numbers,
.slider-counter__link--dots:not(.slider-counter__link--active):hover .dot,
.slider-counter__link--numbers:hover {
  transform: scale(1.1);
}

.slider-counter__link--numbers {
  color: rgba(var(--color-foreground), 0.5);
  text-decoration: none;
}

.slider-counter__link--numbers:hover {
  color: rgb(var(--color-foreground));
}

.slider-counter__link--active.slider-counter__link--numbers {
  text-decoration: underline;
  color: rgb(var(--color-foreground));
}

.swiper-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
}


.slider-button {
  color: rgba(var(--color-foreground), 0.75);
  background: transparent;
  border: none;
  cursor: pointer;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slider-button:not([disabled]):hover {
  color: rgb(var(--color-foreground));
}

.slider-button .icon {
  height: 0.6rem;
}

.slider-button[disabled] .icon {
  color: rgba(var(--color-foreground), 0.3);
  cursor: not-allowed;
}

.slider-button--next .icon {
  transform: rotate(-90deg);
}

.slider-button--prev .icon {
  transform: rotate(90deg);
}

.slider-button--next:not([disabled]):hover .icon {
  transform: rotate(-90deg) scale(1.1);
}

.slider-button--prev:not([disabled]):hover .icon {
  transform: rotate(90deg) scale(1.1);
}
