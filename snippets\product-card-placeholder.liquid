
{{ 'component-rating.css' | asset_url | stylesheet_tag }}
{%- assign product_form_id = 'product-form-' | append: product_card_product.id -%}
<div class="card-wrapper">
  <div class="card card--product" tabindex="-1">
    <span class="visually-hidden">{{ 'onboarding.product_title' | t }}</span>
    <div class="card__inner">       
      {{ 'product-1' | placeholder_svg_tag: 'placeholder-svg' }}    
    </div>
  </div>

  <div class="card-information">
    <div class="card-information__wrapper">      
      {%- if show_vendor -%}
      <span class="visually-hidden">{{ 'accessibility.vendor' | t }}</span>
      <div class="caption-with-letter-spacing light">{{ 'products.product.vendor' | t }}</div>
      {%- endif -%}

      <span class="card-information__text">
        <a href="{{ product_card_product.url |  within: collection | default: '#' }}" class="full-unstyled-link"> {{ 'onboarding.product_title' | t }} </a>
      </span>

      {% comment %} TODO: metafield {% endcomment %}
      <span class="caption-large light">{{ block.settings.description | escape }}</span>
      {% render 'price', product: product_card_product, price_class: '' %}

    </div>

    {% liquid
          assign rating_decimal = 0 
          assign decimal = product_card_product.metafields.reviews.rating.value.rating | modulo: 1 
          if decimal >= 0.3 and decimal <= 0.7
            assign rating_decimal = 0.5
          elsif decimal > 0.7
            assign rating_decimal = 1
          endif          
        %}
    <!-- Ratings -->    
    <span class="shopify-product-reviews-badge" data-id="{{ product_card_product.id }}"></span>
    <!-- Ratings End -->    
  </div>
</div>
