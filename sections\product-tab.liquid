{{ 'section-product-tab.css' | asset_url | stylesheet_tag }}
<link rel="stylesheet" href="{{ 'product-tab.css' | asset_url }}" media="print" onload="this.media='all'">
<link rel="stylesheet" href="{{ 'component-price.css' | asset_url }}" media="print" onload="this.media='all'">
<link rel="stylesheet" href="{{ 'component-card.css' | asset_url }}" media="print" onload="this.media='all'">
<link rel="stylesheet" href="{{ 'template-collection.css' | asset_url }}" media="print" onload="this.media='all'">

<noscript>{{ 'template-collection.css' | asset_url | stylesheet_tag }}</noscript>
<noscript>{{ 'product-tab.css' | asset_url | stylesheet_tag }}</noscript>
<noscript>{{ 'component-price.css' | asset_url | stylesheet_tag }}</noscript>
<noscript>{{ 'component-badge.css' | asset_url | stylesheet_tag }}</noscript>
{{ 'component-card.css' | asset_url | stylesheet_tag }}
{%- if settings.enable_quickadd or settings.enable_quickview -%}
  <link rel="stylesheet" href="{{ 'quick-add.css' | asset_url }}" media="print" onload="this.media='all'">
  <script src="{{ 'quick-add.js' | asset_url }}" defer="defer"></script>
  <script src="{{ 'product-form.js' | asset_url }}" defer="defer"></script>  
{%- endif -%}
  
{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
  @media screen and (max-width: 990px) {
    .section-{{ section.id }}-padding {
      padding-top:65px;
      padding-bottom:0px;
    }
  }
/* .product-tab-wrapper .collection .tabs .tablinks:after{content:'';width:0;left:0;right:0;margin:auto;height:2px;bottom:8px;background:currentcolor;position:absolute;opacity:0;transition:all 0.3s linear;} */
.product-tab-wrapper .collection .tabs .tablinks.active:after,
.product-tab-wrapper .collection .tabs .tablinks:hover:after  {width:100%;opacity:1;}
@media screen and (max-width:575px){
  .product-tab-wrapper .collection .tabs .tablinks.active:after,
.product-tab-wrapper .collection .tabs .tablinks:hover:after  {width:16px;opacity:1;}
  .section-{{ section.id }}-padding {
      padding-top: 38px;
      padding-bottom: 0px;
    }
}
.product-tab-demo .swiper-grid-column>.swiper-wrapper .swiper-slide{margin-top:0 !important;}  
{%- endstyle -%}

{% liquid
        case section.settings.products_per_row
        when 1
        assign column = 'one-column'
        when 2
        assign column = 'two-column'
        when 3
        assign column = 'three-column'
        when 4
        assign column = 'four-column'
        when 5
        assign column = 'five-column'
        when 6
        assign column = 'six-column'
        endcase
        %}
  
<div class="product-tab-wrapper {{ section.settings.custom_class_name }} color-{{ section.settings.color_scheme }} gradient">
  <div class="{% if section.settings.page_full_width %} page-full-width {% else %} page-width {% endif %} {% if section.settings.page_full_width_spacing %} page-full-width_spacing {% endif %}section-{{ section.id }}-padding isolate">
    <div class="row">
  <div class="collection">
     {%- unless section.settings.title == blank -%}
      <div class="title-wrapper-with-link title-wrapper--self-padded-mobile title-wrapper--no-top-margin content-align--{{ section.settings.column_alignment }}">
         {%- if section.settings.sub_heading != blank -%}  
         <h6 class="sub-heading">{{ section.settings.sub_heading | escape }}</h6>
         {%- endif -%} 
         {%- if section.settings.title != blank -%}  
          <h2 class="title {{ section.settings.heading_size }}">
            {{ section.settings.title }}
          </h2>
          {%- endif -%} 
          {%- if section.settings.description != blank -%}  
          <p class="description">{{ section.settings.description }}</p>
          {%- endif -%}   
          <!-- {%- if section.settings.button_label != blank -%}
            <a {% if section.settings.button_link == blank %} role="link" aria-disabled="true"{% else %} href="{{ section.settings.button_link }}"{% endif %} class="button{% if section.settings.button_style_secondary %} button--secondary{% else %} button--primary{% endif %}">{{ section.settings.button_label | escape }}</a>
          {%- endif -%} -->
          </div>
    {%- endunless -%}
    <div class="image-with-text__grid grid{% if section.settings.layout == 'text_first' %} image-with-text__grid--reverse{% endif %}">
      {%- unless section.settings.layout == 'none' -%}
      <div class="grid__item">
         <div class="image-with-text__media media image-with-text__media--placeholder placeholder"
             {% if section.settings.height == 'adapt' and section.settings.image != blank %}
             style="padding-bottom: {{ 1 | divided_by: section.settings.image.aspect_ratio | times: 100 }}%;"
             {% endif %}
             >
          {%- if section.settings.image != blank -%}
          <img srcset="{%- if section.settings.image.width >= 165 -%}{{ section.settings.image | image_url: width: 165 }} 165w,{%- endif -%}
                       {%- if section.settings.image.width >= 360 -%}{{ section.settings.image | image_url: width: 360 }} 360w,{%- endif -%}
                       {%- if section.settings.image.width >= 620 -%}{{ section.settings.image | image_url: width: 620 }} 620w,{%- endif -%}
                       {%- if section.settings.image.width >= 720 -%}{{ section.settings.image | image_url: width: 720 }} 720w,{%- endif -%}
                       {%- if section.settings.image.width >= 940 -%}{{ section.settings.image | image_url: width: 940 }} 940w,{%- endif -%}
                       {%- if section.settings.image.width >= 1070 -%}{{ section.settings.image | image_url: width:  1070 }} 1070w{%- endif -%}"
               src="{{ section.settings.image | image_url: width: 533 }}"
               sizes="(min-width: 1100px) 900px, (min-width: 750px) calc((100vw - 130px) / 2), calc((200vw - 50px) / 2)"
               alt="{{ section.settings.image.alt | escape }}"
               loading="lazy"
               width="{{ section.settings.image.width }}" height="{{ section.settings.image.height }}"
               >
          {%- else -%}
          {{ 'image' | placeholder_svg_tag: 'placeholder-svg' }}
          {%- endif -%}
          {% if section.settings.image_block_heading != blank or section.settings.image_block_sub_heading != blank or section.settings.image_block_description != blank or section.settings.image_block_button_link != blank %}
          <div class="image-block-heading color-{{ section.settings.image_color_scheme }} {{ section.settings.image_block_vertical_position | escape }} {{ section.settings.image_block_horizontal_position | escape }}
                      {%- if section.settings.image_block_enable_overlay -%} image-block-overlay {%- endif -%}
                      ">
            {% if section.settings.image_block_sub_heading != blank %}
            <h6 class="image-block-sub-heading">{{ section.settings.image_block_sub_heading }}</h6>
            {% endif %}
            {% if section.settings.image_block_heading != blank %}
            <h3 class="image-block-main-heading">{{ section.settings.image_block_heading }}</h3>
            {% endif %}
            {% if section.settings.image_block_description != blank %}
            <p class="image-block-heading-description">{{ section.settings.image_block_description }}</p>
            {% endif %}
            {% if section.settings.image_block_button_link != blank and section.settings.image_block_button_text != blank %}
            <a href="{{ section.settings.image_block_button_link }}" class="button button--primary">{{ section.settings.image_block_button_text }}</a>
            {% endif %}
          </div>
          {% endif %}
         </div>
      </div>
      {%- endunless -%}  
      <div class="grid__item tab-{{ section.id }}-js" id="tab-{{ section.id }}-js" data-tab-wrapper="tab-main-{{ section.id }}">
        <div class="tabs {{ section.settings.tab_alignment }}">
          {% for block in section.blocks %}    
          {% assign collection = block.settings.collection %}
          <div class="tablinks"  id="dT_tab-{{ block.id }}">
             {%- if block.settings.collection_image != blank and section.settings.enable_collection_icon != blank -%}
          <img srcset="{%- if block.settings.collection_image.width >= 165 -%}{{ block.settings.collection_image | image_url: width: 165 }} 165w,{%- endif -%}
                       {%- if block.settings.collection_image.width >= 360 -%}{{ block.settings.collection_image | image_url: width: 360 }} 360w,{%- endif -%}
                       {%- if block.settings.collection_image.width >= 620 -%}{{ block.settings.collection_image | image_url: width: 620 }} 620w,{%- endif -%}
                       {%- if block.settings.collection_image.width >= 720 -%}{{ block.settings.collection_image | image_url: width: 720 }} 720w,{%- endif -%}
                       {%- if block.settings.collection_image.width >= 940 -%}{{ block.settings.collection_image | image_url: width: 940 }} 940w,{%- endif -%}
                       {%- if block.settings.collection_image.width >= 1070 -%}{{ block.settings.collection_image | image_url: width:  1070 }} 1070w{%- endif -%}"
               src="{{ block.settings.collection_image | image_url: width: 533 }}"
               sizes="(min-width: 1100px) 900px, (min-width: 750px) calc((100vw - 130px) / 2), calc((200vw - 50px) / 2)"
               alt="{{ block.settings.collection_image.alt | escape }}"
               loading="lazy"
               width="{{ block.settings.collection_image.width }}" height="{{ block.settings.collection_image.height }}"
               >
          {%- endif -%}
            {%- if section.settings.enable_tab_title != blank -%}
            {% unless block.settings.title == blank %} {{ block.settings.title }} {% else %} {{collection.title }}{% endunless %}
            {%- endif -%} 
            {%- if section.settings.enable_tab_count  -%}
           <span class="tab-count">[{{ collection.all_products_count }}]</span>  
              {% endif %}
          </div>   
          {% endfor %}
        </div>
           <div class="tabs_container">
          {% if section.settings.enable_slider %}
          {% for block in section.blocks %}
          <div class="slider-mobile-gutter product-tab-carousel dt-sc-tabs-content-Details"  data-tab="dT_tab-{{ block.id }}" id="block-tab-{{ forloop.index }}">
          <div class="swiper-container" data-tab-carousel="swiper-tab-carousel-{{ section.id }}" data-items="{{ section.settings.products_per_row }}" data-section-id="swiper-tab-{{ forloop.index }}-{{ block.id }}" id="swiper-{{ forloop.index }}-{{ block.id }}-carousel">
            <div class="swiper-wrapper color-{{ section.settings.color_scheme }} gradient " role="list">
              {%- for product in collections[block.settings.collection].products limit: section.settings.products_to_show -%}
              <div class="swiper-slide color-{{ section.settings.color_scheme }} card_style-{{ settings.card_style }}">
               {%  case settings.card_style %}
          {%  when 'standard' %}  
            {% render 'card-product',
              card_product: product,
              media_aspect_ratio: section.settings.image_ratio,
              show_secondary_image: section.settings.show_secondary_image,
              show_vendor: section.settings.show_vendor,
              show_rating: section.settings.show_rating,
              show_quick_add: settings.enable_quickadd,
              show_quick_view: settings.enable_quickview,
              show_new_tag: section.settings.show_new_tag,
              section_id: section.id
            %}
        {%  when 'button_width_icons' %}  
             {% render 'card-product-2',
              card_product: product,
              media_aspect_ratio: section.settings.image_ratio,
              show_secondary_image: section.settings.show_secondary_image,
              show_vendor: section.settings.show_vendor,
              show_rating: section.settings.show_rating,
              show_quick_add: settings.enable_quickadd,
              show_quick_view: settings.enable_quickview,
              show_new_tag: section.settings.show_new_tag,
              section_id: section.id
            %}
        {%  when 'card_with_icons' %} 
             {% render 'card-product-3',
              card_product: product,
              media_aspect_ratio: section.settings.image_ratio,
              show_secondary_image: section.settings.show_secondary_image,
              show_vendor: section.settings.show_vendor,
              show_rating: section.settings.show_rating,
              show_quick_add: settings.enable_quickadd,
              show_quick_view: settings.enable_quickview,
              show_new_tag: section.settings.show_new_tag,
              section_id: section.id
            %}
        {%  when 'card_with_buttons' %}  
             {% render 'card-product-4',
              card_product: product,
              media_aspect_ratio: section.settings.image_ratio,
              show_secondary_image: section.settings.show_secondary_image,
              show_vendor: section.settings.show_vendor,
              show_rating: section.settings.show_rating,
              show_quick_add: settings.enable_quickadd,
              show_quick_view: settings.enable_quickview,
              show_new_tag: section.settings.show_new_tag,
              section_id: section.id
            %}
        {%  when 'card_with_overlay' %}  
             {% render 'card-product-5',
              card_product: product,
              media_aspect_ratio: section.settings.image_ratio,
              show_secondary_image: section.settings.show_secondary_image,
              show_vendor: section.settings.show_vendor,
              show_rating: section.settings.show_rating,
              show_quick_add: settings.enable_quickadd,
              show_quick_view: settings.enable_quickview,
              show_new_tag: section.settings.show_new_tag,
              section_id: section.id
            %}
        {%  endcase %}
              </div>
              {%- else -%}
              {%- for i in (1..section.settings.products_to_show) -%}
                  <div class="swiper-slide">
                  {%- assign placeholder_image_index = forloop.index0 | modulo: 4 | plus: 1 -%}
                  {%- assign placeholder_image = 'product-apparel-' | append: placeholder_image_index -%}
                  {% render 'card-product', show_vendor: section.settings.show_vendor, placeholder_image: placeholder_image %}
                  </div>
                  {%- endfor -%}
              {%- endfor -%}
            </div>       
            {%- if section.settings.enable_arrows -%}
            <div class="swiper-button-next" id="product-tab-{{ forloop.index }}-{{ block.id }}-next">
               <span>
                  <svg xmlns="http://www.w3.org/2000/svg" width="17" height="20" viewBox="0 0 17 20" fill="none">
                  <path d="M0.999998 1.33975L16 10L0.999997 18.6603L0.999998 1.33975Z" stroke="black"/>
                  </svg>
               </span>
                </div>
            <div class="swiper-button-prev" id="product-tab-{{ forloop.index }}-{{ block.id }}-prev">
              <span> 
                 <svg xmlns="http://www.w3.org/2000/svg" width="17" height="20" viewBox="0 0 17 20" fill="none">
                <path d="M16 18.6603L1 10L16 1.33975L16 18.6603Z" stroke="black"/>
                </svg>
              </span>
            </div>
            {%- endif -%}
            {%- if section.settings.enable_dotts -%}
            <div class="swiper-pagination" id="product-tab-{{ forloop.index }}-{{ block.id }}-pagination"></div>
            {%- endif -%}   
          </div>
           <script>       
       
            $(document).ready(function(){     
              var tabSlider = document.querySelector('[data-tab-carousel="swiper-tab-carousel-{{ section.id }}"]'); 
              var tabItems = tabSlider.getAttribute('data-items'); 
              var swiper = new Swiper('#swiper-{{ forloop.index }}-{{ block.id }}-carousel', {
                                      slidesPerView: tabItems,
                                      grid: {
                                        rows: 2,
                                      }, 
                                      autoplay: {
                                        delay: 2500,
                                        disableOnInteraction: false,
                                        pauseOnMouseEnter: true,
                                        },
                                       // spaceBetween: 30,  
                                      //focusableElements: 'input, select, option, textarea, video, label',
                                      navigation: {
                                      nextEl:'#product-tab-{{ forloop.index }}-{{ block.id }}-next',
                                      prevEl: '#product-tab-{{ forloop.index }}-{{ block.id }}-prev',
                                      },
                                      pagination: {
                                      el: '#product-tab-{{ forloop.index }}-{{ block.id }}-pagination',
                                      clickable: true,
                                      loop: false,
                                      },
                                      breakpoints: {
                                        "@0.00": {
                                        slidesPerView: 1,
                                        spaceBetween: 25,  
                                        grid: {
                                            rows: 1,
                                          }
                                        },
                                        767: {
                                        slidesPerView: 2,
                                        spaceBetween: 25,
                                        grid: {
                                            rows: 1,
                                          }
                                        },
                                        1200: {
                                        slidesPerView: 3,
                                        spaceBetween: 25,
                                        grid: {
                                            rows: 1,
                                          }
                                        },
                                        // 1540: {
                                        // slidesPerView: 4,
                                        // spaceBetween: 25,
                                        // grid: {
                                        //     rows: 1,
                                        //   }                                      
                                        // },
                                       1540: {
                                        slidesPerView: tabItems,
                                        spaceBetween: 25,
                                        grid: {
                                            rows: 2,
                                          }                                      
                                        },
                                        },
                                        });

            });
            $('.tab-{{ section.id }}-js .tablinks').each(function(){
              $(this).on('click', function(){
              let thisSwiper = document.getElementById("swiper-{{ forloop.index }}-{{ block.id }}-carousel");
               thisSwiper.swiper.update();
            });  
            });
             
          </script>
          </div>        
          
          {% endfor %}
          {% else %}          
          {% for block in section.blocks %}          
          <ul class="grid dt-sc-column {{ column }} dt-sc-tabs-content-Details" data-tab="dT_tab-{{ block.id }}">
            {% for product in collections[block.settings.collection].products limit: section.settings.products_to_show %}
            <li class="color-{{ section.settings.color_scheme }} card_style-{{ settings.card_style }}">
             {%  case settings.card_style %}
          {%  when 'standard' %}  
            {% render 'card-product',
              card_product: product,
              media_aspect_ratio: section.settings.image_ratio,
              show_secondary_image: section.settings.show_secondary_image,
              show_vendor: section.settings.show_vendor,
              show_rating: section.settings.show_rating,
              show_quick_add: settings.enable_quickadd,
              show_quick_view: settings.enable_quickview,
              show_new_tag: section.settings.show_new_tag,
              section_id: section.id
            %}
        {%  when 'button_width_icons' %}  
             {% render 'card-product-2',
              card_product: product,
              media_aspect_ratio: section.settings.image_ratio,
              show_secondary_image: section.settings.show_secondary_image,
              show_vendor: section.settings.show_vendor,
              show_rating: section.settings.show_rating,
              show_quick_add: settings.enable_quickadd,
              show_quick_view: settings.enable_quickview,
              show_new_tag: section.settings.show_new_tag,
              section_id: section.id
            %}
        {%  when 'card_with_icons' %} 
             {% render 'card-product-3',
              card_product: product,
              media_aspect_ratio: section.settings.image_ratio,
              show_secondary_image: section.settings.show_secondary_image,
              show_vendor: section.settings.show_vendor,
              show_rating: section.settings.show_rating,
              show_quick_add: settings.enable_quickadd,
              show_quick_view: settings.enable_quickview,
              show_new_tag: section.settings.show_new_tag,
              section_id: section.id
            %}
        {%  when 'card_with_buttons' %}  
             {% render 'card-product-4',
              card_product: product,
              media_aspect_ratio: section.settings.image_ratio,
              show_secondary_image: section.settings.show_secondary_image,
              show_vendor: section.settings.show_vendor,
              show_rating: section.settings.show_rating,
              show_quick_add: settings.enable_quickadd,
              show_quick_view: settings.enable_quickview,
              show_new_tag: section.settings.show_new_tag,
              section_id: section.id
            %}
        {%  when 'card_with_overlay' %}  
             {% render 'card-product-5',
              card_product: product,
              media_aspect_ratio: section.settings.image_ratio,
              show_secondary_image: section.settings.show_secondary_image,
              show_vendor: section.settings.show_vendor,
              show_rating: section.settings.show_rating,
              show_quick_add: settings.enable_quickadd,
              show_quick_view: settings.enable_quickview,
              show_new_tag: section.settings.show_new_tag,
              section_id: section.id
            %}
        {%  endcase %}
            </li>
            {%- else -%}
            
              {%- for i in (1..section.settings.products_to_show) -%}
                  <div class="swiper-slide">
                  {%- assign placeholder_image_index = forloop.index0 | modulo: 4 | plus: 1 -%}
                  {%- assign placeholder_image = 'product-apparel-' | append: placeholder_image_index -%}
                  {% render 'card-product', show_vendor: section.settings.show_vendor, placeholder_image: placeholder_image %}
                  </div>
                  {%- endfor -%}
            {% endfor %}
          </ul>
          {% endfor %}
          {% endif %}
        </div>
      </div>  
    </div>
   </div> 
      {%- if section.settings.show_view_all  -%}
      <div class="center collection__view-all">
        <a href="{{ routes.all_products_collection_url }}"
          class="button button--primary"
          aria-label="{{ 'sections.featured_collection.view_all_label' | t: collection_name: section.settings.collection.title }}"
        >
          {{ 'sections.featured_collection.view_all' | t }}
        </a>
      </div>
    {%- endif -%}
</div>
      <div class="view-all-product">
          {%- if section.settings.button_label != blank -%}
            <a {% if section.settings.button_link == blank %} role="link" aria-disabled="true"{% else %} href="{{ section.settings.button_link }}"{% endif %} class="button{% if section.settings.button_style_secondary %} button--secondary{% else %} button--primary{% endif %}">{{ section.settings.button_label | escape }}</a>
          {%- endif -%}
      </div>
</div>
</div>
{%- style -%}

/*   {% if section.settings.enable_collection_icon %}  
  {% for block in section.blocks %}    
  {% unless block.settings.collection_image == blank %}
  .product-tab-wrapper .tabs .tablinks[id*="dT_tab-{{ forloop.index }}"]:before { mask-image: url('{{ block.settings.collection_image | image_url: width: 40 }}'); -webkit-mask-image: url('{{ block.settings.collection_image | image_url: width: 40 }}');  }
  .tabs .tablinks:before {
    content: ""; display: block; position: relative; background: currentcolor; width:40px; height:40px; margin-right: 12px;
    mask-repeat: no-repeat; -webkit-mask-repeat: no-repeat; mask-position: center left; -webkit-mask-position: center;
    mask-size: contain; -webkit-mask-size: contain; }
{% endunless %}
  {% endfor %}
  {% endif %} */
 @media screen and (max-width: 576px) {  
.product-tab-wrapper .tabs_container {    width: 100%;     overflow: hidden;}
 }
 {%- endstyle -%}  
      <script type="text/javascript">  
      dtTabEvents(); 
  function dtTabEvents() {
  $('.tab-{{ section.id }}-js').each(function(){
    $(this).find('.dt-sc-tabs-content-Details:first').addClass('active');
  $(this).find('.tablinks:first').addClass('active');
  });

  $('.tab-{{ section.id }}-js .tablinks').each(function(){
    $(this).on('click', function(){
          $(".tabs").toggleClass('expanded');
    var $this = $(this);
    var $theTab = $(this).attr('id');  
    $(this).fadeIn('slow').siblings(".dt-sc-tabs-content-Details").hide();
    if($this.hasClass('active')){
      // do nothing
      
    } else {
     $this.closest('.tab-{{ section.id }}-js').find('.tabs .tablinks, .tabs_container .dt-sc-tabs-content-Details').removeClass('active'); $('.tablinks[id="'+$theTab+'"]').addClass('active');
     $('.tab-{{ section.id }}-js .tabs_container .dt-sc-tabs-content-Details[data-tab="'+$theTab+'"], .tab-{{ section.id }}-js .tabs .tablinks[id="'+$theTab+'"]').addClass('active');
       }
     });
    });
       $(document).click(function(event) {
    if (!$(event.target).closest(".tabs_container").length) {
      $("body").find(".grid-tabs").removeClass("expanded");
    }
  });
        }

   
   
document.addEventListener("shopify:section:load", function(e) {
  if (document.getElementById(`shopify-section-${e.detail.sectionId}`).classList.contains('product-tab-with-carousel')) {
    dtTabEvents();     
  }
   
});     
document.addEventListener('shopify:block:select', function (event) {
  dtTabEvents();
  const blockSelectedIsSlide = event.target.classList.contains('tablinks');  
  if (!blockSelectedIsSlide) return;
});

document.addEventListener('shopify:block:deselect', function (event) {
  const blockDeselectedIsSlide = event.target.classList.contains('tablinks');
  if (!blockDeselectedIsSlide) return;  
  dtTabEvents();
});
        
</script>
{% schema %}
{
  "name": "product-tab",
  "class": "section product-tab-with-carousel",
  "tag": "section",
  "settings": [
    {
      "type":"checkbox",
      "id":"page_full_width",
       "default": false,
      "label": "t:sections.all.page_full_width.label"
    },
    {
      "type":"checkbox",
      "id":"page_full_width_spacing",
       "default": false,
      "label": "t:sections.all.page_full_width_spacing.label"
    },
      {
      "type": "text",
      "id": "title",
      "default": "Featured Collection",
      "label": "t:sections.all.title.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__3.label"
        }
      ],
      "default": "h1",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "text",
      "id": "sub_heading",
      "default":"Sub Heading",
      "label": "t:sections.all.sub_heading.label"
    },
     {
      "type": "text",
      "id": "description",
      "default":"Use this text to share the information which you like!.",
      "label": "t:sections.all.description.label"
    },
    {
      "type": "text",
      "id": "button_label",
      "default": "Button label",
      "label": "t:sections.all.button_label.label"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "t:sections.all.button_link.label"
    },
    {
      "type": "checkbox",
      "id": "button_style_secondary",
      "default": false,
      "label": "t:sections.all.button_style_secondary.label"
      },
    {
      "type": "select",
      "id": "column_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.product-tab.settings.column_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.product-tab.settings.column_alignment.options__2.label"
        }
      ],
      "default": "center",
      "label": "t:sections.product-tab.settings.column_alignment.label"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "t:sections.all.colors.accent_1.label"
        },
        {
          "value": "accent-2",
          "label": "t:sections.all.colors.accent_2.label"
        },
        {
          "value": "background-1",
          "label": "t:sections.all.colors.background_1.label"
        },
        {
          "value": "background-2",
          "label": "t:sections.all.colors.background_2.label"
        },
        {
          "value": "inverse",
          "label": "t:sections.all.colors.inverse.label"
        }
      ],
      "default": "background-1",
      "label": "t:sections.all.colors.label"
    },
    {
    "type": "range",
    "id": "products_to_show",
    "min": 2,
    "max": 12,
    "step": 1,
    "default": 4,
    "label": "t:sections.product-tab.settings.products_to_show.label"
    },
    {
    "type": "range",
    "id": "products_per_row",
    "min": 1,
    "max": 5,
    "step": 1,
    "default": 4,
    "label": "t:sections.product-tab.settings.products_per_row.label",
    "info": "t:sections.product-tab.settings.products_per_row.info"
    }, 
    {
    "type": "checkbox",
    "id": "enable_slider",
    "default": false,
    "label": "t:sections.product-tab.settings.enable_slider.label"
    },
    {
    "type": "checkbox",
    "id": "enable_dotts",
    "default": false,
    "label": "t:sections.product-tab.settings.enable_dotts.label"
    },
    {
    "type": "checkbox",
    "id": "enable_arrows",
    "default": false,
    "label": "t:sections.product-tab.settings.enable_arrows.label"
    },
    {
    "type": "checkbox",
    "id": "enable_tab_title",
    "default": true,
    "label": "t:sections.product-tab.settings.enable_tab_title.label"
    },
     {
      "type": "checkbox",
      "id": "show_rating",
      "default": false,
      "label": "t:sections.main-collection-product-grid.settings.show_rating.label",
      "info": "t:sections.main-collection-product-grid.settings.show_rating.info"
    },
    {
    "type": "checkbox",
    "id": "enable_tab_count",
    "label": "t:sections.product-tab.settings.enable_tab_count.label"
    },
    {
    "type": "checkbox",
    "id": "enable_collection_icon",
    "label": "t:sections.product-tab.settings.enable_collection_icon.label"
    },
    {
      "type": "checkbox",
      "id": "show_view_all",
      "default": false,
      "label": "t:sections.product-tab.settings.show_view_all.label"
    },
     {
      "type": "select",
      "id": "tab_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.product-tab.settings.tab_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.product-tab.settings.tab_alignment.options__2.label"
        },
        {
          "value": "right",
          "label": "t:sections.product-tab.settings.tab_alignment.options__3.label"
        }
      ],
      "default": "center",
      "label": "t:sections.product-tab.settings.tab_alignment.label"
    },
    {
    "type": "header",
    "content": "t:sections.product-tab.settings.header.content"
    },
    {
    "type": "select",
    "id": "image_ratio",
    "options": [
    {
    "value": "adapt",
    "label": "t:sections.product-tab.settings.image_ratio.options__1.label"
    },
    {
    "value": "portrait",
    "label": "t:sections.product-tab.settings.image_ratio.options__2.label"
    },
    {
    "value": "square",
    "label": "t:sections.product-tab.settings.image_ratio.options__3.label"
    }
    ],
    "default": "adapt",
    "label": "t:sections.product-tab.settings.image_ratio.label"
    },
    {
    "type": "checkbox",
    "id": "show_secondary_image",
    "default": false,
    "label": "t:sections.product-tab.settings.show_secondary_image.label"
    },
    {
    "type": "checkbox",
    "id": "show_vendor",
    "default": false,
    "label": "t:sections.product-tab.settings.show_vendor.label"
    },
    {
    "type": "select",
    "id": "layout",
    "options": [
    {
    "value": "image_first",
    "label": "t:sections.product-tab.settings.layout.options__1.label"
    },
    {
    "value": "text_first",
    "label": "t:sections.product-tab.settings.layout.options__2.label"
    },
    {
    "value": "none",
    "label": "t:sections.product-tab.settings.layout.options__3.label"
    }
    ],
    "default": "none",
    "label": "t:sections.product-tab.settings.layout.label",
    "info": "t:sections.product-tab.settings.layout.info"
    },
    {
    "type": "header",        
    "content": "t:sections.product-tab.settings.header__2.content"
    },
    {
    "type": "image_picker",
    "id": "image",
    "label": "t:sections.product-tab.settings.image.label"
    }, 
    {
    "type": "text",
    "id": "image_block_heading",
    "label": "t:sections.product-tab.settings.image_block_heading.label",
    "default": "Main Heading"	
    },
    {
    "type": "text",
    "id": "image_block_sub_heading",
    "label": "t:sections.product-tab.settings.image_block_sub_heading.label",
    "default": "Sub Heading"	
    },
    {
    "type": "textarea",
    "id": "image_block_description",
    "label": "t:sections.product-tab.settings.image_block_description.label"
    },
    {
    "type": "text",
    "id": "image_block_button_text",
    "label": "t:sections.product-tab.settings.image_block_button_text.label",
    "default": "View More"
    },
    {
    "type": "url",
    "id": "image_block_button_link",
    "label": "t:sections.product-tab.settings.image_block_button_link.label"        
    },
    {
    "type": "select",
    "id": "image_block_horizontal_position",
    "label": "Horizontal Position",
    "options": [
    {
    "value": "left",
    "label": "t:sections.product-tab.settings.image_block_horizontal_position.options__1.label"
    },
    {
    "value": "center",
    "label": "t:sections.product-tab.settings.image_block_horizontal_position.options__2.label"
    },
    {
    "value": "right",
    "label": "t:sections.product-tab.settings.image_block_horizontal_position.options__3.label"
    }
    ],
    "default": "left"
    },
    {
    "type": "select",
    "id": "image_block_vertical_position",
    "label": "Vertical Position",
    "options": [
    {
    "value": "vertical_top",
    "label": "t:sections.product-tab.settings.image_block_vertical_position.options__1.label"
    },
    {
    "value": "vertical_middle",
    "label": "t:sections.product-tab.settings.image_block_vertical_position.options__2.label"
    },
    {
    "value": "vertical_bottom",
    "label": "t:sections.product-tab.settings.image_block_vertical_position.options__3.label"
    }
    ],
    "default": "vertical_top"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 150,
      "step": 5,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 35
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 150,
      "step": 5,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 35
    },
    {
    "type": "header",
    "content": "t:sections.all.custom_class_heading.content"
    },
    {
    "type": "text",
    "id": "custom_class_name",
    "label": "t:sections.all.custom_class_name.label"
    }
  ],
  "blocks": [
    {
    "type": "tab",
    "name": "t:sections.product-tab.blocks.tab.name",
    "settings": [ 
    {
    "type": "collection",
    "id": "collection",
    "label": "t:sections.product-tab.blocks.tab.settings.collection.label"    
    },
    {
    "type": "text",
    "id": "title",
    "default": "Collection name",
    "label": "t:sections.product-tab.blocks.tab.settings.title.label"    
    },
    {
    "type": "image_picker",
    "id": "collection_image",
    "label": "t:sections.product-tab.blocks.tab.settings.collection_image.label",
    "info": "Size Recommended: 100x100"
    }   
    ]
    }
    ],
  "presets": [
    {
      "name": "product-tab",
       "blocks": [
        {
          "type": "tab"
        },
        {
          "type": "tab"
        },
        {
          "type": "tab"
        }
 ] 
    }
  ]
}
{% endschema %}  
  