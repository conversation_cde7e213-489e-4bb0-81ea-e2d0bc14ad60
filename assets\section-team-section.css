.team-section .team-section-section.two-column{ display: grid; grid-template-columns: repeat(2,1fr);}
.team-section .team-section-section.three-column{ display: grid;  grid-template-columns: repeat(3,1fr);}
.team-section .team-section-section.four-column{ display: grid; grid-template-columns: repeat(4,1fr);}
.team-section .team-section-section.five-column{ display: grid; grid-template-columns: repeat(5,1fr);}
.team-section .team-section-section.six-column{ display: grid; grid-template-columns: repeat(6,1fr);}
.team-section .team-section-section{ column-gap: var(--grid-desktop-horizontal-spacing); row-gap: var(--grid-desktop-vertical-spacing);}
@media screen and (max-width: 1199px) and (min-width: 750px) {
.team-section .team-section-section.four-column{ display: grid; grid-template-columns: repeat(2,1fr);}
.team-section .team-section-section.five-column{ display: grid; grid-template-columns: repeat(3,1fr);}
.team-section .team-section-section.six-column{ display: grid; grid-template-columns: repeat(3,1fr);}
.team-section .team-section-section { column-gap: var(--grid-desktop-horizontal-spacing); row-gap: var(--grid-desktop-horizontal-spacing);}  
}
@media screen and (max-width: 749px) {
.team-section .team-section-section.four-column{ display: grid; grid-template-columns: repeat(2,1fr);}
.team-section .team-section-section.five-column{ display: grid; grid-template-columns: repeat(2,1fr);}
.team-section .team-section-section.three-column{ display: grid;  grid-template-columns: repeat(2,1fr);}
.team-section .team-section-section.six-column{ display: grid; grid-template-columns: repeat(2,1fr);}
.team-section .team-section-section { column-gap: var(--grid-mobile-horizontal-spacing); row-gap: var(--grid-mobile-vertical-spacing);}  
}
 /* @media screen and (max-width: 400px) {
 .team-section .team-section-section.two-column{ display: grid; grid-template-columns: repeat(1,1fr);}
.team-section .team-section-section.three-column{ display: grid;  grid-template-columns: repeat(1,1fr);}
.team-section .team-section-section.four-column{ display: grid; grid-template-columns: repeat(1,1fr);}
.team-section .team-section-section.five-column{ display: grid; grid-template-columns: repeat(1,1fr);}
.team-section .team-section-section.six-column{ display: grid; grid-template-columns: repeat(1,1fr);}
.team-section .team-section-section { column-gap: var(--grid-mobile-horizontal-spacing); row-gap: var(--grid-mobile-vertical-spacing);}
  
 } */

.team-section .title-wrapper-with-link.content-align--left{align-items: flex-start;}
.team-section .title-wrapper-with-link.content-align--center{align-items: center;}
.team-section .team-section-section .team-section-wrapper .team-section-block-image img.team-section-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.team-section-inner.banner--content-align-center {
    align-items: center;
    text-align: center;
}
.team-section-inner.banner--content-align-right {
    align-items: flex-end;
    text-align: right;
}
.team-section-inner.banner--content-align-left {
    align-items: flex-start;
    text-align: left;
}

.team-section .team-section-section:not(.background-none) .team-section-wrapper {
  background: rgb(var(--color-background));
  height: 100%;
}

.team-section .dt-sc-team-section-section.background-primary .team-section-wrapper {
  background: rgb(var(--color-background)) linear-gradient(rgba(var(--color-foreground), 0.04), rgba(var(--color-foreground), 0.04));
}
.team-section .team-section-content p.description{  margin-top: 10px; margin-bottom:0;}
.team-section .team-section-content .team-section-inner h4.main-title a{color:var(--color-foreground);}
.team-section .team-section-content{padding:20px;}
.team-section .team-section-content h4.main-title{  margin: 0; font-size: 2.6rem; margin-bottom: 10px; font-weight: 600;  line-height: normal; }
.team-section .team-section-content h6.sub-title{  margin: 0;  font-size: 1.6rem; font-weight: 500;  line-height: normal;}
.team-section .list-social { display: flex; flex-wrap: wrap; /*justify-content: space-evenly;*/ gap:30px;}
.team-section .list-social{margin-top:0;}
.team-section .list-social__link:hover{ color: rgb(var(--color-base-outline-button-labels));}
.team-section .team-section-block-image{position:relative;}
.team-section  .team-section-block-image {  display: flex; /*justify-content: center;*/}
.team-section .list-social__link{    padding: 0; color: rgb(var(--color-base-solid-button-labels));}
.team-section .team-section-block-image img{width:100%;}

 @media screen and (max-width: 480px) {
.team-section .team-section-content h4.main-title{font-size:1.6rem;}
.team-section .team-section-content h6.sub-title{font-size:1.4rem;} 
.team-section .team-section-content p.description{font-size:1.2rem;}   
.team-section .list-social__item .icon { height: 1.4rem; width: 1.4rem; }

/*Overlay style*/
.team-section-section.overlay .team-section-wrapper{ height: 500px; position: relative;}
.team-section-section.overlay .team-section-wrapper .team-section-block-image{width:100%; height:100%; }
.team-section-section.overlay .team-section-wrapper .team-section-content{    position: absolute; top: 0; bottom: 0; margin: auto; left: 0; right: 0; background: rgba(var(--color-background),0.4);}
.team-section-section.overlay .team-section-wrapper .team-section-content .team-section-inner{    width: 100%;  height: 100%;  display: flex;  flex-direction: column;   justify-content: center;}


