(()=>{var b=(a,c,t)=>({currentVariantCard:"",isSelect:!1,productId:t,showOptions:!1,init(){document.addEventListener(`eurus:product-card-variant-select:updated:${a}`,n=>{this.checkVariantSelected(),this.currentVariantCard=n.detail.currentVariant,this.options=n.detail.options})},checkVariantSelected(){Array.from(document.querySelectorAll(`#variant-update-${a} fieldset`)).findIndex(s=>!s.querySelector("input:checked"))===-1&&(this.isSelect=!0)}}),h={onChangeVariant:function(a,c,t,n,s,g=!1){},onVariantSelect:function(a,c,t,n){let s=`${t}&width=1500`,i=[450,750,900,1100,1500].map(e=>`${t}&width=${e} ${e}w`).join(", ");this.updateImage(a,c,s,n,void 0,void 0,i)},updateImage:function(a,c,t,n,s,g,i){let e=a.closest(".card-product"),p=c+`?variant=${n}`;if(!e)return;let d=e.getElementsByClassName("link-product-variant");for(var l=0;l<d.length;l++)d[l].setAttribute("href",p);let m=e.querySelector(".current-variant");m&&!g&&(m.innerText=n);let o=e.querySelector(".x-splide");(t==""||t===void 0||t===null)&&o?.splide?.go?.(0);let r=e.querySelector(`[media="${t}"]`);if(r){let f=r.getAttribute("index");if(o){o.splide?o.splide.go(Number(f)):document.addEventListener(`eurus:${s}:splide-ready`,()=>{o.splide.go(Number(f))});return}}let u=e.getElementsByClassName("preview-img")[0];u&&(i&&u.setAttribute("srcset",i),u.setAttribute("src",t),o?.splide?.go?.(0))}};window.otsb.loadedScript.includes("preview-color-swatches")||document.addEventListener("alpine:init",()=>{Alpine.data("xProductCard",b),Alpine.store("xPreviewColorSwatch",h)});})();