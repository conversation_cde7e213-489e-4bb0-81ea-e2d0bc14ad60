
  
  .dt-sc-product_bundle-items{ background:rgba(var(--color-foreground),.05); padding: 5rem; display: inline-block; width: 100%; border-radius: var(--DTRadius); margin: 7rem 0 0; }
  .dt-sc-product_bundle-items .section-title h3{ margin: 0 0 55px; text-align: center;     font-size: 2.4rem; }
  .dt-sc-product_bundle-items .dt-sc-thumb-image{ display: grid; grid-template-columns: repeat(4,1fr);grid-gap:30px; }
  .dt-sc-product_bundle-items .dt-sc-thumb-image > div{ text-align: center; margin-bottom: 20px; position: relative; width: 100%;display: flex;
    flex-direction: column;}

  .dt-sc-product_bundle-items .frequently-buy-togeather-products .dT_bundleProductToggle{padding:0 25px;}
  .dt-sc-product_bundle-items .dt-sc-thumb-image .item-image img{ width:100%;height:100%;}
  .dt-sc-product_bundle-items .dt-sc-thumb-image a.item-image{margin-bottom: 10px; width:100%;height:100%; display: flex; overflow: hidden; border-radius: var(--DTRadius);}
  .dt-sc-product_bundle-items .dt-sc-thumb-image > div:not(:last-child) > div:first-child{position: relative;}
  
  .dt-sc-product_bundle-items .dt-sc-thumb-image > div:not(:last-child) > div:first-child:after{ content: "+"; color:var(--gradient-base-accent-2); font-weight: 500; 
    position: absolute; top: 50%; transform: translateY(-50%); -webkit-transform: translateY(-50%); right: -31px; font-size: 25px; 
      width: 30px; height: 30px; border-radius: 50%; line-height: 25px; }
  
  .dt-sc-product_bundle-items .dt-sc-thumb-image li:not(:last-child){ margin-bottom:15px;}
  .dt-sc-product_bundle-items .dt-sc-thumb-image li > div[id*="sale-price"],
  .dt-sc-product_bundle-items .dt-sc-thumb-image li > div[class*="price-sale"] { font-size: var(--DTFontSize_H6); }

  .dt-sc-product_bundle-items .dt-sc-thumb-image  [type=checkbox]{ margin: auto; }  
  .dt-sc-product_bundle-items .dt-sc-product-details { display: flex; align-items: center; margin-bottom: 0; flex-flow: column wrap;line-height: normal;}
  .dt-sc-product_bundle-items .dt-sc-product-details span{font-weight: 600; padding: 0 3px; color: var(--DTPrimaryColor); }

  .dt-sc-product_bundle-items .bundle-product-cart-total{ font-weight: 600;  font-size: var(--DTFontSize_H6); }
  .dt-sc-product_bundle-items .dt-sc-select-btn{ width: 100%; padding: 10px 15px; border-radius: var(--DTRadius);  background-color: var(--gradient-base-accent-2); border: 1px solid var(--DTColor_Border);}
  .dt-sc-product_bundle-items .varient-options{ z-index: 2; left:0; display: none; margin-bottom: 15px; padding: 15px !important; width:100% !important;box-shadow: 0 1px 5px #0000001a; border-radius: var(--DTRadius); background-color: var(--gradient-base-background-1); position: absolute; }
  .dt-sc-product_bundle-items .product-form__controls-group{ display:flex; flex-flow: column; }
  .dt-sc-product_bundle-items .product-form__controls-group ~ *{ display:none;}
  .dt-sc-product_bundle-items .product-form__controls-group .selector-wrapper{ display: inline-flex; text-align: left; margin-bottom:15px;}
  .dt-sc-product_bundle-items .product-form__controls-group .selector-wrapper:last-child{  margin-bottom:0px; }
  .dt-sc-product_bundle-items .selector-wrapper label { 
    display: inline-flex;
    font-weight: 600;
    width: 50%;
    font-size: 14px;
    font-family: var(--font-heading-family);}
  .dt-sc-product_bundle-items .selector-wrapper .select2.select2-container { width: 50% !important; }
  .dt-sc-product_bundle-items .selector-wrapper select{     
    padding: 5px 10px;
    width: 100%;
    background-color: transparent;
    border-radius: var(--buttons-radius);
    color: var(--gradient-base-accent-1);
    border: var(--inputs-border-width) solid rgb(var(--color-foreground),0.2);
    cursor: pointer;}
  .dt-sc-product_bundle-items .dt-sc-btn.btn-product-add-to-cart, .dt-sc-product_bundle-items .bundle-product-add-to-cart, .dt-sc-product_bundle-items  .bundle-product-offer-note{ display: inline-block; margin: 0px;}
  .dt-sc-product_bundle-items .selector-wrapper select:focus-visible {
    box-shadow: none;
    outline: none;
}
  .dt-sc-product_bundle-items .products-grouped-info { text-align: center; }
  .dt-sc-product_bundle-items .bundle-product-additional-offer { background:var(--gradient-background); margin: auto; display: block; flex-direction: column; padding: 1.5rem 4rem; align-items: center; 
    justify-content: center; 
  }
  
 .dt-sc-product_bundle-items  .bundle-product-additional-offer > *:not(:last-child) { margin-bottom: 2rem;}

 .dt-sc-product_bundle-items .bundle-product-cart-total{ display: flex;flex-wrap:wrap;justify-content:center; }
.dt-sc-product_bundle-items  .bundle-product-cart-total > * + * { margin-left: 10px;}
.dt-sc-product_bundle-items  .bundle-product-add-to-cart > button { margin-top: 0; }
  .dt-sc-product_bundle-items .bundle-product-offer-note{ width: 100%; font-size: 16px; color: var(--gradient-base-accent-1); }
.dt-sc-product_bundle-items  span.dT_totalBundleOriginalPrice{ text-decoration: line-through; opacity: .5; color: inherit; font-weight: 400;}
.dt-sc-product_bundle-items  .group-product .old-price, .main-product .old-price{ font-size: 80%; opacity: .5; text-decoration: line-through;}
.dt-sc-product_bundle-items  .group-product .old-price, .group-product .special-price{margin: 0 5px;}

.dt-sc-product_bundle-items  .dt-sc-product_bundle-items .dt-sc-thumb-image ul { position: relative; line-height: normal; list-style: none; padding: 0;}
.dt-sc-product_bundle-items  .dt-sc-product_bundle-items .dt-sc-thumb-image ul li .dt-sc-btn { margin: auto; display: inline-block; }
  
.dt-sc-product_bundle-items  .checkbox_style { display: inline-block; position: relative; cursor: pointer; font-size: 0; height: 0; -webkit-user-select: none; -ms-user-select: none; user-select: none; }
.dt-sc-product_bundle-items  .checkbox_style input[type="checkbox"] { float: left; width: 36px; height: 6px; cursor: pointer; background-color: #fff; -webkit-appearance: none;
border-radius: 8px; position: relative; }
.dt-sc-product_bundle-items  .checkbox_style input[type="checkbox"]:before { content: ""; width: 17px; height: 17px; box-shadow: 0 0 0 calc(var(--buttons-border-width) + var(--border-offset)) rgba(var(--color-button-text), var(--border-opacity)),
    0 0 0 var(--buttons-border-width) rgba(var(--color-button), var(--alpha-button-background)); background-color: var(--gradient-background); border-radius: 50%;
position: absolute; top: -6px; z-index: 1; left: -2px; transition: var(--DTBaseTransition); }
.dt-sc-product_bundle-items   .checkbox_style input[type="checkbox"]:after { content: ""; height: 100%; width: 14px; position: absolute; left: 0; border-radius: 17px;
background-color: rgba(186, 141, 105, 0.5);; transition: var(--DTBaseTransition); }
.dt-sc-product_bundle-items  .checkbox_style input[type="checkbox"]:checked:before { left: calc(100% - 16px); background-color: var(--gradient-base-accent-2); }
.dt-sc-product_bundle-items  .checkbox_style input[type="checkbox"]:checked:after { width: 100%; border-radius: 8px; }  
.dt-sc-product_bundle-items .dt-sc-thumb-image li {list-style: none;line-height:normal;}
.dt-sc-product_bundle-items .dt-sc-thumb-image ul{padding:0;margin:0;}
.dt-sc-product_bundle-items .dt-sc-thumb-image .bundle-title {font-weight: 500;}

  @media (max-width: 1199px) and (min-width: 992px)  {
    .dt-sc-product_bundle-items .dt-sc-thumb-image { display: grid;  grid-template-columns: repeat(3,1fr);}
    .dt-sc-product_bundle-items {margin: 7rem 0 0;}
   
  }
  
  @media only screen and (max-width: 991px) and (min-width: 767px)  {    
    .dt-sc-product_bundle-items { padding: 3rem; }
    .dt-sc-product_bundle-items .dt-sc-thumb-image > div:not(:last-child) > div:first-child:after{ right: -26px; width: 20px; height: 20px; line-height: 20px; font-size: 16px; }
    .dt-sc-product_bundle-items .dt-sc-thumb-image ul li .dt-sc-btn,
    .bundle-product-add-to-cart > button { padding: 10px 20px; font-size: 16px; }
    .dt-sc-product_bundle-items .dt-sc-thumb-image { display: grid;  grid-template-columns: repeat(3,1fr);}
     .dt-sc-product_bundle-items {margin: 7rem 0 0;}
  }
    @media only screen  and (min-width: 767px)  { 
      .dt-sc-product_bundle-items .dt-sc-thumb-image a.item-image{height:300px;}
    }
  @media (max-width:767px) {    
    .dt-sc-product_bundle-items { padding: 3rem; }
    .dt-sc-product_bundle-items .dt-sc-thumb-image { flex-wrap: wrap; }
    .dt-sc-product_bundle-items .dt-sc-thumb-image ul { margin: 0; }
    .dt-sc-product_bundle-items .dt-sc-thumb-image > div { display: grid; grid-template-columns: 200px 1fr; width: 100%; align-items: center; text-align: left; gap: 30px; }
    .dt-sc-product_bundle-items .dt-sc-thumb-image > div:not(:last-child) { margin: 0 0 2rem; }
    .dt-sc-product_bundle-items .dt-sc-thumb-image > div:not(:last-child) > div:first-child::after { display: none; }
    .dt-sc-product_bundle-items .dt-sc-thumb-image a.item-image { margin-bottom: 0; }
    .dt-sc-product_bundle-items .dt-sc-thumb-image ul li .dt-sc-btn,
    .bundle-product-add-to-cart > button { padding: 10px 20px; font-size: 16px; }
    .dt-sc-product_bundle-items  .bundle-product-additional-offer { margin-top: 2rem; padding: 1.5rem 2rem; }
    .dt-sc-product_bundle-items .dt-sc-thumb-image { display: grid;  grid-template-columns: repeat(1,1fr);}
     .dt-sc-product_bundle-items {margin: 7rem 0 0;}
  }

  @media (max-width:576px) {
    .dt-sc-product_bundle-items { padding: 1.5rem; }
    .dt-sc-product_bundle-items .dt-sc-thumb-image > div { display: grid; grid-template-columns: 115px 1fr; gap: 15px; }
    .dt-sc-product_bundle-items .section-title h3 { font-size: 2.0rem; margin: 0 0 15px; }
	.bundle-product-additional-offer { padding: 1.5rem 1.5rem; }
    .dt-sc-product_bundle-items .dt-sc-thumb-image li:not(:last-child) { margin-bottom: 10px; }
    .dt-sc-product_bundle-items .dt-sc-thumb-image ul li .dt-sc-btn,
  .dt-sc-product_bundle-items   .bundle-product-add-to-cart > button { padding: 8px 16px; font-size: 14px; }  
    .dt-sc-product_bundle-items .dt-sc-thumb-image { display: grid;  grid-template-columns: repeat(1,1fr);}
  }
  @media (max-width:430px) {
  .dt-sc-product_bundle-items .dt-sc-thumb-image ul{    text-align: center;}
  .dt-sc-product_bundle-items .dt-sc-thumb-image > div{grid-template-columns: repeat(1,1fr);}
  }