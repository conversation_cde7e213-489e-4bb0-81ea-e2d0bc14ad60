
<div class="optional-sidebar facts medium-hide small-hide">
{% if section.settings.show_carousel %}  
<div class="widget product-sidebar-type-carousel">
  {% if collections[section.settings.carousel].products.size > 0 %} 
  {% if section.settings.carousel_title != blank %}
  <h5 class="sidebar_title">{{ section.settings.carousel_title }}</h5> 
  {% endif %}
  {% endif %}   
  <div class="swiper-container" id="swiper-sidebar-carousel">        
    <ul class="swiper-wrapper" data-id="{{ section.id }}">                 
      {% for product in collections[section.settings.carousel].products limit: section.settings.carousel_limit %}
      <li class="swiper-slide">
        {% render 'card-product',
                    card_product: product,
                    media_aspect_ratio: section.settings.image_ratio,
                    show_secondary_image: section.settings.show_secondary_image,
                    show_vendor: section.settings.show_vendor,
                    show_rating: section.settings.show_rating,
                    lazy_load: lazy_load,
                    show_quick_add: section.settings.enable_quick_add,
                    section_id: section.id
                  %}
      </li>
      {%- endfor -%}           
    </ul>               
    <div class="swiper-sidebar-arrows swiper-arrows">
      <div id="swiper-sidebar-next" class="swiper-button-next dt-sc-btn"><span>{% render 'product-icon-left-arrow' %}</span></div>
      <div id="swiper-sidebar-prev" class="swiper-button-prev dt-sc-btn"><span>{% render 'product-icon-right-arrow' %}</span></div>
    </div>                          
  </div>  
</div>
{% endif %}

{% if section.settings.show_image %}  
{% if section.settings.sidebar_image != blank %}
<div class="widget product-sidebar-type-image">
  {% if section.settings.sidebar_title != blank %}
  <h5 class="sidebar_title"> {{section.settings.sidebar_title}}</h5>
  {% endif %}
  <a href="{{section.settings.sidebar_link}}">    
    <img srcset="{%- if section.settings.sidebar_image.width >= 375 -%}{{ section.settings.sidebar_image | img_url: '375x' }} 375w,{%- endif -%}
                 {%- if section.settings.sidebar_image.width >= 750 -%}{{ section.settings.sidebar_image | img_url: '750x' }} 750w,{%- endif -%}
                 {%- if section.settings.sidebar_image.width >= 1100 -%}{{ section.settings.sidebar_image | img_url: '1100x' }} 1100w,{%- endif -%}
                 {%- if section.settings.sidebar_image.width >= 1500 -%}{{ section.settings.sidebar_image | img_url: '1500x' }} 1500w,{%- endif -%}
                 {%- if section.settings.sidebar_image.width >= 1780 -%}{{ section.settings.sidebar_image | img_url: '1780x' }} 1780w,{%- endif -%}
                 {%- if section.settings.sidebar_image.width >= 2000 -%}{{ section.settings.sidebar_image | img_url: '2000x' }} 2000w,{%- endif -%}
                 {%- if section.settings.sidebar_image.width >= 2800 -%}{{ section.settings.sidebar_image | img_url: '2800x' }} 2800w{%- endif -%}"
         sizes="{% if section.settings.sidebar_image_2 != blank and section.settings.stack_sidebar_images_on_mobile %}(min-width: 750px) 50vw, 100vw{% elsif section.settings.sidebar_image_2 != blank %}50vw{% else %}100vw{% endif %}"
         src="{{ section.settings.sidebar_image | img_url: '750x' }}"
         loading="lazy"
         alt="{{ section.settings.sidebar_image.alt | escape }}"
         width="{{ section.settings.sidebar_image.width }}"
         height="{{ section.settings.sidebar_image.width | divided_by: section.settings.sidebar_image.aspect_ratio }}"
         {% if section.settings.sidebar_image_2 != blank %}class="banner__media-sidebar_image-half"{% endif %}
         >

  </a>  

  {% if section.settings.sidebar_link != blank  and  section.settings.sidebar_button != blank %}
  <a href="{{section.settings.sidebar_link}}" class="button">      
    {{section.settings.sidebar_button}}            
  </a>    
  {% endif %}    
</div>  
{% endif %}
{% endif %}

{% if section.settings.show_collection %}  
<div class="widget product-sidebar-type-collection">
  {% if collections[section.settings.collection].products.size > 0 %} 
  {% if section.settings.collection_title != blank %}
  <h5 class="sidebar_title">{{ section.settings.collection_title }}</h5>  
  {% endif %}
  {% endif %}
  <div class="dT_VProdWrapperOther">  
    <ul class="product-list-style" data-id="{{ section.id }}">                 
      {% for product in collections[section.settings.collection].products limit: section.settings.collection_limit %}         
      <li class="item">
       {% render 'card-product',
                    card_product: product,
                    media_aspect_ratio: section.settings.image_ratio,
                    show_secondary_image: section.settings.show_secondary_image,
                    show_vendor: section.settings.show_vendor,
                    show_rating: section.settings.show_rating,
                    lazy_load: lazy_load,
                    show_quick_add: section.settings.enable_quick_add,
                    section_id: section.id

                  %}
      </li>
      {%- endfor -%}
    </ul>                   
  </div>
</div> 
{% endif %}

</div>
<style>
  .widget.product-sidebar-type-collection .card-wrapper .card-information.review{justify-content:flex-start;}
</style>
