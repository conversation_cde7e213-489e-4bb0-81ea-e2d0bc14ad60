<link rel="stylesheet" href="{{ 'component-card.css' | asset_url }}" media="print" onload="this.media='all'">
<link rel="stylesheet" href="{{ 'component-price.css' | asset_url }}" media="print" onload="this.media='all'">
<link
  rel="stylesheet"
  href="{{ 'section-product-recommendations.css' | asset_url }}"
  media="print"
  onload="this.media='all'"
>

<noscript>{{ 'component-card.css' | asset_url | stylesheet_tag }}</noscript>
<noscript>{{ 'component-price.css' | asset_url | stylesheet_tag }}</noscript>
<noscript>{{ 'section-product-recommendations.css' | asset_url | stylesheet_tag }}</noscript>
<link rel="stylesheet" href="{{ 'quick-add.css' | asset_url }}" media="print" onload="this.media='all'">
<script src="{{ 'quick-add.js' | asset_url }}" defer="defer"></script>
<script src="{{ 'product-form.js' | asset_url }}" defer="defer"></script>
{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;

  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}

<div class="color-{{ section.settings.color_scheme }} gradient ">
  <product-recommendations
    class="product-recommendations product-recommendations-component"
    data-url="{{ routes.product_recommendations_url }}?section_id={{ section.id }}&product_id={{ product.id }}&limit={{ section.settings.products_to_show }}"
  >
    <div class="{% if section.settings.page_full_width %} page-full-width {% else %} page-width {% endif %} {% if section.settings.page_full_width_spacing %} page-full-width_spacing {% endif %} section-{{ section.id }}-padding isolate">
      <div class="row">
        {% if recommendations.performed and recommendations.products_count > 0 %}
          <h2 class="product-recommendations__heading {{ section.settings.heading_size }}">
            {{ section.settings.heading | escape }}
          </h2>
          <recommendation-slider>
            <div data-slider-options='{"loop": "{%- if section.settings.centered_slide -%}1{%- else -%}2{%- endif -%}","desktop": "{{ section.settings.desktop_column }}", "laptop": "{{ section.settings.laptop_column }}", "tablet": "{{ section.settings.tablet_column }}","mobile": "{{ section.settings.mobile_column }}","auto_play": "{{ section.settings.auto_play }}"}'>
              <div class="swiper" data-swiper-slider>
                <div class="swiper-wrapper card_style-{{ settings.card_style }}">
                  {% for recommendation in recommendations.products %}
                    {% assign indexCheck = forloop.index %}
                    <div class="swiper-slide">
                      {% case settings.card_style %}
                        {% when 'standard' %}
                        {%- assign placeholder_image_index = forloop.index0 | modulo: 4 | plus: 1 -%}
                        {%- assign placeholder_image = 'product-apparel-' | append: placeholder_image_index -%}
                          {% render 'card-product',
                            card_product: recommendation,
                            media_aspect_ratio: section.settings.image_ratio,
                            show_secondary_image: section.settings.show_secondary_image,
                            show_vendor: section.settings.show_vendor,
                            show_rating: section.settings.show_rating,
                            show_quick_add: settings.enable_quickadd,
                            show_quick_view: settings.enable_quickview,
                            show_new_tag: section.settings.show_new_tag,
                            section_id: section.id,
                            placeholder_image: placeholder_image,
                            from_page: 'from-recommended',
                            loop: indexCheck
                          %}
                        {% when 'button_width_icons' %}
                          {% render 'card-product-2',
                            card_product: recommendation,
                            media_aspect_ratio: section.settings.image_ratio,
                            show_secondary_image: section.settings.show_secondary_image,
                            show_vendor: section.settings.show_vendor,
                            show_rating: section.settings.show_rating,
                            show_quick_add: settings.enable_quickadd,
                            show_quick_view: settings.enable_quickview,
                            show_new_tag: section.settings.show_new_tag,
                            section_id: section.id,
                            from_page: 'from-recommended',
                            loop: indexCheck
                          %}
                        {% when 'card_with_icons' %}
                          {% render 'card-product-3',
                            card_product: recommendation,
                            media_aspect_ratio: section.settings.image_ratio,
                            show_secondary_image: section.settings.show_secondary_image,
                            show_vendor: section.settings.show_vendor,
                            show_rating: section.settings.show_rating,
                            show_quick_add: settings.enable_quickadd,
                            show_quick_view: settings.enable_quickview,
                            show_new_tag: section.settings.show_new_tag,
                            section_id: section.id,
                            from_page: 'from-recommended',
                            loop: indexCheck
                          %}
                        {% when 'card_with_buttons' %}
                          {% render 'card-product-4',
                            card_product: recommendation,
                            media_aspect_ratio: section.settings.image_ratio,
                            show_secondary_image: section.settings.show_secondary_image,
                            show_vendor: section.settings.show_vendor,
                            show_rating: section.settings.show_rating,
                            show_quick_add: settings.enable_quickadd,
                            show_quick_view: settings.enable_quickview,
                            show_new_tag: section.settings.show_new_tag,
                            section_id: section.id,
                            from_page: 'from-recommended',
                            loop: indexCheck
                          %}
                        {% when 'card_with_overlay' %}
                          {% render 'card-product-5',
                            card_product: recommendation,
                            media_aspect_ratio: section.settings.image_ratio,
                            show_secondary_image: section.settings.show_secondary_image,
                            show_vendor: section.settings.show_vendor,
                            show_rating: section.settings.show_rating,
                            show_quick_add: settings.enable_quickadd,
                            show_quick_view: settings.enable_quickview,
                            show_new_tag: section.settings.show_new_tag,
                            section_id: section.id,
                            from_page: 'from-recommended',
                            loop: indexCheck
                          %}
                      {% endcase %}
                    </div>
                  {% endfor %}
                </div>
                {% if section.settings.swiper_navigation != blank %}
                  <div class="swiper-button-next"><span></span></div>
                  <div class="swiper-button-prev"><span></span></div>
                {% endif %}
              </div>
              {% if section.settings.swiper_pagination != blank %}
                <div class="swiper-pagination"></div>
              {% endif %}
            </div>
          </recommendation-slider>
        {% endif %}
      </div>
    </div>
  </product-recommendations>
</div>
{% javascript %}
  class ProductRecommendations extends HTMLElement {
    constructor() {
      super();

      const handleIntersection = (entries, observer) => {
        if (!entries[0].isIntersecting) return;
        observer.unobserve(this);

        fetch(this.dataset.url)
          .then(response => response.text())
          .then(text => {
            const html = document.createElement('div');
            console.log(text);
            html.innerHTML = text;
            const recommendations = html.querySelector('product-recommendations');

            if (recommendations && recommendations.innerHTML.trim().length) {
              this.innerHTML = recommendations.innerHTML;
              $('.loading-image').removeClass('loading-image').addClass('loaded-image');
              let sameCard = document.querySelector('.from-recommended');
              let cardHeight = parseInt(sameCard.clientHeight);
              let topValue = cardHeight / 2 + 5;
              let cardTopCSS = "calc(50% - "+topValue+'px)';
              let swiperContainer = document.querySelector('.product-recommendations .swiper');
              let swiperPrevElement = swiperContainer.querySelector('.swiper-button-prev');
              let swiperNextElement = swiperContainer.querySelector('.swiper-button-next');
              swiperPrevElement.setAttribute('style', 'top:'+cardTopCSS);
              swiperNextElement.setAttribute('style', 'top:'+cardTopCSS);
            }

            if (html.querySelector('.grid__item')) {
              this.classList.add('product-recommendations--loaded');
            }
          })
          .catch(e => {
            console.error(e);
          });
      }

      new IntersectionObserver(handleIntersection.bind(this), {rootMargin: '0px 0px 200px 0px'}).observe(this);
    }
  }

  customElements.define('product-recommendations', ProductRecommendations);
{% endjavascript %}

{% schema %}
{
  "name": "t:sections.product-recommendations.name",
  "tag": "section",
  "class": "section section-recommendation",
  "settings": [
    {
      "type":"checkbox",
      "id":"page_full_width",
       "default": false,
      "label": "t:sections.all.page_full_width.label"
    },
    {
      "type":"checkbox",
      "id":"page_full_width_spacing",
       "default": false,
      "label": "t:sections.all.page_full_width_spacing.label"
    },
    {
      "type": "paragraph",
      "content": "t:sections.product-recommendations.settings.paragraph__1.content"
    },
    {
      "type": "text",
      "id": "heading",
      "default": "You may also like",
      "label": "t:sections.product-recommendations.settings.heading.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__3.label"
        }
      ],
      "default": "h1",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "range",
      "id": "products_to_show",
      "min": 2,
      "max": 10,
      "step": 1,
      "default": 4,
      "label": "t:sections.product-recommendations.settings.products_to_show.label"
    },
     {
      "type": "header",
      "content": "t:sections.all.swiper.swiper_slider_title"
    },

    {
      "type": "range",
      "id": "desktop_column",
      "min": 1,
      "max": 10,
      "step": 1,
      "label": "t:sections.all.swiper.desktop_column",
      "default": 4
    },
    {
      "type": "range",
      "id": "laptop_column",
      "min": 1,
      "max": 10,
      "step": 1,
      "label": "t:sections.all.swiper.laptop_column",
      "default": 4
    },
    {
      "type": "range",
      "id": "tablet_column",
      "min": 1,
      "max": 5,
      "step": 1,
      "label": "t:sections.all.swiper.tablet_column",
      "default": 3
    },
    {
      "type": "range",
      "id": "mobile_column",
      "min": 1,
      "max": 3,
      "step": 1,
      "label": "t:sections.all.swiper.mobile_column",
      "default": 1
    },
    {
      "type": "checkbox",
      "id": "centered_slide",
      "default": false,
      "label": "t:sections.all.swiper.centered_slide"
    },
    {
      "type": "checkbox",
      "id": "swiper_pagination",
      "default": false,
      "label": "t:sections.all.swiper.swiper_pagination"
    },
    {
      "type": "checkbox",
      "id": "swiper_navigation",
      "default": false,
      "label": "t:sections.all.swiper.swiper_navigation"
    },
    {
      "type": "range",
      "id": "auto_play",
      "min": 0,
      "max": 5,
      "step": 1,
      "label": "t:sections.all.swiper.auto_play",
      "default": 0
    },
    {
      "type": "checkbox",
      "id": "swipe_on_mobile",
      "default": false,
      "label": "t:sections.featured-collection.settings.swipe_on_mobile.label"
    },
    {
      "type": "checkbox",
      "id": "arrow_on_mobile",
      "default": false,
      "label": "t:sections.all.swiper.controls"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "t:sections.all.colors.accent_1.label"
        },
        {
          "value": "accent-2",
          "label": "t:sections.all.colors.accent_2.label"
        },
        {
          "value": "background-1",
          "label": "t:sections.all.colors.background_1.label"
        },
        {
          "value": "background-2",
          "label": "t:sections.all.colors.background_2.label"
        },
        {
          "value": "inverse",
          "label": "t:sections.all.colors.inverse.label"
        }
      ],
      "default": "background-1",
      "label": "t:sections.all.colors.label",
      "info": "t:sections.all.colors.has_cards_info"
    },
    {
      "type": "header",
      "content": "t:sections.product-recommendations.settings.header__2.content"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.product-recommendations.settings.image_ratio.options__1.label"
        },
        {
          "value": "portrait",
          "label": "t:sections.product-recommendations.settings.image_ratio.options__2.label"
        },
        {
          "value": "square",
          "label": "t:sections.product-recommendations.settings.image_ratio.options__3.label"
        }
      ],
      "default": "adapt",
      "label": "t:sections.product-recommendations.settings.image_ratio.label"
    },
    {
      "type": "checkbox",
      "id": "show_secondary_image",
      "default": false,
      "label": "t:sections.product-recommendations.settings.show_secondary_image.label"
    },
    {
      "type": "checkbox",
      "id": "show_vendor",
      "default": false,
      "label": "t:sections.product-recommendations.settings.show_vendor.label"
    },
    {
      "type": "checkbox",
      "id": "show_rating",
      "default": false,
      "label": "t:sections.product-recommendations.settings.show_rating.label",
      "info": "t:sections.product-recommendations.settings.show_rating.info"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 35
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 35
    }
  ]
}
{% endschema %}
