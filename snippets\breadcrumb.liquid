{% if settings.use_breadcrumb %}
 {%  style %}
 {%  unless settings.breadcrumb_image == blank  %}
 .breadcrumb {background:url({{ settings.breadcrumb_image | image_url: width: 1920 }});background-repeat:no-repeat;background-size:cover;}  
     {% endunless %}
 .breadcrumb a{color: rgba(var(--color-foreground),1);}  
 .breadcrumb a:hover{ color:rgb(var(--color-base-outline-button-labels));}
 .breadcrumb{padding-top:{{settings.breadcrumb_padding_top}}px;padding-bottom:{{settings.breadcrumb_padding_bottom}}px;margin-bottom:{{settings.breadcrumb_margin_bottom}}px;position: relative;z-index: 1;}  
 .breadcrumb .breadcrumb_title{margin:0; font-weight: 500;font-size: 4rem;text-transform: capitalize;}
 .breadcrumb a, .breadcrumb span{display: inline-block;margin-top:1rem;font-size:1.8rem;font-weight:400; padding: 0 0.4rem;text-transform: capitalize;} 
 .breadcrumb.text-center{text-align:center;}  
 .breadcrumb.text-start{text-align:left;}  
 .breadcrumb.text-end{text-align:right;}
 .breadcrumb:before { position: absolute; content: "";  display: block;  width: 100%;  height: 100%;  left: 0;  top: 0;  z-index: -1;background:rgba(var(--color-base-background-1));opacity:.{{settings.image_overlay_opacity}};}  
/*  span.breadcrumb__sep:after {
    content: '\f105';
    color: var(--color-icon);
    font-family: 'FontAwesome';
} */
   span.breadcrumb__sep svg {
    width: 1rem;
    height: 1rem;
    fill: rgba(var(--color-base-accent-1),0.6);
}
 @media screen and (max-width: 990px) {  
 .breadcrumb{padding-top:calc( {{settings.breadcrumb_padding_top}}px / 2 );padding-bottom:calc( {{settings.breadcrumb_padding_bottom}}px / 2 );margin-bottom:calc( {{settings.breadcrumb_margin_bottom}}px / 2 );}   
 }  
  @media screen and (max-width: 480px) { 
    .breadcrumb .breadcrumb_title{font-size: 2.6rem;}
    .breadcrumb a, .breadcrumb span{font-size:1.4rem;}
 }
{% endstyle %}
{% if request.page_type != 'index' and request.page_type != 'page' and request.page_type != '404'  %}
<nav class="breadcrumb text-{{ settings.breadcrumb_style }}"  aria-label="breadcrumbs {{ request.page_type }}">
  <div class="page-width">
    <div class="row"> 
    {% if template contains 'product' %}  
      {% if settings.use_breadcrumb_title %}
    <h2 class="breadcrumb_title ">{{ product.title }}</h2>
        {% endif %}
    {% if collection %}
    <a href="/" title="{{ 'general.breadcrumbs.home_link_title' | t }}">{{ 'general.breadcrumbs.home' | t }}</a> 
    <span aria-hidden="true" class="breadcrumb__sep"> 
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M 7.75 1.34375 L 6.25 2.65625 L 14.65625 12 L 6.25 21.34375 L 7.75 22.65625 L 16.75 12.65625 L 17.34375 12 L 16.75 11.34375 Z"></path></svg>
    </span>
    {% if collection.handle %}
    {% capture url %}/collections/{{ collection.handle }}{% endcapture %}
    {{ collection.title | link_to: url }}  
    {% endif %}
    {% else %}
    {% capture url %}/collections/all{% endcapture %}
    <a href="{{ url }}">{{ 'general.breadcrumbs.all' | t }}</a>
    {% endif %}

    <span aria-hidden="true" class="breadcrumb__sep">
   <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M 7.75 1.34375 L 6.25 2.65625 L 14.65625 12 L 6.25 21.34375 L 7.75 22.65625 L 16.75 12.65625 L 17.34375 12 L 16.75 11.34375 Z"></path></svg>
    </span>
    <span>{{ product.title }}</span>


    {% elsif template contains 'collection' and collection.handle %}
    {% if template == 'list-collections' %}
     {% if settings.use_breadcrumb_title %}  
    <h1 class="breadcrumb_title">{{ 'general.breadcrumbs.all_collections' | t }}</h1>
      {% endif %}  
    {% else %}
    {% if settings.use_breadcrumb_title %}     
    <h1 class="breadcrumb_title">{{ collection.title }}</h1>
    {% endif %}     
    <a href="/" title="{{ 'general.breadcrumbs.home_link_title' | t }}">{{ 'general.breadcrumbs.home' | t }}</a>
  <span aria-hidden="true" class="breadcrumb__sep">
   <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M 7.75 1.34375 L 6.25 2.65625 L 14.65625 12 L 6.25 21.34375 L 7.75 22.65625 L 16.75 12.65625 L 17.34375 12 L 16.75 11.34375 Z"></path></svg>
    </span>
       <a href="/collections" title="{{ 'general.breadcrumbs.home_link_title' | t }}">{{ 'general.breadcrumbs.all_collections' | t }}</a>
    <span aria-hidden="true" class="breadcrumb__sep">
   <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M 7.75 1.34375 L 6.25 2.65625 L 14.65625 12 L 6.25 21.34375 L 7.75 22.65625 L 16.75 12.65625 L 17.34375 12 L 16.75 11.34375 Z"></path></svg>
    </span>
    {% if current_tags %}
    {% capture url %}/collections/{{ collection.handle }}{% endcapture %}
    {{ collection.title | link_to: url }}
    <span aria-hidden="true" class="breadcrumb__sep">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M 7.75 1.34375 L 6.25 2.65625 L 14.65625 12 L 6.25 21.34375 L 7.75 22.65625 L 16.75 12.65625 L 17.34375 12 L 16.75 11.34375 Z"></path></svg>
    </span>
    <span>{{ current_tags | join: " + " }}</span>
    {% else %}
    <span>{{ collection.title }}</span>

    {% endif %}  
    {% endif %}
    {% elsif template == 'blog' %}
    {% if settings.use_breadcrumb_title %}  
    <h1 class="breadcrumb_title">{{ blog.title }}</h1>
    {% endif %}
    <a href="/" title="{{ 'general.breadcrumbs.home_link_title' | t }}">{{ 'general.breadcrumbs.home' | t }}</a>

    <span aria-hidden="true" class="breadcrumb__sep">
   <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M 7.75 1.34375 L 6.25 2.65625 L 14.65625 12 L 6.25 21.34375 L 7.75 22.65625 L 16.75 12.65625 L 17.34375 12 L 16.75 11.34375 Z"></path></svg>
    </span>
    {% if current_tags %}
    {{ blog.title | link_to: blog.url }}
    <span aria-hidden="true" class="breadcrumb__sep">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M 7.75 1.34375 L 6.25 2.65625 L 14.65625 12 L 6.25 21.34375 L 7.75 22.65625 L 16.75 12.65625 L 17.34375 12 L 16.75 11.34375 Z"></path></svg>
    </span>
    <span>{{ current_tags | join: " + " }}</span>
    {% else %}

    <span>{{ blog.title }}</span>

    {% endif %}

    {% elsif template == 'article' %}
     {% if settings.use_breadcrumb_title %}   
    <h2 class="breadcrumb_title">{{ article.title }}</h2>
     {% endif %} 
    <a href="/" title="{{ 'general.breadcrumbs.home_link_title' | t }}">{{ 'general.breadcrumbs.home' | t }}</a>

    <span aria-hidden="true" class="breadcrumb__sep">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M 7.75 1.34375 L 6.25 2.65625 L 14.65625 12 L 6.25 21.34375 L 7.75 22.65625 L 16.75 12.65625 L 17.34375 12 L 16.75 11.34375 Z"></path></svg>
    </span>
    {{ blog.title | link_to: blog.url }}
    <span aria-hidden="true" class="breadcrumb__sep">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M 7.75 1.34375 L 6.25 2.65625 L 14.65625 12 L 6.25 21.34375 L 7.75 22.65625 L 16.75 12.65625 L 17.34375 12 L 16.75 11.34375 Z"></path></svg>
    </span>
    <span>{{ article.title }}</span>

    {% elsif template contains 'page' %}
    {% if settings.use_breadcrumb_title %}  
    <h1 class="breadcrumb_title">{{ page.title }}</h1>
     {% endif %}   
    <a href="/" title="{{ 'general.breadcrumbs.home_link_title' | t }}">{{ 'general.breadcrumbs.home' | t }}</a>

    <span aria-hidden="true" class="breadcrumb__sep">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M 7.75 1.34375 L 6.25 2.65625 L 14.65625 12 L 6.25 21.34375 L 7.75 22.65625 L 16.75 12.65625 L 17.34375 12 L 16.75 11.34375 Z"></path></svg>
    </span>
    <span>{{ page.title }}</span>

    {% else %}
    {% if template == 'list-collections' %}
    {% if settings.use_breadcrumb_title %}    
    <h1 class="breadcrumb_title">{{ 'general.breadcrumbs.all_collections' | t }}</h1>
    {% endif %}  
    {% else %}
      
    {% if settings.use_breadcrumb_title %}      
    <h1 class="breadcrumb_title">{{ page_title }}</h1>
    {% endif %}    
    <a href="/" title="{{ 'general.breadcrumbs.home_link_title' | t }}">{{ 'general.breadcrumbs.home' | t }}</a>

    <span aria-hidden="true" class="breadcrumb__sep">
   <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M 7.75 1.34375 L 6.25 2.65625 L 14.65625 12 L 6.25 21.34375 L 7.75 22.65625 L 16.75 12.65625 L 17.34375 12 L 16.75 11.34375 Z"></path></svg>
    </span>
    <span>{{ page_title }}</span>
    {% endif %}

    {% endif %}
  </div> 
  </div>
</nav>
{% endif %}
{% endif %}



