.pagination-wrapper {
  margin-top: 4rem;
}

@media screen and (min-width: 990px) {
  .pagination-wrapper {
    margin-top: 5rem;
  }
}

.pagination__list {
  display: flex;
  flex-wrap: nowrap;
  justify-content: center;
}

.pagination__list > li {
  flex: 1 0 4rem;
  max-width: 4rem;
}

.pagination__list > li:not(:last-child) {
  margin-right: 0;
}
ul.pagination__list.list-unstyled li {
    background: transparent;
    margin:0 3px;
  transform:scale(0.95);
  transition:all 0.3s linear;
}
.pagination__item {
  color: var(--color-base-accent-1);
  display: inline-flex;
  justify-content: center;
  align-items: center;
  position: relative;
  height: 4rem;
  width: 100%;
  padding: 0;
  text-decoration: none;
  font-size: 1.4rem;
  font-weight: 500;
  border-radius:50%;
  line-height: normal;
}
.pagination__item svg{fill:transparent;}
a.pagination__item:hover::after {
  height: 0.1rem;
}

.pagination__item .icon-caret {
  height: 1rem;
  width:1rem;
}

.pagination__item--current::after {
  height: 0.1rem;
}

.pagination__item--current::after,
.pagination__item:hover::after {
  content: '';
  display: block;
  width: 2rem;
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
  background-color: currentColor;
}

.pagination__item--next .icon {
  margin-left: -0.2rem;
  transform: rotate(1deg);
}

.pagination__item--next:hover .icon {
  transform: rotate(1deg) scale(1.07);
}

.pagination__item--prev .icon {
  margin-right: -0.2rem;
  transform: rotate(180deg);
}

.pagination__item--prev:hover .icon {
  transform: rotate(180deg) scale(1.07);
}

.pagination__item-arrow:hover::after {
  display: none;
}
.pagination__item--current:after, .pagination__item:hover:after{display:none;}
ul.pagination__list.list-unstyled li .pagination__item--current{background: var(--gradient-base-accent-2);color: var(--gradient-background);}
ul.pagination__list.list-unstyled li .pagination__item--current,
ul.pagination__list.list-unstyled li .pagination__item:hover{transform: scale(1.1);}
ul.pagination__list.list-unstyled li{border-radius:50%;}



@media screen and (max-width: 989px){
  .pagination-wrapper{
    margin-top: 0;
  }
}