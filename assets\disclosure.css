.disclosure {
  position: relative;
}

.disclosure__button {
  align-items: center;
  cursor: pointer;
  display: flex;
  height: 4rem;
  padding: 0 1.5rem 0 1.5rem;
  font-size: 1.3rem;
  background-color: transparent;
}

.disclosure__list-wrapper {
  border-width: var(--popup-border-width);
  border-style: solid;
  border-color: rgba(var(--color-foreground), var(--popup-border-opacity));
  overflow: hidden;
  position: absolute;
  bottom: 100%;
  transform: translateY(0rem);
  z-index: 2;
  background-color: var(--gradient-background);
  border-radius: var(--popup-corner-radius);
  box-shadow: var(--popup-shadow-horizontal-offset) var(--popup-shadow-vertical-offset) var(--popup-shadow-blur-radius) rgba(var(--color-shadow), var(--popup-shadow-opacity));
}
.localization-selector+.disclosure__list-wrapper{margin-left:0px;}
.disclosure__list {
  position: relative;
  overflow-y: auto;
  font-size: 1.4rem;
  padding-bottom: 0.5rem;
  padding-top: 0.5rem;
  scroll-padding: 0.5rem 0;
  min-height: 8.2rem;
  max-height: 19rem;
  max-width: 12rem;
  min-width: 6rem;
  width: max-content;
}
.localization-selector.link{    padding-right: 3rem;}
.disclosure__item {
  position: relative;
}
.disclosure .localization-form__select {
    padding-top: 1.5rem;
    font-size: 1.6rem;
    font-weight: 400;
    font-family: var(--font-body-family);
    background:none;
    padding:0;
}
.localization-form__select .icon-caret{right: calc(var(--inputs-border-width) + 0.2rem); width:0.8rem;}
.disclosure__link {
  display: block;
  padding: 0.5rem 2.2rem;
  text-decoration: none;
  line-height: calc(1 + 0.8 / var(--font-body-scale));
  font-size:1.3rem;
}
li.disclosure__item a:hover{
  transform:scale(1);
  color: rgb(var(--color-base-outline-button-labels));
}