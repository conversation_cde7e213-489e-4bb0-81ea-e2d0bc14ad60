table{
    border-collapse: collapse;
    border-color: inherit;
    text-indent: 0;
    border-spacing: 0;
    clear: both;
    margin: 0 0 1rem;
    width: 100%;
}
pre, table td, table th {
    border: 1px solid var(--gradient-base-background-2);
}
table td, table th {
    padding: 10px;
    text-align: center;
    word-break: break-word;
}
/* dtx-wishlist-grid table{
    border: 1px solid var(--gradient-base-background-2);
} */
dtx-wishlist-grid table thead>tr {
    background-color: var(--gradient-base-background-2);
    border: 1px solid var(--gradient-base-background-2);
  display:none;
}
dtx-wishlist-grid table
thead>tr th {
    border-top: medium none;
    text-align: center;
    text-transform: uppercase;
    vertical-align: middle;
    white-space: nowrap;
    font-weight: 700;
    color:var(--gradient-base-accent-1);
}
.dtx-grid-empty.dtx-grid-show h2 {
    margin-top: 0;
}
/* dtx-wishlist-grid table tbody>tr td.product-thumbnail {
    width: 150px;
} */
dtx-wishlist-grid table tbody>tr td {
    border: none; margin: 3px;
}
dtx-wishlist-grid table tbody>tr td a:focus-visible {
    box-shadow: none;
     outline: none;
     outline-offset: 0;
}
dtx-wishlist-grid table tbody>tr td.product-thumbnail img {
    border-radius:Calc(var(--buttons-radius) - 30px); width:100%;  height: 400px; 
}
dtx-wishlist-grid a.remove-item.product-cart svg {
    width: 26px;
    height: 26px;
}
dtx-wishlist-grid table tbody>tr td{

}
dtx-wishlist-grid table tbody>tr td a {
    color: var(--gradient-base-accent-1);
    font-weight:400;
    display:flex;
    justify-content:center;
    font-family: var(--font-heading-family);
    font-style: var(--font-heading-style);
  line-height: calc(1 + 0.3 / max(1, var(--font-heading-scale)));
  /* display:inline-flex; */
}
dtx-wishlist-grid table tbody>tr td a.remove-item.product-cart:hover {
    color: rgb(var(--color-base-outline-button-labels));
}

@media only screen and (max-width: 991px){
dtx-wishlist-grid table thead {
    display: none;
}
}
@media only screen and (max-width: 767px){
dtx-wishlist-grid table thead {
    display: none;
}
dtx-wishlist-grid table tbody>tr {
    width: 100%;
    padding-bottom: 10px;
    display: block;
}  
dtx-wishlist-grid table tbody>tr td, dtx-wishlist-grid table tbody>tr td.product-thumbnail {
    width: 100%;
    display: block;
} 
}
/* @media only screen and (max-width: 1199px){
   .page-width.wishlist, .page-width.compare{
    padding-top: 0rem;
    padding-bottom: 2rem;
   } 
} */
table.dtx-table.dtx-grid-hide {
    display: none;
}
.dtx-grid-empty.dtx-grid-hide {
    display: none;
}

.dtx-grid-empty.dtx-grid-show img{ margin-bottom:30px;display:none;}

/* @media only screen and (max-width: 991px) and (min-width: 768px){
  dtx-wishlist-grid table tbody>tr{    
    padding: 30px 30px 0px 270px;
    position: relative;
    display: block;
  }
  dtx-wishlist-grid table tbody>tr:last-child{ padding-bottom: 30px;}
  dtx-wishlist-grid table tbody > tr td.product-thumbnail {
    position: absolute;
    left: 0;
    top: 0;
    width: 250px;
    padding:3rem 0px 2rem 3rem;
}
  dtx-wishlist-grid table tbody > tr td {
    padding: 1.5rem;
}  
 dtx-wishlist-grid table tbody>tr td:last-child{padding-bottom:0;}
 dtx-wishlist-grid table tbody>tr td.product-thumbnail img {
    border-radius: var(--DTRadius);
    overflow: hidden;
    aspect-ratio: 1/1;
    object-fit: cover;
}

dtx-wishlist-grid  table tbody>tr td, dtx-wishlist-grid  table tbody>tr td.product-thumbnail {
    display: block;
}
  dtx-wishlist-grid table tbody > tr td {
    text-align: left;
    line-height: normal;
}
} */
dtx-wishlist-grid table tbody>tr td a:hover {
    color: rgb(var(--color-base-outline-button-labels));
}
dtx-remove-wish-item{    display: flex; align-items: center; justify-content: center;  }
/* @media only screen and (max-width: 991px) and (min-width: 768px){
dtx-remove-wish-item {  justify-content: left;}
} */

.wishlist tbody { overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  display: grid;
  grid-auto-flow: column;
  grid-auto-columns: 20.20%;
  min-height: 100%;
  width: 100%; gap: 0;}
.wishlist .dtx-table tbody tr {  display: flex;  flex-direction: column;}
.wishlist .dtx-table tbody tr{ padding: 0 15px 0 15px; }
.wishlist  table td, .wishlist  table th{padding:10px;  border: 1px solid rgba(var(--color-base-accent-1),.1);}
.wishlist .remove-item.product-cart { display: flex; justify-content: center; align-items: center;}
.wishlist  a.remove-item.product-cart span { font-size: 1.6rem; color: var(--gradient-base-accent-1); margin-right: 6px; font-weight: 400; transition: all .3s linear;line-height:normal;}
.wishlist .remove-item.product-cart svg.icon.icon-remove { width: 1.6rem; height: 1.6rem; color: var(--gradient-base-accent-1); transition: all .3s linear;}
.wishlist .remove-item.product-cart:hover span, .wishlist .remove-item.product-cart:hover svg.icon.icon-remove {  color: rgb(var(--color-base-outline-button-labels));  fill: rgb(var(--color-base-outline-button-labels));}
.wishlist .dtx-table tbody{
  padding-bottom: 3%;
}
@media only screen and (max-width: 1439px) {
  .wishlist .dtx-table tbody {
    grid-auto-columns: 25%;
  }
}
@media only screen and (max-width: 990px) {
  .wishlist .dtx-table tbody {
    grid-auto-columns: 33.33%;
    
  }
}
@media only screen and (max-width: 750px) {
  .wishlist .dtx-table tbody {
    grid-auto-columns: 50%;
  }
}
@media only screen and (max-width: 480px) {
  .wishlist .dtx-table tbody {
    grid-auto-columns: 100%;
  }
}
table {
  display: flex;
  justify-content: center;
}
/* @media only screen and (max-width: 749px) {.wishlist tbody{gap:15px;}} */
/* @media only screen and (max-width: 576px) {
  .wishlist table td, .wishlist table th {
    padding: 5px 15px;
}
} */
/* firefox */
//firefox


tbody.slick-wrapper:-webkit-scrollbar {
  width: 20px;
}
tbody.slick-wrapper:-webkit-scrollbar-track {
    background-color: rgba(0, 0, 0, 0.5);
}
tbody.slick-wrapper:-webkit-scrollbar-thumb {
  background-color: red;
  border-radius: 6px;
  border: 3px solid transparent;
}

/* webkit browsers */
tbody.slick-wrapper::-webkit-scrollbar {
  height: 5px;
}

tbody.slick-wrapper::-webkit-scrollbar-track {
   background-color: rgba(0, 0, 0, 0.2);
}

tbody.slick-wrapper::-webkit-scrollbar-thumb {
  height: 5px;
  background-color: rgba(0, 0, 0, 0.5);
}

tbody.slick-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.7);
}

tbody.slick-wrapper::-webkit-scrollbar:vertical {
  display: none;
}
.wishlist .product-wishlist-cart .dt-sc-btn.button.product-cart{padding: 0;min-width: 100%;}