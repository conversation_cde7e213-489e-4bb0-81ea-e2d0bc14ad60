{% if linklists.gift-wrapping.links.size > 0 and linklists.gift-wrapping.links.first.type == 'product_link' %} 
<div class="cart-drawer-detail">
<div
  id="is-a-gift"
  style="clear: left;"
  class="clearfix rte drawer-details"
>
  <p>
    <input
      id="gift-wrapping"
      type="checkbox"
      name="attributes[gift-wrapping]"
      value="yes"
      {% if cart.attributes.gift-wrapping %}
      checked="checked"
      {% endif %}
      style="float: none"
    />
    <label
      for="gift-wrapping"
      style="display:inline; padding-left: 0px; float: none;"
    >
       <button type="button" class="close  link" onclick="this.closest('details').querySelector('summary').click()">
  {%  render 'icon-close' %}
   </button>
      Add this gift wrap worth of  {{ linklists.gift-wrapping.links.first.object.price | money }}      
    </label>
  </p>  
</div>
</div>
{% assign id = linklists.gift-wrapping.links.first.object.variants.first.id
%} {% assign gift_wraps_in_cart = 0 %} {% for item in cart.items %} {% if
item.id == id %} {% assign gift_wraps_in_cart = item.quantity %} {% endif %}
{% endfor %}

<style>
  #updates_{{ id }} { display: none; }
</style>

<script>

  Shopify.Cart = Shopify.Cart || {};

  Shopify.Cart.GiftWrap = {};

  Shopify.Cart.GiftWrap.set = function() {
    var headers = new Headers({ 'Content-Type': 'application/json' });

    var request = {
      method: 'POST',
      headers: headers,
      body: JSON.stringify({ updates: { {{ id }}: 1 }, attributes: { 'gift-wrapping': true } })
    };
    fetch('/cart/update.js', request)
    .then(function() {
      location.href = '/cart';
    });
  }

  Shopify.Cart.GiftWrap.remove = function() {
    var headers = new Headers({ 'Content-Type': 'application/json' });

    var request = {
      method: 'POST',
      headers: headers,
      body: JSON.stringify({ updates: { {{ id }}: 0 }, attributes: { 'gift-wrapping': '', 'gift-note': '' } })
    };
    fetch('/cart/update.js', request)
    .then(function() {
      location.href = '/cart';
    });
  }

  // If we have nothing but gift-wrap items in the cart.
  {% if cart.items.size == 1 and gift_wraps_in_cart > 0 %}
  document.addEventListener("DOMContentLoaded", function(){
    Shopify.Cart.GiftWrap.remove();
  });
  // If we have more than one gift-wrap item in the cart.
  {% elsif gift_wraps_in_cart > 1 %}
  document.addEventListener("DOMContentLoaded", function(){
    Shopify.Cart.GiftWrap.set();
  });
  // If we have a gift-wrap item in the cart but our gift-wrapping cart attribute has not been set.
  {% elsif gift_wraps_in_cart > 0 and cart.attributes.gift-wrapping == blank  %}
  document.addEventListener("DOMContentLoaded", function(){
    Shopify.Cart.GiftWrap.set();
  });
  // If we have no gift-wrap item in the cart but our gift-wrapping cart attribute has been set.
  {% elsif gift_wraps_in_cart == 0 and cart.attributes.gift-wrapping != blank  %}
  document.addEventListener("DOMContentLoaded", function(){
    Shopify.Cart.GiftWrap.set();
  });
  {% endif %}

  // When the gift-wrapping checkbox is checked or unchecked.
  document.addEventListener("DOMContentLoaded", function(){
    document.querySelector('[name="attributes[gift-wrapping]"]').addEventListener("change", function(event) {
      if (event.target.checked) {
        Shopify.Cart.GiftWrap.set();
      } else {
        Shopify.Cart.GiftWrap.remove();
      }

    });

  });
</script>
{% endif %}