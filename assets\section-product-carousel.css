/* Product Carousel Section Styles */

.product-carousel {
  position: relative;
}

.product-carousel .swiper-container {
  position: relative;
  overflow: hidden;
  padding: 0 50px;
}

.product-carousel .swiper {
  overflow: visible;
  padding-bottom: 2rem;
}

.product-carousel .swiper-wrapper {
  align-items: stretch;
}

.product-carousel .swiper-slide {
  height: auto;
  display: flex;
  align-items: stretch;
  transition: transform 0.3s ease;
}

.product-carousel .swiper-slide .card-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.product-carousel .swiper-slide .card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.product-carousel .swiper-slide .card__content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* Navigation Arrows */
.product-carousel .swiper-button-next,
.product-carousel .swiper-button-prev {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 44px;
  height: 44px;
  margin-top: 0;
  color: var(--gradient-base-accent-1);
  background: rgba(var(--color-background), 0.95);
  border: 1px solid rgba(var(--color-border), 0.2);
  border-radius: 50%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  z-index: 10;
  cursor: pointer;
}

.product-carousel .swiper-button-next:hover,
.product-carousel .swiper-button-prev:hover {
  background: rgba(var(--color-background), 1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  transform: translateY(-50%) scale(1.05);
  border-color: rgba(var(--color-border), 0.3);
}

.product-carousel .swiper-button-next::after,
.product-carousel .swiper-button-prev::after {
  font-size: 16px;
  font-weight: 600;
  line-height: 1;
}

.product-carousel .swiper-button-next {
  right: 10px;
}

.product-carousel .swiper-button-prev {
  left: 10px;
}

.product-carousel .swiper-button-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.product-carousel .swiper-button-disabled:hover {
  transform: translateY(-50%) scale(1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Pagination */
.product-carousel .swiper-pagination {
  position: static;
  margin-top: 2rem;
  text-align: center;
}

.product-carousel .swiper-pagination-bullet {
  width: 12px;
  height: 12px;
  background: rgba(var(--color-foreground), 0.3);
  opacity: 1;
  margin: 0 6px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.product-carousel .swiper-pagination-bullet:hover {
  background: rgba(var(--color-foreground), 0.5);
  transform: scale(1.1);
}

.product-carousel .swiper-pagination-bullet-active {
  background: var(--gradient-base-accent-1);
  transform: scale(1.2);
}

/* Product Card Enhancements */
.product-carousel .card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 12px;
  overflow: hidden;
  background: var(--gradient-background);
  border: 1px solid rgba(var(--color-border), 0.1);
  position: relative;
}

.product-carousel .card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 1;
}

.product-carousel .card:hover {
  transform: translateY(-6px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  border-color: rgba(var(--color-border), 0.2);
}

.product-carousel .card:hover::before {
  opacity: 1;
}

.product-carousel .card__media {
  position: relative;
  overflow: hidden;
  border-radius: 12px 12px 0 0;
  background: var(--gradient-background);
}

.product-carousel .card__media::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0);
  transition: background 0.3s ease;
  pointer-events: none;
}

.product-carousel .card:hover .card__media::after {
  background: rgba(0, 0, 0, 0.05);
}

.product-carousel .card__media img {
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-carousel .card:hover .card__media img {
  transform: scale(1.08);
}

.product-carousel .card__information {
  padding: 1.25rem;
  text-align: center;
  position: relative;
  z-index: 2;
}

.product-carousel .card__heading {
  margin-bottom: 0.75rem;
  line-height: 1.4;
}

.product-carousel .card__heading a {
  text-decoration: none;
  color: var(--color-foreground);
  font-weight: 500;
  font-size: 1rem;
  transition: all 0.3s ease;
  display: block;
  position: relative;
}

.product-carousel .card__heading a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--gradient-base-accent-1);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.product-carousel .card:hover .card__heading a {
  color: var(--gradient-base-accent-1);
  transform: translateY(-1px);
}

.product-carousel .card:hover .card__heading a::after {
  width: 100%;
}

/* Price styling */
.product-carousel .price {
  font-weight: 600;
  margin-top: 0.5rem;
  font-size: 1.1rem;
}

.product-carousel .price__regular {
  color: var(--color-foreground);
}

.product-carousel .price__sale {
  color: var(--color-sale-text, #d73502);
}

.product-carousel .price__compare {
  color: rgba(var(--color-foreground), 0.6);
  text-decoration: line-through;
  font-size: 0.9rem;
  margin-left: 0.5rem;
}

/* Badge styling */
.product-carousel .card__badge {
  position: absolute;
  top: 1rem;
  left: 1rem;
  z-index: 3;
}

.product-carousel .badge {
  background: var(--gradient-base-accent-1);
  color: var(--color-base-text);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Vendor styling */
.product-carousel .card__vendor {
  font-size: 0.85rem;
  color: rgba(var(--color-foreground), 0.7);
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Rating styling */
.product-carousel .rating {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 0.5rem;
  gap: 0.25rem;
}

.product-carousel .rating__star {
  color: #ffc107;
  font-size: 0.9rem;
}

/* Quick action buttons */
.product-carousel .quick-add-button,
.product-carousel .quick-view-button {
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease;
  margin-top: 0.75rem;
}

.product-carousel .card:hover .quick-add-button,
.product-carousel .card:hover .quick-view-button {
  opacity: 1;
  transform: translateY(0);
}

.product-carousel .button {
  background: var(--gradient-base-accent-1);
  color: var(--color-base-text);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 0.85rem;
}

.product-carousel .button:hover {
  background: var(--gradient-base-accent-2);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Loading State */
.product-carousel .loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(var(--color-background), 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 5;
}

/* Responsive Design */
@media screen and (max-width: 749px) {
  .product-carousel .swiper-container {
    padding: 0 20px;
  }
  
  .product-carousel .swiper-button-next,
  .product-carousel .swiper-button-prev {
    width: 36px;
    height: 36px;
  }
  
  .product-carousel .swiper-button-next::after,
  .product-carousel .swiper-button-prev::after {
    font-size: 14px;
  }
  
  .product-carousel .swiper-button-next {
    right: 5px;
  }
  
  .product-carousel .swiper-button-prev {
    left: 5px;
  }
  
  .product-carousel .swiper-pagination-bullet {
    width: 10px;
    height: 10px;
    margin: 0 4px;
  }
}

@media screen and (max-width: 575px) {
  .product-carousel .swiper-container {
    padding: 0 10px;
  }
  
  .product-carousel .card__information {
    padding: 0.75rem;
  }
}

/* Tablet Specific */
@media screen and (min-width: 576px) and (max-width: 991px) {
  .product-carousel .swiper-container {
    padding: 0 40px;
  }
}

/* Desktop Specific */
@media screen and (min-width: 992px) {
  .product-carousel .swiper-container {
    padding: 0 60px;
  }
  
  .product-carousel .swiper-button-next,
  .product-carousel .swiper-button-prev {
    width: 48px;
    height: 48px;
  }
  
  .product-carousel .swiper-button-next::after,
  .product-carousel .swiper-button-prev::after {
    font-size: 18px;
  }
}

/* Accessibility */
.product-carousel .swiper-button-next:focus,
.product-carousel .swiper-button-prev:focus {
  outline: 2px solid var(--gradient-base-accent-1);
  outline-offset: 2px;
}

.product-carousel .swiper-pagination-bullet:focus {
  outline: 2px solid var(--gradient-base-accent-1);
  outline-offset: 2px;
}

.product-carousel[tabindex]:focus {
  outline: 2px solid var(--gradient-base-accent-1);
  outline-offset: 4px;
}

/* Screen Reader Only */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Animation for slide transitions */
.product-carousel .swiper-slide-active .card {
  animation: slideInActive 0.6s ease-out;
}

@keyframes slideInActive {
  from {
    opacity: 0.8;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Full width variant */
.product-carousel.full-width {
  width: 100vw;
  margin-left: calc(-50vw + 50%);
}

.product-carousel.full-width .swiper-container {
  max-width: none;
}

/* Grid fallback for non-carousel mode */
.product-carousel .grid {
  display: grid;
  gap: 2rem;
  grid-template-columns: repeat(var(--grid-desktop-columns, 4), 1fr);
}

@media screen and (max-width: 991px) {
  .product-carousel .grid {
    grid-template-columns: repeat(var(--grid-tablet-columns, 2), 1fr);
    gap: 1.5rem;
  }
}

@media screen and (max-width: 575px) {
  .product-carousel .grid {
    grid-template-columns: repeat(var(--grid-mobile-columns, 1), 1fr);
    gap: 1rem;
  }
}
