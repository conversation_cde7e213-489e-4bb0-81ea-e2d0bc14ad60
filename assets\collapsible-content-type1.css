.vindors-collapsible .collapsible-content {
  position: relative;
  z-index: 0;
}

.vindors-collapsible .collapsible-section-layout {
  padding-bottom: 5rem;
  padding-top: 5rem;
}
@media screen and (min-width: 750px)
.vindors-collapsible .collapsible-content__wrapper .grid .collapsible-content__grid-item{
    height: 69.5rem;
}
@media screen and (min-width: 750px) {
  .vindors-collapsible .collapsible-section-layout {
    padding-bottom: 7rem;
    padding-top: 7rem;
  }
}


.vindors-collapsible .collapsible-content__media--small {
  height: 19.4rem;
}
.vindors-collapsible .custom-collapsible-content .collapsible-content__wrapper .grid{flex-direction:row-reverse;}
.vindors-collapsible .custom-collapsible-section .collapsible-content__wrapper .grid{flex-direction:row;}
.vindors-collapsible .collapsible-content__media--large {
  height: 43.5rem;
  width:520px;
}
.vindors-collapsible .collapsible-content__media--large img.collspsible-img,
.vindors-collapsible .collapsible-content__media--large svg.placeholder-svg
{
    width: 522px;
    height: 821px;
    border-radius: 25rem;
}
.vindors-collapsible .collapsible-content__media--large::before{
    content: '';
    width: 517px;
    height: 790px;
    background: transparent;
    position: absolute;
    left: 19px;
    border-radius: 25rem;
    border: 5px solid #FFC312;
    top: -18px;
  }
.vindors-collapsible .custom-collapsible-section .collapsible-content__media--large:before {
    content: "";
    width: 517px;
    height: 790px;
    background: transparent;
    position: absolute;
    left: -15px;
    border-radius: 25rem;
    border: 5px solid #FFC312;
    top: -16px;
}
@media screen and (min-width: 750px) {
  .vindors-collapsible .collapsible-content__media--small {
    height: 31.4rem;
  }

  .vindors-collapsible .collapsible-content__media--large.media {
    height: 74.5rem;
    width: 520px;
  }
}

@media screen and (min-width: 750px) {
  .vindors-collapsible .collapsible-content__grid--reverse {
    flex-direction: row-reverse;
  }
}

.vindors-collapsible .collapsible-content-wrapper-narrow {
  margin: 0 auto;
  padding-right: 1.5rem;
  padding-left: 1.5rem;
  max-width: 73.4rem;
}

.vindors-collapsible .collapsible-content__header {
  word-break: break-word;
}

.vindors-collapsible .collapsible-content__heading {
  margin-bottom: 2rem;
  margin-top: 0;
}

@media screen and (min-width: 750px) {
  .vindors-collapsible .collapsible-content__heading {
    margin-bottom: 3rem;
  }
}

/* .vindors-collapsible .collapsible-none-layout .accordion + .accordion {
  border-top: 0;
} */

.vindors-collapsible .collapsible-row-layout .accordion:not(:first-child):not(.color-background-1) {
  margin-top: 1rem;
}

.vindors-collapsible .caption-with-letter-spacing + h2 {
  margin-top: 1rem;
}

@media screen and (min-width: 750px) {
  .vindors-collapsible .collapsible-content .accordion {
    margin-top: 0;
  }
}

.vindors-collapsible .collapsible-row-layout .accordion {
  border: var(--text-boxes-border-width) solid rgba(var(--color-foreground),var(--text-boxes-border-opacity));
  margin-bottom: 1.5rem;
}

.vindors-collapsible .collapsible-row-layout .accordion summary, .collapsible-row-layout .accordion .accordion__content {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.vindors-collapsible .collapsible-content .accordion:first-child{border-top:none;}


/* check for flexbox gap in older Safari versions */
@supports not (inset: 10px) {
  @media screen and (min-width: 750px) {
    .vindors-collapsible .collapsible-content__grid:not(.collapsible-content__grid--reverse) .grid__item:last-child,
    .vindors-collapsible .collapsible-content__grid--reverse .collapsible-content__grid-item {
      padding-left: 5rem;
      padding-right: 0;
    }
  }

  @media screen and (min-width: 990px) {
    .vindors-collapsible .collapsible-content__grid:not(.collapsible-content__grid--reverse) .grid__item:last-child,
    .vindors-collapsible .collapsible-content__grid--reverse .collapsible-content__grid-item {
      padding-left: 7rem;
    }
  }
}
.vindors-collapsible .collapsible_address-block ul li:not(:last-child){margin-bottom:35px;}
.vindors-collapsible .collapsible_address-block ul li svg{margin-right:16px;}
.vindors-collapsible .collapsible_address-block ul li,
.vindors-collapsible .collapsible_address-block ul li a{display:flex;align-items:center;}


/*custom css file*/
.vindors-collapsible .collapsible-content__wrapper .grid__item h2.title,
.vindors-collapsible .collapsible-content.custom-collapsible-section .collapsible-content__wrapper .grid__item h2.title {font-size: clamp(2.5rem, 2rem + 2.5vw, 5rem);;font-weight: 400;letter-spacing: 5px;line-height: 1.3;margin: 15px 0;}
.vindors-collapsible .collapsible-content__wrapper .grid__item p.description,
.vindors-collapsible .collapsible-content.custom-collapsible-section .collapsible-content__wrapper .grid__item p.description{margin: 0 0 25px;}
.vindors-collapsible .accordion,
.vindors-collapsible .collapsible-content.custom-collapsible-section .accordion{padding: 20px;border: 1px solid #b4b4b4;}
.vindors-collapsible .accordion .accordion__title,
.vindors-collapsible .collapsible-content.custom-collapsible-section .accordion .accordion__title{font-size: clamp(1.6rem, 1.52rem + 0.4vw, 2rem);;font-weight: 400;letter-spacing:normal;line-height: 30px; text-transform: capitalize; }
.vindors-collapsible .accordion summary,
.vindors-collapsible .collapsible-content.custom-collapsible-section .accordion summary{padding: 0;}
.vindors-collapsible .accordion,
.vindors-collapsible .collapsible-content.custom-collapsible-section .accordion{margin: 0 0 25px;}
.vindors-collapsible .accordion p,
.vindors-collapsible .collapsible-content.custom-collapsible-section .accordion p{padding-top: 15px;max-width: calc(95% - 6rem);text-align: left;margin:10px 0;}
.vindors-collapsible .accordion__content,
.vindors-collapsible .collapsible-content.custom-collapsible-section .accordion__content{margin-bottom: 0;}
.vindors-collapsible .collapsible-content__wrapper .grid,
.vindors-collapsible .collapsible-content.custom-collapsible-section .collapsible-content__wrapper .grid{width: 100%; gap: 0;}
.vindors-collapsible .collapsible-content__wrapper .grid .collapsible-content__grid-item,
.vindors-collapsible .collapsible-content.custom-collapsible-section .collapsible-content__wrapper .grid .collapsible-content__grid-item{width: 44%;display: flex;justify-content: center;}
.vindors-collapsible .collapsible-content__wrapper .grid .collapsible-content,
.vindors-collapsible .collapsible-content.custom-collapsible-section .collapsible-content__wrapper .grid .collapsible-content{width: 56%;}
.vindors-collapsible .collapsible-content__wrapper .grid .collapsible-content-section{max-width: 860px;width: 100%;padding-left: 100px;}
.vindors-collapsible .collapsible-content.custom-collapsible-section .collapsible-content__wrapper .grid .collapsible-content-section{max-width: 860px;width: 100%;padding-right: 100px;padding-left:0;margin-left: auto;}

/*responsive*/
@media screen and (min-width: 1200px) and (max-width: 1919px){
  .vindors-collapsible .collapsible-content__wrapper .grid .collapsible-content-section{padding-right: 30px;}
  .vindors-collapsible .collapsible-content.custom-collapsible-section .collapsible-content__wrapper .grid .collapsible-content-section{padding-left: 30px;}
}
@media screen and (min-width: 992px) and (max-width: 1199px){
  .vindors-collapsible .collapsible-content__wrapper .grid .collapsible-content-section{padding-left: 25px;}
  .vindors-collapsible .collapsible-content.custom-collapsible-section .collapsible-content__wrapper .grid .collapsible-content-section{padding-right: 25px;}
  .vindors-collapsible .collapsible-content__wrapper .grid, .vindors-collapsible .collapsible-content.custom-collapsible-section .collapsible-content__wrapper .grid{display: grid; grid-template-columns: 1fr 1fr;grid-gap: 3rem;}
  .vindors-collapsible .collapsible-content__wrapper .grid .collapsible-content__grid-item, .vindors-collapsible .collapsible-content.custom-collapsible-section .collapsible-content__wrapper .grid .collapsible-content__grid-item,
  .vindors-collapsible .collapsible-content__wrapper .grid .collapsible-content, .vindors-collapsible .collapsible-content.custom-collapsible-section .collapsible-content__wrapper .grid .collapsible-content{width: 100%;}
  .collapsible-content .collapsible-content__media{height: 100%;}
}
@media screen and (max-width: 991px){
  .vindors-collapsible .collapsible-content__wrapper .grid, .vindors-collapsible .collapsible-content.custom-collapsible-section .collapsible-content__wrapper .grid{flex-direction: column;row-gap: 3rem;}
  .vindors-collapsible .collapsible-content__wrapper .grid .collapsible-content__grid-item, .vindors-collapsible .collapsible-content.custom-collapsible-section .collapsible-content__wrapper .grid .collapsible-content__grid-item,
  .vindors-collapsible .collapsible-content__wrapper .grid .collapsible-content, .vindors-collapsible .collapsible-content.custom-collapsible-section .collapsible-content__wrapper .grid .collapsible-content{width: 100%;padding: 30px;}
  .vindors-collapsible .collapsible-content__wrapper .grid .collapsible-content-section,
  .vindors-collapsible .collapsible-content.custom-collapsible-section .collapsible-content__wrapper .grid .collapsible-content-section{padding: 0px;}
}

@media screen and (max-width: 576px){
  .vindors-collapsible .accordion p, .vindors-collapsible .collapsible-content.custom-collapsible-section .accordion p{max-width: calc(95% - 0rem);}
}
@media screen and (max-width:425px){
  .vindors-collapsible .collapsible-content__wrapper .grid__item h2.title, .vindors-collapsible .collapsible-content__wrapper .grid__item p{text-align:center;}
   .vindors-collapsible .collapsible-content__wrapper .grid__item h2.title{letter-spacing:1px;}
    .vindors-collapsible .collapsible-content__wrapper .grid .collapsible-content{padding:20px;}
}


@media screen and (max-width: 749px){
  .vindors-collapsible .collapsible-content__media--large {
    height: 76.5rem;
    width: 536px;
  }
  .vindors-collapsible .custom-collapsible-section .collapsible-content__media--large:before{
    width: 427px;
  }
  .vindors-collapsible .collapsible-content__media--large:before{
    width: 397px;
  }
}



