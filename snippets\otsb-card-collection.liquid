{% comment %}
  Renders a collection card

  Accepts:
  - card_collection: {Object} Collection Liquid object
  - image_ratio: {String} Size of the product image card. Values are "square", "rectangle", "round", "natural". Default is "square" (optional)
  - columns_desktop: {Number}
  Usage:
  {% render 'card-collection' %}
{% endcomment %}
{% liquid
  assign corner_image = false
  if section.settings.edges_type == 'rounded_corners'
    assign corner_image = true
  endif
  if is_lazy
    assign loadingImg = "lazy"
  else 
    assign loadingImg = "eager"
  endif

  if title_size != blank
    if settings.heading_base_size != blank
      assign heading_size = title_size | times: settings.heading_base_size | times: 0.000225
    else
      assign heading_size = title_size | times: 100 | times: 0.000225
    endif
  endif
%}
{%- style -%}
  {%- unless color_text.alpha == 0.0 -%}
    .text-color-{{ blockID }} {
      color: {{ color_text }};
      --colors-text: {{ color_text.red }}, {{ color_text.green }}, {{ color_text.blue }};
      --colors-title: {{ title_color.red }}, {{ title_color.green }}, {{ title_color.blue }};
    }
  {%- endunless -%}
  .collection-card-{{ blockID }} .image-overlay,
  .collection-card-{{ section.id }} .image-overlay {
    {% if content_position == 'center' or collection_carousel %}
      opacity: {{ image_overlay_opacity }}%;
      --image-treatment-overlay: {{ image_overlay_color.red }}, {{ image_overlay_color.green }}, {{ image_overlay_color.blue }};
    {% else %}
        background: linear-gradient(to{% if content_position == 'bottom' %} bottom{% elsif content_position == 'top' %} top{% endif %}, transparent 0%, rgba({{ image_overlay_color.red }}, {{ image_overlay_color.green }}, {{ image_overlay_color.blue }}, {{ image_overlay_opacity }}%) 100%);
    {% endif %}
  }
  .dark .collection-card-{{ blockID }} .image-overlay,
  .dark .collection-card-{{ section.id }} .image-overlay {
    {% if content_position == 'center' or collection_carousel %}
      opacity: {{ image_overlay_opacity }}%;
    {% else %}
      {% if settings.colors_dark_image_treatment_overlay != blank %}
        background: linear-gradient(to{% if content_position == 'bottom' %} bottom{% elsif content_position == 'top' %} top{% endif %}, transparent 0%, rgba({{ settings.colors_dark_image_treatment_overlay.red }}, {{ settings.colors_dark_image_treatment_overlay.green }}, {{ settings.colors_dark_image_treatment_overlay.blue }}, {{ image_overlay_opacity }}%) 100%);
      {% else %}
        background: linear-gradient(to{% if content_position == 'bottom' %} bottom{% elsif content_position == 'top' %} top{% endif %}, transparent 0%, rgba(32, 32, 32, {{ image_overlay_opacity }}%) 100%);
      {% endif %}
    {% endif %}
  }
  .content--{{ section.id }} .collection-item-title {
    {% if title_size != blank %}
      font-size: {{ heading_size | times: 0.45 }}rem;
    {% else %}
      font-size: 100%;
    {% endif %}
  }
  @media screen and (min-width: 768px) {
    .spacing--{{ section.id }} {
      gap: {{ section.settings.spacing }}px;
    }
    .content--{{ section.id }} .collection-item-title {
      {% if title_size != blank %}
        font-size: {{ heading_size | times: 0.6 }}rem;
      {% else %}
        font-size: 100%;
    {% endif %}
    }
  }
    .collection-card-{{ blockID }} a, collection-card-{{ section.id }} a {
    color: rgba(var(color_text_link));
    }
    {% if title_color.alpha != 0 %}
        #shopify-section-{{ section.id }} .otsb__root .text-\[rgba\(var\(--colors-title\)\)\]{color:rgba({{ title_color.red }}, {{ title_color.green }}, {{ title_color.blue }})}
    {% else %}
        #shopify-section-{{ section.id }} .otsb__root .text-\[rgba\(var\(--colors-title\)\)\]{color:rgba(var(--color-foreground))};
    {% endif %}

{%- endstyle -%}
{% if collection_carousel %}
                      <div class="collection-card collection-card-{{ blockID }} group disable-effect card relative h-full block{% if extend_height %} flex flex-col{% endif %}">
                    {% else %}
                      <a {% if card_collection == blank %} role="link" aria-disabled="true"{% else %} href="{{ card_collection.url }}"{% endif %} class="collection-card collection-card-{{ section.id }} group disable-effect card relative h-full block{% if extend_height %} flex flex-col{% endif %}">
                    {% endif %}
                      <div class="w-full relative overflow-hidden z-0 before:h-0 before:block{% if corner_image and image_ratio != "round" %} rounded-[10px]{% endif %}{% if extend_height %} flex flex-col h-full{% endif %}{% if image_ratio == "round" %} rounded-full{%- endif -%}{% unless image_ratio == "auto" %} pb-[{{ ratio }}%]{% endunless %}"{% if image_ratio == "auto" and card_collection.featured_image %} style="{% if collage == blank %}height:0;{% endif %} padding-bottom: {{ 100 | divided_by: card_collection.featured_image.aspect_ratio }}%;"{% endif %}>
                        {%- if card_collection != blank -%}
                          <div
                            class="block{% if image_ratio == "round" %} rounded-full{% endif %}{% if corner_image and image_ratio != "round" %} rounded-[10px]{% endif %}"
                          >
                            {% capture sizes %}
                              {% if collage %}
                                {% if full_width %}
                                  (min-width: 768px) {{ columns_desktop | times: 100 | divided_by: max_columns }}vw, {{ columns_mobile | times: 100 | divided_by: min_columns_mobile }}vw
                                {% else %}
                                  (min-width: {{ settings.page_width }}px) {{ settings.page_width | times: columns_desktop | divided_by: max_columns }}px, {{ columns_mobile | times: 100 | divided_by: min_columns_mobile }}vw
                                {% endif %}
                              {% else %}
                                (min-width: {{ settings.page_width }}px) {% if enable_desktop_slider %}{{ settings.page_width | plus: 100 | divided_by: columns_desktop }}px{% else %}calc(100vw / {{ columns_desktop }}){% endif %},
                                  (min-width: 768px) {% if columns_desktop > 1 %}calc(100vw / 2){% else %}100vw{% endif %},
                                  calc(100vw / {{ columns_mobile }})
                              {% endif %}
                            {% endcapture %}
                            {%- if custom_image_collection != blank or image != blank -%}
                                
                              <div class="w-full h-full top-0 left-0 absolute overflow-hidden duration-300 transition ease-out">
                                {% if custom_image_collection != blank %}
                                  <img
                                  srcset="{{ custom_image_collection | image_url: width: 225 }} 225w,
                                  {{ custom_image_collection | image_url: width: 375 }} 375w,
                                  {{ custom_image_collection | image_url: width: 450 }} 450w,
                                  {{ custom_image_collection | image_url: width: 750 }} 750w,
                                  {{ custom_image_collection | image_url: width: 900 }} 900w,
                                  {{ custom_image_collection | image_url: width: 1100 }} 1100w,
                                  {{ custom_image_collection | image_url: width: 1500 }} 1500w,
                                  {{ custom_image_collection | image_url: width: 1780 }} 1780w,
                                  {{ custom_image_collection | image_url: width: 2000 }} 2000w,
                                  {{ custom_image_collection | image_url: width: 3000 }} 3000w,
                                  {{ custom_image_collection | image_url: width: 3840 }} 3840w"
                                  src="{{ custom_image_collection | image_url: width: 3840 }}"
                                  sizes="{{ sizes }}"
                                  alt="{{ custom_image_collection.alt | escape }}"
                                  height="{{ custom_image_collection.height }}"
                                  width="{{ custom_image_collection.width }}"
                                  loading={{ loadingImg }}
                                  class="aspect-{{ image_ratio }} w-full h-full object-cover z-10 object-center otsb-image-hover{% if image_ratio == "round" %} rounded-full{% endif %}{% if corner_image and image_ratio != "round" %} md:rounded-[10px]{% unless card_full_width %} rounded-[10px]{% endunless %}{% endif %}"
                                >
                                {% else %}
                                <img
                                  srcset="{{ image | image_url: width: 225 }} 225w,
                                  {{ image | image_url: width: 375 }} 375w,
                                  {{ image | image_url: width: 450 }} 450w,
                                  {{ image | image_url: width: 750 }} 750w,
                                  {{ image | image_url: width: 900 }} 900w,
                                  {{ image | image_url: width: 1100 }} 1100w,
                                  {{ image | image_url: width: 1500 }} 1500w,
                                  {{ image | image_url: width: 1780 }} 1780w,
                                  {{ image | image_url: width: 2000 }} 2000w,
                                  {{ image | image_url: width: 3000 }} 3000w,
                                  {{ image | image_url: width: 3840 }} 3840w"
                                  src="{{ image | image_url: width: 3840 }}"
                                  sizes="{{ sizes }}"
                                  alt="{{ image.alt | escape }}"
                                  height="{{ image.height }}"
                                  width="{{ image.width }}"
                                  loading={{ loadingImg }}
                                  class="aspect-{{ image_ratio }} w-full h-full object-cover z-10 object-center otsb-image-hover{% if image_ratio == "round" %} rounded-full{% endif %}{% if corner_image and image_ratio != "round" %} md:rounded-[10px]{% unless card_full_width %} rounded-[10px]{% endunless %}{% endif %}"
                                >
                                {% endif %}
                              </div>
                            {%- elsif card_collection.featured_image -%}
                              <div class="w-full h-full top-0 left-0 absolute overflow-hidden duration-300 transition ease-out ">
                                <img
                                  srcset="{{ card_collection.featured_image | image_url: width: 225 }} 225w,
                                  {{ card_collection.featured_image | image_url: width: 375 }} 375w,
                                  {{ card_collection.featured_image | image_url: width: 450 }} 450w,
                                  {{ card_collection.featured_image | image_url: width: 750 }} 750w,
                                  {{ card_collection.featured_image | image_url: width: 900 }} 900w,
                                  {{ card_collection.featured_image | image_url: width: 1100 }} 1100w,
                                  {{ card_collection.featured_image | image_url: width: 1500 }} 1500w,
                                  {{ card_collection.featured_image | image_url: width: 1780 }} 1780w,
                                  {{ card_collection.featured_image | image_url: width: 2000 }} 2000w,
                                  {{ card_collection.featured_image | image_url: width: 3000 }} 3000w,
                                  {{ card_collection.featured_image | image_url: width: 3840 }} 3840w"
                                  src="{{ card_collection.featured_image | image_url: width: 3840 }}"
                                  sizes="{{ sizes }}"
                                  alt="{{ card_collection.featured_image.alt | escape }}"
                                  height="{{ card_collection.featured_image.height }}"
                                  width="{{ card_collection.featured_image.width }}"
                                  loading={{ loadingImg }}
                                  class="aspect-{{ image_ratio }} w-full h-full object-cover z-10 object-center otsb-image-hover{% if image_ratio == "round" %} rounded-full{% endif %}{% if corner_image and image_ratio != "round" %} md:rounded-[10px]{% unless card_full_width %} rounded-[10px]{% endunless %}{% endif %}"
                                >
                              </div>
                            {%- else -%}
                              <div class="flex{% unless image_ratio == "auto" %} absolute{% endunless %} top-0 left-0 w-full h-full items-center bg-[#c9c9c9]">
                                {% render 'otsb-icon-placeholder', icon: 'icon-collection' , class: 'text-[#acacac] w-full h-full' -%}
                              </div>
                            {%- endif -%}
                          </div>
                        {%- else -%}
                          <div class='flex{% unless image_ratio == "auto" %} absolute{% endunless %} top-0 left-0 w-full h-full items-center bg-[#c9c9c9]'>
                            {% render 'otsb-icon-placeholder', icon: 'icon-collection' , class: 'text-[#acacac] w-full h-full' -%}
                          </div>
                        {%- endif -%}
                        {% if text_overlay %}
                          <div class="image-overlay absolute top-0 bottom-0 h-full w-full otsb-image-treatment-overlay"></div>
                        {% else %}
                          </div>
                        {% endif %}
                      <div class="content--{{ section.id }} text-{{ content_alignment }} pt-5 pb-5
                        {%- if text_overlay %} overflow-hidden absolute pl-8 pr-8 md:pl-6 md:pr-6
                          {%- if columns_desktop < 2 and content_alignment == 'center' %} left-1/2 -translate-x-1/2 w-full{% else %} left-0{% endif %} right-0
                          {%- if content_position == 'top' %} top-0{% elsif content_position == 'center' %} top-1/2 -translate-y-1/2{% else %} bottom-0{% endif -%}
                        {%- else %} left-0 right-0 grow{% endif -%}
                        {%- if card_full_width %} pl-5 pr-5 md:pl-0 md:pr-0{% endif -%}
                        {%- if collection_carousel %} text-color-{{ blockID }} md:pt-8 md:pb-8 flex flex-col justify-between h-full{% endif -%}"
                      >
                        <div class="collection-item-title leading-tight text-[rgba(var(--colors-title))]
                          {%- if columns_desktop <= 8 and collage == blank %} otsb-text-medium{% endif -%}
                          {%- if image_ratio == "round" %} mt-1{% endif -%}"
                        >
                          {%- if card_collection != blank -%}
                            <p
                              class="text-[rgba(var(--colors-title))] otsb-p-break-words transition duration-200">
                              
                              {% if card_collection.title != blank or custom_name_collection != blank %}
                                {%- if custom_name_collection != blank  -%}
                                  {{ custom_name_collection }}
                                {% else %}
                                  {{- card_collection.title | escape -}}
                                {% endif %}
                              {%- else -%}
                                Example Collection
                              {%- endif -%}
                            </p>
                            {%- if description != blank -%}
                              <div class="otsb-rte otsb-text-default">
                                {{- description -}}
                              </div>
                            {%- endif -%}
                          {%- else -%}
                            <p>Example Collection</p>
                          {%- endif -%}
                        </div>
                        {%- if card_collection != blank and show_item_count == true -%}
                          <p class="text-[rgba(var(--colors-text))] {% if collage %} mt-2.5 leading-none{% else %} mt-1{% endif %}{% if columns_desktop > 8 %} otsb-text-normal{% endif %}">
                            {{ card_collection.all_products_count }}{% if card_collection.all_products_count > 1 %} items{% else %} item{% endif %}
                          </p>
                        {%- endif -%}

                        {% if card_collection and collection_carousel and button_label != blank %}
                          {%- style -%}
                            {%- if show_button_primary -%}
                              .button--{{ blockID }}.otsb-button-solid,
                              .button--{{ blockID }}.otsb-button-solid:before {
                                {%- unless color_button.alpha == 0.0 -%}
                                  --colors-line-and-border: {{ color_button.red }}, {{ color_button.green }}, {{ color_button.blue }};
                                  --colors-button: {{ color_button.red }}, {{ color_button.green }}, {{ color_button.blue }};
                                {%- else -%}
                                  --colors-line-and-border: var(--colors-button);
                                {%- endunless -%}
                                {%- unless color_button_hover.alpha == 0.0 -%}
                                  --colors-button-hover: rgb({{ color_button_hover.red }}, {{ color_button_hover.green }}, {{ color_button_hover.blue }});
                                {%- endunless -%}
                                {%- unless color_text_button.alpha == 0.0 -%}
                                  --colors-button-text: {{ color_text_button.red }}, {{ color_text_button.green }}, {{ color_text_button.blue }};
                                {%- endunless -%}
                                {%- unless color_text_button_hover.alpha == 0.0 -%}
                                  --colors-button-text-hover: {{ color_text_button_hover.red }}, {{ color_text_button_hover.green }}, {{ color_text_button_hover.blue }};
                                {%- endunless -%}
                              }
                            {%- else -%}
                              .button--{{ blockID }}.otsb-button-outline {
                                {%- if secondary_button_text.alpha != 0.0 -%}
                                  --colors-secondary-button: {{ secondary_button_text.red }}, {{ secondary_button_text.green }}, {{ secondary_button_text.blue }};
                                  --colors-line-secondary-button: {{ secondary_button_text.red }}, {{ secondary_button_text.green }}, {{ secondary_button_text.blue }};
                                  --background-secondary-button: transparent;
                                {%- endif -%}
                                {%- if color_button_secondary.alpha != 0.0 -%}
                                  --background-secondary-button: {{ color_button_secondary.red }}, {{ color_button_secondary.green }}, {{ color_button_secondary.blue }};
                                  --colors-line-secondary-button: {{ color_button_secondary.red }}, {{ color_button_secondary.green }}, {{ color_button_secondary.blue }};
                                {%- endif -%}
                              }
                            {%- endif -%}
                          {%- endstyle -%}
                          <div>
        <a href="{{ card_collection.url }}"{% if open_new_window_button %} target="_blank"{% endif %} class="otsb-button{% if show_button_primary %} otsb-button-solid{% else %} otsb-button-outline{% endif %} button--{{ blockID }} otsb-p-break-words border inline-block empty:otsb-hidden mt-4 lg:mt-6 lg:mb-4 xl:mb-6 pl-4 pr-4 lg:pl-6 lg:pr-6 pt-2.5 pb-2.5 leading-normal md:pt-3 md:pb-3 cursor-pointer pointer-events-auto">
          {% render 'otsb-button-label', button_label: button_label, show_button_primary: show_button_primary %}
        </a>
      </div>
                        {% endif %}
                      </div>
                      {% if text_overlay %}
                        </div>
                      {% endif %}
                    {% if collection_carousel %}
                      </div>
                    {% else %}
                      </a>
                {% endif %}
