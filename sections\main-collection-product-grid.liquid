{{ 'template-collection.css' | asset_url | stylesheet_tag }}
{{ 'component-loading-overlay.css' | asset_url | stylesheet_tag }}
{{ 'component-card.css' | asset_url | stylesheet_tag }}
{{ 'component-price.css' | asset_url | stylesheet_tag }}

<link rel="preload" href="{{ 'component-rte.css' | asset_url }}" as="style" onload="this.onload=null;this.rel='stylesheet'">
{%- if settings.enable_quickadd or settings.enable_quickview -%}
  <link rel="stylesheet" href="{{ 'quick-add.css' | asset_url }}" media="print" onload="this.media='all'">
  <script src="{{ 'sticky-sidebar.js' | asset_url }}" defer="defer"></script>
  <script src="{{ 'quick-add.js' | asset_url }}" defer="defer"></script>
  <script src="{{ 'product-form.js' | asset_url }}" defer="defer"></script>
  <script src="{{ 'ajaxinate.min.js' | asset_url }}" defer></script>  
{%- endif -%}
<noscript>{{ 'component-rte.css' | asset_url | stylesheet_tag }}</noscript>

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
.custom-product-grid {opacity: 1;display: flex;justify-content: space-between; transition: all var(--duration-default) linear;transform: translateY(0);padding:0 ;margin:0;}
.custom-product-grid.panel-disabled { cursor: not-allowed; pointer-events: none; opacity: 0; transform: translateY(-5px); }
.custom-product-grid li { opacity: 1; min-width: 36px; min-height: 36px; padding: 5px 8px; cursor: pointer; display: flex; align-items: center; justify-content: center; }
.custom-product-grid li { position: relative; margin: 0 5px; background: var(--gradient-base-accent-2);border-radius: var(--buttons-radius);transition:all 0.5s;color: var(--gradient-background);}
.custom-product-grid li:after { content: '';display:none; width: auto; height: auto; white-space: nowrap; /*letter-spacing: -4px;*/ transform: translateX(-2.5px); -webkit-transform: translateX(-2.5px); color: var(--gradient-base-accent-1); transition: all var(--duration-default) linear; }
.custom-product-grid li:before { content: ""; position: absolute; right: 0; top: 0; bottom: 0; width: 0; transition: all var(--duration-default) linear; background: var(--gradient-base-accent-1);border-radius: var(--buttons-radius); }
.custom-product-grid li[data-cols="grid--1-col-desktop"]:after {content:  "|" }
.custom-product-grid li[data-cols="grid--2-col-desktop"]:after {content:  "||" }
.custom-product-grid li[data-cols="grid--3-col-desktop"]:after {content:  "|||" }
.custom-product-grid li[data-cols="grid--4-col-desktop"]:after {content:  "||||" }
.custom-product-grid li[data-cols="grid--5-col-desktop"]:after {content:  "|||||" }
.custom-product-grid li[data-cols="grid--6-col-desktop"]:after {content:  "||||||" }
.custom-product-grid li:hover {background:rgba(var(--color-base-outline-button-labels));color: var(--gradient-base-accent-1);}}
.custom-product-grid li.active:after { color: var(--gradient-base-accent-1); } 
  .custom-product-grid li.active .icon-columns{ color:var(--gradient-base-background-1); }
.custom-product-grid li.active:before { left: 0; width: 100%;  background-color:var(--gradient-base-accent-1); }
.custom-product-grid li .icon-columns{z-index:1;width: 2rem;height: 2rem;}

@media screen and (max-width: 1540px) {
.custom-product-grid li[data-cols="grid--5-col-desktop"]{display:none;}
}
@media screen and (max-width: 1540px) {
  .custom-product-grid li[data-cols="grid--4-col-desktop"]{display:none;}
}  
@media screen and (max-width: 767px) {
  .custom-product-grid li[data-cols="grid--3-col-desktop"]{display:none;}
} 
@media screen and (max-width: 990px) {
  .custom-product-grid {justify-content:flex-end;}
} 

  .product-collection .card__information .card__heading{
    padding-right: 35px;
  }
{% if section.settings.sidebar_settings == 'no-sidebar'  %}
  @media screen and (max-width: 989px){
   .no-sidebar facet-filters-form.facets.facets-vertical-sort {justify-content: space-between;}
  }
{% endif %}

{%- endstyle -%}

<div class="product-collection {% if section.settings.page_full_width %} page-full-width {% else %} page-width {% endif %} {% if section.settings.page_full_width_spacing %} page-full-width_spacing {% endif %}section-{{ section.id }}-padding isolate">
  <div class="row">
  {% comment %} Sort is the first tabbable element when filter type is vertical {% endcomment %}

  <div class="{% if section.settings.filter_type == 'vertical' %} facets-vertical {{ section.settings.sidebar_settings }} {% elsif section.settings.filter_type == 'horizontal' %}  facets-horizontal {% endif %}">
    {{ 'component-facets.css' | asset_url | stylesheet_tag }}
    <script src="{{ 'facets.js' | asset_url }}" defer="defer"></script>
    {%- if section.settings.enable_filtering or section.settings.enable_sorting -%}      
      <aside aria-labelledby="verticalTitle" class="facets-wrapper{% unless section.settings.enable_filtering %} facets-wrapper--no-filters{% endunless %}  {% if section.settings.filter_type == 'vertical' %} sidebar-sticky {% endif %}" id="main-collection-filters" data-id="{{ section.id }}">    
      {% render 'facets', results: collection, enable_filtering: section.settings.enable_filtering, enable_sorting: section.settings.enable_sorting,enable_custom_cols: section.settings.collections_custom_cols, filter_type: section.settings.filter_type %}
     
        <!-- Sidebar menu -->            
      {% if section.settings.show_menu %}  
            {% assign sidebar_menu = section.settings.linklist %}
            {% if linklists[sidebar_menu].links.size > 0 %}
            <div class="filter-panel-menu medium-hide small-hide">
            {%- if section.settings.menu_title != '' -%}
            <h5 class="sidebar_title">{{ section.settings.menu_title }}</h5>
            {%- endif -%}
            <div class="filter-panel" id="accordian">
            <ul>
            {% for link in linklists[sidebar_menu].links %}
            {% assign link_handle = link.title | handle %}
            {% if linklists[link_handle] and linklists[link_handle].links.size > 0 %}
            <li class="{% if link.active %}active{% endif %}">
              <a href="{{ link.url }}">{{ link.title }}</a>
              <ul>
                {% for child in linklists[link_handle].links %}
                <li {% if child.active %}class="active"{% endif %}><a href="{{ child.url }}">{{ child.title }}</a></li>
                {% endfor %}
              </ul>
            </li>
            {% else %}
            <li class="{% if link.active %}active{% endif %}"><a href="{{ link.url }}">{{ link.title }}</a></li>
            {% endif %}
            {% endfor %}
            </ul>
            </div>
            </div>
            {% endif %}
            {% endif %}  
        <!-- End Sidebar menu -->      
      {%- unless section.settings.filter_type == 'drawer' or section.settings.filter_type == 'horizontal'  -%}
       {% render 'main-collection-sidebar' %}
       {%- endunless -%}
    </aside>    
    {%- endif -%}
    <div class="product-grid-container{% if section.settings.filter_type == 'vertical' and section.settings.collapse_on_larger_devices %} product-grid-container-vertical{% endif %}" id="ProductGridContainer">      
        {%- if section.settings.enable_sorting -%}
          <facet-filters-form class="facets facets-vertical-sort  {% if section.settings.page_full_width %} page-full-width {% else %} page-width {% endif %} {% if section.settings.sidebar_settings != 'no-sidebar'  %}small-hide{% endif %} no-js-hidden facte-{{ section.settings.filter_type }}">
             <div class="product-count-vertical light {% if section.settings.sidebar_settings != 'no-sidebar' or section.settings.filter_type != 'vertical'  %}medium-hide small-hide {% endif %}" role="status">
                <h2 class="product-count__text text-body">
                  <span class="ProductCountDesktop">
                  <!-- {%- if collection.results_count -%}
                      {{ 'templates.search.results_with_count' | t: terms: collection.terms, count: collection.results_count }}
                    {%- elsif collection.products_count == collection.all_products_count -%}
                      {{ 'products.facets.product_count_simple' | t: count: collection.products_count }}
                    {%- else -%}
                      {{ 'products.facets.product_count' | t: product_count: collection.products_count, count: collection.all_products_count }}
                    {%- endif -%}   -->
                    {%- paginate collection.products by section.settings.products_per_page -%}
                    {%- assign productSize = collection.products.size -%}
                    {{ 'products.facets.count_text' | t:current_count:productSize,total_count:collection.products_count}}
                    {%- endpaginate -%}  
                  </span>
                </h2>
                <div class="loading-overlay__spinner">
                  <svg aria-hidden="true" focusable="false" role="presentation" class="spinner" viewBox="0 0 66 66" xmlns="http://www.w3.org/2000/svg">
                    <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
                  </svg>
                </div>
              </div>  
             
            <!-- {% if section.settings.enable_grid_list %}  
            <div class="filter-style">
              <div class="filter-buttons">
                <div class="grid-view-button layout-mode active"> {% render 'icon-large' %}</div>
                <div class="list-view-button layout-mode ">{% render 'icon-bars' %}</div>
              </div>
            </div>
             {% endif %}  -->
              {% if section.settings.collections_custom_cols %}
              <ul class="custom-product-grid {% if section.settings.sidebar_settings != 'no-sidebar' or  section.settings.filter_type != 'vertical'  %}medium-hide small-hide {% endif %}">
              <li data-val="1"  data-cols="list-view-filter" class="list-view-button layout-mode{% if section.settings.columns_desktop == '1' %}active{% endif %} ">
               <svg class="icon-columns" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="15.25" y="4.25" width="1.5" height="12.5" rx="0.75" transform="rotate(90 15.25 4.25)" fill="currentcolor"></rect>
                <rect x="15.25" y="8.25" width="1.5" height="12.5" rx="0.75" transform="rotate(90 15.25 8.25)" fill="currentcolor"></rect>
                <rect x="15.25" y="12.25" width="1.5" height="12.5" rx="0.75" transform="rotate(90 15.25 12.25)" fill="currentcolor"></rect>
                </svg>
              </li>
              <li data-val="2" data-cols="grid--2-col-desktop" class="{% if section.settings.columns_desktop == '2' %}active{% endif %}">
                <svg class="icon-columns" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="6.25" y="2.75" width="1.5" height="12.5" rx="0.75" fill="currentcolor"></rect>
                <rect x="10.25" y="2.75" width="1.5" height="12.5" rx="0.75" fill="currentcolor"></rect>
                </svg>
              </li>
              <li data-val="3" data-cols="grid--3-col-desktop" class="{% if section.settings.columns_desktop == '3' %}active{% endif %}">
                <svg class="icon-columns" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="4.25" y="2.75" width="1.5" height="12.5" rx="0.75" fill="currentcolor"></rect>
                <rect x="8.25" y="2.75" width="1.5" height="12.5" rx="0.75" fill="currentcolor"></rect>
                <rect x="12.25" y="2.75" width="1.5" height="12.5" rx="0.75" fill="currentcolor"></rect>
                </svg>
              </li>
              <li data-val="4" data-cols="grid--4-col-desktop" class="{% if section.settings.columns_desktop == '4' %}active{% endif %}">
                <svg class="icon-columns" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="2.25" y="2.75" width="1.5" height="12.5" rx="0.75" fill="currentcolor"></rect>
                <rect x="6.25" y="2.75" width="1.5" height="12.5" rx="0.75" fill="currentcolor"></rect>
                <rect x="10.25" y="2.75" width="1.5" height="12.5" rx="0.75" fill="currentcolor"></rect>
                <rect x="14.25" y="2.75" width="1.5" height="12.5" rx="0.75" fill="currentcolor"></rect>
                </svg>
              </li>
              <li data-val="5" data-cols="grid--5-col-desktop" class="{% if section.settings.columns_desktop == '5' %}active{% endif %}">
               <svg class="icon-columns" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="0.25" y="2.75" width="1.5" height="12.5" rx="0.75" fill="currentcolor"></rect>
                <rect x="4.25" y="2.75" width="1.5" height="12.5" rx="0.75" fill="currentcolor"></rect>
                <rect x="8.25" y="2.75" width="1.5" height="12.5" rx="0.75" fill="currentcolor"></rect>
                <rect x="12.25" y="2.75" width="1.5" height="12.5" rx="0.75" fill="currentcolor"></rect>
                <rect x="16.25" y="2.75" width="1.5" height="12.5" rx="0.75" fill="currentcolor"></rect>
                </svg>
              </li>
              </ul>
              {% endif %}
               <form class="facets-vertical-form medium-hide small-hide" id="FacetSortForm">
               <div class="facet-filters sorting caption">
                <div class="facet-filters__field">                                    
                    {%- assign sort_by = collection.sort_by | default: collection.default_sort_by -%}
              <div class="sorting dt-sort__container">
                <div class="facet-filters__field">
                  <label>{{ 'products.facets.sort_by_label' | t }}</label>
                  <details class="disclosure-has-popup facets__disclosure facet-filters__sort">                    
                    <summary class="facets__summary">
                       <span class="button button--tertiary button--small facets__summary-label"></span>{% render 'icon-caret' %}   
                    </summary>
                    <div class="facets__display">
                      
                      <ul class="facets__list list-unstyled dt-sort__data" role="list">
                        {%- for option in collection.sort_options -%}
                          <li class="list-menu__item facets__item">
                            <label for="Filter-{{ option.value | escape }}-{{ forloop.index }}" class="facet-checkbox">
                              <input type="radio" name="sort_by" value="{{ option.value | escape }}" id="Filter-{{ option.value | escape }}-{{ forloop.index }}" {% if option.value == sort_by %}checked="checked"{% endif %} class="visually-hidden dt-sort-list" data-value="{{  option.name | escape}}" />
                              <span class="label">{{ option.name | escape }}</span>
                            </label>
                          </li>
                        {%- endfor -%}
                      </ul>
                    </div>
                  </details>                         
              </div>
                  </div>
                </div>
                
              </div>
            </form>
           
          </facet-filters-form>
        {%- endif -%}
  
      {%- paginate collection.products by section.settings.products_per_page -%}
        {%- if collection.products.size == 0 -%}
          <div class="collection collection--empty page-width" id="product-grid" data-id="{{ section.id }}">
            <div class="loading-overlay gradient"></div>
            <div class="title-wrapper center">
              <h2 class="title title--primary">
                {{ 'sections.collection_template.empty' | t }}<br>
                {{ 'sections.collection_template.use_fewer_filters_html' | t: link: collection.url, class: "underlined-link link" }}
              </h2>
            </div>
          </div>
        {%- else -%}
          <div class="collection">
            <div class="loading-overlay gradient"></div>
    {% liquid 
  if  section.settings.columns_desktop == 2
  assign column_width = "grid grid--2-col-desktop  grid--2-col-tablet-down"
  elsif section.settings.columns_desktop == 3
  assign column_width = "grid grid--3-col-desktop  grid--2-col-tablet-down"
  elsif section.settings.columns_desktop == 4
  assign column_width = "grid grid--4-col-desktop  grid--2-col-tablet-down"
  elsif section.settings.columns_desktop == 5
  assign column_width = "grid grid--5-col-desktop  grid--2-col-tablet-down"
  elsif section.settings.columns_desktop == 6
  assign column_width = "grid grid--6-col-desktop  grid--2-col-tablet-down"
  endif 
  %}
            <ul id="product-grid" data-id="{{ section.id }}" class="grid product-grid  view-mode  grid-view-filter  grid--{{ section.settings.columns_mobile }}-col-mobile {{ column_width }} {% unless section.settings.pagination == 'numbers' %} AjaxinateContainer {% endunless %} card_style-{{ settings.card_style }}">
              {%- for product in collection.products -%}
                {%- assign lazy_load = true -%}                
               <li class="grid__item">
       {%  case settings.card_style %}
          {%  when 'standard' %}  
            {% render 'card-product',
              card_product: product,
              media_aspect_ratio: section.settings.image_ratio,
              show_secondary_image: section.settings.show_secondary_image,
              show_vendor: section.settings.show_vendor,
              show_rating: section.settings.show_rating,
              show_quick_add: settings.enable_quickadd,
              show_quick_view: settings.enable_quickview,
              show_new_tag: section.settings.show_new_tag,
              section_id: section.id
            %}
        {%  when 'button_width_icons' %}  
             {% render 'card-product-2',
              card_product: product,
              media_aspect_ratio: section.settings.image_ratio,
              show_secondary_image: section.settings.show_secondary_image,
              show_vendor: section.settings.show_vendor,
              show_rating: section.settings.show_rating,
              show_quick_add: settings.enable_quickadd,
              show_quick_view: settings.enable_quickview,
              show_new_tag: section.settings.show_new_tag,
              section_id: section.id
            %}
        {%  when 'card_with_icons' %} 
             {% render 'card-product-3',
              card_product: product,
              media_aspect_ratio: section.settings.image_ratio,
              show_secondary_image: section.settings.show_secondary_image,
              show_vendor: section.settings.show_vendor,
              show_rating: section.settings.show_rating,
              show_quick_add: settings.enable_quickadd,
              show_quick_view: settings.enable_quickview,
              show_new_tag: section.settings.show_new_tag,
              section_id: section.id
            %}
        {%  when 'card_with_buttons' %}  
             {% render 'card-product-4',
              card_product: product,
              media_aspect_ratio: section.settings.image_ratio,
              show_secondary_image: section.settings.show_secondary_image,
              show_vendor: section.settings.show_vendor,
              show_rating: section.settings.show_rating,
              show_quick_add: settings.enable_quickadd,
              show_quick_view: settings.enable_quickview,
              show_new_tag: section.settings.show_new_tag,
              section_id: section.id
            %}
        {%  when 'card_with_overlay' %}  
             {% render 'card-product-5',
              card_product: product,
              media_aspect_ratio: section.settings.image_ratio,
              show_secondary_image: section.settings.show_secondary_image,
              show_vendor: section.settings.show_vendor,
              show_rating: section.settings.show_rating,
              show_quick_add: settings.enable_quickadd,
              show_quick_view: settings.enable_quickview,
              show_new_tag: section.settings.show_new_tag,
              section_id: section.id
            %}
        {%  endcase %}
                </li>
              {%- endfor -%}
            </ul>
             {% if section.settings.pagination == 'numbers' %}
            {%- if paginate.pages > 1 -%}
              {% render 'pagination', paginate: paginate, anchor: '' %}
            {%- endif -%}
             {% endif %}   
             {% unless section.settings.pagination == 'numbers' %}
            <div id="AjaxinatePagination" class="pagination-method-{{- section.settings.pagination -}}">
            <div class="custom-page-progress-bar" data-page-items-count='{{collection.products_count }}'>
            {%- assign productSize = collection.products.size | plus: paginate.current_offset -%}
            <p>{{ 'general.pagination.count_text' | t:current_count:productSize,total_count:collection.products_count}}</p>
            <div class="progress-bar">
            <div class="active-bar" style="width: {{productSize | times:100.0 | divided_by: collection.products_count}}%"></div>
            </div>
            </div>
            
            {% if paginate.next %}
            <div class="load_more_btn"><a class="dt-sc-btn button text-center" href="{{ paginate.next.url }}">{{ 'general.pagination.load_more' | t}}</a></div>
            {% endif %} 
            
            </div>
            {% endunless %}
          </div>
        {%- endif -%}
      {%- endpaginate -%}
    </div>
  </div>
</div>
</div>

{% schema %}
{
  "name": "t:sections.main-collection-product-grid.name",
  "class": "section",
  "settings": [
    {
      "type": "range",
      "id": "products_per_page",
      "min": 8,
      "max": 24,
      "step": 4,
      "default": 16,
      "label": "t:sections.main-collection-product-grid.settings.products_per_page.label"
    },    
    
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 6,
      "step": 1,
      "default": 4,
      "label": "t:sections.main-collection-product-grid.settings.columns_desktop.label"
    },
    {
      "type": "checkbox",
      "id": "collections_custom_cols",
      "label": "Custom columns",
      "default": true
    },
    {
      "type":"checkbox",
      "id":"page_full_width",
       "default": false,
      "label": "t:sections.all.page_full_width.label"
    },
    {
      "type":"checkbox",
      "id":"page_full_width_spacing",
       "default": false,
      "label": "t:sections.all.page_full_width_spacing.label"
    },
    {
      "type": "header",
      "content": "t:sections.main-collection-product-grid.settings.header__3.content"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.main-collection-product-grid.settings.image_ratio.options__1.label"
        },
        {
          "value": "portrait",
          "label": "t:sections.main-collection-product-grid.settings.image_ratio.options__2.label"
        },
        {
          "value": "square",
          "label": "t:sections.main-collection-product-grid.settings.image_ratio.options__3.label"
        }
      ],
      "default": "adapt",
      "label": "t:sections.main-collection-product-grid.settings.image_ratio.label"
    },
    {
      "type": "checkbox",
      "id": "show_secondary_image",
      "default": false,
      "label": "t:sections.main-collection-product-grid.settings.show_secondary_image.label"
    },
    {
      "type": "checkbox",
      "id": "show_vendor",
      "default": false,
      "label": "t:sections.main-collection-product-grid.settings.show_vendor.label"
    },
    {
      "type": "checkbox",
      "id": "show_new_tag",
      "default": false,
      "label": "t:sections.main-collection-product-grid.settings.show_new_tag.label"
    },
    {
      "type": "checkbox",
      "id": "show_rating",
      "default": false,
      "label": "t:sections.main-collection-product-grid.settings.show_rating.label",
      "info": "t:sections.main-collection-product-grid.settings.show_rating.info"
    },
    {
      "type": "header",
      "content": "t:sections.main-collection-product-grid.settings.header__1.content"
    },
    {
      "type": "checkbox",
      "id": "enable_filtering",
      "default": true,
      "label": "t:sections.main-collection-product-grid.settings.enable_filtering.label",
      "info": "t:sections.main-collection-product-grid.settings.enable_filtering.info"
    },
    {
      "type": "select",
      "id": "filter_type",
      "options": [
        {
          "value": "horizontal",
          "label": "t:sections.main-collection-product-grid.settings.filter_type.options__1.label"
        },
        {
          "value": "vertical",
          "label": "t:sections.main-collection-product-grid.settings.filter_type.options__2.label"
        },
        {
          "value": "drawer",
          "label": "t:sections.main-collection-product-grid.settings.filter_type.options__3.label"
        }
      ],
      "default": "horizontal",
      "label": "t:sections.main-collection-product-grid.settings.filter_type.label",
      "info": "t:sections.main-collection-product-grid.settings.filter_type.info"
    },
    {
      "type": "checkbox",
      "id": "enable_sorting",
      "default": true,
      "label": "t:sections.main-collection-product-grid.settings.enable_sorting.label"
    },
     {
  "type": "select",
  "id": "pagination",
  "label": "t:sections.main-collection-product-grid.settings.pagination.label",
  "default": "numbers",
  "options": [
  {
  "value": "numbers",
  "label": "t:sections.main-collection-product-grid.settings.pagination.numbers.label"
  },
  {
  "value": "loadmore_btn",
   "label": "t:sections.main-collection-product-grid.settings.pagination.loadmore_btn.label"
  },
  {
  "value": "infinit_scroll",
  "label": "t:sections.main-collection-product-grid.settings.pagination.infinit_scroll.label"
  }
  ]
  },
    {
      "type": "header",
      "content": "t:sections.main-collection-product-grid.settings.header_mobile.content"
    },
    {
      "type": "select",
      "id": "columns_mobile",
      "default": "2",
      "label": "t:sections.main-collection-product-grid.settings.columns_mobile.label",
      "options": [
        {
          "value": "1",
          "label": "t:sections.main-collection-product-grid.settings.columns_mobile.options__1.label"
        },
        {
          "value": "2",
          "label": "t:sections.main-collection-product-grid.settings.columns_mobile.options__2.label"
        }
      ]
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
{
"type": "select",
"id": "sidebar_settings",
"options": [
{
"value": "sidebar-left",
"label": "t:sections.main-collection-product-grid.settings.sidebar_settings.options__1.label"
},
{
"value": "sidebar-right",
"label": "t:sections.main-collection-product-grid.settings.sidebar_settings.options__2.label"
},
{
"value": "no-sidebar",
"label": "t:sections.main-collection-product-grid.settings.sidebar_settings.options__3.label"
}
],
"default": "sidebar-left",
"label": "t:sections.main-collection-product-grid.settings.sidebar_settings.label"
},
{
"type": "header",
"content": "t:sections.main-collection-product-grid.settings.sidebar__4.content"
},
{
"type": "checkbox",
"id": "show_menu",
"label": "t:sections.main-collection-product-grid.settings.show_menu.label"
},
{
"type": "text",
"id": "menu_title",
"default": "Heading",
"label": "t:sections.main-collection-product-grid.settings.menu_title.label"
},
{
"type": "link_list",
"id": "linklist",      
"label": "t:sections.main-collection-product-grid.settings.linklist.label"
},
{
"type": "header",
"content": "t:sections.main-collection-product-grid.settings.sidebar__2.content"
},
{
"type": "checkbox",
"id": "show_carousel",
"label": "t:sections.main-collection-product-grid.settings.show_carousel.label"
},
{
"type": "text",
"id": "carousel_title",
"default": "Heading",
"label": "t:sections.main-collection-product-grid.settings.carousel_title.label"
},
{
"type": "collection",
"id": "carousel",
"label": "t:sections.main-collection-product-grid.settings.carousel.label"
},
{
"type": "range",
"id": "carousel_limit",
"min": 1,
"max": 5,
"step": 1,
"label": "t:sections.main-collection-product-grid.settings.carousel_limit.label",
"default": 5
},
{
"type": "header",
"content": "t:sections.main-collection-product-grid.settings.sidebar__1.content"
},
{
"type": "checkbox",
"id": "show_image",
"label": "t:sections.main-collection-product-grid.settings.show_image.label"
},
{
"type": "image_picker",
"id": "sidebar_image",
"label": "t:sections.main-collection-product-grid.settings.sidebar_image.label"
},
{
"type": "text",
"id": "sidebar_title",
"default": "Heading",
"label": "t:sections.main-collection-product-grid.settings.sidebar_title.label"
},
{
"type": "text",
"id": "sidebar_button",
"default": "Shop Now",
"label": "t:sections.main-collection-product-grid.settings.sidebar_button.label"
},
{
"type": "url",
"id": "sidebar_link",
"label": "t:sections.main-collection-product-grid.settings.sidebar_link.label"
},
{
"type": "header",
"content": "t:sections.main-collection-product-grid.settings.sidebar__3.content"
},
{
"type": "checkbox",
"id": "show_collection",
"label": "t:sections.main-collection-product-grid.settings.show_collection.label"
},
{
"type": "text",
"id": "collection_title",
"default": "Heading",
"label": "t:sections.main-collection-product-grid.settings.collection_title.label"
},
{
"type": "collection",
"id": "collection",
"label": "t:sections.main-collection-product-grid.settings.collection.label"
},
{
"type": "range",
"id": "collection_limit",
"min": 1,
"max": 5,
"step": 1,
"label": "t:sections.main-collection-product-grid.settings.collection_limit.label",
"default": 5
}
  ]
}
{% endschema %}