{% unless section.settings.disable_announcement_bar %}
<div class="announcement-bar-wrapper marquee color-{{ section.settings.color_scheme }} gradient  {% if section.settings.disable_announcement_bar_mobile %} hide-mobile {% endif %}">
<div class="marquee__content">
 <div class="marquee_annoucement list-inline">
{%- for block in section.blocks -%}
  {%- case block.type -%}
    {%- when 'announcement' -%}
    
      <div class="announcement-bar " role="region" aria-label="{{ 'sections.header.announcement' | t }}" {{ block.shopify_attributes }}>
        
       {%- if block.settings.text != blank -%}
          {%- if block.settings.link != blank -%}
            <a href="{{ block.settings.link }}" class="announcement-bar__link link link--text focus-inset animate-arrow">
          {%- endif -%}
              <p class="announcement-bar__message h5">
                {{ block.settings.text | escape }}
              </p>
          {%- if block.settings.link != blank -%}
            </a>
          {%- endif -%}
        {%- endif -%}
      </div>
  {%- endcase -%}
{%- endfor -%}
 </div>
   <div class="marquee_annoucement list-inline">
{%- for block in section.blocks -%}
  {%- case block.type -%}
    {%- when 'announcement' -%}
    
      <div class="announcement-bar  " role="region" aria-label="{{ 'sections.header.announcement' | t }}" {{ block.shopify_attributes }}>
        
       {%- if block.settings.text != blank -%}
          {%- if block.settings.link != blank -%}
            <a href="{{ block.settings.link }}" class="announcement-bar__link link link--text focus-inset animate-arrow">
          {%- endif -%}
              <p class="announcement-bar__message h5">
                {{ block.settings.text | escape }}
              </p>
          {%- if block.settings.link != blank -%}
            </a>
          {%- endif -%}
        {%- endif -%}
      </div>
  {%- endcase -%}
{%- endfor -%}
 </div>
</div>
</div>
   {% endunless %}
{%- style -%}

   @media screen and (max-width: 990px) {
  {% if section.settings.disable_announcement_bar_mobile %}
    .announcement-bar-wrapper.hide-mobile{display:none;}
  {% endif %} }

  .marquee:before {
  left: 0;
  background: linear-gradient(to right, white 5%, transparent 100%);
}
.marquee:after {
  right: 0;
  background: linear-gradient(to left, white 5%, transparent 100%);
}
.marquee:before, .marquee:after {
   position: absolute;
  top: 0;
  width: 100px;
  height: 35px;
  content: "";
  z-index: 1;
}

.announcement-bar-wrapper {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    padding: 8px 0;
    border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.08);
} 
.marquee__content {
    display: flex;
    align-items: center;
    white-space: nowrap;
    overflow: hidden;
}  
.marquee_annoucement  {animation: scroll-left 20s linear infinite;display: flex;
    align-items: center;
    justify-content: space-around;
    flex-shrink: 0;
    min-width: 100%;}

.announcement-bar {padding:0 5vw;}
.announcement-bar .announcement-bar__message{margin:0;padding:0} 
@keyframes scroll-left{0%{transform:translateX(0%);}100%{transform:translateX(-100%);}}

body.overflow-hidden #shopify-section-announcement-bar, body.overflow-hidden-mobile #shopify-section-announcement-bar{ z-index: 0;}
  body.overflow-hidden-tablet #shopify-section-announcement-bar { z-index: 0;  display: none;}  
{%- endstyle -%}