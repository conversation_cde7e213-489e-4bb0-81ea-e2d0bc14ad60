<script src="{{ 'slick.min.js' | asset_url }}" async></script>
{% if section.settings.enable_product_zoom == "zoom" %}
<script src="{{ 'jquery.zoom.min.js' | asset_url }}" defer></script>
{% elsif section.settings.enable_product_zoom == "outer" or section.settings.enable_product_zoom == "cloud" %}  
<script src="{{ 'Drift.min.js' | asset_url }}"></script>
  <script src="{{ 'jquery.ez-plus.js' | asset_url }}" defer></script>
{% endif %}
{%- assign file_extension = 'png' -%}
<section id="MainProduct-{{ section.id }}" class="main-product-template" data-section="{{ section.id }}">
  <div class="{% if section.settings.page_full_width %} page-full-width {% else %} page-width {% endif %} {% if section.settings.page_full_width_spacing %} page-full-width_spacing {% endif %}section-{{ section.id }}-padding isolate">
    <div class="row {% for block in section.blocks %}{% case block.type %}{% when 'fbt' %} fbt_hide_primary_cart_btn{% endcase %}{% endfor %}">     
      {{ 'Drift.min.css' | asset_url | stylesheet_tag }}
      {{ 'slick.min.css' | asset_url | stylesheet_tag }}
      {{ 'sticky-cart.css' | asset_url | stylesheet_tag }}
      {{ 'custom.css' | asset_url | stylesheet_tag }}
      {{ 'section-main-product.css' | asset_url | stylesheet_tag }}
      {{ 'component-accordion.css' | asset_url | stylesheet_tag }}
      {{ 'component-price.css' | asset_url | stylesheet_tag }}
      {{ 'component-rte.css' | asset_url | stylesheet_tag }}
      {{ 'component-slider.css' | asset_url | stylesheet_tag }}
      {{ 'component-rating.css' | asset_url | stylesheet_tag }}
      {{ 'component-loading-overlay.css' | asset_url | stylesheet_tag }}
      {{ 'component-deferred-media.css' | asset_url | stylesheet_tag }}
      {{ 'product-detail-page.css' | asset_url | stylesheet_tag }}
      {{ 'component-facets.css' | asset_url | stylesheet_tag }}
      {%- style -%}
        .section-{{ section.id }}-padding {
        padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
        padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
        }

        .adv-product-list .active {
          border: 1px solid;
        }
     
        {% if section.settings.enable_product_zoom != "default" %}
        #ex1 {
          overflow: hidden !important;
          z-index: 9;
          cursor: zoom-in;
        }
        {% endif %}

        @media screen and (min-width: 750px) {
        .section-{{ section.id }}-padding {
        padding-top: {{ section.settings.padding_top }}px;
        padding-bottom: {{ section.settings.padding_bottom }}px;

        }
        }

        .product__info-wrapper .dT_VProdWishList a.add-wishlist:after { content:"{{ 'products.wishlist.addToWishList' | t }}"; position:relative; left: 25px; }
        .product__info-wrapper .dT_VProdWishList a.add-wishlist.adding:after { content:"{{ 'products.wishlist.addingWihlist' | t }}"; }
        .product__info-wrapper .dT_VProdWishList a.add-wishlist.added:after { content:"{{ 'products.wishlist.viewMyWishList' | t }}"; }
        .product__info-wrapper .dT_VProdWishList  a.dt-sc-btn.add-wishlist {    display: block;    }

        .product__info-wrapper .dT_VProdCompareList a.add-compare:after {content:"{{ 'products.compare.add_to_compare' | t }}";  position:relative; left: 25px; font-size: 1.6rem;}
        .product__info-wrapper .dT_VProdCompareList a.add-compare.adding:after { content:"{{ 'products.compare.add_to_compare' | t }}"; }
        .product__info-wrapper .dT_VProdCompareList a.add-compare.added:after { content:"{{ 'products.compare.view_compare' | t }}"; }
        .product__info-wrapper .dT_VProdCompareList  a.dt-sc-btn.add-compare {    display: block;    }
         button.slick-prev.slick-arrow, button.slick-next.slick-arrow {
          width: 78px;
          height: 30px;
          background: var(--gradient-base-accent-2);
          border: none;
          transition: all 0.3s linear;
          cursor: pointer;
          margin-left: 0;
       }
        a.slick-prev.pull-left:before{ 
          content: "";
          color: currentColor;
          position: absolute;
          width: 13px;
          height: 13px;
          border-right: 2px solid currentColor;
          border-bottom: 2px solid currentColor;
          transform: rotate(225deg);
          left: 0;
          right: 0;
          margin: auto;
          top: 17px;  
          cursor: pointer;
        }
        a.slick-next.pull-right:before{
           content: "";
          color: currentColor;
          position: absolute;
          width: 13px;
          height: 13px;
          border-right: 2px solid currentColor;
          border-bottom: 2px solid currentColor;
          transform: rotate(45deg);
          bottom: 11px;
          left: 0;
          right: 0;
          margin: auto;
        }
        button.slick-next.slick-arrow a.slick-next.pull-right, button.slick-prev.slick-arrow a.slick-next.pull-right { cursor: pointer;}
        .product--thumbnail_slider_left .thumbnail-slider .thumbnail-list.slider--tablet-up:not(:hover) .slick-arrow {
        opacity: 0;
        transition: all 0.3s linear;
}
        .main-product_info .thumbnail-slider .thumbnail-list.slider--tablet-up:hover button.slick-prev.slick-arrow:hover,
        .main-product_info .thumbnail-slider .thumbnail-list.slider--tablet-up:hover button.slick-next.slick-arrow:hover{ background: var(--gradient-base-background-2); color: var(--gradient-base-accent-2);}
  
    .product__info-container .product-form__input{padding: 0; border: none; font-weight: normal; display: flex; align-items: center; flex-wrap: wrap; row-gap: 5px;/* min-height: 5rem;*/ line-height: 30px; clear: both;margin-left:0;margin-right:0;}
    .product__info-container .product-form__input { margin: 0 0 20px;}
  {% if section.settings.meta_style == "full-width" %}
    .product__info-container .product-attributes .product-label, .product__info-container .product-form__quantity .form__label, 
    .product__info-container .inventory-form__label label.form__label, .product__info-container .sub-total p.product-label,
    .product__info-container fieldset.product-form__input .form__label, .product__info-container .product-form__input{display:block;}
    .product__info-container .inventory-form__label, .product__info-container .total-price__container, 
    .product__info-container .product-form__input.product-form__quantity  {  display: flex;  flex-direction: column;  align-items: flex-start;}
    .product__info-container fieldset.product-form__input .form__label, .product__info-container .sub-total p.product-label, 
    .product__info-container .inventory-form__label .form__label, 
    .advance-product-style .advanced-title, .product__info-container .product-attributes .product-label  {font-size:16px;font-weight:500;margin-bottom:6px;}
    .product__info-container fieldset.product-form__input .form__label{margin-bottom:3px;min-width:max-content;}
    .product__info-wrapper variant-selects .product-form__input .form__label{font-size:16px;font-weight:400;margin-right:6px;margin-bottom:0;} 
    .product__info-wrapper variant-selects .product-form__input .form__label:after{content:':';margin:0 5px;}  
    .product__info-wrapper .product__info-container .product-form__input.product-form__input--dropdown{display:flex;margin-bottom: 2rem;}
    /* .product__info-wrapper .product-form__input.product-form__input--dropdown:not(:first-child){margin-bottom:3rem;}     */
    .main-product-template .product__info-container .product__sku .product-label,
    .main-product-template .product__info-container .product_vendor .product-label,
    .main-product-template .product__info-container .product_type .product-label{font-size:16px;min-width:auto;margin:0;font-weight:500;position:relative;}
    
    .product__info-container fieldset.product-form__input .form__label span{color:var(--gradient-base-accent-1);}    
    .main-product-template .product__info-container .product__sku ,
    .main-product-template .product__info-container .product_vendor ,
    .main-product-template .product__info-container .product_type {display:flex;align-items:center;line-height: normal;}
    @media screen and (max-width: 450px){      
    .product__info-wrapper variant-selects .product-form__input .form__label{width: 82px;}
    }    
    {% endif %} 
 {% if section.settings.meta_style == "one-half" %}
    .product__info-container .product-attributes .product-label, .product__info-container .product-form__quantity .form__label, 
    .product__info-container .inventory-form__label label.form__label, .product__info-container .sub-total p.product-label,
    .product__info-container fieldset.product-form__input .form__label{ margin: 0; min-width: 140px; display: inline-block; font-size: 16px;letter-spacing: normal;}
    .product__info-container .inventory-form__label, .product__info-container .total-price__container,  
    .product__info-container .product-form__input.product-form__quantity   {  display: flex; flex-direction: row;} 
    .product__info-container fieldset.product-form__input .form__label {
    float: left; margin-right: 10px;
}    
    @media screen and (max-width: 1439px){
      .product__info-container .product-attributes .product-label, .product__info-container .product-form__quantity .form__label, 
    .product__info-container .inventory-form__label label.form__label, .product__info-container .sub-total p.product-label,
    .product__info-container fieldset.product-form__input .form__label{ min-width: 100px;}
    }
           @media screen and (max-width: 750px) {
    .product__info-as-bottom-tabs .dt-sc-tabs-content {display:none;}
    .product__info-as-bottom-tabs .acc__title.active + .dt-sc-tabs-content {display:block !important;}
           }
        
 {% endif %}  



.product-form__input input[type='radio'] + label:hover {
  border-color: rgb(var(--color-foreground));
}

.product-form__input input[type='radio']:checked + label {
  background-color: rgb(var(--color-foreground));
  color: rgb(var(--color-background));
}

.product-form__input input[type='radio']:not(.disabled) + label > .visually-hidden {
  display: none;
}
.product-form__input input[type=radio].disabled+label:after,
.product-form__input input[type=radio]:disabled+label:after{
    content: '';
    position: absolute;
    width: 116%;
    height: 1px;
    background: rgba(var(--color-foreground),.2);
    left: 50%;
    top: 50%;
    transform: translate(-50%,-50%) rotate(-30deg);
}
.product-form__input input[type=radio]:disabled+label, .product-form__input input[type=radio].disabled+label {
    color: rgba(var(--color-foreground),.2);
}
  /* @media screen and (max-width: 749px){
 .main-product_info .slider.slider--mobile .slider__slide {margin-right:10px;}
        } */
        
  {%- endstyle -%}

      <script src="{{ 'product-form.js' | asset_url }}" defer="defer"></script>

      {%- assign first_3d_model = product.media | where: 'media_type', 'model' | first -%}
      {%- if section.settings.gallery_layout == 'thumbnail_slider' %}
        {%- assign thumb_carousel_layout = true -%}
      {%- endif -%}
      {%- if section.settings.thumb_carousel_layout == 'thumbnail_slider_left' or section.settings.thumb_carousel_layout == 'thumbnail_slider_right' %}
        {%- assign thumb_slick_layout = true -%}
      {%- endif -%}
      {%- if first_3d_model -%}
        {{ 'component-product-model.css' | asset_url | stylesheet_tag }}
        <link
          id="ModelViewerStyle"
          rel="stylesheet"
          href="https://cdn.shopify.com/shopifycloud/model-viewer-ui/assets/v1.0/model-viewer-ui.css"
          media="print"
          onload="this.media='all'"
        >
        <link
          id="ModelViewerOverride"
          rel="stylesheet"
          href="{{ 'component-model-viewer-ui.css' | asset_url }}"
          media="print"
          onload="this.media='all'"
        >
      {%- endif -%}
      <div class="facets-vertical {{ section.settings.sidebar_settings }}">
        {%- unless section.settings.sidebar_settings == 'no-sidebar' -%}
          <button class="toggleFilter"> {% render 'icon-filter' %} Filter</button>
          <aside>
            {% render 'main-product-sidebar', show_sticky: section.settings.enable_sticky_info, section: section: block: block %}
          </aside>
        {%- endunless -%}
        <div class="main-product_info product  product--{{ section.settings.media_size }} product--{{ section.settings.gallery_layout }}  {%  if thumb_carousel_layout %}{% if thumb_slick_layout %} media-slick-slider{% endif %} product--{{ section.settings.thumb_carousel_layout }}{% endif %} grid grid--1-col {% if product.media.size > 0 %}grid--2-col-tablet{% else %}product--no-media{% endif %} ">
          <div class="grid__item product__media-wrapper {% if product.metafields.custom.grouped_variant == true  %}show-grouped__variant{% endif %}">
           {%- for block in section.blocks -%}                                 
                  {%- if block.type == 'breadcrumb' and  block.settings.breadcrumb_position == 'left' -%}                  
                  <div class="{{ block.type }}-main-template">
                  {% if collection %}
                  <a href="/" title="{{ 'general.breadcrumbs.home_link_title' | t }}">{{ 'general.breadcrumbs.home' | t }}</a> 
                  <span aria-hidden="true" class="breadcrumb__sep"> 
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M 7.75 1.34375 L 6.25 2.65625 L 14.65625 12 L 6.25 21.34375 L 7.75 22.65625 L 16.75 12.65625 L 17.34375 12 L 16.75 11.34375 Z"></path></svg>
                  </span>
                  {% if collection.handle %}
                  {% capture url %}/collections/{{ collection.handle }}{% endcapture %}
                  {{ collection.title | link_to: url }}  
                  {% endif %}
                  {% else %}
                  {% capture url %}/collections/all{% endcapture %}
                  <a href="{{ url }}">{{ 'general.breadcrumbs.all' | t }}</a>
                  {% endif %}
                  <span aria-hidden="true" class="breadcrumb__sep">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M 7.75 1.34375 L 6.25 2.65625 L 14.65625 12 L 6.25 21.34375 L 7.75 22.65625 L 16.75 12.65625 L 17.34375 12 L 16.75 11.34375 Z"></path></svg>
                  </span>
                  <span>{{ product.title }}</span>        
                  </div>
                {%- endif -%}
                {%  endfor %}
            <media-gallery
              id="MediaGallery-{{ section.id }}"
              role="region"              
              class="product__media-gallery {%  if section.settings.enable_sticky_info %}product__info-container--sticky{% endif %}"              
              aria-label="{{ 'products.product.media.gallery_viewer' | t }}"
              data-desktop-layout="{{ section.settings.gallery_layout }}"
            >
              <div id="GalleryStatus-{{ section.id }}" class="visually-hidden" role="status"></div>
              <slider-component id="GalleryViewer-{{ section.id }}" class="slider-mobile-gutter">
                <a
                  class="skip-to-content-link button visually-hidden quick-add-hidden"
                  href="#ProductInfo-{{ section.id }}"
                >
                  {{ 'accessibility.skip_to_product_info' | t }}
                </a>
                {% if product.metafields.custom.custom_tag != blank %}
                  <div class="ribbon">{{ product.metafields.custom.custom_tag }}</div>
                  {% endif %}
                <ul
                  id="Slider-Gallery-{{ section.id }}"
                  class="product__media-list contains-media grid grid--peek list-unstyled slider slider--mobile {{ section.settings.stacked_column_alignment }}-column"
                  role="list"
                >
                  {%- liquid
                    assign variant_images = product.images | where: 'attached_to_variant?', true | map: 'src'
                    assign media_count = product.media.size
                    if section.settings.hide_variants
                      assign media_count = media_count | minus: variant_images.size | plus: 1
                    endif

                    if section.settings.media_size == 'large'
                      assign media_width = 0.65
                    elsif section.settings.media_size == 'medium'
                      assign media_width = 0.55
                    elsif section.settings.media_size == 'small'
                      assign media_width = 0.45
                    endif
                  -%}
                  {%- if product.selected_or_first_available_variant.featured_media != null -%}
                    {%- assign featured_media = product.selected_or_first_available_variant.featured_media -%}
                    <li
                      id="Slide-{{ section.id }}-{{ featured_media.id }}"
                      class="product__media-item grid__item slider__slide is-active{% if featured_media.media_type != 'image' %} product__media-item--full{% endif %}{% if section.settings.hide_variants and variant_images contains featured_media.src %} product__media-item--variant{% endif %}"
                      data-media-id="{{ section.id }}-{{ featured_media.id }}"
                    >
                      {%- assign media_position = 1 -%}
                      {% render 'product-thumbnail',
                        media: featured_media,
                        position: media_position,
                        loop: section.settings.enable_video_looping,
                        modal_id: section.id,
                        xr_button: true,
                        media_width: media_width,
                        lazy_load: false
                      %}
                    </li>
                  {%- endif -%}
                  {%- for media in product.media -%}
                    {%- unless media.id == product.selected_or_first_available_variant.featured_media.id -%}
                      <li
                        id="Slide-{{ section.id }}-{{ media.id }}"
                        class="product__media-item grid__item slider__slide{% if product.selected_or_first_available_variant.featured_media == null and forloop.index == 1 %} is-active{% endif %}{% if media.media_type != 'image' %} product__media-item--full{% endif %}{% if section.settings.hide_variants and variant_images contains media.src %} product__media-item--variant{% endif %}"
                        data-media-id="{{ section.id }}-{{ media.id }}"
                      >
                        {%- assign media_position = media_position | default: 0 | plus: 1 -%}
                        {% render 'product-thumbnail',
                          media: media,
                          position: media_position,
                          loop: section.settings.enable_video_looping,
                          modal_id: section.id,
                          xr_button: true,
                          media_width: media_width,
                          lazy_load: true
                        %}
                      </li>
                    {%- endunless -%}
                  {%- endfor -%}
                </ul>
                <div class="slider-buttons no-js-hidden quick-add-hidden{% if media_count < 2 or section.settings.mobile_thumbnails == 'show' %} small-hide{% endif %}">
                  <button
                    type="button"
                    class="slider-button slider-button--prev"
                    name="previous"
                    aria-label="{{ 'general.slider.previous_slide' | t }}"
                  >
                    {% render 'icon-caret' %}
                  </button>
                  <div class="slider-counter caption">
                    <span class="slider-counter--current">1</span>
                    <span aria-hidden="true"> / </span>
                    <span class="visually-hidden">{{ 'general.slider.of' | t }}</span>
                    <span class="slider-counter--total">{{ media_count }}</span>
                  </div>
                  <button
                    type="button"
                    class="slider-button slider-button--next"
                    name="next"
                    aria-label="{{ 'general.slider.next_slide' | t }}"
                  >
                    {% render 'icon-caret' %}
                  </button>
                </div>
              </slider-component>
              {%- if first_3d_model -%}
                <button
                  class="button button--full-width product__xr-button"
                  type="button"
                  aria-label="{{ 'products.product.xr_button_label' | t }}"
                  data-shopify-xr
                  data-shopify-model3d-id="{{ first_3d_model.id }}"
                  data-shopify-title="{{ product.title | escape }}"
                  data-shopify-xr-hidden
                >
                  {% render 'icon-3d-model' %}
                  {{ 'products.product.xr_button' | t }}
                </button>
              {%- endif -%}
              {%- if media_count > 1
                and section.settings.gallery_layout != 'stacked'
                or section.settings.mobile_thumbnails == 'show'
              -%}
                <slider-component
                  id="GalleryThumbnails-{{ section.id }}"
                  class="slider-vertical thumbnail-slider slider-mobile-gutter quick-add-hidden{% if section.settings.mobile_thumbnails == 'hide' %} small-hide{% endif %}{% if media_count <= 3 %} thumbnail-slider--no-slide{% endif %}"
                >
                  <button
                    type="button"
                    class="slider-button slider-button--prev{% if media_count <= 3 %} small-hide{% endif %}{% if media_count <= 4 %} medium-hide large-up-hide{% endif %}"
                    name="previous"
                    aria-label="{{ 'general.slider.previous_slide' | t }}"
                    aria-controls="GalleryThumbnails-{{ section.id }}"
                    {% unless section.settings.slider_position == 'bottom' %}
                      data-step="1"
                    {% else %}
                      data-step="3"
                    {% endunless %}
                  >
                    {% render 'icon-caret' %}
                  </button>
                  <ul
                    id="Slider-Thumbnails-{{ section.id }} slider-vert-height"
                    class="thumbnail-list list-unstyled slider slider--mobile{% if section.settings.gallery_layout == 'thumbnail_slider' %} slider--tablet-up{% endif %}"
                  >
                    {%- if featured_media != null -%}
                      {%- liquid
                        capture media_index
                          if featured_media.media_type == 'model'
                            increment model_index
                          elsif featured_media.media_type == 'video' or featured_media.media_type == 'external_video'
                            increment video_index
                          elsif featured_media.media_type == 'image'
                            increment image_index
                          endif
                        endcapture
                        assign media_index = media_index | plus: 1
                      -%}
                      <li
                        id="Slide-Thumbnails-{{ section.id }}-0"
                        class="thumbnail-list__item slider__slide{% if section.settings.hide_variants and variant_images contains featured_media.src %} thumbnail-list_item--variant{% endif %}"
                        data-target="{{ section.id }}-{{ featured_media.id }}"
                        data-media-position="{{ media_index }}"
                      >
                        <button
                          class="thumbnail global-media-settings global-media-settings--no-shadow {% if featured_media.preview_image.aspect_ratio > 1 %}thumbnail--wide{% else %}thumbnail--narrow{% endif %}"
                          aria-label="{%- if featured_media.media_type == 'image' -%}{{ 'products.product.media.load_image' | t: index: media_index }}{%- elsif featured_media.media_type == 'model' -%}{{ 'products.product.media.load_model' | t: index: media_index }}{%- elsif featured_media.media_type == 'video' or featured_media.media_type == 'external_video' -%}{{ 'products.product.media.load_video' | t: index: media_index }}{%- endif -%}"
                          aria-current="true"
                          aria-controls="GalleryViewer-{{ section.id }}"
                          aria-describedby="Thumbnail-{{ section.id }}-0"
                        >
                          <img
                            id="Thumbnail-{{ section.id }}-0"
                            srcset="                              
                              {% if featured_media.preview_image.width >= 324 %}{{ featured_media.preview_image | image_url: width: 324 }} 324w,{% endif %}
                              {% if featured_media.preview_image.width >= 416 %}{{ featured_media.preview_image | image_url: width: 416 }} 416w,{% endif %},
                              {{ featured_media.preview_image | image_url }} {{ media.preview_image.width }}w
                            "
                            src="{{ featured_media | image_url: width: 416 }}"
                            sizes="(min-width: {{ settings.page_width }}px) calc(({{ settings.page_width | minus: 100 | times: media_width | round }} - 4rem) / 2), (min-width: 990px) calc(({{ media_width | times: 100 }}vw - 4rem) / 2), (min-width: 750px) calc((100vw - 15rem) / 1), calc((100vw - 14rem) / 1)"
                            alt="{{ featured_media.alt | escape }}"
                            height="{{ featured_media.height}}"
                            width="{{ featured_media.width}}"
                            loading="lazy"
                          >
                        </button>
                      </li>
                    {%- endif -%}
                    {%- for media in product.media -%}
                      {%- unless media.id == product.selected_or_first_available_variant.featured_media.id -%}
                        {%- liquid
                          capture media_index
                            if media.media_type == 'model'
                              increment model_index
                            elsif media.media_type == 'video' or media.media_type == 'external_video'
                              increment video_index
                            elsif media.media_type == 'image'
                              increment image_index
                            endif
                          endcapture
                          assign media_index = media_index | plus: 1
                        -%}
                        <li
                          id="Slide-Thumbnails-{{ section.id }}-{{ forloop.index }}"
                          class="thumbnail-list__item slider__slide{% if section.settings.hide_variants and variant_images contains media.src %} thumbnail-list_item--variant{% endif %}"
                          data-target="{{ section.id }}-{{ media.id }}"
                          data-media-position="{{ media_index }}"
                        >
                          {%- if media.media_type == 'model' -%}
                            <span class="thumbnail__badge" aria-hidden="true">
                              {%- render 'icon-3d-model' -%}
                            </span>
                          {%- elsif media.media_type == 'video' or media.media_type == 'external_video' -%}
                            <span class="thumbnail__badge" aria-hidden="true">
                              {%- render 'icon-play' -%}
                            </span>
                          {%- endif -%}
                          <button
                            class="thumbnail global-media-settings global-media-settings--no-shadow {% if media.preview_image.aspect_ratio > 1 %}thumbnail--wide{% else %}thumbnail--narrow{% endif %}"
                            aria-label="{%- if media.media_type == 'image' -%}{{ 'products.product.media.load_image' | t: index: media_index }}{%- elsif media.media_type == 'model' -%}{{ 'products.product.media.load_model' | t: index: media_index }}{%- elsif media.media_type == 'video' or media.media_type == 'external_video' -%}{{ 'products.product.media.load_video' | t: index: media_index }}{%- endif -%}"
                            {% if media == product.selected_or_first_available_variant.featured_media
                              or product.selected_or_first_available_variant.featured_media == null
                              and forloop.index == 1
                            %}
                              aria-current="true"
                            {% endif %}
                            aria-controls="GalleryViewer-{{ section.id }}"
                            aria-describedby="Thumbnail-{{ section.id }}-{{ forloop.index }}"
                          >
                            <img
                              id="Thumbnail-{{ section.id }}-{{ forloop.index }}"
                              srcset="                                
                                {% if media.preview_image.width >= 130 %}{{ media.preview_image | image_url: width: 130 }} 130w,{% endif %}
                                {% if media.preview_image.width >= 260 %}{{ media.preview_image | image_url: width: 260 }} 260w{% endif %}
                              "
                              src="{{ media | image_url: width: 84, height: 84 }}"
                              sizes="(min-width: 1200px) calc((1200px - 19.5rem) / 2), (min-width: 750px) calc((100vw - 16.5rem) / 2), calc((100vw - 8rem) / 2)"
                              alt="{{ media.alt | escape }}"
                              height="200"
                              width="200"
                              loading="lazy"
                            >
                          </button>
                        </li>
                      {%- endunless -%}
                    {%- endfor -%}
                  </ul>
                
                  <button
                    type="button"
                    class="slider-button slider-button--next{% if media_count <= 3 %} small-hide{% endif %}{% if media_count <= 4 %} medium-hide large-up-hide{% endif %}"
                    name="next"
                    aria-label="{{ 'general.slider.next_slide' | t }}"
                    aria-controls="GalleryThumbnails-{{ section.id }}"
                    {% unless section.settings.slider_position == 'bottom' %}
                      data-step="1"
                    {% else %}
                      data-step="3"
                    {% endunless %}
                  >
                    {% render 'icon-caret' %}
                  </button>
                </slider-component>
              {%- endif -%}
            </media-gallery>             
          </div>
          <div class="product__info-wrapper grid__item{% if settings.page_width > 1400 and section.settings.media_size == "small" %} product__info-wrapper--extra-padding{% endif %}">
            <div
              id="ProductInfo-{{ section.id }}"
              class="product__info-container {% if section.settings.enable_sticky_info %} product__info-container--sticky{% endif %}"
            >
                 
           {% if section.settings.prev_next_products %}
             <div class="prev-next-product-navigation">
              {% if collection.previous_product %} 
              <a class="prev-product" href="{{collection.previous_product.url}}" title="Previous product">Previous{{ collection.previous_product.featured_media | image_url: width: 100 | image_tag: loading: 'lazy' }}</a>
             
              <div class="product-main">
                <?xml version="1.0" encoding="utf-8"?>
                <svg width="20px" height="20px" viewBox="0 0 21 21" xmlns="http://www.w3.org/2000/svg">
                <path d="m12.5 3.5-4 14" fill="none" stroke="currentcolor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>
              </div>  
                {% endif %} 
              {% if collection.next_product %}              
              <a class="next-product" href="{{ collection.next_product.url }}" title="Next product">Next {{ collection.next_product.featured_media | image_url: width: 100 | image_tag: loading: 'lazy' }}</a>              
              {% endif %}
               </div>
            {% endif %}
             {%- for block in section.blocks -%}                                 
                  {%- if block.type == 'breadcrumb' and  block.settings.breadcrumb_position == 'right' -%}                  
                  <div class="{{ block.type }}-main-template">
                  {% if collection %}
                  <a href="/" title="{{ 'general.breadcrumbs.home_link_title' | t }}">{{ 'general.breadcrumbs.home' | t }}</a> 
                  <span aria-hidden="true" class="breadcrumb__sep"> 
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M 7.75 1.34375 L 6.25 2.65625 L 14.65625 12 L 6.25 21.34375 L 7.75 22.65625 L 16.75 12.65625 L 17.34375 12 L 16.75 11.34375 Z"></path></svg>
                  </span>
                  {% if collection.handle %}
                  {% capture url %}/collections/{{ collection.handle }}{% endcapture %}
                  {{ collection.title | link_to: url }}  
                  {% endif %}
                  {% else %}
                  {% capture url %}/collections/all{% endcapture %}
                  <a href="{{ url }}">{{ 'general.breadcrumbs.all' | t }}</a>
                  {% endif %}
                  <span aria-hidden="true" class="breadcrumb__sep">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M 7.75 1.34375 L 6.25 2.65625 L 14.65625 12 L 6.25 21.34375 L 7.75 22.65625 L 16.75 12.65625 L 17.34375 12 L 16.75 11.34375 Z"></path></svg>
                  </span>
                  <span>{{ product.title }}</span>        
                  </div>
                {%- endif -%}
                {%  endfor %}   


              <div class="dt-zoom-detail"></div>
              {%- assign product_form_id = 'product-form-' | append: section.id -%}
              {%- for block in section.blocks -%}                                 
                {%- case block.type -%}                           
                  {%- when '@app' -%}
                    {% render block %}
                  {%- when 'text' -%}
                    <p
                      class="product__text{% if block.settings.text_style == 'uppercase' %} caption-with-letter-spacing{% elsif block.settings.text_style == 'subtitle' %} subtitle{% endif %}"
                      {{ block.shopify_attributes }}
                    >
                      {{- block.settings.text -}}
                    </p>
                  {%- when 'title' -%}
                    <div class="product__title" {{ block.shopify_attributes }}>
                      <h1>{{ product.title | escape }}</h1>
                      <a href="{{ product.url }}" class="product__title">
                        <h2 class="h1">
                          {{ product.title | escape }}
                        </h2>
                      </a>
                    </div>
                    <div class="rating-fake-sales">
                     {%- when 'rating' -%}
                    {%- if product.metafields.reviews.rating.value != blank -%}
                      {% liquid
                        assign rating_decimal = 0
                        assign decimal = product.metafields.reviews.rating.value.rating | modulo: 1
                        if decimal >= 0.3 and decimal <= 0.7
                          assign rating_decimal = 0.5
                        elsif decimal > 0.7
                          assign rating_decimal = 1
                        endif
                      %}
                      <div
                        class="rating"
                        role="img"
                        aria-label="{{ 'accessibility.star_reviews_info' | t: rating_value: product.metafields.reviews.rating.value, rating_max: product.metafields.reviews.rating.value.scale_max }}"
                      >
                        <span
                          aria-hidden="true"
                          class="rating-star color-icon-{{ settings.accent_icons }}"
                          style="--rating: {{ product.metafields.reviews.rating.value.rating | floor }}; --rating-max: {{ product.metafields.reviews.rating.value.scale_max }}; --rating-decimal: {{ rating_decimal }};"
                        ></span>
                      </div>
                      <p class="rating-text caption">
                        <span aria-hidden="true">
                          {{- product.metafields.reviews.rating.value }} /
                          {{ product.metafields.reviews.rating.value.scale_max -}}
                        </span>
                      </p>
                      <p class="rating-count caption">
                        <span aria-hidden="true">({{ product.metafields.reviews.rating_count }})</span>
                        <span class="visually-hidden">
                          {{- product.metafields.reviews.rating_count }}
                          {{ 'accessibility.total_reviews' | t -}}
                        </span>
                      </p>
                    {%- endif -%}
                     {%- when 'fake_sales' -%}
                    {% render 'social-proof-sales', block: block %}  
                  </div>
                 
                  {%- when 'price' -%}
                    <div class="no-js-hidden price-wrapper" id="price-{{ section.id }}" role="status" {{ block.shopify_attributes }}>
                      {%- render 'price',
                        product: product,
                        use_variant: true,
                        show_badges: true,
                        price_class: 'price--large'
                      -%}
                    </div>
                    {%- if shop.taxes_included or shop.shipping_policy.body != blank -%}
                      <div class="product__tax caption rte">
                        {%- if shop.taxes_included -%}
                          {{ 'products.product.include_taxes' | t }}
                        {%- endif -%}
                        {%- if shop.shipping_policy.body != blank -%}
                          {{ 'products.product.shipping_policy_html' | t: link: shop.shipping_policy.url }}
                        {%- endif -%}
                      </div>
                    {%- endif -%}
                    <div {{ block.shopify_attributes }}>
                      {%- assign product_form_installment_id = 'product-form-installment-' | append: section.id -%}
                      {%- form 'product', product, id: product_form_installment_id, class: 'installment caption-large' -%}
                        <input type="hidden" name="id" value="{{ product.selected_or_first_available_variant.id }}">
                        {{ form | payment_terms }}
                      {%- endform -%}
                    </div>
                  {%- when 'custom_liquid' -%}
                    {{ block.settings.custom_liquid }}
                   {%- when 'vendor' -%}
                <div class="product-attributes product_vendor">
                  <p class="product-label">{{ 'products.product.vendor' | t }}</p>
                  <span class="product-attributes-value">{{ product.vendor | link_to_vendor }}</span>
                </div>
                
                {%- when 'sku' -%}              
               <div class="product__sku product-attributes no-js-hidden{% if product.selected_or_first_available_variant.sku.size == 0 %} visibility-hidden{% endif %}" id="Sku-{{ section.id }}" role="status" {{ block.shopify_attributes }}>
                <p class="product-label">{{ 'products.product.sku' | t }}</p>
                 <span class="product-attributes-value"> {{- product.selected_or_first_available_variant.sku -}}</span>
              </div>
                {%- when 'type' -%}
                <div class="product-attributes product_type">
                  <p class="product-label">{{ 'products.product.type' | t }}</p> <span class="product-attributes-value">{{ product.type }}</span>
                </div>
                   {%- when 'deal' -%}
                    {% render 'deal-snippet', product: product %}
                   <div class="advance-product-style">
              {%- assign choose_style_metafields =  product.metafields.my_fields.choose_style.value -%}
              {% if choose_style_metafields != blank %}
              {% if section.settings.show_adv_product %}
                {% if section.settings.choose_label != blank %}
                <p class="advanced-title">{{ section.settings.choose_label }}</p>
                {% endif %}
                <div data-section-type="advanced-product-link-section">
                  <div class="dT_vDynamicPWrap-{{ section.id }}-block-{{ block.id}} dT_vProdWrap">
                    <ul class="adv-product-list">
                      {% for pro in choose_style_metafields limit: 3 %}
                      <li class="{% if pro.handle == product.handle %}active {% endif %}">
                        <a href="{{ pro.url }}">
                          {{ pro.featured_image | image_url: width: 400 | image_tag: loading: 'lazy' }}
                          <span>{{ pro.title}}</span>
                        </a>
                      </li>
                      {% endfor %}
                    </ul>
                  </div>
                </div>
                {% endif %}
                {% endif %}
                </div>
                {%- when 'variant_picker' -%}
                                      {%- liquid
                      assign variants_available_arr = product.variants | map: 'available'
                      assign variants_option1_arr = product.variants | map: 'option1'
                      assign variants_option2_arr = product.variants | map: 'option2'
                      assign variants_option3_arr = product.variants | map: 'option3'
                    
                      assign product_form_id = 'product-form-' | append: section.id
                    -%}

  
                    {%- unless product.has_only_default_variant -%}
                      {%- if block.settings.picker_type == 'button' -%}
                        <variant-radios
                        id="variant-radios-{{ section.id }}"
                        class="no-js-hidden"
                        data-section="{{ section.id }}"
                        data-url="{{ product.url }}"
                        {% if update_url == false %}
                          data-update-url="false"
                        {% endif %}
                        {{ block.shopify_attributes }}
                        >
                        
                          {%- for option in product.options_with_values -%}
                             {%- liquid
                                    assign option_disabled = true
                                
                                    for option1_name in variants_option1_arr
                                      case option.position
                                        when 1
                                          if variants_option1_arr[forloop.index0] == value and variants_available_arr[forloop.index0]
                                            assign option_disabled = false
                                          endif
                                        when 2
                                          if option1_name == product.selected_or_first_available_variant.option1 and variants_option2_arr[forloop.index0] == value and variants_available_arr[forloop.index0]
                                            assign option_disabled = false
                                          endif
                                        when 3
                                          if option1_name == product.selected_or_first_available_variant.option1 and variants_option2_arr[forloop.index0] == product.selected_or_first_available_variant.option2 and variants_option3_arr[forloop.index0] == value and variants_available_arr[forloop.index0]
                                            assign option_disabled = false
                                          endif
                                      endcase
                                    endfor
                                  -%}
                            {% assign option_index = forloop.index0 %}
                            <fieldset class="js product-form__input option-name-{{ option.name | downcase }}">   
                              <div class="swatch-label-text">
                                <legend class="form__label">{{ option.name }}: <span class="append-options-values">{{ option.selected_value }}</span>
                                </legend>
                            </div>
                               {% if block.settings.picker_method == 'variant_image' or block.settings.picker_method == 'custom_color' %}<div class="swatch-group">{% endif %}                            
                            {% if  option.name  == 'Size' %}                             
                                <div class="varient-model-wrapper">                              
                                <div class="varient-class">
                                {%- for value in option.values -%}                                
                                <input
                                type="radio"
                                id="{{ section.id }}-{{ option.position }}-{{ forloop.index0 }}"
                                name="{{ option.name }}"
                                value="{{ value | escape }}"
                                form="{{ product_form_id }}"                                
                                class="{% if variant.available %} available {% else %} unavailable {% endif %}" {% if option.selected_value == value %}checked{% endif %}
                                 {% if option_disabled %}
                                 class="disabled"
                                 {% endif %}>                                
                                {% if option.name == 'Color' %}                                
                                {%- if block.settings.picker_method == 'custom_color' -%}
                                  {%- liquid
                                  assign colorName = value | handle            
                                  assign colorFile = value | handle | append: '.' | append: file_extension
                                  assign colorFileURL = colorFile | file_img_url: '50x50' | prepend: 'https:' | split: '?' | first                
                                   -%}
                                <div data-value="{{ value | escape }}" class="swatch-element">  
                                <div class="tooltip x">
                                <label for="{{ section.id }}-{{ option.position }}-{{ forloop.index0 }}" class="swatch-variant-text">{{ value | handle }}</label>
                                </div>
                                 <label
                                for="{{ section.id }}-{{ option.position }}-{{ forloop.index0 }}"
                                class="swatch-variant-color bg-color-{{ value | handle }}"                                
                                style="background-size: cover;background-color: {{- colorName -}}; background-image: url({{ colorFileURL }}); background-repeat: no-repeat;">
                                </label>
                                </div> 
                                {%- else block.settings.picker_method == 'variant_image' -%} 
                                
                                <div data-value="{{ value | escape }}" class="swatch-element">
                                <div class="tooltip">
                                <label
                                for="{{ section.id }}-{{ option.position }}-{{ forloop.index0 }}"
                                class="swatch-variant-text"
                                >{{ value | escape }}</label>
                                </div>
                                <label
                                for="{{ section.id }}-{{ option.position }}-{{ forloop.index0 }}"
                                class="swatch-variant-image "
                                style="background-image: url('{{ product.variants[forloop.index0].image.src  | product_img_url: '460x' }}');"
                                ></label>
                                </div> 
                                {%- endif -%}
                                {% else %}
                                <label for="{{ section.id }}-{{ option.position }}-{{ forloop.index0 }}">
                                {{ value }}
                                </label>
                                
                                {% endif %}
                                
                                {%- endfor -%}
                                </div>
                               {%- for block in section.blocks -%}
                                {%- case block.type -%}
                                {%- when 'size-popup' -%}
                                 <div class="size-chart"> 
                                <svg xmlns="http://www.w3.org/2000/svg" class="popup-svg" width="16.432" height="28.2" viewBox="0 0 16.432 28.2">
                                <g id="Component_46_1" data-name="Component 46 – 1" transform="translate(0.6 0.6)">
                                <rect id="Rectangle_8800" data-name="Rectangle 8800" width="8" height="27" rx="1" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.2"/>
                                <line id="Line_302" data-name="Line 302" x2="3" transform="translate(5 3.5)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.2"/>
                                <line id="Line_303" data-name="Line 303" x2="2" transform="translate(6 5.5)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.2"/>
                                <line id="Line_304" data-name="Line 304" x2="3" transform="translate(5 7.5)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.2"/>
                                <line id="Line_305" data-name="Line 305" x2="2" transform="translate(6 9.5)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.2"/>
                                <line id="Line_306" data-name="Line 306" x2="3" transform="translate(5 11.5)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.2"/>
                                <line id="Line_307" data-name="Line 307" x2="2" transform="translate(6 13.5)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.2"/>
                                <line id="Line_308" data-name="Line 308" x2="3" transform="translate(5 15.5)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.2"/>
                                <line id="Line_309" data-name="Line 309" x2="2" transform="translate(6 17.5)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.2"/>
                                <line id="Line_310" data-name="Line 310" x2="3" transform="translate(5 19.5)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.2"/>
                                <line id="Line_311" data-name="Line 311" x2="2" transform="translate(6 21.5)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.2"/>
                                <line id="Line_312" data-name="Line 312" x2="3" transform="translate(5 23.5)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.2"/>
                                <path id="Path_54441" data-name="Path 54441" d="M11.5,3.5l2-3,2,3" transform="translate(-0.5 -0.5)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.2"/>
                                <path id="Path_54442" data-name="Path 54442" d="M11.5,24.5l2,3,2-3" transform="translate(-0.5 -0.5)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.2"/>
                                <line id="Line_313" data-name="Line 313" y2="24" transform="translate(13 2)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.2"/>
                                </g>
                                </svg> 
                                <modal-opener
                                class="product-popup-modal__opener no-js-hidden quick-add-hidden"
                                data-modal="#PopupModal-{{ block.id }}"
                                {{ block.shopify_attributes }}
                                >
                                <button
                                id="ProductPopup-{{ block.id }}"
                                class="product-popup-modal__button link"
                                type="button"
                                aria-haspopup="dialog"
                                >
                                {{ block.settings.text | default: block.settings.page.title }}
                                </button>
                                </modal-opener>
                                <a href="{{ block.settings.page.url }}" class="product-popup-modal__button link no-js">
                                {{- block.settings.text -}}
                                </a>
                                {% assign popups = section.blocks | where: 'type', 'size-popup' %}
                                {%- for block in popups -%}
                                <modal-dialog
                                id="PopupModal-{{ block.id }}"
                                class="product-popup-modal"
                                {{ block.shopify_attributes }}
                                >
                                <div
                                role="dialog"
                                aria-label="{{ block.settings.text }}"
                                aria-modal="true"
                                class="product-popup-modal__content"
                                tabindex="-1"
                                >
                                <button
                                id="ModalClose-{{ block.id }}"
                                type="button"
                                class="product-popup-modal__toggle"
                                aria-label="{{ 'accessibility.close' | t }}"
                                >
                                {% render 'icon-close' %}
                                </button>
                                <div class="product-popup-modal__content-info">
                                <h1 class="h2">{{ block.settings.page.title }}</h1>
                                {{ block.settings.page.content }}
                                </div>
                                </div>
                                </modal-dialog>
                                  </div> 
                                {%- endfor -%}
                                {%- endcase -%}
                                {%- endfor -%}
                                </div>  
                                {% else %}
                                
                                {%- for value in option.values -%}
                                <input
                                type="radio"
                                id="{{ section.id }}-{{ option.position }}-{{ forloop.index0 }}"
                                name="{{ option.name }}"
                                value="{{ value | escape }}"
                                form="{{ product_form_id }}"
                                class="{% if variant.available %} available {% else %} unavailable {% endif %}" {% if option.selected_value == value %}checked{% endif %}>
                                
                                {% if option.name == 'Color' %}
                                {%- liquid
                                  assign colorName = value | handle            
                                  assign colorFile = value | handle | append: '.' | append: file_extension
                                  assign colorFileURL = colorFile | file_img_url: '50x50' | prepend: 'https:' | split: '?' | first                
                                   -%}
                                {%- if block.settings.picker_method == 'custom_color' -%}
                                 <div data-value="{{ value | escape }}" class="swatch-element">  
                                <div class="tooltip">
                                <label for="{{ section.id }}-{{ option.position }}-{{ forloop.index0 }}" class="swatch-variant-text">{{ value | escape }}</label>
                                </div>
                                  
                                 <label
                                for="{{ section.id }}-{{ option.position }}-{{ forloop.index0 }}"
                                class="swatch-variant-color bg-color-{{ value | handle }}"                                
                                 style="background-size: cover;background-color: {{- colorName -}}; background-image: url({{ colorFileURL }}); background-repeat: no-repeat;"> 
                                </label>
                                </div> 
                                {%- else block.settings.picker_method == 'variant_image' -%} 
                                
                                <div data-value="{{ value | escape }}" class="swatch-element">
                                <div class="tooltip">
                                <label
                                for="{{ section.id }}-{{ option.position }}-{{ forloop.index0 }}"
                                class="swatch-variant-text"
                                >{{ value | escape }}</label>
                                </div>
                                <label
                                for="{{ section.id }}-{{ option.position }}-{{ forloop.index0 }}"
                                class="swatch-variant-image "
                                style="background-image: url('{{ product.variants[forloop.index0].image.src  | product_img_url: '460x' }}');"
                                ></label>
                                </div> 
                                {%- endif -%}
                                {% else %}
                                <label for="{{ section.id }}-{{ option.position }}-{{ forloop.index0 }}">
                                {{ value }}
                                </label>                                
                                {% endif %}                                
                                {%- endfor -%}                       
                            {% endif %}                        
                             {%- if block.settings.picker_method == 'variant_image' or block.settings.picker_method == 'custom_color' -%}</div>{% endif %}
                           </fieldset>
                          {%- endfor -%}
                          <script type="application/json">
                            {{ product.variants | json }}
                          </script>
                        </variant-radios>
                      {%- else -%}
                        <variant-selects
                          class="no-js-hidden"
                          data-section="{{ section.id }}"
                          data-url="{{ product.url }}"
                          {{ block.shopify_attributes }}
                        >
                          {%- for option in product.options_with_values -%}
                            <div class="product-form__input product-form__input--dropdown">
                              <label class="form__label" for="Option-{{ section.id }}-{{ forloop.index0 }}">
                                {{ option.name }}
                              </label>
                              <div class="select">
                                <select
                                  id="Option-{{ section.id }}-{{ forloop.index0 }}"
                                  class="select__select"
                                  name="options[{{ option.name | escape }}]"
                                  form="{{ product_form_id }}"
                                >
                                  {%- for value in option.values -%}
                                    <option
                                      value="{{ value | escape }}"
                                      {% if option.selected_value == value %}
                                        selected="selected"
                                      {% endif %}
                                    >
                                      {{ value }}
                                    </option>
                                  {%- endfor -%}
                                </select>
                                {% render 'icon-caret' %}
                              </div>
                            </div>
                          {%- endfor -%}

                          <script type="application/json">
                            {{ product.variants | json }}
                          </script>
                        </variant-selects>
                      {%- endif -%}
                    {%- endunless -%}
                 
                 {%- when 'inventory' -%}
                    <div
                class="product__inventory no-js-hidden inventory-form__label {% if block.settings.text_style == 'uppercase' %} caption-with-letter-spacing{% elsif block.settings.text_style == 'subtitle' %} subtitle{% endif %}{% if product.selected_or_first_available_variant.inventory_management != 'shopify' %} visibility-hidden{% endif %}"
                {{ block.shopify_attributes }}
                id="Inventory-{{ section.id }}"
                role="status"
              >
                       <!-- <label class="form__label">{{ 'products.product.stock_status' | t }}</label> -->
                      <p>
                {%- if product.selected_or_first_available_variant.inventory_management == 'shopify' -%}
                  {%- if product.selected_or_first_available_variant.inventory_quantity > 0 -%}
                    {%- if product.selected_or_first_available_variant.inventory_quantity <= block.settings.inventory_threshold -%}
                      <!-- <span class="stock-availablity low-stock"></span> -->
                      <span class="low_stock">
                      {%- if block.settings.show_inventory_quantity -%}
                        {{- 'products.product.inventory_low_stock_show_count' | t: quantity: product.selected_or_first_available_variant.inventory_quantity -}}
                      {%- else -%}
                        {{- 'products.product.inventory_low_stock' | t -}}
                      {%- endif -%}
                      </span>
                    {%- else -%}
                      <!-- <span class="stock-availablity in-stock"></span>     -->
                      <span class="in_stock">
                      {%- if block.settings.show_inventory_quantity -%}
                        {{- 'products.product.inventory_in_stock_show_count' | t: quantity: product.selected_or_first_available_variant.inventory_quantity -}}
                      {%- else -%}
                          {{- 'products.product.inventory_in_stock' | t -}}
                      {%- endif -%}
                      </span>
                    {%- endif -%}
                  {%- else -%}
                    <!-- <span class="stock-availablity out_of_stock"></span> -->
                    <span class="out_of_stock">
                    {%- if product.selected_or_first_available_variant.inventory_policy == 'continue' -%}
                      {{- 'products.product.inventory_out_of_stock_continue_selling' | t -}}
                    {%- else -%}
                      {{- 'products.product.inventory_out_of_stock' | t -}}
                    {%- endif -%}
                    </span>
                  {%- endif -%}
                {%- endif -%}
                        </p>
                    </div>
                  
                  {%- when 'sub_total' -%}
                {% assign current_variant = product.selected_or_first_available_variant %}
                 <div class="no-js-hidden sub-total" id="subtotal-{{ section.id }}" role="status" {{ block.shopify_attributes }}>
                   <div class="total-price__container">
                  <p class="product-label"> {{ 'products.product.total' | t }}</p> 
                  <span id="subtotal-value">{{ current_variant.price | money }}</span>
                  </div>
                </div>
                  {%- when 'popup' -%}
                            <modal-opener
                            class="product-popup-modal__opener no-js-hidden quick-add-hidden"
                            data-modal="#PopupModal-{{ block.id }}"
                            {{ block.shopify_attributes }}
                            >
                            <button
                            id="ProductPopup-{{ block.id }}"
                            class="product-popup-modal__button link"
                            type="button"
                            aria-haspopup="dialog"
                            >
                            {{ block.settings.text | default: block.settings.page.title }}
                            </button>
                            </modal-opener>
                            <a href="{{ block.settings.page.url }}" class="product-popup-modal__button link no-js">
                            {{- block.settings.text -}}
                            </a>
                            {% assign popups = section.blocks | where: 'type', 'popup' %}
                                {%- for block in popups -%}
                                <modal-dialog
                                id="PopupModal-{{ block.id }}"
                                class="product-popup-modal"
                                {{ block.shopify_attributes }}
                                >
                                <div
                                role="dialog"
                                aria-label="{{ block.settings.text }}"
                                aria-modal="true"
                                class="product-popup-modal__content"
                                tabindex="-1"
                                >
                                <button
                                id="ModalClose-{{ block.id }}"
                                type="button"
                                class="product-popup-modal__toggle"
                                aria-label="{{ 'accessibility.close' | t }}"
                                >
                                {% render 'icon-close' %}
                                </button>
                                <div class="product-popup-modal__content-info">
                                <h1 class="h3">{{ block.settings.page.title }}</h1>
                                {{ block.settings.page.content }}
                                </div>
                                </div>
                                </modal-dialog>
                                {%- endfor -%}
                 
                  
                {%- endcase -%}
                {%- endfor -%}
                   
                    <noscript class="product-form__noscript-wrapper-{{ section.id }}">
                      <div class="product-form__input{% if product.has_only_default_variant %} hidden{% endif %}">
                        <label class="form__label" for="Variants-{{ section.id }}">
                          {{- 'products.product.product_variants' | t -}}
                        </label>
                        <div class="select">
                          <select
                            name="id"
                            id="Variants-{{ section.id }}"
                            class="select__select"
                            form="{{ product_form_id }}"
                          >
                            {%- for variant in product.variants -%}
                              <option
                                {% if variant == product.selected_or_first_available_variant %}
                                  selected="selected"
                                {% endif %}
                                {% if variant.available == false %}
                                  disabled
                                {% endif %}
                                value="{{ variant.id }}"
                              >
                                {{ variant.title }}
                                {%- if variant.available == false %} - {{ 'products.product.sold_out' | t }}{% endif %}
                                - {{ variant.price | money | strip_html }}
                              </option>
                            {%- endfor -%}
                          </select>
                          {% render 'icon-caret' %}
                        </div>
                      </div>
                    </noscript>
              {%- for block in section.blocks -%}
                {%- case block.type -%}
                  {%- when 'buy_buttons' -%}                  
                    <div {{ block.shopify_attributes }}>
              {%- liquid 
                    assign gift_card_recipient_feature_active = false
                    if block.settings.show_gift_card_recipient and product.gift_card?
                      assign gift_card_recipient_feature_active = true
                    endif
              %}       
                      <product-form class="product-form" data-hide-errors="{{ gift_card_recipient_feature_active }}">
                        <div class="product-form__error-message-wrapper" role="alert" hidden>
                          <svg
                            aria-hidden="true"
                            focusable="false"
                            role="presentation"
                            class="icon icon-error"
                            viewBox="0 0 13 13"
                          >
                            <circle cx="6.5" cy="6.50049" r="5.5" stroke="white" stroke-width="2"/>
                            <circle cx="6.5" cy="6.5" r="5.5" fill="#EB001B" stroke="#EB001B" stroke-width="0.7"/>
                            <path d="M5.87413 3.52832L5.97439 7.57216H7.02713L7.12739 3.52832H5.87413ZM6.50076 9.66091C6.88091 9.66091 7.18169 9.37267 7.18169 9.00504C7.18169 8.63742 6.88091 8.34917 6.50076 8.34917C6.12061 8.34917 5.81982 8.63742 5.81982 9.00504C5.81982 9.37267 6.12061 9.66091 6.50076 9.66091Z" fill="white"/>
                            <path d="M5.87413 3.17832H5.51535L5.52424 3.537L5.6245 7.58083L5.63296 7.92216H5.97439H7.02713H7.36856L7.37702 7.58083L7.47728 3.537L7.48617 3.17832H7.12739H5.87413ZM6.50076 10.0109C7.06121 10.0109 7.5317 9.57872 7.5317 9.00504C7.5317 8.43137 7.06121 7.99918 6.50076 7.99918C5.94031 7.99918 5.46982 8.43137 5.46982 9.00504C5.46982 9.57872 5.94031 10.0109 6.50076 10.0109Z" fill="white" stroke="#EB001B" stroke-width="0.7">
                          </svg>
                          <span class="product-form__error-message"></span>
                        </div>
                       
                         {%- if gift_card_recipient_feature_active -%}
                        {%- render 'gift-card-recipient-form', product: product, form: form, section: section -%}
                      {%- endif -%}
                  
                       <div class="icon-with-text">   
                          {% if section.settings.show_wishlist %}
                          <div class="dT_VProdWishList">
                          {%- if product.handle != blank -%}
                          <div>
                          <dtx-wishlist
                          > <a
                          href="javascript:void(0);"
                          class=" add-wishlist button--secondary"
                          data-product_handle="{{ product.handle }}"
                          ></a
                          ></dtx-wishlist>
                          </div>
                          {%- endif -%}
                          </div>
                          {% endif %}
                          <div class="dT_VProdCompareList">
                          {%- if product.handle != blank -%}
                          <div><dtx-compare><a href="javascript:void(0);" class="add-compare button--secondary" data-product_handle="{{ product.handle }}"></a></dtx-compare></div>
                          {%- endif -%}                  
                          </div>
                        {%  if section.settings.show_share %}
                       {% assign share_url = product.selected_variant.url | default: product.url | prepend: request.origin %}                        
                        {%  render 'product-share-button', share_url: share_url %}
                           {%- endif -%}         
                       </div>
                     {%- form 'product',
                          product,
                          id: product_form_id,
                          class: 'form',
                          novalidate: 'novalidate',
                          data-type: 'add-to-cart-form'
                        -%}
                          <input
                            type="hidden"
                            name="id"
                            value="{{ product.selected_or_first_available_variant.id }}"
                            disabled
                          >
                          
                          <div class="product-form__buttons">    
                              {%  if section.settings.show_quantity_selector %}
                              {%  render 'quantity-selector', product_form_id: product_form_id %}
                              {% endif %}
                            <button
                              type="submit"
                              name="add"
                              class="product-form__submit product-form__s button button--full-width {% if block.settings.show_dynamic_checkout and product.selling_plan_groups == empty %}button--secondary{% else %}button--primary{% endif %}"
                              {% if product.selected_or_first_available_variant.available == false %}
                                disabled
                              {% endif %}
                            >
                              <span>
                                {%- if product.selected_or_first_available_variant.available -%}
                                  {{ 'products.product.add_to_cart' | t }}
                                {%- else -%}
                                  {{ 'products.product.sold_out' | t }}
                                {%- endif -%}
                              </span>
                              <div class="loading-overlay__spinner hidden">
                                <svg
                                  aria-hidden="true"
                                  focusable="false"
                                  role="presentation"
                                  class="spinner"
                                  viewBox="0 0 66 66"
                                  xmlns="http://www.w3.org/2000/svg"
                                >
                                  <circle class="path" fill="none" stroke-width="3" cx="33" cy="33" r="30"></circle>
                                </svg>
                              </div>
                            </button>
                            
                            {%- if block.settings.show_dynamic_checkout -%}
                              {{ form | payment_button }}
                            {%- endif -%}
                          </div>
                        {%- endform -%}
                      </product-form>

                  {%  endcase %}
                {%  endfor %}
                  
                  {% render 'dT-fbt' %}
                      {{ 'component-pickup-availability.css' | asset_url | stylesheet_tag }}
                      {%- assign pick_up_availabilities = product.selected_or_first_available_variant.store_availabilities | where: 'pick_up_enabled', true -%}
                      <pickup-availability
                        class="product__pickup-availabilities no-js-hidden quick-add-hidden"
                        {% if product.selected_or_first_available_variant.available and pick_up_availabilities.size > 0 %}
                          available
                        {% endif %}
                        data-root-url="{{ routes.root_url }}"
                        data-variant-id="{{ product.selected_or_first_available_variant.id }}"
                        data-has-only-default-variant="{{ product.has_only_default_variant }}"
                      >
                        <template>
                          <pickup-availability-preview class="pickup-availability-preview">
                            {% render 'icon-unavailable' %}
                            <div class="pickup-availability-info">
                              <p class="caption-large">{{ 'products.product.pickup_availability.unavailable' | t }}</p>
                              <button class="pickup-availability-button link link--text underlined-link">
                                {{ 'products.product.pickup_availability.refresh' | t }}
                              </button>
                            </div>
                          </pickup-availability-preview>
                        </template>
                      </pickup-availability>
                    </div>
             <script src="{{ 'pickup-availability.js' | asset_url }}" defer="defer"></script>
                    {%- for block in section.blocks -%}
                      {%- case block.type -%}
              {%- when 'complementary' -%}
              <product-recommendations class="complementary-products quick-add-hidden no-js-hidden{% if block.settings.make_collapsible_row %} is-accordion{% endif %}{% if block.settings.enable_quick_add %} complementary-products-contains-quick-add{% endif %}" data-url="{{ routes.product_recommendations_url }}?section_id={{ section.id }}&product_id={{ product.id }}&limit={{ block.settings.product_list_limit }}&intent=complementary">
                {%- if recommendations.performed and recommendations.products_count > 0 -%}
                  <aside aria-label="{{ 'accessibility.complementary_products' | t }}" {{ block.shopify_attributes }}{% if block.settings.make_collapsible_row %} class="product__accordion accordion"{% endif %}>
                    <div class="complementary-products__container">
                      {%- if block.settings.make_collapsible_row -%}
                        <details id="Details-{{ block.id }}-{{ section.id }}" open>
                          <summary>
                      {%- endif %}
                      <div class="summary__title">
                        {%- if block.settings.make_collapsible_row -%}
                          {% render 'icon-accordion', icon: block.settings.icon %}
                          <h2 class="h4 accordion__title">{{ block.settings.block_heading }}</h2>
                        {%- else -%}
                          <h2 class="h3 accordion__title">{{ block.settings.block_heading }}</h2>
                        {%- endif -%}
                      </div>
                      {%- if block.settings.make_collapsible_row -%}
                          {% render 'icon-caret' %}
                        </summary>
                      {%- endif -%}
                      <div class="slider-mobile-gutter">
                        {%- assign number_of_slides = recommendations.products_count | plus: 0.0 | divided_by: block.settings.products_per_page | ceil -%}
                        <div id="Slider-{{ block.id }}" class="contains-card contains-card--product complementary-slider grid grid--1-col slider slider--everywhere" role="list"{% if number_of_slides > 1 %} aria-label="{{ 'general.slider.name' | t }}"{% endif %}>
                          {%- for i in (1..number_of_slides) -%}
                            <div id="Slide-{{ block.id }}-{{ forloop.index }}" class="complementary-slide complementary-slide--{{ settings.card_style }} grid__item slider__slide slideshow__slide" tabindex="-1" role="group"{% if number_of_slides > 1 %} aria-roledescription="{{ 'sections.slideshow.slide' | t }}" aria-label="{{ forloop.index }} {{ 'general.slider.of' | t }} {{ forloop.length }}"{% endif %}>
                              <ul class="list-unstyled" role="list">
                                {%- for product in recommendations.products limit: block.settings.products_per_page -%}
                                  <li>
                                    {% render 'card-product-2',
                                      card_product: product,
                                      media_aspect_ratio: block.settings.image_ratio,
                                      show_secondary_image: false,
                                      lazy_load: false,
                                      show_quick_add: block.settings.enable_quick_add,
                                      section_id: section.id,
                                      horizontal_class: true,
                                      horizontal_quick_add: true
                                    %}
                                  </li>
                                {%- endfor -%}
                              </ul>
                            </div>
                          {%- endfor -%}
                        </div>
                      </div>
                      {%- if block.settings.make_collapsible_row -%}
                        </details>
                      {%- endif -%}
                    </div>
                  </aside>
                {%- endif -%}
                {{ 'component-card.css' | asset_url | stylesheet_tag }}
                {{ 'component-complementary-products.css' | asset_url | stylesheet_tag }}
                {%- if block.settings.enable_quick_add -%}
                  {{ 'quick-add.css' | asset_url | stylesheet_tag }}
                  <script src="{{ 'quick-add.js' | asset_url }}" defer="defer"></script>
                {%- endif -%}
              </product-recommendations>

                        
                        {%- when 'Html_c' -%}
                          <div class="html-sections-product">{{ block.settings.html }}</div>
                        {%- when 'notify-form' -%}
                          {% render 'notify-form', product: product   %}
                        {%- when 'icon_text' -%}
                          {%  render 'icon_text', block: block, class: 'from-main-product' %} 
                        {%- when 'fake_visitors' -%}
                          {%- render 'fake-visitors', block: block -%}
                         {%- when 'badge' -%}
                          {% if block.settings.image != blank %}
                          <div class="secure-badges">
                            <p class="badge-label">{{ block.settings.badge_label }}</p>
                            <img src="{{ block.settings.image | img_url: 'master' }}" alt="{{ block.settings.image.alt }}" />
                          </div>
                          {% endif %}
                      {%- endcase -%}
                    {%- endfor -%}
                  
                      <div class="product__payment">
                      <ul class="list list-payment" role="list">
                      {%- for type in shop.enabled_payment_types -%}
                      <li class="list-payment__item">
                      {{ type | payment_type_svg_tag: class: 'icon icon--full-color' }}
                      </li>
                      {%- endfor -%}
                      </ul>
                        <p class="product_payment_text">{{ 'products.product.more_payment_option' | t }}</p>
                      </div>
                    <a href="{{ product.url }}" class="link product__view-details animate-arrow">
                    {{ 'products.product.view_full_details' | t }}
                    {% render 'icon-arrow' %}
                    </a>
                  
                   {%- if section.settings.enable_enquiry -%}
                    <div class="text-center dt-sc-enquiry-form">
<!--                         <svg height="20px" width="20px" viewBox="0 0 439.57422 439" xmlns="http://www.w3.org/2000/svg">
                        <path d="m428.882812 376.707031-67.875-67.867187c-2.589843-2.597656-6.03125-4.027344-9.695312-4.027344 0 0-.003906 0-.007812 0-3.664063 0-7.109376 1.429688-9.699219 4.019531l-9.695313 9.695313-19.710937-19.714844c18.492187-21.519531 28.660156-48.589844 28.660156-77.265625 0-31.742187-12.359375-61.589844-34.808594-84.035156-6.878906-6.875-14.511719-12.679688-22.617187-17.597657v-92.199218c0-15.125-12.304688-27.425782-27.429688-27.425782h-168.398437l-2.671875 2.710938c-.003906.003906-.011719.007812-.015625.011719l-82.289063 83.527343-2.394531 2.476563-.23046875.234375v.007812l-.00390625.003907v25.980469l.00390625-.003907v296.476563c0 15.125 12.30468775 27.429687 27.42968775 27.429687h228.570312c15.125 0 27.429688-12.304687 27.429688-27.429687v-88.527344c5.542968-3.363281 10.84375-7.15625 15.835937-11.445312l19.710938 19.714843-9.695313 9.695313c-5.347656 5.347656-5.347656 14.046875 0 19.394531l67.878906 67.882813c6.90625 6.910156 16.089844 10.714843 25.863282 10.714843 9.765625 0 18.949218-3.804687 25.855468-10.714843 14.253907-14.257813 14.253907-37.464844 0-51.722657zm-106.308593-155.160156c0 26.867187-10.457031 52.125-29.453125 71.113281-18.996094 19.003906-44.25 29.457032-71.117188 29.457032-26.863281 0-52.117187-10.453126-71.109375-29.457032-18.996093-18.988281-29.460937-44.246094-29.460937-71.113281 0-26.859375 10.464844-52.117187 29.460937-71.105469 18.992188-19 44.246094-29.457031 71.109375-29.457031 26.867188 0 52.121094 10.457031 71.117188 29.457031 18.996094 18.988282 29.453125 44.246094 29.453125 71.105469zm-244.058594-185.78125c.820313-.835937 1.582031-1.605469 2.390625-2.425781.464844-.472656.921875-.9375 1.382812-1.40625v42.738281c0 5.042969-4.101562 9.140625-9.144531 9.140625h-42.046875c14.828125-15 32.164063-32.566406 47.417969-48.046875zm186.628906 375.949219c0 5.046875-4.101562 9.144531-9.140625 9.144531h-228.570312c-5.039063 0-9.144532-4.097656-9.144532-9.144531v-309.613282h54.855469c15.128907 0 27.429688-12.304687 27.429688-27.429687v-56.097656h155.429687c5.039063 0 9.140625 4.097656 9.140625 9.140625v83.125c-13.578125-5.289063-28.128906-8.140625-43.140625-8.140625-31.746094 0-61.59375 12.363281-84.039062 34.8125-22.453125 22.445312-34.816406 52.292969-34.816406 84.035156 0 31.75 12.363281 61.597656 34.816406 84.042969 22.445312 22.445312 52.292968 34.8125 84.039062 34.8125 15.011719 0 29.5625-2.855469 43.140625-8.140625zm150.8125 3.785156c-6.917969 6.921875-18.957031 6.902344-25.863281 0l-64.648438-64.652344 25.859376-25.855468 64.652343 64.644531c7.125 7.132812 7.125 18.730469 0 25.863281zm0 0"></path>
                        <path d="m45.714844 137.429688h18.285156v18.285156h-18.285156zm0 0"></path>
                        <path d="m45.714844 192.289062h18.285156v18.285157h-18.285156zm0 0"></path>
                        <path d="m45.714844 247.144531h18.285156v18.285157h-18.285156zm0 0"></path>
                        <path d="m45.714844 302h18.285156v18.289062h-18.285156zm0 0"></path>
                        <path d="m45.714844 356.859375h18.285156v18.285156h-18.285156zm0 0"></path>
                        </svg> -->
                     <div class="enquiry-text">
                    <h5 class="enquiry_heading">{{ 'enquiry.form.heading' | t }}</h5> 
                    <a id="trigger-enquiry" class="link">{{ 'enquiry.form.sub_heading' | t }}</a>
                     </div>
                    <div class="enquiry-image">
                      {% if section.settings.enquiry_image != blank %}
                   <img
                      class="enquiry_image"
                      srcset="{%- if section.settings.enquiry_image.width >= 375 -%}{{section.settings.enquiry_image | image_url: width: 375 }} 375w,{%- endif -%}
                      {%- if section.settings.enquiry_image.width >= 550 -%}{{section.settings.enquiry_image | image_url: width: 550 }} 550w,{%- endif -%}
                      {%- if section.settings.enquiry_image.width >= 750 -%}{{section.settings.enquiry_image | image_url: width: 750 }} 750w,{%- endif -%}
                      {%- if section.settings.enquiry_image.width >= 1100 -%}{{section.settings.enquiry_image | image_url: width: 1100 }} 1100w,{%- endif -%}
                      {%- if section.settings.enquiry_image.width >= 1500 -%}{{section.settings.enquiry_image | image_url: width: 1500 }} 1500w,{%- endif -%}
                      {%- if section.settings.enquiry_image.width >= 1780 -%}{{section.settings.enquiry_image | image_url: width: 1780 }} 1780w,{%- endif -%}
                      {%- if section.settings.enquiry_image.width >= 2000 -%}{{section.settings.enquiry_image | image_url: width: 2000 }} 2000w,{%- endif -%}
                      {%- if section.settings.enquiry_image.width >= 3000 -%}{{section.settings.enquiry_image | image_url: width: 3000 }} 3000w,{%- endif -%}
                      {%- if section.settings.enquiry_image.width >= 3840 -%}{{section.settings.enquiry_image | image_url: width: 3840 }} 3840w,{%- endif -%}
                      {{section.settings.enquiry_image | image_url: width: 1500 }} {{section.settings.enquiry_image.width }}w"
                      sizes="100vw"
                      src="{{section.settings.enquiry_image | image_url: width: 1500 }}"
                      loading="lazy"              
                      alt="{{section.settings.enquiry_image.alt | escape }}"
                      width="{{section.settings.enquiry_image.width }}"
                      height="{{section.settings.enquiry_image.width | divided_by:section.settings.enquiry_image.aspect_ratio | round }}"
                    >  
            {% endif %}
                    </div>
                    </div>
                    {%- endif -%}
                   
               
                    {% if section.settings['tab_position'] == 'right' %}
                      {% if section.settings['tab_style'] == 'toggle' %}                     
                      {%- for block in section.blocks -%}
                      {%- case block.type -%}
                        {%- when 'description' -%}
                            <div class="product__accordion accordion  {% if section.settings['tab_position'] == 'right' %} toggle_right {%  endif %}" {{ block.shopify_attributes }}>
                              <details class="dt-details">
                                <summary>
                                  <div class="summary__title 1">
                                    {% render 'icon-accordion', icon: 'check_mark' %}
                                    <h6 class="accordion__title">
                                      {{ 'products.product.description' | t }}
                                    </h6>
                                  </div>
                                  {% render 'icon-caret' %}
                                </summary>
                                <div class="accordion__content rte">
                                  {%- if product.description != blank -%}
                                    {{ product.description }}
                                  {%- endif -%}
                                </div>
                              </details>
                            </div>
                        {%- when 'collapsible_tab' -%}
                            <div class="product__accordion accordion" {{ block.shopify_attributes }}>
                              <details class="dt-details">
                                <summary>
                                  <div class="summary__title">
                                    {% render 'icon-accordion', icon: block.settings.icon %}
                                    <h6 class="accordion__title">
                                      {{ block.settings.heading | default: block.settings.page.title }}
                                    </h6>
                                  </div>
                                  {% render 'icon-caret' %}
                                </summary>
                                <div class="accordion__content rte">
                                  {{ block.settings.content }}
                                  {{ block.settings.page.content }}
                                </div>
                              </details>
                            </div>
                      {%- endcase -%}                        
                      {%- endfor -%}
                          {%- if section.settings.show_review_tab -%}
                        <div class="product__accordion accordion  {% if section.settings['tab_position'] == 'right' %} toggle_right {%  endif %}" {{ block.shopify_attributes }}>
                              <details class="dt-details">
                                <summary>
                                  <div class="summary__title 3">
                                    {% render 'icon-accordion', icon: 'check_mark' %}
                                    <h6 class="accordion__title">
                                     {{ 'products.product.reviews' | t }}
                                    </h6>
                                  </div>
                                  {% render 'icon-caret' %}
                                </summary>
                                <div class="accordion__content rte">                                 
                                    {{ section.settings.rating_script }}                                 
                                </div>
                              </details>
                            </div>
                            {%- endif -%}
                      {% endif %}
                      {% if section.settings['tab_style'] == 'tab' %}
                        <div class="product__info-as-bottom-tabs  {% if section.settings['tab_position'] == 'right' %} tab_right {% endif %}">
                          <div class="product__info-bottom tabs" {{ block.shopify_attributes }}>
                            {%- if product.description != blank -%}
                              <div class="summary__title tablinks active-tab" data-id="tab-description">
                                <h6 class="accordion__title">
                                  {{ 'products.product.description' | t }}
                                </h6>
                              </div>
                            {%- endif -%}
                
                            {%- for block in section.blocks -%}
                              {%- case block.type -%}
                                {%- when 'collapsible_tab' -%}
                                  <div class="summary__title tablinks"  data-id="{{block.settings.heading | handle }}">
                                    <h6 class="accordion__title">
                                      {{ block.settings.heading | default: block.settings.page.title }}
                                    </h6>
                                  </div>
                              {%- endcase -%}
                            {%- endfor -%}
                            {%- if section.settings.show_review_tab -%}
                              <div  class="summary__title tablinks"  data-id="tab-reviews"> 
                              <h6 class="accordion__title">
                                {{ 'products.product.reviews' | t }}
                              </h6>
                             </div> 
                            {%- endif -%}
                          </div>
                
                          {%- if product.description != blank -%}
                              <h6 class="accordion__title acc__title">
                              {{ 'products.product.description' | t }}
                              </h6>
                            <div class="dt-sc-tabs-content rte tab-active-content" data-id="tab-description">
                              {{ product.description }}
                            </div>
                          {%- endif -%}
                
                          {%- for block in section.blocks -%}
                            {%- case block.type -%}
                              {%- when 'collapsible_tab' -%}
                              <h6 class="accordion__title acc__title">
                                   {{ block.settings.heading | default: block.settings.page.title }}
                               </h6>
                                <div class="dt-sc-tabs-content rte"  data-id="{{ block.settings.heading | handle }}">
                                  {{ block.settings.content }}
                                  {{ block.settings.page.content }}
                                </div>
                            {%- endcase -%}
                          {%- endfor -%}
                          {%- if section.settings.show_review_tab -%}
                             <h6 class="accordion__title acc__title">
                               {{ 'products.product.reviews' | t }}
                             </h6>
                            <div class="dt-sc-tabs-content rte"  data-id="tab-reviews">
                              {{ section.settings.rating_script }}
                            </div>
                          {%- endif -%}
                        </div>
                      {% endif %}
                      {% endif %}
             
          </div>  
          </div>
        </div>

        <product-modal id="ProductModal-{{ section.id }}" class="product-media-modal media-modal">
          <div
            class="product-media-modal__dialog"
            role="dialog"
            aria-label="{{ 'products.modal.label' | t }}"
            aria-modal="true"
            tabindex="-1"
          >
            <button
              id="ModalClose-{{ section.id }}"
              type="button"
              class="product-media-modal__toggle"
              aria-label="{{ 'accessibility.close' | t }}"
            >
              {% render 'icon-close' %}
            </button>

            <div
              class="product-media-modal__content gradient"
              role="document"
              aria-label="{{ 'products.modal.label' | t }}"
              tabindex="0"
            >
              {%- liquid
                if product.selected_or_first_available_variant.featured_media != null
                  assign media = product.selected_or_first_available_variant.featured_media
                  render 'product-media', media: media, loop: section.settings.enable_video_looping, variant_image: section.settings.hide_variants
                endif
              -%}

              {%- for media in product.media -%}
                {%- liquid
                  if section.settings.hide_variants and variant_images contains media.src
                    assign variant_image = true
                  else
                    assign variant_image = false
                  endif

                  unless media.id == product.selected_or_first_available_variant.featured_media.id
                    render 'product-media', media: media, loop: section.settings.enable_video_looping, variant_image: variant_image
                  endunless
                -%}
              {%- endfor -%}
            </div>
          </div>
        </product-modal>
      </div>

      {% if section.settings['tab_position'] == 'bottom' %}
      {% if section.settings['tab_style'] == 'toggle' %}
      {%- for block in section.blocks -%}
      {%- case block.type -%}
        {%- when 'description' -%}
            <div class="product__accordion accordion" {{ block.shopify_attributes }}>
              <details class="dt-details">
                <summary>
                  <div class="summary__title 1">
                    {% render 'icon-accordion', icon: 'check_mark' %}
                    <h6 class="accordion__title">
                      {{ 'products.product.description' | t }}
                    </h6>
                  </div>
                  {% render 'icon-caret' %}
                </summary>
                <div class="accordion__content rte">
                  {%- if product.description != blank -%}
                    {{ product.description }}
                  {%- endif -%}
                </div>
              </details>
            </div>
        {%- when 'collapsible_tab' -%}
            <div class="product__accordion accordion" {{ block.shopify_attributes }}>
              <details class="dt-details">
                <summary>
                  <div class="summary__title">
                    {% render 'icon-accordion', icon: block.settings.icon %}
                    <h6 class="accordion__title">
                      {{ block.settings.heading | default: block.settings.page.title }}
                    </h6>
                  </div>
                  {% render 'icon-caret' %}
                </summary>
                <div class="accordion__content rte">
                  {{ block.settings.content }}
                  {{ block.settings.page.content }}
                </div>
              </details>
            </div>
      {%- endcase -%}
      {%- endfor -%}
        {%- if section.settings.show_review_tab -%}
        <div class="product__accordion accordion" {{ block.shopify_attributes }}>
          <details class="dt-details">
            <summary>
              <div class="summary__title 3">
                {% render 'icon-accordion', icon: 'check_mark' %}
                <h6 class="accordion__title">
                 {{ 'products.product.reviews' | t }}
                </h6>
              </div>
              {% render 'icon-caret' %}
            </summary>
            <div class="accordion__content rte">                                 
                {{ section.settings.rating_script }}                                 
            </div>
          </details>
        </div>
        {%- endif -%}
      {% endif %}
      {% if section.settings['tab_style'] == 'tab' %}
        <div class="product__info-as-bottom-tabs">
          <div class="product__info-bottom tabs" {{ block.shopify_attributes }}>
            {%- if product.description != blank -%}
              <div class="summary__title tablinks active-tab" data-id="tab-description">
                <h6 class="accordion__title h4">
                  {{ 'products.product.description' | t }}
                </h6>
              </div>
            {%- endif -%}

            {%- for block in section.blocks -%}
              {%- case block.type -%}
                {%- when 'collapsible_tab' -%}
                  <div class="summary__title tablinks" data-id="{{ block.settings.heading | handle }}">
                    <h6 class="accordion__title h4">
                      {{ block.settings.heading | default: block.settings.page.title }}
                    </h6>
                  </div>
              {%- endcase -%}
            {%- endfor -%}
          {%- if section.settings.show_review_tab -%}
            <div  class="summary__title tablinks" data-id="tab-reviews"> 
            <h6 class="accordion__title h4">
              {{ 'products.product.reviews' | t }}
            </h6>
            </div> 
          {%- endif -%}
          </div>

          {%- if product.description != blank -%}
            <h6 class="accordion__title acc__title">
                  {{ 'products.product.description' | t }}
            </h6>
            <div class="dt-sc-tabs-content rte tab-active-content" data-id="tab-description">            
              {{ product.description }}
            </div>
          {%- endif -%}

          {%- for block in section.blocks -%}
            {%- case block.type -%}
              {%- when 'collapsible_tab' -%}
                <h6 class="accordion__title acc__title">
                      {{ block.settings.heading | default: block.settings.page.title }}
                    </h6>
                <div class="dt-sc-tabs-content rte" data-id="{{ block.settings.heading | handle }}">
                  {{ block.settings.content }}
                  {{ block.settings.page.content }}
                </div>
            {%- endcase -%}
          {%- endfor -%}
         {%- if section.settings.show_review_tab -%}
              <h6 class="accordion__title acc__title">
              {{ 'products.product.reviews' | t }}
            </h6>
              <div class="dt-sc-tabs-content rte" data-id="tab-reviews">
                {{ section.settings.rating_script }}
              </div>
            {%- endif -%}
        </div>
      {% endif %}
      {% endif %}
      

      {%- if product.media.size > 0 -%}
        <script src="{{ 'product-modal.js' | asset_url }}" defer="defer"></script>
        <script src="{{ 'media-gallery.js' | asset_url }}" defer="defer"></script>
      {%- endif -%}

      {%- if first_3d_model -%}
        <script type="application/json" id="ProductJSON-{{ product.id }}">
          {{ product.media | where: 'media_type', 'model' | json }}
        </script>
        <script src="{{ 'product-model.js' | asset_url }}" defer></script>
      {%- endif -%}

      {%- liquid
        render 'dT_product_bundle'
        if section.settings.enable_enquiry
          render 'enquiry-form'
        endif
      %}
{%  render 'sticky-cart-bar' %}
      <script>
        document.addEventListener('DOMContentLoaded', function() {
          function isIE() {
            const ua = window.navigator.userAgent;
            const msie = ua.indexOf('MSIE ');
            const trident = ua.indexOf('Trident/');

            return (msie > 0 || trident > 0);
          }

          if (!isIE()) return;
          const hiddenInput = document.querySelector('#{{ product_form_id }} input[name="id"]');
          const noScriptInputWrapper = document.createElement('div');
          const variantSwitcher = document.querySelector('variant-radios[data-section="{{ section.id }}"]') || document.querySelector('variant-selects[data-section="{{ section.id }}"]');
          noScriptInputWrapper.innerHTML = document.querySelector('.product-form__noscript-wrapper-{{ section.id }}').textContent;
          variantSwitcher.outerHTML = noScriptInputWrapper.outerHTML;
          document.querySelector('#Variants-{{ section.id }}').addEventListener('change', function(event) {
          hiddenInput.value = event.currentTarget.value;
  
        });
        });



        
        // function openTab(evt, TabName) {
        //   var i, tabcontent, tablinks;
        //   tabcontent = document.getElementsByClassName("dt-sc-tabs-content");
        //   for (i = 0; i < tabcontent.length; i++) {
        //     tabcontent[i].style.display = "none";
        //   }
        //   tablinks = document.getElementsByClassName("tablinks");
        //   for (i = 0; i < tablinks.length; i++) {
        //     tablinks[i].className = tablinks[i].className.replace(" active", "");
        //   }
        //   document.getElementById(TabName).style.display = "block";
        //   evt.currentTarget.className += " active";
        //                     $(".tabs").toggleClass('expanded');
        // }
        // $( ".tablinks" ).first().addClass( "active" );
        // $( ".dt-sc-tabs-content" ).first().css( "display", "block" );
      </script>

      {%- liquid
        if product.selected_or_first_available_variant.featured_media
          assign seo_media = product.selected_or_first_available_variant.featured_media
        else
          assign seo_media = product.featured_media
        endif
      -%}

      <script type="application/ld+json">
        {
          "@context": "http://schema.org/",
          "@type": "Product",
          "name": {{ product.title | json }},
          "url": {{ request.origin | append: product.url | json }},
          {% if seo_media -%}
            "image": [
              {{ seo_media | image_url: width: seo_media.preview_image.width | prepend: "https:" | json }}
            ],
          {%- endif %}
          "description": {{ product.description | strip_html | json }},
          {% if product.selected_or_first_available_variant.sku != blank -%}
            "sku": {{ product.selected_or_first_available_variant.sku | json }},
          {%- endif %}
          "brand": {
            "@type": "Thing",
            "name": {{ product.vendor | json }}
          },
          "offers": [
            {%- for variant in product.variants -%}
              {
                "@type" : "Offer",
                {%- if variant.sku != blank -%}
                  "sku": {{ variant.sku | json }},
                {%- endif -%}
                "availability" : "http://schema.org/{% if variant.available %}InStock{% else %}OutOfStock{% endif %}",
                "price" : {{ variant.price | divided_by: 100.00 | json }},
                "priceCurrency" : {{ cart.currency.iso_code | json }},
                "url" : {{ request.origin | append: variant.url | json }}
              }{% unless forloop.last %},{% endunless %}
            {%- endfor -%}
          ]
        }
      </script>
    </div>
  </div>
</section>

<script>
      $(document).ready(function(){
        {% if section.settings.enable_product_zoom == "zoom" %}
          $('.zoom').zoom(2);
        {% endif %}
        {% if section.settings.enable_product_zoom == "outer" %}         
        var demoTrigger = document.querySelector('.dt-zoom-trigger');
        var paneContainer = document.querySelector('.dt-zoom-detail');
        
        new Drift(demoTrigger, {
          paneContainer: paneContainer,
          inlinePane: false
        });

        
        {% elsif section.settings.enable_product_zoom == "cloud" %}
        $('.zoom_out').ezPlus({
            zoomType: 'lens',
            lensShape: 'round',
            lensSize: 200
        });
        {% endif %}
        $(document).on('click', '#notify-me', function() {
            $('#notify-me-wrapper').fadeToggle();
            return false;
        });
      });
  </script>
{% schema %}
  {
  "name": "t:sections.main-product.name",
  "tag": "section",
  "class": "section",
  "blocks": [
     {
      "type": "breadcrumb",
       "name": "t:sections.main-product.blocks.breadcrumb.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "breadcrumb_position",
          "options": [
            {
              "value": "left",
              "label": "t:sections.main-product.blocks.breadcrumb.settings.breadcrumb_position.options__1.label"
            },
            {
              "value": "right",
              "label": "t:sections.main-product.blocks.breadcrumb.settings.breadcrumb_position.options__2.label"
            }            
          ],
          "default": "left",
          "label": "t:sections.main-product.blocks.breadcrumb.settings.breadcrumb_position.label"
        }        
      ]
    },
  {
  "type": "@app"
  },
  {
  "type": "text",
  "name": "t:sections.main-product.blocks.text.name",
  "settings": [
  {
  "type": "text",
  "id": "text",
  "default": "Text block",
  "label": "t:sections.main-product.blocks.text.settings.text.label"
  },
  {
  "type": "select",
  "id": "text_style",
  "options": [
  {
  "value": "body",
  "label": "t:sections.main-product.blocks.text.settings.text_style.options__1.label"
  },
  {
  "value": "subtitle",
  "label": "t:sections.main-product.blocks.text.settings.text_style.options__2.label"
  },
  {
  "value": "uppercase",
  "label": "t:sections.main-product.blocks.text.settings.text_style.options__3.label"
  }
  ],
  "default": "body",
  "label": "t:sections.main-product.blocks.text.settings.text_style.label"
  }
  ]
  },
  {
  "type": "title",
  "name": "t:sections.main-product.blocks.title.name",
  "limit": 1
  },

  {
  "type": "price",
  "name": "t:sections.main-product.blocks.price.name",
  "limit": 1
  },
    {
  "type": "sub_total",
  "name": "t:sections.main-product.blocks.sub_total.name",
  "limit": 1
  },    
  {
  "type": "quantity_selector",
  "name": "t:sections.main-product.blocks.quantity_selector.name",
  "limit": 1
  },
  {
      "type": "inventory",
      "name": "t:sections.main-product.blocks.inventory.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "body",
              "label": "t:sections.main-product.blocks.inventory.settings.text_style.options__1.label"
            },
            {
              "value": "subtitle",
              "label": "t:sections.main-product.blocks.inventory.settings.text_style.options__2.label"
            },
            {
              "value": "uppercase",
              "label": "t:sections.main-product.blocks.inventory.settings.text_style.options__3.label"
            }
          ],
          "default": "body",
          "label": "t:sections.main-product.blocks.inventory.settings.text_style.label"
        },
        {
          "type": "range",
          "id": "inventory_threshold",
          "label": "t:sections.main-product.blocks.inventory.settings.inventory_threshold.label",
          "min": 0,
          "max": 100,
          "step": 1,
          "info": "t:sections.main-product.blocks.inventory.settings.inventory_threshold.info",
          "default": 10
        },
        {
          "type": "checkbox",
          "id": "show_inventory_quantity",
          "label": "t:sections.main-product.blocks.inventory.settings.show_inventory_quantity.label",
          "default": true
        }
      ]
    },
    {
      "type": "product",
      "name": "t:sections.main-product.blocks.product.name",
      "limit": 5,
      "settings": [              
        {
          "type": "product",
          "id": "product",
          "label": "t:sections.main-product.blocks.product.settings.product.label"          
        }
      ]
    },
  {
  "type": "icon_text",
  "limit": 1,
  "name": "Icon with text",
  "settings": [
  {
  "type": "text",
  "id": "text1",
  "label": "Title",
  "default": "Title"
  },
  {
  "type": "image_picker",
  "id": "icon1",
  "label": "Icon"
  },
  {
  "type": "html",
  "id": "html1",
  "label": "Html",
  "default": "You can add text with html tag here"
  },
  {
  "type": "url",
  "id": "link1",
  "label": "Link"
  },
  {
  "type": "color",
  "id": "text1_color",
  "label": "Heading Color",
  "default": "#000"
  },
  {
  "type": "color",
  "id": "html1_color",
  "label": " Description color",
  "default": "#000"
   },  
  {
  "type": "text",
  "id": "text2",
  "label": "Title",
  "default": "Title"
  },  
  {
  "type": "image_picker",
  "id": "icon2",
  "label": "Icon"
  },
  {
  "type": "html",
  "id": "html2",
  "label": "Html",
  "default": "You can add text with html tag here"
  },
  {
  "type": "url",
  "id": "link2",
  "label": "Link"
  },
  {
  "type": "color",
  "id": "text2_color",
  "label": "Heading Color",
  "default": "#000"
  },
  {
  "type": "color",
  "id": "html2_color",
  "label": " Description color",
  "default": "#000"
   },  
    {
  "type": "text",
  "id": "text3",
  "label": "Title",
  "default": "Title"
  },
  {
  "type": "image_picker",
  "id": "icon3",
  "label": "Icon"
  },
  {
  "type": "html",
  "id": "html3",
  "label": "Html",
  "default": "You can add text with html tag here"
  },
  {
  "type": "url",
  "id": "link3",
  "label": "Link"
  },
  {
  "type": "color",
  "id": "text3_color",
  "label": "Heading Color",
  "default": "#000"
  },
  {
  "type": "color",
  "id": "html3_color",
  "label": " Description color",
  "default": "#000"
  }  
  ]
  },
  {
  "type": "Html_c",
  "name": "Html",
    "settings": [
    {
    "type": "html",
    "id": "html",
    "label": "Shipping & delivery",
    "default": "<p>Estimated deliver 5-7 days</p>",
    "info": "Use HTML format"
    }
    ]
  },
  {
  "type": "fbt",
  "name": "t:sections.main-product.blocks.fbt.name",
  "limit": 1
  },
  {
  "type": "variant_picker",
  "name": "t:sections.main-product.blocks.variant_picker.name",
  "limit": 1,
  "settings": [
  {
  "type": "select",
  "id": "picker_type",
  "options": [
  {
  "value": "dropdown",
  "label": "t:sections.main-product.blocks.variant_picker.settings.picker_type.options__1.label"
  },
  {
  "value": "button",
  "label": "t:sections.main-product.blocks.variant_picker.settings.picker_type.options__2.label"
  }
  ],
  "default": "button",
  "label": "t:sections.main-product.blocks.variant_picker.settings.picker_type.label"
  },
   {
  "type": "select",
  "id": "picker_method",
  "options": [
  {
  "value": "custom_color",
  "label": "t:sections.main-product.blocks.variant_picker.settings.picker_method.options__1.label"
  },
  {
  "value": "variant_image",
  "label": "t:sections.main-product.blocks.variant_picker.settings.picker_method.options__2.label"
  }
  ],
  "default": "custom_color",
  "label": "t:sections.main-product.blocks.variant_picker.settings.picker_method.label"
  }
  ]
  },
  {
  "type": "buy_buttons",
  "name": "t:sections.main-product.blocks.buy_buttons.name",
  "limit": 1,
  "settings": [
  {
  "type": "checkbox",
  "id": "show_dynamic_checkout",
  "default": true,
  "label": "t:sections.main-product.blocks.buy_buttons.settings.show_dynamic_checkout.label",
  "info": "t:sections.main-product.blocks.buy_buttons.settings.show_dynamic_checkout.info"
  },
{
      "type": "checkbox",
      "id": "show_gift_card_recipient",
      "default": true,
      "label": "t:sections.main-product.blocks.buy_buttons.settings.show_gift_card_recipient.label",
      "info": "t:sections.main-product.blocks.buy_buttons.settings.show_gift_card_recipient.info"
    }
  ]
  },
  {
  "type": "description",
  "name": "t:sections.main-product.blocks.description.name",
  "limit": 1
  },
  {
      "type": "deal",
      "name": "t:sections.main-product.blocks.deal.name",
      "limit": 1
    },

  {
"type": "vendor",
"name": "t:sections.main-product.blocks.vendor.name",
"limit": 1
},
{
"type": "type",
"name": "t:sections.main-product.blocks.type.name",
"limit": 1
},
{
"type": "sku",
"name": "t:sections.main-product.blocks.sku.name",
"limit": 1
},
{
"type": "notify-form",
"name": "t:sections.main-product.blocks.notify-form.name",
"limit": 1
},
{
  "type": "rating",
  "limit": 1,
  "name": "t:sections.main-product.blocks.rating.name"
},
{
"type": "badge",
"limit": 1,
"name": "t:sections.main-product.blocks.badge.name",
"settings": [
{
"type": "image_picker",
"id": "image",
"label": "t:sections.main-product.blocks.badge.settings.image.label"
},
{
"type": "text",
"id": "badge_label",
 "label": "t:sections.main-product.blocks.badge.settings.badge_label.label",
"default": "100% Secured Payment"
}
]
}, 
  {
  "type": "custom_liquid",
  "name": "t:sections.main-product.blocks.custom_liquid.name",
  "settings": [
  {
  "type": "liquid",
  "id": "custom_liquid",
  "label": "t:sections.main-product.blocks.custom_liquid.settings.custom_liquid.label",
  "info": "t:sections.main-product.blocks.custom_liquid.settings.custom_liquid.info"
  }
  ]
  },
  {
  "type": "fake_visitors",
  "name": "t:sections.main-product.blocks.fake_visitors.name",
  "settings": [
  {
  "type": "text",
  "id": "text",
  "label": "t:sections.main-product.blocks.fake_visitors.settings.text.label"
  },
  {
  "type": "range",
  "id": "min_count",
  "label": "t:sections.main-product.blocks.fake_visitors.settings.min_count.label",
  "default": 50,
  "min": 10,
  "max": 100,
  "step": 1
  },
  {
  "type": "range",
  "id": "max_count",
  "label": "t:sections.main-product.blocks.fake_visitors.settings.max_count.label",
  "default": 200,
  "min": 100,
  "max": 200,
  "step": 1
  }
  ]
  },

  {
  "type": "fake_sales",
  "name": "t:sections.main-product.blocks.fake_sales.name",
  "settings": [

  {
        "type": "range",
        "id": "minqty",
        "label": "t:sections.main-product.blocks.fake_sales.settings.minqty.label",
        "default": 20,
        "min": 1,
        "max": 100,
        "step": 1
      },
      {
        "type": "range",
        "id": "maxqty",
         "label": "t:sections.main-product.blocks.fake_sales.settings.maxqty.label",
        "default": 30,
        "min": 1,
        "max": 100,
        "step": 1
      },
      {
        "type": "text",
        "id": "item_sold",
         "label": "t:sections.main-product.blocks.fake_sales.settings.item_sold.label",
        "default": "Sold"
      },
      {
        "type": "text",
        "id": "products",
        "label": "t:sections.main-product.blocks.fake_sales.settings.products.label",
        "default": "Products in last"
      },
      {
        "type": "text",
        "id": "hours",
        "label": "t:sections.main-product.blocks.fake_sales.settings.hours.label",
        "default": "Hours"
      }
  ]
  },
{
      "type": "complementary",
      "name": "t:sections.main-product.blocks.complementary_products.name",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "t:sections.main-product.blocks.complementary_products.settings.paragraph.content"
        },
        {
          "type": "text",
          "id": "block_heading",
          "default": "Pairs well with",
          "label": "t:sections.main-product.blocks.complementary_products.settings.heading.label"
        },
        {
          "type": "checkbox",
          "id": "make_collapsible_row",
          "default": false,
          "label": "t:sections.main-product.blocks.complementary_products.settings.make_collapsible_row.label"
        },
        {
          "type": "select",
          "id": "icon",
          "options": [
            {
              "value": "none",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__1.label"
            },
            {
              "value": "apple",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__2.label"
            },
            {
              "value": "banana",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__3.label"
            },
            {
              "value": "bottle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__4.label"
            },
            {
              "value": "box",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__5.label"
            },
            {
              "value": "carrot",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__6.label"
            },
            {
              "value": "chat_bubble",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__7.label"
            },
            {
              "value": "check_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__8.label"
            },
            {
              "value": "clipboard",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__9.label"
            },
            {
              "value": "dairy",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__10.label"
            },
            {
              "value": "dairy_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__11.label"
            },
            {
              "value": "dryer",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__12.label"
            },
            {
              "value": "eye",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__13.label"
            },
            {
              "value": "fire",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__14.label"
            },
            {
              "value": "gluten_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__15.label"
            },
            {
              "value": "heart",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__16.label"
            },
            {
              "value": "iron",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__17.label"
            },
            {
              "value": "leaf",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__18.label"
            },
            {
              "value": "leather",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__19.label"
            },
            {
              "value": "lightning_bolt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__20.label"
            },
            {
              "value": "lipstick",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__21.label"
            },
            {
              "value": "lock",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__22.label"
            },
            {
              "value": "map_pin",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__23.label"
            },
            {
              "value": "nut_free",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__24.label"
            },
            {
              "value": "pants",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__25.label"
            },
            {
              "value": "paw_print",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__26.label"
            },
            {
              "value": "pepper",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__27.label"
            },
            {
              "value": "perfume",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__28.label"
            },
            {
              "value": "plane",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__29.label"
            },
            {
              "value": "plant",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__30.label"
            },
            {
              "value": "price_tag",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__31.label"
            },
            {
              "value": "question_mark",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__32.label"
            },
            {
              "value": "recycle",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__33.label"
            },
            {
              "value": "return",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__34.label"
            },
            {
              "value": "ruler",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__35.label"
            },
            {
              "value": "serving_dish",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__36.label"
            },
            {
              "value": "shirt",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__37.label"
            },
            {
              "value": "shoe",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__38.label"
            },
            {
              "value": "silhouette",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__39.label"
            },
            {
              "value": "snowflake",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__40.label"
            },
            {
              "value": "star",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__41.label"
            },
            {
              "value": "stopwatch",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__42.label"
            },
            {
              "value": "truck",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__43.label"
            },
            {
              "value": "washing",
              "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__44.label"
            }
          ],
          "default": "price_tag",
          "info": "t:sections.main-product.blocks.complementary_products.settings.icon.info",
          "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.label"
        },
        {
          "type": "range",
          "id": "product_list_limit",
          "min": 1,
          "max": 10,
          "step": 1,
          "default": 10,
          "label": "t:sections.main-product.blocks.complementary_products.settings.product_list_limit.label"
        },
        {
          "type": "range",
          "id": "products_per_page",
          "min": 1,
          "max": 4,
          "step": 1,
          "default": 3,
          "label": "t:sections.main-product.blocks.complementary_products.settings.products_per_page.label"
        }
      ]
},
  {
  "type": "collapsible_tab",
  "name": "t:sections.main-product.blocks.collapsible_tab.name",
  "settings": [
  {
  "type": "text",
  "id": "heading",
  "default": "Collapsible row",
  "info": "t:sections.main-product.blocks.collapsible_tab.settings.heading.info",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.heading.label"
  },
  {
  "type": "select",
  "id": "icon",
  "options": [
  {
  "value": "none",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__1.label"
  },
  {
  "value": "apple",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__2.label"
  },
  {
  "value": "banana",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__3.label"
  },
  {
  "value": "bottle",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__4.label"
  },
  {
  "value": "box",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__5.label"
  },
  {
  "value": "carrot",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__6.label"
  },
  {
  "value": "chat_bubble",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__7.label"
  },
  {
  "value": "check_mark",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__8.label"
  },
  {
  "value": "clipboard",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__9.label"
  },
  {
  "value": "dairy",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__10.label"
  },
  {
  "value": "dairy_free",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__11.label"
  },
  {
  "value": "dryer",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__12.label"
  },
  {
  "value": "eye",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__13.label"
  },
  {
  "value": "fire",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__14.label"
  },
  {
  "value": "gluten_free",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__15.label"
  },
  {
  "value": "heart",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__16.label"
  },
  {
  "value": "iron",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__17.label"
  },
  {
  "value": "leaf",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__18.label"
  },
  {
  "value": "leather",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__19.label"
  },
  {
  "value": "lightning_bolt",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__20.label"
  },
  {
  "value": "lipstick",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__21.label"
  },
  {
  "value": "lock",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__22.label"
  },
  {
  "value": "map_pin",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__23.label"
  },
  {
  "value": "nut_free",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__24.label"
  },
  {
  "value": "pants",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__25.label"
  },
  {
  "value": "paw_print",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__26.label"
  },
  {
  "value": "pepper",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__27.label"
  },
  {
  "value": "perfume",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__28.label"
  },
  {
  "value": "plane",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__29.label"
  },
  {
  "value": "plant",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__30.label"
  },
  {
  "value": "price_tag",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__31.label"
  },
  {
  "value": "question_mark",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__32.label"
  },
  {
  "value": "recycle",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__33.label"
  },
  {
  "value": "return",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__34.label"
  },
  {
  "value": "ruler",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__35.label"
  },
  {
  "value": "serving_dish",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__36.label"
  },
  {
  "value": "shirt",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__37.label"
  },
  {
  "value": "shoe",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__38.label"
  },
  {
  "value": "silhouette",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__39.label"
  },
  {
  "value": "snowflake",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__40.label"
  },
  {
  "value": "star",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__41.label"
  },
  {
  "value": "stopwatch",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__42.label"
  },
  {
  "value": "truck",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__43.label"
  },
  {
  "value": "washing",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.options__44.label"
  }
  ],
  "default": "check_mark",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.icon.label"
  },
  {
  "type": "richtext",
  "id": "content",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.content.label"
  },
  {
  "type": "page",
  "id": "page",
  "label": "t:sections.main-product.blocks.collapsible_tab.settings.page.label"
  }
  ]
  },
  {
  "type": "popup",
  "name": "t:sections.main-product.blocks.popup.name",
  "settings": [
  {
  "type": "text",
  "id": "text",
  "default": "Pop-up link text",
  "label": "t:sections.main-product.blocks.popup.settings.link_label.label"
  },
  {
  "id": "page",
  "type": "page",
  "label": "t:sections.main-product.blocks.popup.settings.page.label"
  }
  ]
  },
  {
  "type": "size-popup",
  "name": "t:sections.main-product.blocks.size-popup.name",
  "settings": [
  {
  "type": "text",
  "id": "text",
  "default": "Size Guide",
  "label": "t:sections.main-product.blocks.size-popup.settings.link_label.label"
  },
  {
  "id": "page",
  "type": "page",
  "label": "t:sections.main-product.blocks.size-popup.settings.page.label"
  }
  ]
  }  
  ],
  "settings": [
    {
    "type":"checkbox",
    "id":"page_full_width",
     "default": false,
    "label": "t:sections.all.page_full_width.label"
  },
  {
    "type":"checkbox",
    "id":"page_full_width_spacing",
     "default": false,
    "label": "t:sections.all.page_full_width_spacing.label"
  },  
  {
  "type": "checkbox",
  "id": "enable_sticky_info",
  "default": true,
  "label": "t:sections.main-product.settings.enable_sticky_info.label"
  },
  {
  "type": "select",
  "id": "enable_product_zoom",
  "options": [
  {
  "value": "default",
  "label": "t:sections.main-product.settings.enable_product_zoom.default_zoom"
  },
  {
  "value": "zoom",
  "label": "t:sections.main-product.settings.enable_product_zoom.inner_zoom"
  },
  {
  "value": "outer",
  "label": "t:sections.main-product.settings.enable_product_zoom.outer_zoom"
  },
  {
  "value": "cloud",
  "label": "t:sections.main-product.settings.enable_product_zoom.cloud_zoom"
  }
  ],
  "default": "default",
  "label": "t:sections.main-product.settings.enable_product_zoom.label"
  },
  {
  "type": "header",
  "content": "t:sections.main-product.settings.header.content",
  "info": "t:sections.main-product.settings.header.info"
  },
  {
  "type": "select",
  "id": "gallery_layout",
  "options": [
  {
  "value": "stacked",
  "label": "t:sections.main-product.settings.gallery_layout.options__1.label"
  },
  {
  "value": "thumbnail",
  "label": "t:sections.main-product.settings.gallery_layout.options__2.label"
  },
  {
  "value": "thumbnail_slider",
  "label": "t:sections.main-product.settings.gallery_layout.options__3.label"
  }
  ],
  "default": "stacked",
  "label": "t:sections.main-product.settings.gallery_layout.label"
  },
  {
  "type": "select",
  "id": "stacked_column_alignment",
  "options": [
  {
  "value": "one",
  "label": "One"
  },
  {
  "value": "two",
  "label": "Two"
  }
  ],
  "default": "two",
  "label": "Stacked Column Alignment"
  },  
     {
  "type": "select",
  "id": "thumb_carousel_layout",
  "options": [
  {
  "value": "thumbnail_slider_left",
  "label": "t:sections.main-product.settings.thumb_carousel_layout.options__1.label"
  },
  {
  "value": "thumbnail_slider_right",
  "label": "t:sections.main-product.settings.thumb_carousel_layout.options__2.label"
  }
  ],
  "default": "thumbnail_slider_left",
  "label": "t:sections.main-product.settings.thumb_carousel_layout.label"
  },
  {
  "type": "select",
  "id": "media_size",
  "options": [
  {
  "value": "small",
  "label": "t:sections.main-product.settings.media_size.options__1.label"
  },
  {
  "value": "medium",
  "label": "t:sections.main-product.settings.media_size.options__2.label"
  },
  {
  "value": "large",
  "label": "t:sections.main-product.settings.media_size.options__3.label"
  }
  ],
  "default": "large",
  "label": "t:sections.main-product.settings.media_size.label",
  "info": "t:sections.main-product.settings.media_size.info"
  },
   {
  "type": "radio",
  "id": "meta_style",
  "label": "t:sections.main-product.settings.meta_style.label",
  "options": [
  {
  "value": "one-half",
  "label": "t:sections.main-product.settings.meta_style.options_1"
  },
  {
  "value": "full-width",
  "label": "t:sections.main-product.settings.meta_style.options_2"
  }
  ],
  "default": "one-half"
  },  
  {
  "type": "checkbox",
  "id": "hide_variants",
  "default": false,
  "label": "t:sections.main-product.settings.hide_variants.label"
  },
  {
  "type": "checkbox",
  "id": "enable_video_looping",
  "default": false,
  "label": "t:sections.main-product.settings.enable_video_looping.label"
  },
  {
  "type": "header",
  "content": "t:sections.main-product.settings.tab-position.heading_1"
  },
  {
  "type": "radio",
  "id": "tab_position",
  "label": "t:sections.main-product.settings.tab-position.label_1",
  "options": [
  {
  "value": "right",
  "label": "t:sections.main-product.settings.tab-position.options_1"
  },
  {
  "value": "bottom",
  "label": "t:sections.main-product.settings.tab-position.options_2"
  }
  ],
  "default": "bottom"
  },
  {
  "type": "radio",
  "id": "tab_style",
  "label": "t:sections.main-product.settings.tab-position.label_2",
  "options": [
  {
  "value": "tab",
  "label": "t:sections.main-product.settings.tab-position.options_3"
  },
  {
  "value": "toggle",
  "label": "t:sections.main-product.settings.tab-position.options_4"
  }
  ],
  "default": "tab"
  },
  {
  "type": "header",
  "content": "t:sections.all.padding.section_padding_heading"
  },
    {
  "type": "checkbox",
  "id": "show_wishlist",
  "default": false,
  "label": "t:sections.main-product.settings.show_wishlist.label"
  },
  {
  "type": "checkbox",
  "id": "show_review_tab",
  "default": false,
  "label": "t:sections.main-product.settings.show_review_tab.label"
  },
  {
  "type": "liquid",
  "id": "rating_script",
  "label": "t:sections.main-product.settings.rating_script.label",
  "info": "t:sections.main-product.settings.rating_script.info"
  },
  {
    "type": "checkbox",
    "id": "enable_sticky_bar",
    "default": true,
    "label": "Enable Sticky Bar"
  },
  {
  "type": "select",
  "id": "sidebar_settings",
  "options": [
  {
  "value": "sidebar-left",
  "label": "t:sections.main-product.settings.sidebar_settings.options__1.label"
  },
  {
  "value": "sidebar-right",
  "label": "t:sections.main-product.settings.sidebar_settings.options__2.label"
  },
  {
  "value": "no-sidebar",
  "label": "t:sections.main-product.settings.sidebar_settings.options__3.label"
  }
  ],
  "default": "no-sidebar",
  "label": "t:sections.main-product.settings.sidebar_settings.label"
  },
    {
  "type": "header",
  "content": "t:sections.main-product.settings.sidebar__4.content"
  },
  {
  "type": "checkbox",
  "id": "show_menu",
  "label": "t:sections.main-product.settings.show_menu.label"
  },
  {
  "type": "text",
  "id": "menu_title",
  "default": "Heading",
  "label": "t:sections.main-product.settings.menu_title.label"
  },
  {
  "type": "link_list",
  "id": "linklist",
  "label": "t:sections.main-product.settings.linklist.label"
  },
  {
  "type": "header",
  "content": "t:sections.main-product.settings.sidebar__1.content"
  },
  {
  "type": "checkbox",
  "id": "show_image",
  "label": "t:sections.main-product.settings.show_image.label"
  },
  {
  "type": "image_picker",
  "id": "sidebar_image",
  "label": "t:sections.main-product.settings.sidebar_image.label"
  },
  {
  "type": "text",
  "id": "sidebar_title",
  "default": "Heading",
  "label": "t:sections.main-product.settings.sidebar_title.label"
  },
  {
  "type": "text",
  "id": "sidebar_button",
  "default": "Shop Now",
  "label": "t:sections.main-product.settings.sidebar_button.label"
  },
  {
  "type": "url",
  "id": "sidebar_link",
  "label": "t:sections.main-product.settings.sidebar_link.label"
  },
  {
  "type": "header",
  "content": "t:sections.main-product.settings.sidebar__3.content"
  },
  {
  "type": "checkbox",
  "id": "show_collection",
  "label": "t:sections.main-product.settings.show_collection.label"
  },
  {
  "type": "text",
  "id": "collection_title",
  "default": "Heading",
  "label": "t:sections.main-product.settings.collection_title.label"
  },
  {
  "type": "collection",
  "id": "collection",
  "label": "t:sections.main-product.settings.collection.label"
  },
  {
  "type": "range",
  "id": "collection_limit",
  "min": 1,
  "max": 5,
  "step": 1,
  "label": "t:sections.main-product.settings.collection_limit.label",
  "default": 5
  },
   {
"type": "header",
"content": "t:sections.main-collection-product-grid.settings.sidebar__2.content"
},
{
"type": "checkbox",
"id": "show_carousel",
"label": "t:sections.main-collection-product-grid.settings.show_carousel.label"
},
{
"type": "text",
"id": "carousel_title",
"default": "Heading",
"label": "t:sections.main-collection-product-grid.settings.carousel_title.label"
}, 
  
    {
      "type": "header",
      "content": "t:sections.main-product.settings.content.label"     
    }, 
     {
      "type": "text",
      "id": "iconwithtext",
      "default": "Heading",
      "label": "t:sections.main-product.settings.iconwithtext.label"      
    },
    {
      "type": "text",
      "id": "heading_1",
      "default": "Heading",
      "label": "t:sections.main-product.settings.heading_1.label"      
    },    
    {
      "type": "inline_richtext",
      "id": "heading_1_text",
      "default": "Text",
      "label": "t:sections.main-product.settings.heading_1_text.label"      
    }, 
    {
      "type": "image_picker",
      "id": "heading_1_icon",
      "label": "t:sections.main-product.settings.heading_1_icon.label"
    },
    {
      "type": "text",
      "id": "heading_2",
      "default": "Heading",
      "label": "t:sections.main-product.settings.heading_2.label"      
    },    
    {
      "type": "inline_richtext",
      "id": "heading_2_text",
      "default": "Text",
      "label": "t:sections.main-product.settings.heading_2_text.label"      
    }, 
    {
      "type": "image_picker",
      "id": "heading_2_icon",
      "label": "t:sections.main-product.settings.heading_2_icon.label"
    },  
    {
      "type": "text",
      "id": "heading_3",
      "default": "Heading",
      "label": "t:sections.main-product.settings.heading_3.label"      
    },    
    {
      "type": "inline_richtext",
      "id": "heading_3_text",
      "default": "Text",
      "label": "t:sections.main-product.settings.heading_3_text.label"      
    }, 
    {
      "type": "image_picker",
      "id": "heading_3_icon",
      "label": "t:sections.main-product.settings.heading_3_icon.label"
    }, 
    {
  "type": "html",
  "id": "html",
  "label": "t:sections.main-product.settings.html.label"  
  },
    {
  "type": "checkbox",
  "id": "enable_enquiry",
  "label": "t:sections.main-product.settings.enable_enquiry.label",
  "default": false
  },
  {
    "type":"image_picker",
    "id":"enquiry_image",
    "label": "t:sections.main-product.settings.enquiry_image.label"
  },
  {
  "type": "range",
  "id": "padding_top",
  "min": 0,
  "max": 100,
  "step": 5,
  "unit": "px",
  "label": "t:sections.all.padding.padding_top",
  "default": 35
  },
  {
  "type": "range",
  "id": "padding_bottom",
  "min": 0,
  "max": 100,
  "step": 5,
  "unit": "px",
  "label": "t:sections.all.padding.padding_bottom",
  "default": 35
  },
  {
  "type": "checkbox",
  "id": "display_back_in_stock",
  "label": "t:sections.all.notify.enable",
  "default": false
  },
  {
  "type": "text",
  "id": "notify_text",
  "label": "t:sections.all.notify.notify_text"
  },
  {
  "type": "text",
  "id": "notify_error",
  "label": "t:sections.all.notify.notify_error"
  },
  {
  "type": "checkbox",
  "id": "show_adv_product",
  "label": "t:sections.main-product.settings.show_adv_product.label",
  "default": false
  },
  {
  "type": "text",
  "id": "choose_label",
  "label": "t:sections.main-product.settings.choose_label.label",
  "default":"Choose style"
  },
  {
  "type": "checkbox",
  "id": "prev_next_products",  
  "label": "t:sections.main-product.settings.prev_next_products.label",
  "default": false
  },
    {
  "type": "checkbox",
  "id": "show_share",
   "label": "t:sections.main-product.settings.show_share.label",
  "default": false
  },
    {
  "type": "text",
  "id": "share_label",
   "label": "t:sections.main-product.settings.share_label.label",
  "default": "Share"
  },
    {
  "type": "checkbox",
  "id": "show_quantity_selector",
   "label": "t:sections.main-product.settings.show_quantity_selector.label",
  "default": false
  }   
  ]
  }
{% endschema %}
