{{ 'component-image-banner.css' | asset_url | stylesheet_tag }}
{{ 'component-slider.css' | asset_url | stylesheet_tag }}
{{ 'component-slideshow.css' | asset_url | stylesheet_tag }}
{{ 'section-image-banner.css' | asset_url | stylesheet_tag }}
{%- style -%}
{%- if section.blocks.first.settings.image != blank -%}
  
  @media screen and (max-width: 749px) {
    #Slider-{{ section.id }}::before,
    #Slider-{{ section.id }} .media::before,
    #Slider-{{ section.id }}:not(.banner--mobile-bottom) .banner__content::before {
      padding-bottom: {{ 1 | divided_by: section.blocks.first.settings.image.aspect_ratio | times: 100 }}%;
      content: '';
      display: block;
    }
  }

  @media screen and (min-width: 750px) {
    #Slider-{{ section.id }}::before,
    #Slider-{{ section.id }} .media::before {
      padding-bottom: {{ 1 | divided_by: section.blocks.first.settings.image.aspect_ratio | times: 100 }}%;
      content: '';
      display: block;
    }
  }
     {%- for block in section.blocks -%}      
        #Slide-{{ section.id }}-{{ forloop.index }} .banner__media::after {
          opacity: {{ block.settings.image_overlay_opacity | divided_by: 100.0 }};
        }      
    {%  endfor %}
{%- endif -%}  


      .section-{{ section.id }}-slideshow .slide-banner { height:{{section.settings.slider_image_height}}px;}
      .section-{{ section.id }}-slideshow  .banner--mobile-bottom .slideshow__text-wrapper.banner__content{position:absolute;}
  
   @media screen and (max-width: 1540px) {   
    .section-{{ section.id }}-slideshow .slide-banner { height:{{section.settings.slider_image_height_laptop}}px;}
    }
   @media screen and (max-width: 990px) { 
    .section-{{ section.id }}-slideshow .slide-banner { height:{{section.settings.slider_image_height_tab}}px;}
    }
    @media screen and (max-width: 576px) {
    .section-{{ section.id }}-slideshow .slide-banner { height:{{section.settings.slider_image_height_mobile}}px;}
    .section-{{ section.id }}-slideshow .slide-banner.banner--mobile-bottom{height:100%;}
    .section-{{ section.id }}-slideshow .banner--mobile-bottom .slideshow__text-wrapper.banner__content {position:relative;} 
    }

 {%- endstyle -%} 

<div class="{% if section.settings.page_full_width %} page-full-width {% else %} page-width {% endif %} {% if section.settings.page_full_width_spacing %} page-full-width_spacing {% endif %} {{ section.settings.custom_class_name }} section-{{ section.id }}-slideshow">
<slideshow-component class="{% if section.settings.show_text_below %} mobile-text-below{% endif %}">
   <div class="row">
  <div data-slider-options='{"loop": "true","effect": "{{ section.settings.slider_effect }}", "auto_play": "{{ section.settings.auto_play }}"}'>
  <div class="swiper" data-swiper-slider>
  <div class="swiper-wrapper slide-banner   {% if section.settings.show_text_below %} banner--mobile-bottom {% endif %}{% if section.blocks.first.settings.image == blank %} slideshow--placeholder{% endif %}" id="Slider-{{ section.id }}">
    {%- for block in section.blocks -%}      
      <div class="swiper-slide"
        id="Slide-{{ section.id }}-{{ forloop.index }}"
        {{ block.shopify_attributes }}
        role="group"
        aria-roledescription="{{ 'sections.slideshow.slide' | t }}"
        aria-label="{{ forloop.index }} {{ 'general.slider.of' | t }} {{ forloop.length }}"
        tabindex="-1"
      >
        <div class="slideshow__media  {% if block.settings.image == blank %} placeholder{% endif %}">
          {%- if block.settings.image != blank -%}
            <img
              srcset="{%- if block.settings.image.width >= 375 -%}{{ block.settings.image | image_url: width: 375 }} 375w,{%- endif -%}
              {%- if block.settings.image.width >= 550 -%}{{ block.settings.image | image_url: width: 550 }} 550w,{%- endif -%}
              {%- if block.settings.image.width >= 750 -%}{{ block.settings.image | image_url: width: 750 }} 750w,{%- endif -%}
              {%- if block.settings.image.width >= 1100 -%}{{ block.settings.image | image_url: width: 1100 }} 1100w,{%- endif -%}
              {%- if block.settings.image.width >= 1500 -%}{{ block.settings.image | image_url: width: 1500 }} 1500w,{%- endif -%}
              {%- if block.settings.image.width >= 1780 -%}{{ block.settings.image | image_url: width: 1780 }} 1780w,{%- endif -%}
              {%- if block.settings.image.width >= 2000 -%}{{ block.settings.image | image_url: width: 2000 }} 2000w,{%- endif -%}
              {%- if block.settings.image.width >= 3000 -%}{{ block.settings.image | image_url: width: 3000 }} 3000w,{%- endif -%}
              {%- if block.settings.image.width >= 3840 -%}{{ block.settings.image | image_url: width: 3840 }} 3840w,{%- endif -%}
              {{ block.settings.image | image_url }} {{ block.settings.image.width }}w"
              sizes="100vw"
              src="{{ block.settings.image | image_url: width: 3000 }}"
              loading="lazy"
              alt="{{ block.settings.image.alt | escape }}"
              width="{{ block.settings.image.width }}"
              height="{{ block.settings.image.width | divided_by: block.settings.image.aspect_ratio | round }}"
              class="desktop-slider"
            >
          {%- else -%}
                {%- assign placeholder_slide = forloop.index | modulo: 2 -%}
                {%- if placeholder_slide == 1 -%}
                {{ 'hero-apparel-2' | placeholder_svg_tag: 'placeholder-svg' }}
                {%- else -%}
                {{ 'hero-apparel-1' | placeholder_svg_tag: 'placeholder-svg' }}
            {% endif %}
          {%- endif -%}
         {%- if block.settings.mobile_image -%}
            <img
              srcset="{%- if block.settings.mobile_image.width >= 375 -%}{{ block.settings.mobile_image | image_url: width: 375 }} 375w,{%- endif -%}
              {%- if block.settings.mobile_image.width >= 550 -%}{{ block.settings.mobile_image | image_url: width: 550 }} 550w,{%- endif -%}
              {%- if block.settings.mobile_image.width >= 750 -%}{{ block.settings.mobile_image | image_url: width: 750 }} 750w,{%- endif -%}
              {%- if block.settings.mobile_image.width >= 1100 -%}{{ block.settings.mobile_image | image_url: width: 1100 }} 1100w,{%- endif -%}
              {%- if block.settings.mobile_image.width >= 1500 -%}{{ block.settings.mobile_image | image_url: width: 1500 }} 1500w,{%- endif -%}
              {%- if block.settings.mobile_image.width >= 1780 -%}{{ block.settings.mobile_image | image_url: width: 1780 }} 1780w,{%- endif -%}
              {%- if block.settings.mobile_image.width >= 2000 -%}{{ block.settings.mobile_image | image_url: width: 2000 }} 2000w,{%- endif -%}
              {%- if block.settings.mobile_image.width >= 3000 -%}{{ block.settings.mobile_image | image_url: width: 3000 }} 3000w,{%- endif -%}
              {%- if block.settings.mobile_image.width >= 3840 -%}{{ block.settings.mobile_image | image_url: width: 3840 }} 3840w,{%- endif -%}
              {{ block.settings.mobile_image | image_url }} {{ block.settings.mobile_image.width }}w"
              sizes="100vw"
              src="{{ block.settings.mobile_image | image_url: width: 1500 }}"
              loading="lazy"
              alt="{{ block.settings.mobile_image.alt | escape }}"
              width="{{ block.settings.mobile_image.width }}"
              height="{{ block.settings.mobile_image.width | divided_by: block.settings.mobile_image.aspect_ratio | round }}"
              class="mobile-slider"
            >          
          {%- endif -%}

          
        </div>
        <div class="slideshow__text-wrapper banner__content banner__content--{{ block.settings.box_align }} page-width{% if block.settings.show_text_box == false %} banner--desktop-transparent{% endif %}">
          <div class="slideshow__text banner__box content-container content-container--full-width-mobile color-{{ block.settings.color_scheme }} gradient slideshow__text--{{ block.settings.text_alignment }} slideshow__text-mobile--{{ block.settings.text_alignment_mobile }}">
            {%- if block.settings.subheading != blank -%}
            <p class="banner__sub_heading">{{ block.settings.subheading }}</p>
            {%- endif -%}
            {%- if block.settings.heading != blank -%}
              <h2 class="banner__heading {{ block.settings.heading_size }}">{{ block.settings.heading }}</h2>
            {%- endif -%}           
            {%- if block.settings.text != blank -%}
              <div class="banner__text" {{ block.shopify_attributes }}>
                <span>{{ block.settings.text }}</span>
              </div>
            {%- endif -%}
            {%- if block.settings.button_label != blank -%}
              <div class="banner__buttons">
                <a{% if block.settings.link %} href="{{ block.settings.link }}"{% else %} role="link" aria-disabled="true"{% endif %} class="button {% if block.settings.button_style_secondary %}button--secondary{% else %}button--primary{% endif %}">{{ block.settings.button_label | escape }}</a>
              </div>
            {%- endif -%}
          </div>
        </div>
      </div>
    {%- endfor -%}
  </div>
    {% if section.settings.swiper_navigation != blank %}
    <div class="swiper-button-next"><span></span></div>
    <div class="swiper-button-prev"><span></span></div>
    {% endif %}    
    {% if section.settings.swiper_pagination != blank %}
    <div class="swiper-pagination"></div>
    {% endif %}
    </div>
  </div>
   </div>
</slideshow-component>
</div>
{%- if request.design_mode -%}
  <script src="{{ 'theme-editor.js' | asset_url }}" defer="defer"></script>
{%- endif -%}

{% schema %}
{
  "name": "t:sections.slideshow.name",
  "tag": "section",
  "class": "section slideshow slidershow",
  "settings": [
    {
      "type":"checkbox",
      "id":"page_full_width",
       "default": false,
      "label": "t:sections.all.page_full_width.label"
    },
    {
      "type":"checkbox",
      "id":"page_full_width_spacing",
       "default": false,
      "label": "t:sections.all.page_full_width_spacing.label"
    },
      {
      "type": "header",
      "content": "t:sections.slideshow.settings.slider_image_height_settings.content"
    },
    {
      "type": "range",
      "id": "slider_image_height",
      "min": 400,
      "max": 1200,
      "step": 10,
      "unit": "px",
      "default": 900,
      "label": "t:sections.slideshow.settings.slider_image_height.label"
   },
     {
      "type": "range",
      "id": "slider_image_height_laptop",
      "min": 400,
      "max": 1200,
      "step": 10,
      "unit": "px",
      "default": 700,
      "label": "t:sections.slideshow.settings.slider_image_height_laptop.label"
    },
     {
      "type": "range",
      "id": "slider_image_height_tab",
       "min": 400,
      "max": 1200,
      "step": 10,
      "unit": "px",
      "default": 500,
      "label": "t:sections.slideshow.settings.slider_image_height_tab.label"
    },
     {
      "type": "range",
      "id": "slider_image_height_mobile",
      "min": 400,
      "max": 1200,
      "step": 10,
      "unit": "px",
      "default": 400,
      "label": "t:sections.slideshow.settings.slider_image_height_mobile.label"
    },
    {
      "type": "header",
      "content": "t:sections.all.swiper.swiper_slider_title"
    },
    {
      "type": "checkbox",
      "id": "swiper_pagination",
      "default": false,
      "label": "t:sections.all.swiper.swiper_pagination"
    },
    {
      "type": "checkbox",
      "id": "swiper_navigation",
      "default": false,
      "label": "t:sections.all.swiper.swiper_navigation"
    },
    {
      "type": "range",
      "id": "auto_play",
      "min": 0,
      "max": 5,
      "step": 1,
      "label": "t:sections.all.swiper.auto_play",
      "default": 0
    },
    {
      "type": "select",
      "id": "slider_effect",
      "options": [
        {
          "value": "slide",
          "label": "t:sections.all.swiper.slide"
        },
        {
          "value": "fade",
          "label": "t:sections.all.swiper.fade"
        }
      ],
      "default": "slide",
      "label": "t:sections.all.swiper.effects"
    },
     {
      "type": "header",
      "content": "t:sections.slideshow.settings.mobile.content"
    },
    {
      "type": "checkbox",
      "id": "show_text_below",
      "label": "t:sections.slideshow.settings.show_text_below.label",
      "default": true
    },
    {
    "type": "header",
    "content": "t:sections.all.custom_class_heading.content"
    },
    {
    "type": "text",
    "id": "custom_class_name",
    "label": "t:sections.all.custom_class_name.label"
    }
  ],
  "blocks": [
    {
      "type": "slide",
      "name": "t:sections.slideshow.blocks.slide.name",
      "limit": 5,
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.slideshow.blocks.slide.settings.image.label"
        },
        {
          "type": "image_picker",
          "id": "mobile_image",
          "label": "t:sections.slideshow.blocks.slide.settings.mobile_image.label"
        },
        {
          "type": "text",
          "id": "heading",
          "default": "Image slide",
          "label": "t:sections.slideshow.blocks.slide.settings.heading.label"
        },
        {
          "type": "select",
          "id": "heading_size",
          "options": [
            {
              "value": "h2",
              "label": "t:sections.all.heading_size.options__1.label"
            },
            {
              "value": "h1",
              "label": "t:sections.all.heading_size.options__2.label"
            },
            {
              "value": "h0",
              "label": "t:sections.all.heading_size.options__3.label"
            }
          ],
          "default": "h1",
          "label": "t:sections.all.heading_size.label"
        },
        {
          "type": "text",
          "id": "subheading",
          "default": "Image sub heading",
          "label": "t:sections.slideshow.blocks.slide.settings.subheading.label"
        },
			{
          "type": "text",
          "id": "text",
          "default": "Tell your brand's story through images",
          "label": "t:sections.slideshow.blocks.slide.settings.text.label"
        },
        {
          "type": "text",
          "id": "button_label",
          "default": "Button label",
          "label": "t:sections.slideshow.blocks.slide.settings.button_label.label",
          "info": "t:sections.slideshow.blocks.slide.settings.button_label.info"
        },
        {
          "type": "url",
          "id": "link",
          "label": "t:sections.slideshow.blocks.slide.settings.link.label"
        },
        {
          "type": "checkbox",
          "id": "button_style_secondary",
          "label": "t:sections.slideshow.blocks.slide.settings.secondary_style.label",
          "default": false
        },
        {
          "type": "select",
          "id": "box_align",
          "options": [
            {
              "value": "top-left",
              "label": "t:sections.slideshow.blocks.slide.settings.box_align.options__1.label"
            },
            {
              "value": "top-center",
              "label": "t:sections.slideshow.blocks.slide.settings.box_align.options__2.label"
            },
            {
              "value": "top-right",
              "label": "t:sections.slideshow.blocks.slide.settings.box_align.options__3.label"
            },
            {
              "value": "middle-left",
              "label": "t:sections.slideshow.blocks.slide.settings.box_align.options__4.label"
            },
            {
              "value": "middle-center",
              "label": "t:sections.slideshow.blocks.slide.settings.box_align.options__5.label"
            },
            {
              "value": "middle-right",
              "label": "t:sections.slideshow.blocks.slide.settings.box_align.options__6.label"
            },
            {
              "value": "bottom-left",
              "label": "t:sections.slideshow.blocks.slide.settings.box_align.options__7.label"
            },
            {
              "value": "bottom-center",
              "label": "t:sections.slideshow.blocks.slide.settings.box_align.options__8.label"
            },
            {
              "value": "bottom-right",
              "label": "t:sections.slideshow.blocks.slide.settings.box_align.options__9.label"
            }
          ],
          "default": "middle-center",
          "label": "t:sections.slideshow.blocks.slide.settings.box_align.label",
          "info": "t:sections.slideshow.blocks.slide.settings.box_align.info"
        },
        {
          "type": "checkbox",
          "id": "show_text_box",
          "label": "t:sections.slideshow.blocks.slide.settings.show_text_box.label",
          "default": true
        },
        {
          "type": "select",
          "id": "text_alignment",
          "options": [
            {
              "value": "left",
              "label": "t:sections.slideshow.blocks.slide.settings.text_alignment.option_1.label"
            },
            {
              "value": "center",
              "label": "t:sections.slideshow.blocks.slide.settings.text_alignment.option_2.label"
            },
            {
              "value": "right",
              "label": "t:sections.slideshow.blocks.slide.settings.text_alignment.option_3.label"
            }
          ],
          "default": "center",
          "label": "t:sections.slideshow.blocks.slide.settings.text_alignment.label"
        },
        {
          "type": "range",
          "id": "image_overlay_opacity",
          "min": 0,
          "max": 100,
          "step": 10,
          "unit": "%",
          "label": "t:sections.slideshow.blocks.slide.settings.image_overlay_opacity.label",
          "default": 0
        },
        {
          "type": "select",
          "id": "color_scheme",
          "options": [
            {
              "value": "accent-1",
              "label": "t:sections.all.colors.accent_1.label"
            },
            {
              "value": "accent-2",
              "label": "t:sections.all.colors.accent_2.label"
            },
            {
              "value": "background-1",
              "label": "t:sections.all.colors.background_1.label"
            },
            {
              "value": "background-2",
              "label": "t:sections.all.colors.background_2.label"
            },
            {
              "value": "inverse",
              "label": "t:sections.all.colors.inverse.label"
            }
          ],
          "default": "background-1",
          "label": "t:sections.all.colors.label",
          "info": "t:sections.slideshow.blocks.slide.settings.color_scheme.info"
        },
        {
          "type": "header",
          "content": "t:sections.slideshow.settings.mobile.content"
        },
        {
          "type": "select",
          "id": "text_alignment_mobile",
          "options": [
            {
              "value": "left",
              "label": "t:sections.slideshow.blocks.slide.settings.text_alignment_mobile.options__1.label"
            },
            {
              "value": "center",
              "label": "t:sections.slideshow.blocks.slide.settings.text_alignment_mobile.options__2.label"
            },
            {
              "value": "right",
              "label": "t:sections.slideshow.blocks.slide.settings.text_alignment_mobile.options__3.label"
            }
          ],
          "default": "center",
          "label": "t:sections.slideshow.blocks.slide.settings.text_alignment_mobile.label"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.slideshow.presets.name",
      "blocks": [
        {
          "type": "slide"
        },
        {
          "type": "slide"
        }
      ]
    }
  ]
}
{% endschema %}
