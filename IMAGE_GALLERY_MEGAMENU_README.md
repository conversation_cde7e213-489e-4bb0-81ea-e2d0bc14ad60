# Custom Image Gallery Mega Menu

This feature adds a custom mega menu section that displays up to 9 linkable images in a responsive grid layout when hovering over specific navigation menu items.

## Features

- **Up to 9 Images**: Add up to 9 linkable images per mega menu
- **Responsive Design**: Images automatically align and resize based on screen size
- **Customizable Settings**:
  - Adjustable image height for desktop and mobile
  - Configurable border radius (None, Small, Medium, Large, Round)
  - Custom mega menu width
  - Individual image hiding capability
- **Linkable Images**: Each image can have its own URL and title
- **Smooth Animations**: Images fade in with staggered animations
- **Hover Effects**: Images have hover effects with overlay titles

## How to Set Up

### 1. Add Image Gallery Block to Header

1. Go to **Customize** → **Header** in your Shopify admin
2. Click **Add block** and select **Image Gallery**
3. Configure the following settings:

#### Basic Settings
- **Map menu item**: Enter the exact title of the menu item you want to target (case-sensitive)
- **Custom width**: Enable if you want a custom width for this specific mega menu
- **Custom mega menu width**: Set the width percentage (25-100%)

#### Gallery Settings
- **Image height (Desktop)**: Set height in pixels (100-400px)
- **Image height (Mobile)**: Set height in pixels (100-300px)
- **Image border radius**: Choose from None, Small, Medium, Large, or Round

#### Images (1-9)
For each image slot:
- **Image**: Upload your image (recommended: 400 x 300 px)
- **Image title**: Add a title that appears on hover
- **Image link**: Set the URL to link to when clicked
- **Hide this image**: Toggle to hide/show specific images

### 2. Create Navigation Menu

1. Go to **Online Store** → **Navigation** in your Shopify admin
2. Create or edit your main navigation menu
3. Add menu items that match the "Map menu item" you specified in the Image Gallery block
4. The mega menu will appear when hovering over these menu items

### 3. Menu Item Mapping

**Important**: The "Map menu item" field must exactly match your navigation menu item title:

- ✅ **Correct**: If your menu item is "Categories", enter "Categories"
- ❌ **Incorrect**: If your menu item is "Categories", entering "categories" won't work
- ❌ **Incorrect**: Adding extra spaces or characters won't work

## Automatic Layout

The image gallery automatically adjusts its layout based on the number of visible images:

- **1 image**: Centered single column
- **2 images**: 2 columns
- **3 images**: 3 columns
- **4 images**: 2×2 grid (desktop), 2×2 (mobile)
- **5-6 images**: 3 column layout (adjusts to 5/6 columns on larger screens)
- **7-9 images**: 3 column layout with wrapping

## Responsive Behavior

- **Desktop**: Full grid layout with hover effects and animations
- **Tablet**: Adjusted grid with appropriate spacing
- **Mobile**: Optimized for smaller screens with reduced image heights

## Customization Options

### CSS Variables
You can customize the appearance by modifying these CSS variables in the theme:

```css
--image-height: 200px; /* Desktop image height */
--image-height-mobile: 150px; /* Mobile image height */
--image-border-radius: 8px; /* Border radius */
```

### Advanced Styling
The following CSS classes can be targeted for custom styling:

- `.mega-menu__image-gallery`: Main container
- `.image-gallery-container`: Grid container
- `.image-gallery-item`: Individual image items
- `.image-overlay`: Hover overlay with title
- `.image-title`: Image title text

## Accessibility Features

- **Keyboard Navigation**: Images are focusable and can be activated with Enter/Space
- **Screen Reader Support**: Proper alt text and ARIA labels
- **Focus Indicators**: Clear focus states for keyboard users

## Troubleshooting

### Mega Menu Not Appearing
1. Check that the "Map menu item" exactly matches your navigation menu item title
2. Ensure at least one image is uploaded and not hidden
3. Verify the navigation menu is assigned to the header in theme settings

### Images Not Loading
1. Check image file sizes (recommended under 500KB each)
2. Ensure images are uploaded correctly in the theme customizer
3. Verify image URLs are working properly

### Layout Issues
1. Check responsive settings for different screen sizes
2. Adjust image heights if images appear distorted
3. Consider reducing the number of images if layout looks crowded

## Browser Support

This feature is compatible with all modern browsers:
- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+

## Performance Notes

- Images are lazy-loaded for better performance
- CSS and JavaScript are optimized and minified
- Animations use CSS transforms for smooth performance
- Images should be optimized (WebP format recommended)

## File Structure

The image gallery mega menu feature consists of:

- `assets/component-image-gallery-megamenu.css` - Main stylesheet
- `assets/image-gallery-megamenu.js` - JavaScript functionality
- `snippets/dt-megaImageGallery.liquid` - Image gallery template
- `sections/header.liquid` - Updated header with new block type
- `snippets/navigation.liquid` - Updated navigation logic
