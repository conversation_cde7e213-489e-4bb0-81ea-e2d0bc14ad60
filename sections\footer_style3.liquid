{% comment %}theme-check-disable UndefinedObject{% endcomment %}
{{ 'section-footer-style3.css' | asset_url | stylesheet_tag }}
<link rel="stylesheet" href="{{ 'component-newsletter.css' | asset_url }}" media="print" onload="this.media='all'">
<link rel="stylesheet" href="{{ 'component-list-menu.css' | asset_url }}" media="print" onload="this.media='all'">
<link rel="stylesheet" href="{{ 'component-list-payment.css' | asset_url }}" media="print" onload="this.media='all'">
<link rel="stylesheet" href="{{ 'component-list-social.css' | asset_url }}" media="print" onload="this.media='all'">
<link rel="stylesheet" href="{{ 'component-rte.css' | asset_url }}" media="print" onload="this.media='all'">
<link rel="stylesheet" href="{{ 'disclosure.css' | asset_url }}" media="print" onload="this.media='all'">

<noscript>{{ 'component-newsletter.css' | asset_url | stylesheet_tag }}</noscript>
<noscript>{{ 'component-list-menu.css' | asset_url | stylesheet_tag }}</noscript>
<noscript>{{ 'component-list-payment.css' | asset_url | stylesheet_tag }}</noscript>
<noscript>{{ 'component-list-social.css' | asset_url | stylesheet_tag }}</noscript>
<noscript>{{ 'component-rte.css' | asset_url | stylesheet_tag }}</noscript>
<noscript>{{ 'disclosure.css' | asset_url | stylesheet_tag }}</noscript>

{%- style -%}
  .footer-style3 {
    margin-top: {{ section.settings.margin_top | times: 0.75 | round: 0 }}px;
  }

    .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.50 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.50 | round: 0 }}px;
  }
   @media screen and (min-width: 576px) and (max-width: 749px){
   .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }
   }

  @media screen and (min-width: 750px) {
    .footer-style3 {
      margin-top: {{ section.settings.margin_top }}px;
    }

    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}

<footer  id="Banner-{{ section.id }}" class="{% if request.page_type == 'index' %}index-footer{% endif %} footer-style3 color-{{ section.settings.color_scheme }} gradient  {% if section.settings.footer_default %}theme__default-footer_style{% endif %}">
  {%- if section.settings.image != blank -%}
   <div class="banner__media media">
  <img
        srcset="{%- if section.settings.image.width >= 375 -%}{{ section.settings.image | image_url: width: 375 }} 375w,{%- endif -%}
          {%- if section.settings.image.width >= 550 -%}{{ section.settings.image | image_url: width: 550 }} 550w,{%- endif -%}
          {%- if section.settings.image.width >= 750 -%}{{ section.settings.image | image_url: width: 750 }} 750w,{%- endif -%}
          {%- if section.settings.image.width >= 1100 -%}{{ section.settings.image | image_url: width: 1100 }} 1100w,{%- endif -%}
          {%- if section.settings.image.width >= 1500 -%}{{ section.settings.image | image_url: width: 1500 }} 1500w,{%- endif -%}
          {%- if section.settings.image.width >= 1780 -%}{{ section.settings.image | image_url: width: 1780 }} 1780w,{%- endif -%}
          {%- if section.settings.image.width >= 2000 -%}{{ section.settings.image | image_url: width: 2000 }} 2000w,{%- endif -%}
          {%- if section.settings.image.width >= 3000 -%}{{ section.settings.image | image_url: width: 3000 }} 3000w,{%- endif -%}
          {%- if section.settings.image.width >= 3840 -%}{{ section.settings.image | image_url: width: 3840 }} 3840w,{%- endif -%}
          {{ section.settings.image | image_url }} {{ section.settings.image.width }}w"
        sizes="{% if section.settings.image_2 != blank and section.settings.stack_images_on_mobile %}(min-width: 750px) 50vw, 100vw{% elsif section.settings.image_2 != blank %}50vw{% else %}100vw{% endif %}"
        src="{{ section.settings.image | image_url: width: 1500 }}"
        loading="lazy"
        alt="{{ section.settings.image.alt | escape }}"
        width="{{ section.settings.image.width }}"
        height="{{ section.settings.image.width | divided_by: section.settings.image.aspect_ratio }}"
        {% if section.settings.image_2 != blank %}class="banner__media-image-half"{% endif %}
      >
   </div>
  {%- endif -%}  
  {%- if section.blocks.size > 0 or section.settings.show_social -%}
    <div class="footer__content-top {% if section.settings.page_full_width %} page-full-width {% else %} page-width {% endif %} {% if section.settings.page_full_width_spacing %} page-full-width_spacing {% endif %}">      
    <div class="row">
    {%- if section.blocks.size > 0 -%}
      
        {%- liquid
          if section.blocks.size == 9
            assign footer_grid_class = 'grid--3-col-tablet'
          elsif section.blocks.size > 6
            assign footer_grid_class = 'grid--4-col-desktop'
          elsif section.blocks.size > 5
            assign footer_grid_class = 'grid--4-col-tablet'
          endif
        -%}
      
      
        <div class="footer__blocks-wrapper grid grid--1-col grid--2-col grid--4-col-tablet {{ footer_grid_class }} {%- if section.settings.border_right != blank -%}border-right{% endif %} section-{{ section.id }}-padding">
          {%- for block in section.blocks -%}
            <div class="footer-block grid__item {% if block.type == 'address' %}footer_address {% endif %}{% if block.type == 'link_list' and block.settings.heading != blank %} footer-block--menu footer-links{% endif %} {{ block.settings.alignment }}" {{ block.shopify_attributes }}>
             {%- if block.settings.sub_heading != blank -%}
                <h2 class="footer-block__subheading {{ block.settings.alignment }}">{{- block.settings.sub_heading | escape -}}</h2>
              {%- endif -%}
            {%- if block.settings.heading != blank -%}
                <h2 class="footer-block__heading  {% if block.type == 'link_list' %}footer__title{% endif %} {{ block.settings.alignment }}">{{- block.settings.heading | escape -}}</h2>
              {%- endif -%}

              {%- case block.type -%}
                {%- when 'text' -%}
                    <div class="footer-block">
                   {%- if block.settings.image != blank -%}
                      {%- assign image_size_2x = block.settings.image_width | times: 2 | at_most: 5760 -%}
                      <img
                        srcset= "{{ block.settings.image | image_url: width: block.settings.image_width }}, {{ block.settings.image | image_url: width: image_size_2x }} 2x"
                        src="{{ block.settings.image | image_url: width: 400 }}"
                        alt="{{ block.settings.image.alt | escape }}"
                        loading="lazy"
                        width="{{ block.settings.image.width }}"
                        height="{{ block.settings.image.height }}"
                        style="max-width: min(100%, {{ block.settings.image_width }}px);"
                      >
                    {%- else -%}
                      {{ 'image' | placeholder_svg_tag: 'placeholder-svg placeholder' }}
                    {%- endif -%}
                  <div class="footer-block__details-content rte {{ block.settings.alignment }}">
                    {{ block.settings.subtext }}
                  </div>
                 </div>
                {%- when 'link_list' -%}
                
                 
                  {%- if block.settings.menu != blank  -%}
                    <ul class="footer-block__details-content footer_menu {% unless block.settings.list_style == 'vertical' %}list-menu list-menu--inline {% endunless %}list-unstyled {{ block.settings.alignment }}">
                      {%- for link in block.settings.menu.links -%}
                        <li>
                          <a href="{{ link.url }}" class="link link--text list-menu__item list-menu__item--link{% if link.active %} list-menu__item--active{% endif %}">
                            {{ link.title }}
                          </a>
                        </li>
                      {%- endfor -%}
                    </ul>
                  {%- endif -%}
                   
                {%- when 'image' -%}
                  <div class="footer-block__details-content footer-block-image {{ block.settings.alignment }}">
                    {%- if block.settings.image != blank -%}
                      {%- assign image_size_2x = block.settings.image_width | times: 2 | at_most: 5760 -%}
                      <img
                        srcset= "{{ block.settings.image | image_url: width: block.settings.image_width }}, {{ block.settings.image | image_url: width: image_size_2x }} 2x"
                        src="{{ block.settings.image | image_url: width: 400 }}"
                        alt="{{ block.settings.image.alt | escape }}"
                        loading="lazy"
                        width="{{ block.settings.image.width }}"
                        height="{{ block.settings.image.height }}"
                        style="max-width: min(100%, {{ block.settings.image_width }}px);"
                      >
                    {%- else -%}
                      {{ 'image' | placeholder_svg_tag: 'placeholder-svg placeholder' }}
                    {%- endif -%}
                  </div>
                {%- style -%}
                .footer-block-image:before {    
                   content: "";
                   position: absolute;
                   background: url('{{ block.settings.before_image | image_url: width: 1920 }}');
                   background-repeat: no-repeat; width: 500px; height: 100%; top: 35px; bottom: 0; transition: all 0.3s linear; transform: translate(-25%, 30%); z-index: -1;
                  }
                   
                  {%- endstyle -%}
                  {%- when 'address' -%}
                <div class="footer-block__details-content footer-block-address {{ block.settings.alignment }}">
                <div class="footer-block__address">
                  {%- if block.settings.address_heading != blank -%}
                  <h2 class="footer-block__heading">{{ block.settings.address_heading | escape }}</h2>
                  {%- endif -%}
                <div class="footer-block footer--address">
                {% if block.settings.footer_address != blank or  block.settings.footer_contact_no != blank or  block.settings.office_hours != blank  or block.settings.footer_contact_link != blank or block.settings.footer_contact_id != blank %}
                  <ul class="contact-info list-unstyled">
                    {% if block.settings.footer_address != blank %}
                        <li class="address">         
                          <address>{%- render 'icon-location' -%}{{ block.settings.footer_address }}</address>
                        </li>
                        {% endif %}  
                        {% if block.settings.footer_contact_no != blank %}
                        <li class="contact-phone">    
                          {%- render 'icon-phone' -%}{{ block.settings.footer_contact_no }}    
                        </li>
                        {% endif %}
                  
                        {% if block.settings.footer_contact_id != blank %}
                        <li class="office-mail">
                          <a href="mailto:{{ block.settings.footer_contact_id}}">
                            {%- render 'icon-mail' -%}<span>{{ block.settings.footer_contact_id }}</span></a>     
                        </li>
                        {% endif %}
                  
                        {% if block.settings.office_hours != blank %}
                        <li class="office-hours">
                          {%- render 'icon-clock' -%} {{ block.settings.office_hours }}
                        </li>
                        {% endif %}
                      
                        
                    </ul>
                  {% endif %}   
                      <div class="address_socialicon">
                 {%- if section.settings.show_social and block.settings.enable_socialicon -%}
                <ul class="footer__list-social list-unstyled list-social" role="list">
                   {% render 'social-icons' %}
                </ul>
                {%- endif -%}
                </div>
                </div>
          
                </div>
                </div>
                 
                {%- when 'newsletter' -%}
              <div class="footer-block__details-content  footer-block--newsletter {{ block.settings.alignment }}">
                <div class="footer-block__newsletter">
                  {%- if section.settings.newsletter_heading != blank -%}
                  <h2 class="footer-block__heading">{{ section.settings.newsletter_heading }}</h2>
                  {%- endif -%}
                 <div class="footer-block__details-content-newsletter">
                    {{ block.settings.newsletter_subtext }}
                  </div>
                  {%- form 'customer', id: 'ContactFooter', class: 'footer__newsletter newsletter-form' -%}
                  <input type="hidden" name="contact[tags]" value="newsletter">
                  <div class="newsletter-form__field-wrapper">
                    <div class="field">
                    <span class="newsletter_icon"> {%- render 'icon-mail' -%}</span>
                      <input
                             id="NewsletterForm--{{ section.id }}"
                             type="email"
                             name="contact[email]"
                             class="field__input"
                             value="{{ form.email }}"
                             aria-required="true"
                             autocorrect="off"
                             autocapitalize="off"
                             autocomplete="email"
                             {% if form.errors %}
                             autofocus
                             aria-invalid="true"
                             aria-describedby="ContactFooter-error"
                             {% elsif form.posted_successfully? %}
                             aria-describedby="ContactFooter-success"
                             {% endif %}
                             placeholder="{{ 'newsletter.label' | t }}"
                             required
                             >
                      <label class="field__label" for="NewsletterForm--{{ section.id }}">
                        {{ 'newsletter.label' | t }}
                      </label>
                      <button type="submit" class="newsletter-form__button field__button" name="commit" id="Subscribe" aria-label="{{ 'newsletter.button_label' | t }}">
                      {%- render 'icon-arrow' -%}
                      </button>
                    </div>
                    {%- if form.errors -%}
                    <small class="newsletter-form__message form__message" id="ContactFooter-error">{% render 'icon-error' %}{{ form.errors.translated_fields['email'] | capitalize }} {{ form.errors.messages['email'] }}</small>
                    {%- endif -%}
                  </div>
                  {%- if form.posted_successfully? -%}
                  <h3 class="newsletter-form__message newsletter-form__message--success form__message" id="ContactFooter-success" tabindex="-1" autofocus>{% render 'icon-success' %}{{ 'newsletter.success' | t }}</h3>
                  {%- endif -%}
                  {%- endform -%}
                  {%- if section.settings.show_social and block.settings.enable_socialicon -%}
                <ul class="footer__list-social list-unstyled list-social" role="list">
                  {%- if settings.social_twitter_link != blank -%}
                  <li class="list-social__item">
                    <a href="{{ settings.social_twitter_link }}" class="link list-social__link" >
                      {%- render 'icon-twitter' -%}
                      <span class="visually-hidden">{{ 'general.social.links.twitter' | t }}</span>
                    </a>
                  </li>
                  {%- endif -%}
                  {%- if settings.social_facebook_link != blank -%}
                  <li class="list-social__item">
                    <a href="{{ settings.social_facebook_link }}" class="link list-social__link" >
                      {%- render 'icon-facebook' -%}
                      <span class="visually-hidden">{{ 'general.social.links.facebook' | t }}</span>
                    </a>
                  </li>
                  {%- endif -%}
                  {%- if settings.social_pinterest_link != blank -%}
                  <li class="list-social__item">
                    <a href="{{ settings.social_pinterest_link }}" class="link list-social__link" >
                      {%- render 'icon-pinterest' -%}
                      <span class="visually-hidden">{{ 'general.social.links.pinterest' | t }}</span>
                    </a>
                  </li>
                  {%- endif -%}
                  {%- if settings.social_instagram_link != blank -%}
                  <li class="list-social__item">
                    <a href="{{ settings.social_instagram_link }}" class="link list-social__link" >
                      {%- render 'icon-instagram' -%}
                      <span class="visually-hidden">{{ 'general.social.links.instagram' | t }}</span>
                    </a>
                  </li>
                  {%- endif -%}
                  {%- if settings.social_tiktok_link != blank -%}
                  <li class="list-social__item">
                    <a href="{{ settings.social_tiktok_link }}" class="link list-social__link" >
                      {%- render 'icon-tiktok' -%}
                      <span class="visually-hidden">{{ 'general.social.links.tiktok' | t }}</span>
                    </a>
                  </li>
                  {%- endif -%}
                  {%- if settings.social_tumblr_link != blank -%}
                  <li class="list-social__item">
                    <a href="{{ settings.social_tumblr_link }}" class="link list-social__link" >
                      {%- render 'icon-tumblr' -%}
                      <span class="visually-hidden">{{ 'general.social.links.tumblr' | t }}</span>
                    </a>
                  </li>
                  {%- endif -%}
                  {%- if settings.social_snapchat_link != blank -%}
                  <li class="list-social__item">
                    <a href="{{ settings.social_snapchat_link }}" class="link list-social__link" >
                      {%- render 'icon-snapchat' -%}
                      <span class="visually-hidden">{{ 'general.social.links.snapchat' | t }}</span>
                    </a>
                  </li>
                  {%- endif -%}
                  {%- if settings.social_youtube_link != blank -%}
                  <li class="list-social__item">
                    <a href="{{ settings.social_youtube_link }}" class="link list-social__link" >
                      {%- render 'icon-youtube' -%}
                      <span class="visually-hidden">{{ 'general.social.links.youtube' | t }}</span>
                    </a>
                  </li>
                  {%- endif -%}
                  {%- if settings.social_vimeo_link != blank -%}
                  <li class="list-social__item">
                    <a href="{{ settings.social_vimeo_link }}" class="link list-social__link" >
                      {%- render 'icon-vimeo' -%}
                      <span class="visually-hidden">{{ 'general.social.links.vimeo' | t }}</span>
                    </a>
                  </li>
                  {%- endif -%}
                </ul>
                {%- endif -%}
                </div>                
              </div>
                {%- endcase -%}
            </div>
          {%- endfor -%}
        </div>
      {%- endif -%}
    </div>
    </div>
  {%- endif -%}
{%- if section.settings.show_footer_bottom  -%}
  <div class="footer__content-bottom  {% if section.settings.page_full_width %} page-full-width {% else %} page-width {% endif %} {% if section.settings.page_full_width_spacing %} page-full-width_spacing {% endif %} ">
   <div class="row">
    <div class="footer__content-bottom-wrapper">     
      <div class="footer__column footer__column--info">
        <div class="footer__copyright caption">
         {% if section.settings.copyright_content == blank %}
         <small class="copyright__content">{{ powered_by_link }}</small>
          <small class="copyright__content">&copy; {{ 'now' | date: "%Y" }}, {{ shop.name | link_to: routes.root_url }}</small>
           {%  else %}
           <small class="copyright__content">{{ section.settings.copyright_content }}</small>
        {% endif %}
        </div>
        {%- if section.settings.payment_enable -%}
          <div class="footer__payment">
            <span class="visually-hidden">{{ 'sections.footer.payment' | t }}</span>
            <ul class="list list-payment" role="list">
              {%- for type in shop.enabled_payment_types -%}
                <li class="list-payment__item">
                  {{ type | payment_type_svg_tag: class: 'icon icon--full-color' }}
                </li>
              {%- endfor -%}
            </ul>
          </div>
        {%- endif -%}
     
           {%- if section.settings.show_social and section.settings.show_bottom-icon -%}
                <ul class="footer__list-social list-unstyled list-social" role="list">
                  {%- if settings.social_twitter_link != blank -%}
                  <li class="list-social__item">
                    <a href="{{ settings.social_twitter_link }}" class="link list-social__link" >
                      {%- render 'icon-twitter' -%}
                      <span class="visually-hidden">{{ 'general.social.links.twitter' | t }}</span>
                    </a>
                  </li>
                  {%- endif -%}
                  {%- if settings.social_facebook_link != blank -%}
                  <li class="list-social__item">
                    <a href="{{ settings.social_facebook_link }}" class="link list-social__link" >
                      {%- render 'icon-facebook' -%}
                      <span class="visually-hidden">{{ 'general.social.links.facebook' | t }}</span>
                    </a>
                  </li>
                  {%- endif -%}
                  {%- if settings.social_pinterest_link != blank -%}
                  <li class="list-social__item">
                    <a href="{{ settings.social_pinterest_link }}" class="link list-social__link" >
                      {%- render 'icon-pinterest' -%}
                      <span class="visually-hidden">{{ 'general.social.links.pinterest' | t }}</span>
                    </a>
                  </li>
                  {%- endif -%}
                  {%- if settings.social_instagram_link != blank -%}
                  <li class="list-social__item">
                    <a href="{{ settings.social_instagram_link }}" class="link list-social__link" >
                      {%- render 'icon-instagram' -%}
                      <span class="visually-hidden">{{ 'general.social.links.instagram' | t }}</span>
                    </a>
                  </li>
                  {%- endif -%}
                  {%- if settings.social_tiktok_link != blank -%}
                  <li class="list-social__item">
                    <a href="{{ settings.social_tiktok_link }}" class="link list-social__link" >
                      {%- render 'icon-tiktok' -%}
                      <span class="visually-hidden">{{ 'general.social.links.tiktok' | t }}</span>
                    </a>
                  </li>
                  {%- endif -%}
                  {%- if settings.social_tumblr_link != blank -%}
                  <li class="list-social__item">
                    <a href="{{ settings.social_tumblr_link }}" class="link list-social__link" >
                      {%- render 'icon-tumblr' -%}
                      <span class="visually-hidden">{{ 'general.social.links.tumblr' | t }}</span>
                    </a>
                  </li>
                  {%- endif -%}
                  {%- if settings.social_snapchat_link != blank -%}
                  <li class="list-social__item">
                    <a href="{{ settings.social_snapchat_link }}" class="link list-social__link" >
                      {%- render 'icon-snapchat' -%}
                      <span class="visually-hidden">{{ 'general.social.links.snapchat' | t }}</span>
                    </a>
                  </li>
                  {%- endif -%}
                  {%- if settings.social_youtube_link != blank -%}
                  <li class="list-social__item">
                    <a href="{{ settings.social_youtube_link }}" class="link list-social__link" >
                      {%- render 'icon-youtube' -%}
                      <span class="visually-hidden">{{ 'general.social.links.youtube' | t }}</span>
                    </a>
                  </li>
                  {%- endif -%}
                  {%- if settings.social_vimeo_link != blank -%}
                  <li class="list-social__item">
                    <a href="{{ settings.social_vimeo_link }}" class="link list-social__link" >
                      {%- render 'icon-vimeo' -%}
                      <span class="visually-hidden">{{ 'general.social.links.vimeo' | t }}</span>
                    </a>
                  </li>
                  {%- endif -%}
              {%- if section.settings.show_usd -%}
                  <div class="disclosure">
                  <button type="button" class="disclosure__button localization-form__select localization-selector link link--text caption-large" aria-expanded="false" aria-controls="FooterCountryList" aria-describedby="FooterCountryLabel">
                    {% assign nameFlagUp = localization.country.iso_code | downcase %}
                    {{ localization.country.iso_code }} <span class="flag-icon flag-icon-{{ nameFlagUp }}"></span>
                    {% render 'icon-caret' %}
                  </button>
                  <div class="disclosure__list-wrapper" hidden>
                    <ul id="FooterCountryList" role="list" class="disclosure__list list-unstyled">
                      {%- for country in localization.available_countries -%}
                        {% assign nameFlag = country.iso_code | downcase %}

                        <li class="disclosure__item" tabindex="-1">
                          <a class="link link--text disclosure__link caption-large{% if country.iso_code == localization.country.iso_code %} disclosure__link--active{% endif %} focus-inset" href="#"{% if country.iso_code == localization.country.iso_code %} aria-current="true"{% endif %} data-value="{{ country.iso_code }}">
                            {{ country.iso_code }} <span class="localization-form__currency flag-icon flag-icon-{{ nameFlag }}"></span>
                          </a>
                        </li>
                      {%- endfor -%}
                    </ul>
                  </div>
                </div>
                 {%- endif -%} 
                </ul>
                {%- endif -%}
                
      </div>
    </div>
    </div>
  </div>
   {%- endif -%}
</footer>

{%- style -%}
  .footer-style3 .footer-block-image{position:relative;}
  {%  unless section.settings.mask_image == blank %}
  .footer:before {
   content: ""; position: absolute;
    mask-image: url({{ section.settings.mask_image |  image_url: width: 1920 }});
   -webkit-mask-image: url({{ section.settings.mask_image |  image_url: width: 1920 }});
    mask-repeat: no-repeat; background:rgb(var(--color-background)); -webkit-mask-repeat: no-repeat; width: 100%; height: 100%; top: -30px; bottom: 0; transition:all 0.3s linear;
  }    
  {%  endunless %}
{%- endstyle -%}
{% schema %}
{
  "name": "t:sections.footer-style3.name",
  "blocks": [
    {
      "type": "link_list",
      "name": "t:sections.footer-style3.blocks.link_list.name",
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "default": "Quick links",
          "label": "t:sections.footer-style3.blocks.link_list.settings.heading.label",
          "info": "t:sections.footer-style3.blocks.link_list.settings.heading.info"
        },
        {
          "type": "link_list",
          "id": "menu",
          "default": "footer",
          "label": "t:sections.footer-style3.blocks.link_list.settings.menu.label",
          "info": "t:sections.footer-style3.blocks.link_list.settings.menu.info"
        },
      {
                "type": "select",
                "id": "alignment",
                "label": "Alignment on large screen",
                "options": [
                 {
                    "value": "left",
                    "label": "t:sections.footer-style3.blocks.link_list.settings.alignment.options__1.label"
                  },
                  {
                    "value": "center",
                    "label": "t:sections.footer-style3.blocks.link_list.settings.alignment.options__2.label"
                  },
                  {
                    "value": "right",
                    "label": "t:sections.footer-style3.blocks.link_list.settings.alignment.options__3.label"
                  }
                ],
                "default": "center",
                "label": "t:sections.footer-style3.blocks.link_list.settings.alignment.label"
              },
{
                "type": "select",
                "id": "list_style",
                "label": "List style",
                "options": [
                  {
                    "value": "horizontal",
                    "label": "Horizontal"
                  },
                  {
                    "value": "vertical",
                    "label": "Vertical"
                  }                  
                ],
                "default": "vertical"
              }
      ]
    },

{
      "type": "newsletter",
      "name": "t:sections.footer-style3.blocks.newsletter.name",
      "settings": [
        {
        "type": "header",
        "content": "t:sections.footer-style3.blocks.newsletter.settings.header__1.content",
        "info": "t:sections.footer-style3.blocks.newsletter.settings.header__1.info"
        },    
        {
        "type": "text",
        "id": "sub_heading",
        "default": "emails",
         "label": "t:sections.footer-style3.blocks.newsletter.settings.sub_heading.label"
        },
        {
        "type": "text",
        "id": "heading",
        "default": "Subscribe to our emails",
         "label": "t:sections.footer-style3.blocks.newsletter.settings.heading.label"
        },
         {
          "type": "richtext",
          "id": "newsletter_subtext",
          "default": "<p>Share contact information, store details, and brand content with your customers.</p>",
          "label": "t:sections.footer-style3.blocks.newsletter.settings.newsletter_subtext.label"
        },
      {
                "type": "select",
                "id": "alignment",
                "label": "Alignment on large screen",
                "options": [
                 {
                    "value": "left",
                    "label": "t:sections.footer-style3.blocks.newsletter.settings.alignment.options__1.label"
                  },
                  {
                    "value": "center",
                    "label": "t:sections.footer-style3.blocks.newsletter.settings.alignment.options__2.label"
                  },
                  {
                    "value": "right",
                    "label": "t:sections.footer-style3.blocks.newsletter.settings.alignment.options__3.label"
                  }
                ],
                "default": "center",
                "label": "t:sections.footer-style3.blocks.newsletter.settings.alignment.label"
              },
         {
      "type": "checkbox",
      "id": "enable_socialicon",
      "default": false,
      "label": "t:sections.footer-style3.blocks.newsletter.settings.enable_socialicon.label"
    }
      ]
  
    },

    {
      "type": "text",
      "name": "t:sections.footer-style3.blocks.text.name",
      "settings": [
         {
          "type": "image_picker",
          "id": "image",
          "label":"t:sections.footer-style3.blocks.text.settings.image.label"
        },
        {
          "type": "text",
          "id": "heading",
          "default": "Heading",
          "label": "t:sections.footer-style3.blocks.text.settings.heading.label"
        },
        {
          "type": "richtext",
          "id": "subtext",
          "default": "<p>Share contact information, store details, and brand content with your customers.</p>",
          "label": "t:sections.footer.blocks.text.settings.subtext.label"
        },
       {
                "type": "select",
                "id": "alignment",
                "label": "Alignment on large screen",
                "options": [
                 {
                    "value": "left",
                    "label": "t:sections.footer-style3.blocks.text.settings.alignment.options__1.label"
                  },
                  {
                    "value": "center",
                    "label": "t:sections.footer-style3.blocks.text.settings.alignment.options__2.label"
                  },
                  {
                    "value": "right",
                    "label": "t:sections.footer-style3.blocks.text.settings.alignment.options__3.label"
                  }
                ],
                "default": "center",
                "label": "t:sections.footer-style3.blocks.text.settings.alignment.label"
              }
      ]
    },
    {
      "type": "address",
      "name": "t:sections.footer-style3.blocks.address.name",
      "settings": [
        {
          "type": "text",
          "id": "address_heading",
          "default": "Heading",
          "label": "t:sections.footer-style3.blocks.address.settings.address_heading.label"
        },
        {
        "type": "textarea",
        "id": "footer_address",
        "label": "t:sections.footer-style3.blocks.address.settings.footer_address.label"
        },
        {
        "type": "text",
        "id": "footer_contact_no",
        "label": "t:sections.footer-style3.blocks.address.settings.footer_contact_no.label"
        },
         {
        "type": "text",
        "id": "footer_contact_id",
        "label": "t:sections.footer-style3.blocks.address.settings.footer_contact_id.label"
        },
        {
        "type": "text",
        "id": "office_hours",
        "label": "t:sections.footer-style3.blocks.address.settings.office_hours.label"
        },
        {
        "type": "checkbox",
        "id": "enable_socialicon",
        "default": false,
        "label": "t:sections.footer-style3.blocks.address.settings.enable_socialicon.label"
        },
         {
                "type": "select",
                "id": "alignment",
                "label": "Alignment on large screen",
                "options": [
                 {
                    "value": "left",
                    "label": "t:sections.footer-style3.blocks.address.settings.alignment.options__1.label"
                  },
                  {
                    "value": "center",
                    "label": "t:sections.footer-style3.blocks.address.settings.alignment.options__2.label"
                  },
                  {
                    "value": "right",
                    "label": "t:sections.footer-style3.blocks.address.settings.alignment.options__3.label"
                  }
                ],
                "default": "center",
                "label": "t:sections.footer-style3.blocks.address.settings.alignment.label"
              }
      ]
    }
  ],
  "settings": [
     {
      "type":"checkbox",
      "id":"page_full_width",
       "default": false,
      "label": "t:sections.all.page_full_width.label"
    },
    {
      "type":"checkbox",
      "id":"page_full_width_spacing",
       "default": false,
      "label": "t:sections.all.page_full_width_spacing.label"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "t:sections.all.colors.accent_1.label"
        },
        {
          "value": "accent-2",
          "label": "t:sections.all.colors.accent_2.label"
        },
        {
          "value": "background-1",
          "label": "t:sections.all.colors.background_1.label"
        },
        {
          "value": "background-2",
          "label": "t:sections.all.colors.background_2.label"
        },
        {
          "value": "inverse",
          "label": "t:sections.all.colors.inverse.label"
        }
      ],
      "default": "background-1",
      "label": "t:sections.all.colors.label"
     }, 
     {
      "type": "image_picker",
      "id": "image",
      "label": "t:sections.footer-style3.settings.image.label"
    },
    {
      "type": "header",
      "content": "t:sections.footer-style3.settings.header__2.content",
      "info": "t:sections.footer-style3.settings.header__2.info"
    },
    {
      "type": "checkbox",
      "id": "show_social",
      "default": false,
      "label": "t:sections.footer-style3.settings.show_social.label"
    },
    {
      "type": "checkbox",
      "id": "show_usd",
      "default": false,
      "label": "t:sections.footer-style3.settings.show_usd.label",
      "info": "t:sections.footer-style3.settings.show_usd.info"
    },
    {
      "type": "checkbox",
      "id": "show_footer_bottom",
      "default": false,
      "label": "t:sections.footer-style3.settings.show_footer_bottom.label"
    },
     {
      "type": "richtext",
      "id": "copyright_content",
      "default": "<p>All Right Reserved © 2023 Designthemes</p>",
      "label": "t:sections.footer-style3.settings.copyright_content.label"
    },
    
    {
      "type": "header",
      "content": "t:sections.footer-style3.settings.header__7.content"
    },
    {
      "type": "checkbox",
      "id": "payment_enable",
      "default": true,
      "label": "t:sections.footer-style3.settings.payment_enable.label"
    },	
    {
      "type": "checkbox",
      "id": "footer_default",
      "default": false,
      "label": "t:sections.footer-style3.settings.footer_default.label"
    },
    {
      "type": "header",
      "content": "t:sections.footer.settings.header__8.content",
      "info": "t:sections.footer.settings.header__8.info"
    },
    {
      "type": "checkbox",
      "id": "border_right",
      "default": true,
      "label": "t:sections.footer.settings.border_right.label"
    },
     {
      "type": "checkbox",
      "id": "show_bottom-icon",
      "default": false,
      "label": "t:sections.footer-style3.settings.show_bottom-icon.label"
    },
    {
      "type": "header",
      "content": "t:sections.all.spacing"
    },
    {
      "type": "range",
      "id": "margin_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.footer-style3.settings.margin_top.label",
      "default": 0
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    }
  ],
  "default": {
    "blocks": [
      {
        "type": "link_list"
      },
      {
        "type": "text"
      }
    ]
  }
}
{% endschema %}