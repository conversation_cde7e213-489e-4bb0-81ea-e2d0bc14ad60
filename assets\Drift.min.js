var u="undefined"!=typeof window&&window===this?this:"undefined"!=typeof global&&null!=global?global:this;function v(){v=function(){},u.Symbol||(u.Symbol=A)}var B=0;function A(t){return"jscomp_symbol_"+(t||"")+B++}!function(t){function i(n){if(e[n])return e[n].R;var s=e[n]={ia:n,ea:!1,R:{}};return t[n].call(s.R,s,s.R,i),s.ea=!0,s.R}var e={};i.v=t,i.h=e,i.c=function(t,e,n){i.g(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},i.r=function(t){v(),v(),"undefined"!=typeof Symbol&&Symbol.toStringTag&&(v(),Object.defineProperty(t,Symbol.toStringTag,{value:"Module"})),Object.defineProperty(t,"__esModule",{value:!0})},i.l=function(t,e){if(1&e&&(t=i(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.aa)return t;var n=Object.create(null);if(i.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var s in t)i.c(n,s,function(i){return t[i]}.bind(null,s));return n},i.i=function(t){var e=t&&t.aa?function(){return t.default}:function(){return t};return i.c(e,"a",e),e},i.g=function(t,i){return Object.prototype.hasOwnProperty.call(t,i)},i.o="",i(i.K=0)}([function(t,i,e){function n(t,i){if(i=void 0===i?{}:i,this.h=t,this.c=this.c.bind(this),!a(this.h))throw new TypeError("`new Drift` requires a DOM element as its first argument.");t=i.namespace||null;var e=i.showWhitespaceAtEdges||!1,n=i.containInline||!1,s=i.inlineOffsetX||0,o=i.inlineOffsetY||0,h=i.inlineContainer||document.body,r=i.sourceAttribute||"data-zoom",d=i.zoomFactor||3,u=void 0===i.paneContainer?document.body:i.paneContainer,l=i.inlinePane||375,f=i.handleTouch||!0,c=i.onShow||null,p=i.onHide||null,b=i.injectBaseStyles||!0,m=i.hoverDelay||0,v=i.touchDelay||0,g=i.hoverBoundingBox||!1,y=i.touchBoundingBox||!1;if(i=i.boundingBoxContainer||document.body,!0!==l&&!a(u))throw new TypeError("`paneContainer` must be a DOM element when `inlinePane !== true`");if(!a(h))throw new TypeError("`inlineContainer` must be a DOM element");this.a={j:t,N:e,G:n,I:s,J:o,u:h,O:r,f:d,fa:u,da:l,w:f,M:c,L:p,ca:b,B:m,D:v,A:g,C:y,F:i},this.a.ca&&!document.querySelector(".drift-base-styles")&&((i=document.createElement("style")).type="text/css",i.classList.add("drift-base-styles"),i.appendChild(document.createTextNode(".drift-bounding-box,.drift-zoom-pane{position:absolute;pointer-events:none}@keyframes noop{0%{zoom:1}}@-webkit-keyframes noop{0%{zoom:1}}.drift-zoom-pane.drift-open{display:block}.drift-zoom-pane.drift-closing,.drift-zoom-pane.drift-opening{animation:noop 1ms;-webkit-animation:noop 1ms}.drift-zoom-pane{overflow:hidden;width:100%;height:100%;top:0;left:0}.drift-zoom-pane-loader{display:none}.drift-zoom-pane img{position:absolute;display:block;max-width:none;max-height:none}")),(t=document.head).insertBefore(i,t.firstChild)),this.l(),this.i()}function s(t){t=void 0===t?{}:t,this.h=this.h.bind(this),this.g=this.g.bind(this),this.l=this.l.bind(this),this.s=!1;var i=void 0===t.H?null:t.H,e=void 0===t.f?l():t.f,n=void 0===t.S?l():t.S,s=void 0===t.j?null:t.j,o=void 0===t.N?l():t.N,h=void 0===t.G?l():t.G;this.a={H:i,f:e,S:n,j:s,N:o,G:h,I:void 0===t.I?0:t.I,J:void 0===t.J?0:t.J,u:void 0===t.u?document.body:t.u},this.o=this.i("open"),this.W=this.i("opening"),this.v=this.i("closing"),this.K=this.i("inline"),this.V=this.i("loading"),this.ga()}function o(t){t=void 0===t?{}:t,this.l=this.l.bind(this),this.h=this.h.bind(this),this.g=this.g.bind(this),this.c=this.c.bind(this);var i=t;t=void 0===i.b?l():i.b;var e=void 0===i.m?l():i.m,n=void 0===i.O?l():i.O,s=void 0===i.w?l():i.w,o=void 0===i.M?null:i.M,a=void 0===i.L?null:i.L,r=void 0===i.B?0:i.B,d=void 0===i.D?0:i.D,u=void 0===i.A?l():i.A,f=void 0===i.C?l():i.C,c=void 0===i.j?null:i.j,p=void 0===i.f?l():i.f;i=void 0===i.F?l():i.F,this.a={b:t,m:e,O:n,w:s,M:o,L:a,B:r,D:d,A:u,C:f,j:c,f:p,F:i},(this.a.A||this.a.C)&&(this.o=new h({j:this.a.j,f:this.a.f,P:this.a.F})),this.enabled=!0,this.K()}function h(t){this.s=!1;var i=void 0===t.j?null:t.j,e=void 0===t.f?l():t.f;t=void 0===t.P?l():t.P,this.a={j:i,f:e,P:t},this.c=this.g("open"),this.h()}function a(t){return f?t instanceof HTMLElement:t&&"object"==typeof t&&null!==t&&1===t.nodeType&&"string"==typeof t.nodeName}function r(t,i){i.forEach(function(i){t.classList.add(i)})}function d(t,i){i.forEach(function(i){t.classList.remove(i)})}function l(){throw Error("Missing parameter")}e.r(i);var f="object"==typeof HTMLElement;h.prototype.g=function(t){var i=["drift-"+t],e=this.a.j;return e&&i.push(e+"-"+t),i},h.prototype.h=function(){this.b=document.createElement("div"),r(this.b,this.g("bounding-box"))},h.prototype.show=function(t,i){this.s=!0,this.a.P.appendChild(this.b);var e=this.b.style;e.width=Math.round(t/this.a.f)+"px",e.height=Math.round(i/this.a.f)+"px",r(this.b,this.c)},h.prototype.T=function(){this.s&&this.a.P.removeChild(this.b),this.s=!1,d(this.b,this.c)},h.prototype.setPosition=function(t,i,e){var n=window.pageXOffset,s=window.pageYOffset;t=e.left+t*e.width-this.b.clientWidth/2+n,i=e.top+i*e.height-this.b.clientHeight/2+s,this.b.getBoundingClientRect(),t<e.left+n?t=e.left+n:t+this.b.clientWidth>e.left+e.width+n&&(t=e.left+e.width-this.b.clientWidth+n),i<e.top+s?i=e.top+s:i+this.b.clientHeight>e.top+e.height+s&&(i=e.top+e.height-this.b.clientHeight+s),this.b.style.left=t+"px",this.b.style.top=i+"px"},o.prototype.K=function(){this.a.b.addEventListener("mouseenter",this.g,!1),this.a.b.addEventListener("mouseleave",this.h,!1),this.a.b.addEventListener("mousemove",this.c,!1),this.a.w&&(this.a.b.addEventListener("touchstart",this.g,!1),this.a.b.addEventListener("touchend",this.h,!1),this.a.b.addEventListener("touchmove",this.c,!1))},o.prototype.ba=function(){this.a.b.removeEventListener("mouseenter",this.g,!1),this.a.b.removeEventListener("mouseleave",this.h,!1),this.a.b.removeEventListener("mousemove",this.c,!1),this.a.w&&(this.a.b.removeEventListener("touchstart",this.g,!1),this.a.b.removeEventListener("touchend",this.h,!1),this.a.b.removeEventListener("touchmove",this.c,!1))},o.prototype.g=function(t){t.preventDefault(),this.i=t,"mouseenter"==t.type&&this.a.B?this.v=setTimeout(this.l,this.a.B):this.a.D?this.v=setTimeout(this.l,this.a.D):this.l()},o.prototype.l=function(){if(this.enabled){var t=this.a.M;t&&"function"==typeof t&&t(),this.a.m.show(this.a.b.getAttribute(this.a.O),this.a.b.clientWidth,this.a.b.clientHeight),this.i&&((t=this.i.touches)&&this.a.C||!t&&this.a.A)&&this.o.show(this.a.m.b.clientWidth,this.a.m.b.clientHeight),this.c()}},o.prototype.h=function(t){t.preventDefault(),this.i=null,this.v&&clearTimeout(this.v),this.o&&this.o.T(),(t=this.a.L)&&"function"==typeof t&&t(),this.a.m.T()},o.prototype.c=function(t){if(t)t.preventDefault(),this.i=t;else{if(!this.i)return;t=this.i}if(t.touches)var i=(t=t.touches[0]).clientX,e=t.clientY;else i=t.clientX,e=t.clientY;i=(i-(t=this.a.b.getBoundingClientRect()).left)/this.a.b.clientWidth,e=(e-t.top)/this.a.b.clientHeight,this.o&&this.o.setPosition(i,e,t),this.a.m.setPosition(i,e,t)},u.Object.defineProperties(o.prototype,{s:{configurable:!0,enumerable:!0,get:function(){return this.a.m.s}}}),t=document.createElement("div").style;var c="undefined"!=typeof document&&("animation"in t||"webkitAnimation"in t);s.prototype.i=function(t){var i=["drift-"+t],e=this.a.j;return e&&i.push(e+"-"+t),i},s.prototype.ga=function(){this.b=document.createElement("div"),r(this.b,this.i("zoom-pane"));var t=document.createElement("div");r(t,this.i("zoom-pane-loader")),this.b.appendChild(t),this.c=document.createElement("img"),this.b.appendChild(this.c)},s.prototype.Y=function(t){this.c.setAttribute("src",t)},s.prototype.X=function(t,i){this.c.style.width=t*this.a.f+"px",this.c.style.height=i*this.a.f+"px"},s.prototype.setPosition=function(t,i,e){var n=this.c.getBoundingClientRect(),s=n.width,o=n.height,h=this.b.getBoundingClientRect(),a=(n=h.width)/2-s*t,r=(h=h.height)/2-o*i,d=n-s,u=h-o,l=0<d,f=0<u;s=l?d/2:0,o=f?u/2:0,d=l?d/2:d,u=f?u/2:u,this.b.parentElement===this.a.u&&(f=window.pageXOffset,l=window.pageYOffset,t=e.left+t*e.width-n/2+this.a.I+f,i=e.top+i*e.height-h/2+this.a.J+l,this.a.G&&(t<e.left+f?t=e.left+f:t+n>e.left+e.width+f&&(t=e.left+e.width-n+f),i<e.top+l?i=e.top+l:i+h>e.top+e.height+l&&(i=e.top+e.height-h+l)),this.b.style.left=t+"px",this.b.style.top=i+"px"),this.a.N||(a>s?a=s:a<d&&(a=d),r>o?r=o:r<u&&(r=u)),this.c.style.transform="translate("+a+"px, "+r+"px)",this.c.style.webkitTransform="translate("+a+"px, "+r+"px)"},s.prototype.U=function(){this.b.removeEventListener("animationend",this.h,!1),this.b.removeEventListener("animationend",this.g,!1),this.b.removeEventListener("webkitAnimationEnd",this.h,!1),this.b.removeEventListener("webkitAnimationEnd",this.g,!1),d(this.b,this.o),d(this.b,this.v)},s.prototype.show=function(t,i,e){this.U(),this.s=!0,r(this.b,this.o),r(this.b,this.V),this.c.addEventListener("load",this.l,!1),this.Y(t),this.X(i,e),this.ha?this.$():this.Z(),c&&(this.b.addEventListener("animationend",this.h,!1),this.b.addEventListener("webkitAnimationEnd",this.h,!1),r(this.b,this.W))},s.prototype.$=function(){this.a.u.appendChild(this.b),r(this.b,this.K)},s.prototype.Z=function(){this.a.H.appendChild(this.b)},s.prototype.T=function(){this.U(),this.s=!1,c?(this.b.addEventListener("animationend",this.g,!1),this.b.addEventListener("webkitAnimationEnd",this.g,!1),r(this.b,this.v)):(d(this.b,this.o),d(this.b,this.K))},s.prototype.h=function(){this.b.removeEventListener("animationend",this.h,!1),this.b.removeEventListener("webkitAnimationEnd",this.h,!1),d(this.b,this.W)},s.prototype.g=function(){this.b.removeEventListener("animationend",this.g,!1),this.b.removeEventListener("webkitAnimationEnd",this.g,!1),d(this.b,this.o),d(this.b,this.v),d(this.b,this.K),this.b.setAttribute("style",""),this.b.parentElement===this.a.H?this.a.H.removeChild(this.b):this.b.parentElement===this.a.u&&this.a.u.removeChild(this.b)},s.prototype.l=function(){this.c.removeEventListener("load",this.l,!1),d(this.b,this.V)},u.Object.defineProperties(s.prototype,{ha:{configurable:!0,enumerable:!0,get:function(){var t=this.a.S;return!0===t||"number"==typeof t&&window.innerWidth<=t}}}),n.prototype.l=function(){this.m=new s({H:this.a.fa,f:this.a.f,N:this.a.N,G:this.a.G,S:this.a.da,j:this.a.j,I:this.a.I,J:this.a.J,u:this.a.u})},n.prototype.i=function(){this.g=new o({b:this.h,m:this.m,w:this.a.w,M:this.a.M,L:this.a.L,O:this.a.O,B:this.a.B,D:this.a.D,A:this.a.A,C:this.a.C,j:this.a.j,f:this.a.f,F:this.a.F})},n.prototype.c=function(){this.g.ba()},u.Object.defineProperties(n.prototype,{s:{configurable:!0,enumerable:!0,get:function(){return this.m.s}},f:{configurable:!0,enumerable:!0,get:function(){return this.a.f},set:function(t){this.a.f=t,this.m.a.f=t,this.g.a.f=t,this.o.a.f=t}}}),window.Drift=n}]);
//# sourceMappingURL=Drift.min.js.map