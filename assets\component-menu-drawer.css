.header__icon--menu {
  position: initial;
}

.js menu-drawer > details > summary::before,
.js menu-drawer > details[open]:not(.menu-opening) > summary::before {
  content: "";
  position: absolute;
  cursor: default;
  width: 100%;
  height: calc(100vh - 100%);
  height: calc(
    var(--viewport-height, 100vh) - (var(--header-bottom-position, 100%))
  );
  top: 100%;
  left: 0;
  background: rgba(var(--color-foreground), 0.5);
  opacity: 0;
  visibility: hidden;
  z-index: 2;
  transition: opacity 0s, visibility 0s;
}

menu-drawer > details[open] > summary::before {
  visibility: visible;
  opacity: 1;
  transition: opacity var(--duration-default) ease,
    visibility var(--duration-default) ease;
}
li.dt-sc-menu-product {
  list-style: none;
}

.menu-drawer {
  position: absolute;
  transform: translateX(-100%);
  visibility: hidden;
  z-index: 5;
  left: 0;
  top: 100%;
  width: calc(100vw - 4rem);
  padding: 0;
  border-width: 0 var(--drawer-border-width) 0 0;
  background-color: rgb(var(--color-background));
  /* overflow-x: hidden;  */
  border-style: solid;
  border-color: rgba(var(--color-foreground), var(--drawer-border-opacity));
  filter: drop-shadow(
    var(--drawer-shadow-horizontal-offset) var(--drawer-shadow-vertical-offset)
      var(--drawer-shadow-blur-radius)
      rgba(var(--color-shadow), var(--drawer-shadow-opacity))
  );
}

.js .menu-drawer {
  height: 100vh;
  /*   height: calc(
    var(--viewport-height, 100vh) - (var(--header-bottom-position, 100%))
  ); */
}

.js details[open] > .menu-drawer,
.js details[open] > .menu-drawer__submenu {
  transition: transform var(--duration-default) ease,
    visibility var(--duration-default) ease;
}

.no-js details[open] > .menu-drawer,
.js details[open].menu-opening > .menu-drawer,
details[open].menu-opening > .menu-drawer__submenu {
  transform: translateX(0);
  visibility: visible;
  top: 0 !important;
}

.js .menu-drawer__navigation .submenu-open {
  visibility: hidden; /* hide menus from screen readers when hidden by submenu */
}

@media screen and (min-width: 750px) {
  .menu-drawer {
    width: 40rem;
  }

  .no-js .menu-drawer {
    height: auto;
  }
}

.menu-drawer__inner-container {
  position: relative;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
}

.menu-drawer__navigation-container {
  /*   display: grid;
  grid-template-rows: 1fr auto;
  align-content: space-between; 
  overflow-y: auto;*/
  display: flex;
  height: 100%;
  flex-direction: column;
  justify-content: space-between;
  height: calc(100% - 85px);
}

.menu-drawer__navigation {
  margin: 5rem 0 0rem;
  /* margin-top: 2rem; */
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
}

.menu-drawer__inner-submenu {
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  background: var(--gradient-base-accent-1);
}

.no-js .menu-drawer__navigation {
  padding: 0;
}

.no-js .menu-drawer__navigation > ul > li {
  border-bottom: 0.1rem solid rgba(var(--color-foreground), 0.04);
}

.no-js .menu-drawer__submenu ul > li {
  border-top: 0.1rem solid rgba(var(--color-foreground), 0.04);
}

.js .menu-drawer__menu li {
  margin-bottom: 0.2rem;
}

.menu-drawer__menu-item {
  padding: 1.1rem 3.2rem;
  text-decoration: none;
  font-size: 1.5rem;
}

.no-js .menu-drawer__menu-item {
  font-size: 1.6rem;
}

.no-js .menu-drawer__submenu .menu-drawer__menu-item {
  padding: 1.2rem 5.2rem 1.2rem 6rem;
}

.no-js .menu-drawer__submenu .menu-drawer__submenu .menu-drawer__menu-item {
  padding-left: 9rem;
}

 .menu-drawer summary.menu-drawer__menu-item {
  padding-right: 0;
} 

.no-js .menu-drawer__menu-item .icon-caret {
  right: 3rem;
}

.menu-drawer__menu-item--active,
/* .menu-drawer__menu-item:focus, */
.menu-drawer__close-button:focus,
.menu-drawer__menu-item:hover,
.menu-drawer__close-button:hover {
  /* color: rgb(var(--color-foreground)); */
  background-color: rgba(var(--color-foreground), 0.04);
}

.menu-drawer__menu-item--active:hover {
  background-color: rgba(var(--color-foreground), 0.08);
}

.js .menu-drawer__menu-item .icon-caret,
.no-js .menu-drawer .icon-arrow {
  display: none;
}

.menu-drawer__menu-item > .icon-arrow {
  position: absolute;
  right: 2.5rem;
  top: 50%;
  transform: translateY(-50%);
}

.js .menu-drawer__submenu {
  position: absolute;
  top: 0;
  width: 100%;
  bottom: 0;
  left: 0;
  /* background-color: var(--gradient-base-background-1); */
  /*   border-left: 0.1rem solid rgba(var(--color-foreground), 0.2); */
  z-index: 1;
  transform: translateX(100%);
  visibility: hidden;
}

.js .menu-drawer__submenu .menu-drawer__submenu {
  overflow-y: auto;
}

.menu-drawer__close-button {
  margin-top: 1.5rem;
  padding: 1.2rem 2.6rem;
  text-decoration: none;
  display: flex;
  align-items: center;
  font-size: 1.8rem;
  width: 100%;
  background-color: transparent;
  font-family: var(--font-body-family);
  font-style: var(--font-body-style);
  text-align: left;
  font-weight:400;
}

.no-js .menu-drawer__close-button {
  display: none;
}

.menu-drawer__close-button .icon-arrow {
  transform: rotate(180deg);
  margin-right: 1rem;
}

.menu-drawer__utility-links {
  padding: 2rem;
  background-color: rgba(var(--color-foreground), 0.03);
  width: 100%;
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.menu-drawer__account {
  display: inline-flex;
  align-items: center;
  text-decoration: none;
  padding: 0;
  margin-left: 0;
  font-size: 1.4rem;
  color: rgb(var(--color-foreground));
}

.menu-drawer__account svg {
  height: 2rem;
  width: 2rem;
  margin-right: 1rem;
}

.menu-drawer__account:hover svg {
  transform: scale(1.07);
}

.menu-drawer .list-social {
  justify-content: flex-start;
  margin-left: 0;
  margin-top: 0;
}

.menu-drawer .list-social:empty {
  display: none;
}

ul.dt-nav > li > a span {
  position: relative;
}
/*  ul.dt-nav > li > a span:before, ul.dt-nav > li > a span:after { content: ""; position: absolute; height: 2px; bottom: -5px; width: 0; margin: auto; border-radius: 2px; }
  ul.dt-nav > li > a span:before { left: 0; }
  ul.dt-nav > li > a span:after { right: 0; transition: width .8s cubic-bezier(.22, .61, .36, 1); -moz-transition: width .8s cubic-bezier(.22, .61, .36, 1);-webkit-transition: width .8s cubic-bezier(.22, .61, .36, 1); }

  ul.dt-nav > li:hover > a span:before, ul.dt-nav > li.active > a span:before { transition: width .8s cubic-bezier(.22, .61, .36, 1); width: 100%; -moz-transition: width .8s cubic-bezier(.22, .61, .36, 1); -webkit-transition: width .8s cubic-bezier(.22, .61, .36, 1); }
  ul.dt-nav > li:hover > a span:after, ul.dt-nav > li.active > a span:after { background: transparent; width: 100%; }
  ul.dt-nav > li > a span:before, ul.dt-nav > li > a span:after {  background-color:  var(--gradient-base-accent-2); } */

ul.dt-nav > li:hover > a,
ul.dt-nav > li.active > a {
  background-color: var(--gradient-base-accent-2);
  background-color: transparent;
}
ul.dt-nav > li:hover > a,
ul.dt-nav li.active > a,
ul.dt-nav > li:hover > a,
ul.dt-nav > li.active > a,
ul.dt-nav > li.active > a.mega-menu > span:after {
  color: var(--gradient-base-accent-2);
}
.mega-menu__content ul.sub-menu-lists.vertical {
  padding: 0;
}
.menu-drawer ul.sub-menu-lists li,
.menu-drawer ul.sub-menu-block li {
  width: 100%;
}
.menu-drawer ul.sub-menu-block,
.menu-drawer ul.sub-menu-lists,
.menu-drawer ul li.menu-item-object-dt_mega_menus {
  width: 100% !important;
}
.menu-drawer__navigation
  ul.sub-menu-lists.mega-menu-brands
  li.dt-sc-menu-image-with-text:not(:last-child),
.menu-drawer__navigation
  .sub-menu-block.mega-menu__content
  ul
  li:not(:last-child) {
  margin-bottom: 15px;
}
.menu-drawer__navigation .dt-sc-menu-image-with-text .dt-sc-details {
  bottom: 10px;
}
.menu-drawer__navigation .dt-sc-menu-image-with-text:hover .dt-sc-details {
  bottom: 0px;
}
