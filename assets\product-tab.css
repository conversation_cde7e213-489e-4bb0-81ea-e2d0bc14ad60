.product-tab-demo .view-all-product{
    display: flex;
    justify-content: center;
    margin-top: 0rem;
}
.product-tab-demo .collection .description{width:100%;max-width:40%;margin-top:20px;margin-bottom:40px;}
.suggested-product-tab .tabs_container{
  margin-top:70px;
}
.suggested-product-tab .collection .tabs {
  display: flex;
  /* justify-content: center; */
  flex-wrap: wrap;
  margin-bottom: 1rem;
  justify-content: flex-end;
  column-gap:25px;
}
.product-tab-wrapper .collection .tabs .tablinks {
  color: var(--color-base-accent-1);
  cursor: pointer;
  /* padding: 0.5rem 0.5rem; */
  margin:  0 0 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  /* background-color: var(--gradient-base-background-1);
  border: 1px solid var(--gradient-base-background-1); */
  /* border-right: none; */
  box-shadow: inset 0 0 0 var(--gradient-base-background-2);
  transition: all 0.3s linear;
  font-weight: 400;
  font-family: var(--font-heading-family);
  font-size: clamp(1.6rem, 1.52rem + 0.4vw, 2rem);
}
.product-tab-wrapper .collection .tabs .tablinks:not(:last-child) {margin-right:20px;}
.product-tab-wrapper .collection .tabs .tablinks:last-child {
  /* border-right: 1px solid var(--gradient-base-background-1); */
}

.product-tab-wrapper .collection .tabs .tablinks:hover,
.product-tab-wrapper .collection .tabs .tablinks.active {
  color: rgb(var(--color-base-solid-button-labels));
  /* box-shadow: inset 0 -3px 0 var(--gradient-base-accent-1); */
  /* box-shadow: none;
  background: var(--gradient-base-background-2); */
}

.product-tab-wrapper .collection .tabs_container {
  position: relative;
  width: 100%;
}

.list-style-none {
  list-style: none !important;
}

.text-align-left {
  text-align: left !important;
  align-items: flex-start;
}

.text-align-center {
  text-align: center !important;
  align-items: center;
}

.product-tab-wrapper .collection .tabs_container .product-tab-carousel {
  width: 100%;
  transition: all 0.3s linear;
  padding-bottom: 80px;
}

.product-tab-wrapper .collection .tabs_container .product-tab-carousel:not(:first-child, :only-child),
.product-tab-wrapper .collection .tabs_container .dt-sc-tabs-content:not(:first-child, :only-child) {
  /*   position: absolute;  */
  left: 0;
  top: 0;
}

.product-tab-wrapper .collection .tabs_container .product-tab-carousel:not(.active),
.product-tab-wrapper .collection .tabs_container .dt-sc-tabs-content:not(.active) {
  opacity: 0;
  pointer-events: none;
}

.product-tab-wrapper .collection .grid {
  justify-content: space-between;
  margin: 0;
  width: 100%;
  padding: 0;
}

.product-tab-wrapper .collection .grid > .grid__item {
  max-width: calc(50% - 18px);
  width: calc(50% - 18px);
  overflow:hidden;
}
.product-tab-wrapper
  .collection
  .grid
  > .grid__item[class*="tab-template--"]:not(:only-child) {
  max-width: calc(50% - 18px);
  width: calc(50% - 18px);
}

.product-tab-wrapper .collection .grid__item:only-child {
  max-width: 100%;
  width: 100%;
}

.product-tab-wrapper
  .collection
  .grid
  > .grid__item:not(:only-child)
  .tabs_container
  .dt-sc-column.four-column {
  grid-template-columns: repeat(2, 1fr);
}
.product-tab-demo .swiper-grid-column > .swiper-wrapper{flex-direction:row;gap:100px 0;}
.product-tab-demo.product-tab-2 .swiper-grid-column>.swiper-wrapper{flex-wrap:nowrap;}
.product-tab-demo.product-tab-2 .tabs_container .swiper-button-prev span svg, 
.product-tab-demo.product-tab-2 .tabs_container .swiper-button-next span svg{
  fill:none;
  width:17px;
  height:20px;
}
.product-tab-demo.product-tab-2 .collection .title-wrapper-with-link{margin-bottom:2.2rem;}
.product-tab-demo.product-tab-2 .tabs_container .swiper-button-prev{
    position: absolute;
    top: -40px !important;
    right: 23%;
    left: 0;
    margin: auto;
}
.product-tab-demo.product-tab-2 .tabs_container .swiper-button-next{
    position: absolute;
    top: -40px !important;
    right: 0;
    left: 23%;
    margin: auto;
}
.product-tab-wrapper.product-tab-2 .collection .tabs .tablinks:first-child::after{
    content:'';
    position: absolute;
    background: var(--gradient-base-accent-1);
    height: 35px;
    width: 2px;
    right: 0;
}
.product-tab-wrapper.product-tab-2 .collection .tabs .tablinks:not(:last-child){margin-right:0;}
.product-tab-wrapper
  .collection
  .grid
  > .grid__item:not(:only-child)
  .tabs_container
  .dt-sc-column.five-column,
.product-tab-wrapper
  .collection
  .grid
  > .grid__item:not(:only-child)
  .tabs_container
  .dt-sc-column.six-column {
  grid-template-columns: repeat(3, 1fr);
}

.product-tab-wrapper .collection .grid__item,
.product-tab-wrapper .template-search .grid__item {
  padding: 0;
}

.product-tab-wrapper .collection .grid__item > .media {
  height: 100%;
  background: none;
  width: 100%;
}
.product-tab-wrapper .collection .grid__item > .media > img {
  height: 100%;
  position: relative;
  min-height: 295px;
}

.product-tab-wrapper .collection .grid__item > .media .image-block-heading {
  display: flex;
  flex-wrap: wrap;
  padding: 5rem;
  z-index: 1;
}
.product-tab-wrapper .collection .grid__item>.media .image-block-heading.center{justify-content:center;}
.product-tab-wrapper .collection .grid__item>.media .image-block-heading.left{justify-content:flex-start;}
.product-tab-wrapper .collection .grid__item>.media .image-block-heading.right{justify-content:flex-end;}


.product-tab-wrapper
  .collection
  .grid__item
  > .media
  .image-block-heading.vertical_top {
  align-content: flex-start;
}
.product-tab-wrapper
  .collection
  .grid__item
  > .media
  .image-block-heading.vertical_middle {
  align-content: center;
}
.product-tab-wrapper
  .collection
  .grid__item
  > .media
  .image-block-heading.vertical_bottom {
  align-content: flex-end;
}

.product-tab-wrapper .collection .grid__item > .media .image-block-heading > * {
  width: 100%;
  margin: 0;
}
.product-tab-wrapper
  .collection
  .grid__item
  > .media
  .image-block-heading
  > *:not(.button) {
  margin-bottom: 1rem;
}
.product-tab-wrapper
  .collection
  .grid__item
  > .media
  .image-block-heading
  > .button {
  width: auto;
  margin-bottom: 0;
  margin-top: 1.5rem;
}



.product-tab-wrapper
  .collection
  > .grid.image-with-text__grid.image-with-text__grid--reverse {
  flex-direction: row-reverse;
}
.product-tab-wrapper
  .collection
  > .grid.image-with-text__grid.image-with-text__grid--reverse
  .grid__item
  > .media {
  float: right;
}

.tabs_container .dt-sc-tabs-content-Details:not(.active) {
  opacity: 0;
  pointer-events: none;
  display: none;
}
.tabs .tablinks img{margin-right:10px;}

@media screen and (max-width:1440px){
.product-tab-demo .collection .description{width:100%;max-width:56%;margin:10px;}
}
@media screen and (min-width: 1201px) and (max-width: 1440px) {
  .product-tab-wrapper
    .collection
    .grid
    > .grid__item[class*="tab-template--"]:not(:only-child)
    .tabs_container
    .dt-sc-column {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media screen and (max-width: 1200px) {
  /*   .product-tab .collection .grid, */
  .product-tab-wrapper
    .collection
    .grid[class*="tab-template--"]:not(:only-child) {
    display: grid;
    grid-template-columns: 1fr;
  }

  .product-tab-wrapper .collection .grid > .grid__item,
  .product-tab-wrapper
    .collection
    .grid
    > .grid__item[class*="tab-template--"]:not(:only-child) {
    
    max-width: 100%;
    width: 100%;
  }

  .product-tab-wrapper
    .collection
    .grid
    > .grid__item:only-child
    .tabs_container
    .dt-sc-column.three-column,
  .product-tab-wrapper
    .collection
    .grid
    > .grid__item:only-child
    .tabs_container
    .dt-sc-column.four-column {
    grid-template-columns: repeat(3, 1fr);
  }

  .product-tab-wrapper
    .collection
    .grid
    > .grid__item:only-child
    .tabs_container
    .dt-sc-column.five-column,
  .product-tab-wrapper
    .collection
    .grid
    > .grid__item:only-child
    .tabs_container
    .dt-sc-column.six-column {
    grid-template-columns: repeat(3, 1fr);
  }

  #dtx-quickview-content .product .product__title {
    font-size: calc(0.4 * var(--heading_font_size));
  }
}
@media screen and (max-width:1024px){
.product-tab-demo .collection .description{width:100%;max-width:66%;margin:10px;}
}
@media screen and (max-width:990px){
  .product-tab-wrapper .collection .tabs_container .product-tab-carousel {
  width: 100%;
  transition: all 0.3s linear;
  padding-bottom: 20px;
}
}
@media screen and (max-width: 780px) {
.product-tab-demo .view-all-product{margin-top:0;margin-bottom: 3rem;}
.product-tab-demo .collection .description{width:100%;max-width:90%;margin:10px;}
.product-tab-demo .title-wrapper-with-link{margin-bottom:0;}
}
@media screen and (max-width: 750px) {
  .product-tab-wrapper
    .collection
    .grid
    > .grid__item[class*="tab-template--"]:not(:only-child)
    .tabs_container
    .dt-sc-column,
  .product-tab-wrapper
    .collection
    .grid
    > .grid__item:not(:only-child)
    .tabs_container
    .dt-sc-column,
  .product-tab-wrapper
    .collection
    .grid
    > .grid__item:only-child
    .tabs_container
    .dt-sc-column.five-column,
  .product-tab-wrapper
    .collection
    .grid
    > .grid__item:only-child
    .tabs_container
    .dt-sc-column.six-column {
    grid-template-columns: repeat(2, 1fr);
  }
  .product-tab-wrapper .collection .grid__item > .media .image-block-heading {
    padding: 3rem;
  }
}

@media screen and (max-width: 575px) {
  .product-tab-wrapper
    .collection
    .grid
    > .grid__item[class*="tab-template--"]:not(:only-child)
    .tabs_container
    .dt-sc-column,
  .product-tab-wrapper
    .collection
    .grid
    > .grid__item:not(:only-child)
    .tabs_container
    .dt-sc-column,
  .product-tab-wrapper
    .collection
    .grid
    > .grid__item:only-child
    .tabs_container
    .dt-sc-column.five-column,
  .product-tab-wrapper
    .collection
    .grid
    > .grid__item:only-child
    .tabs_container
    .dt-sc-column.six-column {
    grid-template-columns: 1fr;
  }


  .product-tab-wrapper .collection .tabs .tablinks {
    width: 100%;
    padding: 1.5rem 2rem;
  }

  .product-tab-wrapper
    .collection
    .grid
    > .grid__item:only-child
    .tabs_container
    .dt-sc-column.three-column,
  .product-tab-wrapper
    .collection
    .grid
    > .grid__item:only-child
    .tabs_container
    .dt-sc-column.four-column {
    grid-template-columns: repeat(2, 1fr);
  }
  .product-tab-demo .collection .description{width:100%;max-width:100%;margin:10px;}
 .product-tab-demo .title-wrapper-with-link{margin-bottom:1rem;}
}
  @media screen and (max-width: 576px) {
/************************************/
.product-tab-wrapper .collection .tabs .tablinks:not(:last-child){margin-right:0;}  
.product-tab-wrapper .collection .tabs .tablinks{ text-transform: uppercase; margin: 0;padding: 1rem 0; cursor: pointer;}
.product-tab-wrapper .collection .grid__item{position:relative; display:grid; grid-template-columns:repeat(1,1fr); align-items:center; justify-content:center; row-gap:4.5rem;}  
.product-tab-wrapper .collection .tabs{    grid-row: 1; grid-column: 1/3; width: 92%; max-width: 35rem; background: var(--gradient-background); position: absolute; top: 0; z-index: 3; justify-content: flex-start; height: auto; margin: 0 auto; left: 0; right: 0; box-shadow: 0 0 15px #00000026;}
.product-tab-wrapper .collection .tabs_container{grid-row: 3;}   
    /*Drop-down-tab*/
.product-tab-wrapper .collection  .tabs :is(.tablinks) { transition: none; border-radius: inherit;}
.product-tab-wrapper .collection  .tabs .tablinks.active{background: var(--gradient-base-accent-1);width: 100%;position: relative;  color: var(--gradient-base-background-1);  padding-right: 3rem;}
.product-tab-wrapper .collection .tabs.expanded .tablinks.active { order: 0; z-index: 1;background: var(--gradient-base-accent-1);}
.product-tab-wrapper .collection  .tabs.expanded .tablinks:not(.active):hover { background: var(--gradient-base-background-2);}
    /*not-active tab*/
.product-tab-wrapper .collection  .tabs .tablinks:not(.active) { position: absolute; top: 0; left: 0; right: 0; width: 100%; opacity: 0;}
.product-tab-wrapper .collection  .tabs.expanded .tablinks:not(.active) { position: relative; top: auto; opacity: 1; order: 1; animation: fadeIn 1.2s ease-out both; border:none;}
.product-tab-wrapper .collection  .tabs:not(.expanded) .tablinks:not(.active) {pointer-events: none;}
.product-tab-wrapper .collection  .tabs :is(.tablinks.active):after{ content: ""; position: absolute;cursor: pointer;
      -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='icon icon-caret' width='8.828' height='5.414' viewBox='0 0 8.828 5.414'%3E%3Cpath id='Path_20' data-name='Path 20' d='M4,10,7,7,4,4' transform='translate(11.414 -2.586) rotate(90)' fill='none' stroke='currentcolor' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5'%3E%3C/path%3E%3C/svg%3E");
      -webkit-appearance: none;-webkit-mask-position: center; -webkit-mask-repeat: no-repeat; -webkit-mask-size: 100%; content: ""; width: 1.5rem; height: 1.5rem; z-index: 1; color: var(--gradient-base-background-1); right: 1.5rem; background-color: var(--gradient-base-background-1);top: 50%;transform: translateY(-50%); } 
.product-tab-wrapper .collection .tabs .tablinks{font-size:1.4rem;}    
  }
@media screen and (max-width: 1199px){
  .product-tab-wrapper .collection .tabs_container .product-tab-carousel{
    padding-bottom: 20px;
}
}

.product-tab-with-carousel .product-tab-demo .tabs.center{ margin-bottom:3rem; }
.product-tab-with-carousel .product-tab-demo .collection .title-wrapper-with-link{ margin:0; }
