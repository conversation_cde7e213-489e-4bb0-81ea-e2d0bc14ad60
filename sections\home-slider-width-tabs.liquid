<script src="{{ 'slick.min.js' | asset_url }}" async></script>
{{ 'slick.min.css' | asset_url | stylesheet_tag }}

 {%- style -%}
    .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.50 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.50 | round: 0 }}px;
  }
   @media screen and (min-width: 576px) and (max-width: 749px){
   .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }
   }
  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}
<div class="section-{{ section.id }}-home-slider-with-tabs color-{{ section.settings.color_scheme }} gradient{% if section.settings.title == blank %} no-heading{% endif %} {{ section.settings.custom_class_name }} ">
 <div class="{% if section.settings.page_full_width %} page-full-width {% else %} page-width {% endif %} {% if section.settings.page_full_width_spacing %} page-full-width_spacing {% endif %}section-{{ section.id }}-padding isolate">
 <div class="row">
      {%- unless section.settings.title == blank -%}
      <div class="title-wrapper-with-link title-wrapper--self-padded-mobile title-wrapper--no-top-margin content-align--{{ section.settings.column_alignment }}">
         {%- if section.settings.sub_heading != blank -%}  
         <h6 class="sub-heading">{{ section.settings.sub_heading | escape }}</h6>
         {%- endif -%} 
         {%- if section.settings.title != blank -%}  
          <h2 class="title {{ section.settings.heading_size }}">
            {{ section.settings.title | escape }}
          </h2>
          {%- endif -%} 
          {%- if section.settings.description != blank -%}  
          <p class="description">{{ section.settings.description }}</p>
          {%- endif -%}   
          {%- if section.settings.button_label != blank -%}
            <a {% if section.settings.button_link == blank %} role="link" aria-disabled="true"{% else %} href="{{ section.settings.button_link }}"{% endif %} class="button{% if section.settings.button_style_secondary %} button--secondary{% else %} button--primary{% endif %}">{{ section.settings.button_label | escape }}</a>
          {%- endif -%}
          </div>
      {%- endunless -%}
      <div class="dt-sc-slider-image wow {% if section.settings.animation_effect_section != 'none' %} {{ section.settings.animation_effect_section }} {% endif %} ">
        <div id="sliderTabs-{{ section.id }}-ctrls"></div>
        <div id="sliderTabs-{{ section.id }}-slider">
          {% for block in section.blocks %}
          <div class="top-data_block">
            <div class="dt-sc-banner-content dt-sc-progress-content--{{ block.id }}">
                {% if block.settings.show_image %}
                <div class="dt-sc-slider-image">
                {% if block.settings.image != blank %}
                <img
                  srcset="{%- if block.settings.image.width >= 165 -%}{{ block.settings.image | image_url: width: 165 }} 165w,{%- endif -%}
                    {%- if block.settings.image.width >= 360 -%}{{ block.settings.image | image_url: width: 360 }} 360w,{%- endif -%}
                    {%- if block.settings.image.width >= 535 -%}{{ block.settings.image | image_url: width: 535 }} 535w,{%- endif -%}
                    {%- if block.settings.image.width >= 750 -%}{{ block.settings.image | image_url: width: 750 }} 750w,{%- endif -%}
                    {%- if block.settings.image.width >= 1070 -%}{{ block.settings.image | image_url: width: 1070 }} 1070w,{%- endif -%}
                    {%- if block.settings.image.width >= 1500 -%}{{ block.settings.image | image_url: width: 1500 }} 1500w,{%- endif -%}
                    {{ block.settings.image | image_url: width: 1920 }} {{ block.settings.image.width }}w"
                  src="{{ block.settings.image | image_url: width: 1500 }}"
                  sizes="(min-width: {{ settings.container_width }}px) {{ settings.container_width | minus: 100 | divided_by: 2 }}px, (min-width: 750px) calc(50vw - 130px), calc(50vw - 55px)"
                  alt="{{ block.settings.image.alt }}"
                  loading="lazy"
                  width="{{ block.settings.image.width }}"
                  height="{{ block.settings.image.height }}"
                  class="desktop-slider"
                >
              {% else %}
              {{ 'lifestyle-2' | placeholder_svg_tag: 'placeholder_svg' }}
              {% endif %}
                  {%- if block.settings.mobile_image -%}
              <img
              srcset="{%- if block.settings.mobile_image.width >= 375 -%}{{ block.settings.mobile_image | image_url: width: 375 }} 375w,{%- endif -%}
              {%- if block.settings.mobile_image.width >= 550 -%}{{ block.settings.mobile_image | image_url: width: 550 }} 550w,{%- endif -%}
              {%- if block.settings.mobile_image.width >= 750 -%}{{ block.settings.mobile_image | image_url: width: 750 }} 750w,{%- endif -%}
              {%- if block.settings.mobile_image.width >= 1100 -%}{{ block.settings.mobile_image | image_url: width: 1100 }} 1100w,{%- endif -%}
              {%- if block.settings.mobile_image.width >= 1500 -%}{{ block.settings.mobile_image | image_url: width: 1500 }} 1500w,{%- endif -%}
              {%- if block.settings.mobile_image.width >= 1780 -%}{{ block.settings.mobile_image | image_url: width: 1780 }} 1780w,{%- endif -%}
              {%- if block.settings.mobile_image.width >= 2000 -%}{{ block.settings.mobile_image | image_url: width: 2000 }} 2000w,{%- endif -%}
              {%- if block.settings.mobile_image.width >= 3000 -%}{{ block.settings.mobile_image | image_url: width: 3000 }} 3000w,{%- endif -%}
              {%- if block.settings.mobile_image.width >= 3840 -%}{{ block.settings.mobile_image | image_url: width: 3840 }} 3840w,{%- endif -%}
              {{ block.settings.mobile_image | image_url }} {{ block.settings.mobile_image.width }}w"
              sizes="100vw"
              src="{{ block.settings.mobile_image | image_url: width: 1500 }}"
              loading="lazy"
              alt="{{ block.settings.mobile_image.alt | escape }}"
              width="{{ block.settings.mobile_image.width }}"
              height="{{ block.settings.mobile_image.width | divided_by: block.settings.mobile_image.aspect_ratio | round }}"
              class="mobile-slider"
            >          
          {%- endif -%}
               </div>
               {% endif %}
              {% if block.settings.show_content %}
                <div class="dt-sc-slider-content  content_position-{{ block.settings.content_position }}">
                    <div class="dt-sc-slider-content-align dt-sc-align-{{ block.settings.text_alignment }} color-{{ section.settings.color_scheme }} gradient ">
                      {% if block.settings.sub_title != blank %}
                      <h4>{{ block.settings.sub_title }}</h4>
                      {% endif %}
                      {% if block.settings.main_title != blank %}
                      <h2 class="slider-heading">{{ block.settings.main_title }}</h2>
                      {% endif %}
                    
                      {% if block.settings.text != blank %}
                      <p class="slider-description">{{ block.settings.text }}</p>
                      {% endif %}
                      {% if block.settings.link != blank %}
                      <a href="{{ block.settings.link }}" class="button dt-sc-btn">{{ block.settings.link_text }}</a>
                      {% endif %}
                     
                    </div>
                  </div>
                 {% endif %}
            </div>
          </div>
          {% endfor %}
        </div>
        <div id="sliderTabs-{{ section.id }}-nav">
          {% for block in section.blocks %}
          <div class="dt-sc-progress-tab">
            <div class="dt-sc-progress-wrapper">
              <div class="dt-sc-progress-content dt-sc-progress-content--{{ block.id }}">
                  {% if block.settings.show_content %}
                {% if block.settings.tab != blank %}
                <h4><span class="dt-sc-on-progress"></span><p>{{ block.settings.tab }}</p></h4>
                {% endif %}
                  {% endif %}
              </div>
            </div>
          </div>
          {% endfor %}
        </div>
      </div>
    </div>
  </div>
</div>
{%- style -%}
    
    .section-{{ section.id }}-home-slider-with-tabs ..dt-sc-banner-content { position: relative; }
    .section-{{ section.id }}-home-slider-with-tabs .dt-sc-slider-content { position: absolute; width: 100%; display: flex; flex-wrap: wrap;
      align-items: center; justify-content: center; left: 0; flex-direction: column; z-index: 1;  text-align: center;/*margin:80px;*/}

/*   { max-width: 600px; padding: var(--DTGutter_Width); } */
    .section-{{ section.id }}-home-slider-with-tabs .dt-sc-slider-content > *,
    .section-{{ section.id }}-home-slider-with-tabs .dt-sc-slider-content > *:not(:only-child) { margin:0; }
    .section-{{ section.id }}-home-slider-with-tabs .dt-sc-slider-content > *:not(:last-child){ margin:0 0 15px 0; }
    .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-tab h4 p { color: var(--DTColor_Heading); color: {{ section.settings.slider_progress_title_color }}; }
    .section-{{ section.id }}-home-slider-with-tabs #sliderTabs-{{ section.id }}-nav .dt-sc-progress-tab h4 span.dt-sc-on-progress {
      background: var(--DTTertiaryColor);
        background: {{ section.settings.slider_progress_bar_color }}; }
    .section-{{ section.id }}-home-slider-with-tabs #sliderTabs-{{ section.id }}-nav .dt-sc-progress-tab h4 span.dt-sc-on-progress:before {
      background: var(--DTPrimaryColor);
        background: {{ section.settings.slider_progress_bar_bg_color }};
        }
    .section-{{ section.id }}-home-slider-with-tabs .dt-sc-banner-content h2,
    .section-{{ section.id }}-home-slider-with-tabs .dt-sc-banner-content h4 { margin: 0; }
    .section-{{ section.id }}-home-slider-with-tabs .dt-sc-banner-content h4 p {  margin-bottom: 0; padding: 0 10px; }
    {% for block in section.blocks %}
    .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }} h2 { color: {{ block.settings.slider_heading_color }};
      font-size: {{ block.settings.main_title_size }}px; }
    .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }} h4 { color: {{ block.settings.slider_sub_heading_color }};
      font-size: {{ block.settings.sub_title_size }}px;}
    .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }} p.slider-description { color: {{ block.settings.slider_description_color }};margin:0;
      font-size:{{ block.settings.text_size }}px; }
    .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }} .dt-sc-btn {
      background-color: {{ block.settings.slider_button_bg_color }}; color: {{ block.settings.slider_button_text_color }}; }
    .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }} .dt-sc-btn:hover {
      background-color: {{ block.settings.slider_button_hover_bg_color }}; color: {{ block.settings.slider_button_hover_text_color }}; }

    .section-{{ section.id }}-home-slider-with-tabs  .dt-sc-progress-content--{{ block.id }} .dt-sc-slider-content .dt-sc-slider-content-align:before  {
  content: "";
  display: block;
  position: absolute;
  left:0;
  width: 100%;
  top: 0;
  height: 100%; z-index: -1;
  background: var(--DTTertiaryColor);
  background: {{ block.settings.slider_overlay_color | color_modify: 'alpha', 0.75 }};

  }
   .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }} img{width:100%;object-fit:cover;height:100%}
   @media screen and (min-width: 1540px) {
    .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }} img{ height: {{ section.settings.slider_height }}px; }
      }
    @media screen and (max-width: 1540px) {
    .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }} img{ height: {{ section.settings.slider_height_lap }}px; }
      }
    @media screen and (max-width: 1199px) {
      .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }} h2 { font-size: calc(.9 * {{ block.settings.main_title_size }}px); }
      .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }} h4 { font-size: calc(.9 * {{ block.settings.sub_title_size }}px);}
      .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }} img{ height: {{ section.settings.slider_height_tab }}px; }
      .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }} p.slider-description { font-size:  {{ block.settings.text_size }}px;}
      }
    @media screen and (max-width: 767px) {
      .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }} h2 { font-size: calc(.55 * {{ block.settings.main_title_size }}px); }
      .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }} h4 { font-size: calc(.65 * {{ block.settings.sub_title_size }}px);}
      .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }}  p.slider-description { font-size: {{ block.settings.text_size }}px; margin: 0;}

      .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }} img{ height: {{ section.settings.slider_height_mobile }}px; }

      }

    .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }} .content_position-top-left { align-items: flex-start;top:0;}
    .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }} .content_position-top-right {align-items: flex-end;top:0; }
    .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }} .content_position-top-center {align-items:center ;top:0;}
    .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }} .content_position-bottom-left { align-items: flex-start;bottom:0;}
    .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }} .content_position-bottom-right {align-items: flex-end;bottom:0;}
    .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }} .content_position-bottom-center {align-items:center;bottom:0;}
    .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }} .content_position-center { left: 0; right: 0; margin: auto; top: 0; bottom: 0; }
    .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }} .content_position-center-left { align-items: flex-start;top: 0;bottom: 0;}
    .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }} .content_position-center-right {align-items: flex-end;top: 0;bottom: 0; }

    .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }} .dt-sc-align-left { justify-content:left;align-items:flex-start;    text-align: left; }
    .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }} .dt-sc-align-right { justify-content:right;align-items:flex-end;    text-align: right; }
    .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }} .dt-sc-align-center { justify-content:center;align-items:center;    text-align: center; }
    .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }} .dt-sc-slider-content  .dt-sc-slider-content-align{display:flex;flex-direction:column;padding:30px;position:relative;margin:80px;}
    {% endfor %}
    .section-{{ section.id }}-home-slider-with-tabs .dt-sc-slider-image { width: 100%; position: relative; }
    .section-{{ section.id }}-home-slider-with-tabs #sliderTabs-{{ section.id }}-ctrls .slick-arrow { position: absolute; top: 50%; transform: translateY(-50%); z-index: 1; cursor: pointer;
      font-size: 30px; }
    .section-{{ section.id }}-home-slider-with-tabs #sliderTabs-{{ section.id }}-ctrls .prev-arrow { left: 30px; }
    .section-{{ section.id }}-home-slider-with-tabs #sliderTabs-{{ section.id }}-ctrls .next-arrow { right: 30px; }
    .section-{{ section.id }}-home-slider-with-tabs #sliderTabs-{{ section.id }}-nav .dt-sc-progress-tab { width:auto !important; display:inline-block; float: none;
      transition: var(--DTBaseTransition); }
    .section-{{ section.id }}-home-slider-with-tabs #sliderTabs-{{ section.id }}-nav .dt-sc-progress-tab h4 { cursor: pointer; padding: 0 14px;
      width: auto; height: auto; display: block; margin: 0; text-transform: initial; font-weight: 600; line-height: 1.2; background: 0 0 !important; opacity: .5;
      font-size: 16px; transition: var(--DTBaseTransition); margin-bottom: 20px; min-width: 100px;
        }
    .section-{{ section.id }}-home-slider-with-tabs #sliderTabs-{{ section.id }}-nav { position: absolute; bottom: 0; left: 0; width: 100%; text-align: center; }
    .section-{{ section.id }}-home-slider-with-tabs #sliderTabs-{{ section.id }}-nav .slick-track { transform: none !important; text-align: center; }
    .section-{{ section.id }}-home-slider-with-tabs #sliderTabs-{{ section.id }}-nav .dt-sc-progress-tab .slick-current h4,
    .section-{{ section.id }}-home-slider-with-tabs #sliderTabs-{{ section.id }}-nav .dt-sc-progress-tab:hover h4 { opacity: 1; }
    .section-{{ section.id }}-home-slider-with-tabs #sliderTabs-{{ section.id }}-nav .dt-sc-progress-tab h4 span.dt-sc-on-progress { width: 100%; height: 2px; display: block; margin: 8px 0 0; transition: var(--DTBaseTransition); position: relative; }
    .section-{{ section.id }}-home-slider-with-tabs #sliderTabs-{{ section.id }}-nav .dt-sc-progress-tab h4 span.dt-sc-on-progress:before { content: ''; width: 100%; height: 100%; display: block; transform-origin: left; transform: scaleX(0); }
    .section-{{ section.id }}-home-slider-with-tabs #sliderTabs-{{ section.id }}-nav .dt-sc-progress-tab.slick-current h4 { opacity: 1; }
    .section-{{ section.id }}-home-slider-with-tabs #sliderTabs-{{ section.id }}-nav .dt-sc-progress-tab.slick-current h4 span.dt-sc-on-progress:before {
      animation: 2.35s linear forwards running progress-horizontal;
    }
    {% if section.settings.disable_arrows != blank %}
    .section-{{ section.id }}-home-slider-with-tabs #sliderTabs-{{ section.id }}-ctrls { display: none; }
    {% endif %}
    .section-{{ section.id }}-home-slider-with-tabs #sliderTabs-{{ section.id }}-ctrls { height: 100%; position: absolute; width: 100%; top: 0; left: 0; }
    .section-{{ section.id }}-home-slider-with-tabs #sliderTabs-{{ section.id }}-ctrls .slick-arrow { margin: 0; width: 40px; height: 40px; }
    .section-{{ section.id }}-home-slider-with-tabs #sliderTabs-{{ section.id }}-ctrls .slick-arrow.prev-arrow:after,
    .section-{{ section.id }}-home-slider-with-tabs #sliderTabs-{{ section.id }}-ctrls .slick-arrow.next-arrow:after { content: "" !important; display: block; background: none;  width: 12px; height: 12px; transform: rotate(45deg); position: absolute; }
    .section-{{ section.id }}-home-slider-with-tabs #sliderTabs-{{ section.id }}-ctrls .slick-arrow.prev-arrow:after { right: 10px; border-left: 1px solid; border-bottom: 1px solid; }
    .section-{{ section.id }}-home-slider-with-tabs #sliderTabs-{{ section.id }}-ctrls .slick-arrow.next-arrow:after { left: 10px; border-right: 1px solid; border-top: 1px solid; }
    @keyframes progress-horizontal { from { transform:scaleX(0) } to { transform:scaleX(1) } }
    
  
  {% if section.settings.enable_two_column_block %}
    .section-{{ section.id }}-home-slider-with-tabs  .dt-sc-banner-content {display:flex;flex-direction:row-reverse;}
    .section-{{ section.id }}-home-slider-with-tabs  .dt-sc-slider-content{position:relative;}
    .section-{{ section.id }}-home-slider-with-tabs  .dt-sc-banner-content .dt-sc-slider-image,
    .section-{{ section.id }}-home-slider-with-tabs  .dt-sc-banner-content .dt-sc-slider-content{width:50%;margin:0;}
    .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }} img{height:100%}
  {% endif %}
  
 /* product section */
  
 .section-{{ section.id }}-home-slider-with-tabs .slider-product .slider-product-details .product-image {width: 100%;height: 100%;object-fit:cover;}
 .section-{{ section.id }}-home-slider-with-tabs .slider-product {display: flex;justify-content: space-between;} 
 .section-{{ section.id }}-home-slider-with-tabs .slider-product .slider-product-details{width:calc(35% - var(--grid-desktop-vertical-spacing));}
 .section-{{ section.id }}-home-slider-with-tabs .slider-product .slider-details{width:calc(75% - var(--grid-desktop-vertical-spacing));} 
 
  .section-{{ section.id }}-home-slider-with-tabs .slider-product-details a{text-decoration:none;}  
  
 

   @media only screen and (max-width: 1540px) {
       {% for block in section.blocks %}
 
    .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }} img{height:100%} 
    {% endfor %}
      
     .section-{{ section.id }}-home-slider-with-tabs .slider-product .slider-product-details{width:calc(35% - var(--grid-desktop-vertical-spacing));}
     .section-{{ section.id }}-home-slider-with-tabs .slider-product .slider-details{width:calc(70% - var(--grid-desktop-vertical-spacing));} 
   }
  
   @media only screen and (min-width: 1200px) {
      .section-{{ section.id }}-home-slider-with-tabs .dt-sc-section-wrapper { margin-top:{{ margin_top }}px; margin-bottom:{{ margin_bottom }}px;padding-top:{{ padding_top }}px; padding-bottom:{{ padding_bottom }}px; }
    }
    @media only screen and (max-width: 1199px) {
      .section-{{ section.id }}-home-slider-with-tabs .dt-sc-section-wrapper { margin-top:{{ margin_top | divided_by: 2 }}px; margin-bottom:{{ margin_bottom | divided_by: 2 }}px;padding-top:{{ padding_top | divided_by: 2 }}px; padding-bottom:{{ padding_bottom | divided_by: 2 }}px;}
      .section-{{ section.id }}-home-slider-with-tabs .dt-sc-slider-content > *:not(:last-child){ margin:0 0 8px 0; }
       {% for block in section.blocks %}
      .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }} img{height:100%}
      {% endfor %}
    }
   @media only screen and (max-width: 990px) {
    .section-{{ section.id }}-home-slider-with-tabs  .dt-sc-banner-content .dt-sc-slider-image,
    .section-{{ section.id }}-home-slider-with-tabs  .dt-sc-banner-content .dt-sc-slider-content{width:100%;margin:0;}
    .section-{{ section.id }}-home-slider-with-tabs  .dt-sc-banner-content .dt-sc-slider-content{position: absolute;background: transparent;height:100%;padding:30px;}
    .section-{{ section.id }}-home-slider-with-tabs  .dt-sc-banner-content .dt-sc-slider-image:before{
      content:'';width:100%;height:100%;position:absolute;z-index:1;background:rgba(var(--color-base-background-1), 0.6);
    }
    {% for block in section.blocks %}
    .section-{{ section.id }}-home-slider-with-tabs.custom-slider-with-tabs .dt-sc-progress-content--{{ block.id }} .dt-sc-slider-content  .dt-sc-slider-content-align{margin:0;padding:0;} 
    .section-{{ section.id }}-home-slider-with-tabs.custom-slider-with-tabs .dt-sc-progress-content--{{ block.id }} img{ height: {{ section.settings.slider_height_tab }}px; }
    {% endfor %} 
   }
      @media screen and (max-width: 767px) {
      .dt-sc-slider-image img.desktop-slider{display:none;}
      .slideshow__media img.mobile-slider{display:block;}
      {% for block in section.blocks %}  
       .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }} img{ height: {{ section.settings.slider_height_mobile }}px; }
      {% endfor %}
      }
      @media screen and (min-width: 768px) {
      .dt-sc-slider-image img.mobile-slider{display:none;}
      .dt-sc-slider-image img.desktop-slider{display:block;}
      } 
    @media screen and (max-width: 767px) {
/*       .section-{{ section.id }}-home-slider-with-tabs h4 p { display: none; } */
      .section-{{ section.id }}-home-slider-with-tabs #sliderTabs-{{ section.id }}-nav .dt-sc-progress-tab h4 { min-width: 75px; }
     {% for block in section.blocks %}
      .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }} .dt-sc-slider-content  .dt-sc-slider-content-align{margin:30px;}
       {% endfor %}
      .section-{{ section.id }}-home-slider-with-tabs .slider-product{flex-direction:column;}
      .section-{{ section.id }}-home-slider-with-tabs .slider-product .slider-product-details,
      .section-{{ section.id }}-home-slider-with-tabs .slider-product .slider-details{width:100%;} 
      .section-{{ section.id }}-home-slider-with-tabs.custom-slider-with-tabs .slider-product .slider-product-details .product-tilte a{margin:20px 0 5px;}
      
    }
    @media screen and (max-width: 576px) {

      .section-{{ section.id }}-home-slider-with-tabs .dt-sc-slider-content > *:not(:last-child){ margin:0 0 0 0; }
      .section-{{ section.id }}-home-slider-with-tabs #sliderTabs-{{ section.id }}-nav .dt-sc-progress-tab h4 { min-width: 60px; }
      {% for block in section.blocks %}
       .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }} .dt-sc-slider-content{align-items: center!important;justify-content: center!important; top: 0;bottom: 0;}
       .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }} .dt-sc-slider-content  .dt-sc-slider-content-align{margin:10px;text-align:center!important}
       .section-{{ section.id }}-home-slider-with-tabs .dt-sc-progress-content--{{ block.id }} .dt-sc-slider-content div> *{margin:10px auto 0;text-align:center!important}
          
        {% endfor %}
          }
  {%- endstyle -%}

<script type="text/javascript">
  $(document).ready(function(){
    $('#sliderTabs-{{ section.id }}-slider').not('.slick-initialized').slick({
      slidesToShow: 1,
      slidesToScroll: 1,
      arrows: true,
      appendArrows: '#sliderTabs-{{ section.id }}-ctrls',
      prevArrow: '<a class="prev-arrow dt-sc-btn"></a>',
      nextArrow: '<a class="next-arrow dt-sc-btn"></a>',
      fade: true,
      asNavFor: '#sliderTabs-{{ section.id }}-nav',
      autoplay:true,
      autoplaySpeed:2500,
      adaptiveHeight: true
      });

    $('#sliderTabs-{{ section.id }}-nav').not('.slick-initialized').slick({
      slidesToShow: {{ section.blocks.size }},
      slidesToShow: 6,
      slidesToScroll: 1,
      asNavFor: '#sliderTabs-{{ section.id }}-slider',
      dots: false,
      focusOnSelect: true,
      autoplay:true,
      autoplaySpeed:2500,
      adaptiveHeight: true,
      responsive: [
      {
      breakpoint: 1024,
      settings: {
      slidesToShow: {{ section.blocks.size }},
      slidesToScroll: 1,
      infinite: true,
      dots: false
      }
      }
      ]
      });
  });
</script>

{% schema %}
{
"name": "t:sections.slider_with_tab.name",
"class": "section index-section home-slider-with-tabs",
"max_blocks": 4,
"settings": [
 {
      "type":"checkbox",
      "id":"page_full_width",
       "default": false,
      "label": "t:sections.all.page_full_width.label"
    },
    {
      "type":"checkbox",
      "id":"page_full_width_spacing",
       "default": false,
      "label": "t:sections.all.page_full_width_spacing.label"
    },
    {
      "type": "text",
      "id": "title",
      "default": "Slider with tab",
      "label": "t:sections.all.title.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__3.label"
        }
      ],
      "default": "h1",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "text",
      "id": "sub_heading",
     "default": "Sub Heading",  
      "label": "t:sections.all.sub_heading.label"
    },
     {
      "type": "text",
      "id": "description",
      "default": "Use This Text To Share The Information Which You Like!.",   
      "label": "t:sections.all.description.label"
    },
    {
      "type": "text",
      "id": "button_label",
      "default": "Button label",
      "label": "t:sections.all.button_label.label"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "t:sections.all.button_link.label"
    },
    {
      "type": "checkbox",
      "id": "button_style_secondary",
      "default": false,
      "label": "t:sections.all.button_style_secondary.label"
      },
    {
      "type": "select",
      "id": "column_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.slider_with_tab.settings.column_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.slider_with_tab.settings.column_alignment.options__2.label"
        }
      ],
      "default": "center",
      "label": "t:sections.slider_with_tab.settings.column_alignment.label"
    }, 
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "t:sections.all.colors.accent_1.label"
        },
        {
          "value": "accent-2",
          "label": "t:sections.all.colors.accent_2.label"
        },
        {
          "value": "background-1",
          "label": "t:sections.all.colors.background_1.label"
        },
        {
          "value": "background-2",
          "label": "t:sections.all.colors.background_2.label"
        },
        {
          "value": "inverse",
          "label": "t:sections.all.colors.inverse.label"
        }
      ],
      "default": "background-1",
      "label": "t:sections.all.colors.label"
    },
{
"type": "header",
"content": "t:sections.slider_with_tab.settings.slider_color_settings.content"
},
{
"type": "color",
"id": "slider_progress_title_color",
"label": "t:sections.slider_with_tab.settings.slider_progress_title_color.label"
},
{
"type": "color",
"id": "slider_progress_bar_color",
"label": "t:sections.slider_with_tab.settings.slider_progress_bar_color.label"
},
{
"type": "color",
"id": "slider_progress_bar_bg_color",
"label": "t:sections.slider_with_tab.settings.slider_progress_bar_bg_color.label"
},
{
"type" : "checkbox",
"id" : "disable_arrows",
"label" : "t:sections.slider_with_tab.settings.disable_arrows.label",
"default": true
},
{
"type" : "checkbox",
"id" : "enable_two_column_block",
"label" : "t:sections.slider_with_tab.settings.enable_two_column_block.label",
"default": false
},  
{
"type": "header",
"content": "t:sections.slider_with_tab.settings.slide_image_height.content"
},
{
"type": "range",
"id": "slider_height",
"label": "t:sections.slider_with_tab.settings.slider_height.label",
"min": 400,
"max": 1200,
"step": 10,
"default": 400,
"unit": "px"
},
{
"type": "range",
"id": "slider_height_lap",
"label": "t:sections.slider_with_tab.settings.slider_height_lap.label",
"min": 400,
"max": 1200,
"step": 10,
"default": 400,
"unit": "px"
},
{
"type": "range",
"id": "slider_height_tab",
"label": "t:sections.slider_with_tab.settings.slider_height_tab.label",
"min": 400,
"max": 1200,
"step": 10,
"default": 600,
"unit": "px"
},
{
"type": "range",
"id": "slider_height_mobile",
"label": "t:sections.slider_with_tab.settings.slider_height_mobile.label",
"min": 300,
"max": 1200,
"step": 10,
"default": 350,
"unit": "px"
},
{
  "type": "header",
  "content": "t:sections.all.padding.section_padding_heading"
},
{
  "type": "range",
  "id": "padding_top",
  "min": 0,
  "max": 100,
  "step": 4,
  "unit": "px",
  "label": "t:sections.all.padding.padding_top",
  "default": 36
},
{
  "type": "range",
  "id": "padding_bottom",
  "min": 0,
  "max": 100,
  "step": 4,
  "unit": "px",
  "label": "t:sections.all.padding.padding_bottom",
  "default": 36
},  
{
"type": "header",
"content": "t:sections.all.custom_class_heading.content"
},
{
"type": "text",
"id": "custom_class_name",
"label": "t:sections.all.custom_class_name.label"
}
],
"blocks": [
{
"type": "slider_tab",
"name":"t:sections.slider_with_tab.blocks.slider_tab.name",
"settings": [
{
"type": "image_picker",
"id": "image",
"label": "t:sections.slider_with_tab.blocks.slider_tab.settings.image.label"
},
{
"type": "image_picker",
"id": "mobile_image",
"label": "t:sections.slider_with_tab.blocks.slider_tab.settings.mobile_image.label",
"info": "768x900"
},
{
"type": "checkbox",
"id": "show_image",
"label": "t:sections.slider_with_tab.blocks.slider_tab.settings.image.label",
"default": true
},
{
"type" : "checkbox",
"id" : "show_content",
"label" : "t:sections.slider_with_tab.blocks.slider_tab.settings.show_content.label",
"default": true
},
{
"type": "select",
"id": "content_position",
"label": "t:sections.slider_with_tab.blocks.slider_tab.settings.content_position.label",
"default": "center",
"options": [
{
"value": "top-left",
"label": "t:sections.slider_with_tab.blocks.slider_tab.settings.content_position.options__1.label"
},
{
"value": "top-center",
"label": "t:sections.slider_with_tab.blocks.slider_tab.settings.content_position.options__2.label"
},
{
"value": "top-right",
"label": "t:sections.slider_with_tab.blocks.slider_tab.settings.content_position.options__3.label"
},
{
"value": "center-left",
"label": "t:sections.slider_with_tab.blocks.slider_tab.settings.content_position.options__4.label"
},
{
"value": "center",
"label": "t:sections.slider_with_tab.blocks.slider_tab.settings.content_position.options__5.label"
},
{
"value": "center-right",
"label": "t:sections.slider_with_tab.blocks.slider_tab.settings.content_position.options__6.label"
},
{
"value": "bottom-left",
"label": "t:sections.slider_with_tab.blocks.slider_tab.settings.content_position.options__7.label"
},
{
"value": "bottom-center",
"label": "t:sections.slider_with_tab.blocks.slider_tab.settings.content_position.options__8.label"
},
{
"value": "bottom-right",
"label": "t:sections.slider_with_tab.blocks.slider_tab.settings.content_position.options__9.label"
}
]
},
{
"type": "select",
"id": "text_alignment",
"label": "t:sections.slider_with_tab.blocks.slider_tab.settings.text_alignment.label",
"default": "left",
"options": [
{
"value": "left",
"label": "t:sections.slider_with_tab.blocks.slider_tab.settings.text_alignment.options__1.label"
},
{
"value": "right",
"label": "t:sections.slider_with_tab.blocks.slider_tab.settings.text_alignment.options__2.label"
},
{
"value": "center",
"label": "t:sections.slider_with_tab.blocks.slider_tab.settings.text_alignment.options__3.label"
}
]
},
{
"type": "text",
"id": "tab",
"label": "t:sections.slider_with_tab.blocks.slider_tab.settings.tab.label",
"default": "Title"
},
{
"type": "text",
"id": "main_title",
"label": "t:sections.slider_with_tab.blocks.slider_tab.settings.main_title.label",
"default": "Main Heading"
},
{
"type": "text",
"id": "main_title_size",
"label": "t:sections.slider_with_tab.blocks.slider_tab.settings.main_title_size.label",
"default": "50"
},
{
"type": "text",
"id": "sub_title",
"label": "t:sections.slider_with_tab.blocks.slider_tab.settings.sub_title.label",
"default": "Sub Heading"
},
{
"type": "text",
"id": "sub_title_size",
"label": "t:sections.slider_with_tab.blocks.slider_tab.settings.sub_title_size.label",
"default": "30"
},
{
"type": "textarea",
"id": "text",
"label": "t:sections.slider_with_tab.blocks.slider_tab.settings.text.label",
"default": "Description"
},
{
"type": "text",
"id": "text_size",
"label": "t:sections.slider_with_tab.blocks.slider_tab.settings.text_size.label",
"default": "16"
},
{
"type": "text",
"id": "link_text",
"label": "t:sections.slider_with_tab.blocks.slider_tab.settings.link_text.label",
"default": "Shop Now"
},
{
"type": "url",
"id": "link",
"label": "t:sections.slider_with_tab.blocks.slider_tab.settings.link.label"
} 
]
}
],
"presets": [
{
"name": "Slider with tabs",
"category": "Slider",
   "blocks": [
        {
          "type": "slider_tab"
        }
 ] 
}
]
}
{% endschema %}