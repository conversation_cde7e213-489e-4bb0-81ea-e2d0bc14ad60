{%- assign dt_mega = false -%}
{%- assign primary = false -%}
{%- if link.levels == 1 -%}
{%- assign primary = true -%}
{%- endif -%}
{%- assign secondary = false -%}
{%- if link.levels == 2 -%}
{%- assign secondary = true -%}
{%- endif -%}

{% if section.settings.enable_new %}
{% assign new_tag =  section.settings.new_tags | split:"," | strip | downcase | handle %}
{% assign new_label = section.settings.new_txt %}
{% endif %}

{% if section.settings.enable_sale %}
{% assign sale_tag = section.settings.sale_tags | split:"," | strip | downcase | handle %}
{% assign sale_label = section.settings.sale_txt %}
{% endif %}

{% if section.settings.enable_hot %}
{% assign hot_tag = section.settings.hot_tags | split:"," | strip | downcase | handle %}
{% assign hot_label = section.settings.hot_txt %}
{% endif %}



{%- assign current_blockType = 'text-menu' -%}
{%- assign current_blockID = '' -%}
{%- assign current_columnSize = '' -%}
{%- assign current_tabColumnSize = '' -%}
{%- assign current_reverse = false -%}
{%- assign current_tabStyle = '' -%}
{%- assign current_custom_megamenu_width = '' -%}
{%- assign current_MenuContainerStyle = '' -%}
{%- assign current_megaPromo = '' -%}
{%- assign current_megaBanner1 = '' -%}
{%- assign current_shopBrands = '' -%}
{%- assign current_megaBanner2 = '' -%}
{%- assign current_megaProduct = '' -%}
{%- assign current_megaTabs = '' -%}
{%- assign current_megaImageGallery = '' -%}
{%- assign dt_mega = false -%}

{%- for block in section.blocks -%}
{% for i in (1..5) %}
{%- assign megamenu_title = '' -%}
{%- assign link_title = link.title | escape | strip | handleize -%}
{%- assign dt_menu_pick = i | prepend: 'mega_' -%}
{% if block.settings[dt_menu_pick] != blank %}
{% assign megamenu_title = block.settings[dt_menu_pick] | handleize %}

{%- if  link_title == megamenu_title -%}
{% assign current_columnSize = block.settings.mega_columns %}
{% assign current_tabColumnSize = block.settings.tab_columns %}
{% if  block.settings.row_reverse %}
{% assign current_reverse = true %}
{% endif %}

{% assign current_tabStyle =  block.settings.tab-menu -%}
{% assign current_custom_megamenu_width =  block.settings.custom_megamenu_width -%}
{% assign current_showContainerWidth = block.settings.enable_custom_width %}
{% if current_showContainerWidth and current_custom_megamenu_width == 100 %}
{%  assign current_MenuContainerStyle = current_custom_megamenu_width %}
{%  endif %}

{% assign current_blockType = block.type %}
  {% assign current_blockID = block.id %}

{%- assign dt_mega = true -%}

{%- capture current_megaPromo -%}
{% render 'dt-megaPromo', block: block %}
{%- endcapture -%}

{%- capture current_megaBanner1 -%}
{% render 'dt-megaBanner-1', block: block %}
{%- endcapture -%}

{%- capture current_shopBrands -%}
{% render 'dt-megaBrands', block: block, columnSize: current_columnSize %}
{%- endcapture -%}

{%- capture current_megaBanner2 -%}
{% render 'dt-megaBanner-2', block: block %}
{%- endcapture -%}
<!-- Product -->
{%- capture current_megaProduct -%}
{% render 'dt-megaProduct', block: block %}
{%- endcapture -%}
<!-- Product End -->

{%- capture current_megaTabs -%}
{% render 'dt-megaTabs', block: block, columnSize: current_columnSize, tabColumnSize: current_tabColumnSize %}
{%- endcapture -%}

{%- capture current_megaImageGallery -%}
{% render 'dt-megaImageGallery', block: block %}
{%- endcapture -%}

{%- endif -%}
{%- endif -%}
{%- endfor -%}
{%- endfor -%}
<li class="{{link.levels}} {% if current_blockType == "promo_image" %}promo_image{% elsif current_blockType == "product" %}megaProduct{% elsif current_blockType == "promo_banner" %}promo_banner{% elsif current_blockType == "tab" %}tabs-menu{% elsif current_blockType == "image_gallery" %}image-gallery-menu{% else %}text-menu{% endif %}
           top-level-link            
           {% if dt_mega %} has-mega-menu dt-sc-primary
           {% elsif secondary %} dt-sc-secondary
           {% else %} dt-sc-child
           {%endif%}
           {% if link.active %}active{% endif %} {% if primary or secondary or dt_mega %}menu-item-has-children{% endif %} menu-container-width-{{ current_MenuContainerStyle }} subMenuBlock">
   
<div class="megamenu_megamenu {% if dt_mega %}mega-menu{% endif %}">      
  <a href="{{ link.url }}" class="dt-sc-nav-link dropdown h4">
      {% assign mainTag = link.handle  %}
      {% if sale_tag contains mainTag %}
      <em class="sale tag">{{ sale_label }}</em>
      {% elsif new_tag contains mainTag %}
      <em class="new tag">{{ new_label }}</em>
      {% elsif hot_tag contains mainTag %}
      <em class="hot tag">{{ hot_label }}</em>
      {% endif %}
    <span data-hover="{{ link.title | escape }}">
      {{ link.title | escape }}
    </span>
    {% if primary or secondary or dt_mega %}<span class="dt-sc-caret"></span>
    {% endif %}
  </a>

  {% if  dt_mega %}
  <div class="sub-menu-block block-{{ current_blockID }}-type is-hidden mega-menu__content" style="display:none">    
    <div class="submenu_inner">
    <div class="dt-sc-dropdown-menu{% if dt_mega %} dt-sc--main-menu--mega{% endif %} {% if primary or secondary %} dt-sc_main-menu--has-links{% endif %}">
       {% unless current_blockType == "brands" or current_blockType == "image_gallery" %} 
    <ul class="sub-menu-lists page-width {% if dt_mega %} {% unless current_blockType == "tab" %} dt-sc-column  {{current_columnSize}} {% else %} {{ current_tabStyle -}}{% endunless %} {% if current_reverse %}row-reverse{% endif %} {% endif %} ">
        {% if current_blockType == "promo_banner"  %}
        {{ current_megaBanner1 }}
        {% endif %}

        {% unless current_blockType == "tab" or current_blockType == "image_gallery" %}
        {%- if link.links != blank -%}
        {%- for child_link in link.links  limit: 5  -%}
        <li class="{% if link.child_active %}active{% endif %}">
           <a data-hover="{{ child_link.title | escape }}" class="headding" href="{{ child_link.url }}" {% if link.child_current %}aria-current="page"{% endif %}>
            {{ child_link.title }}
            </a>
          {%- if child_link.links != blank -%}
          <ul>
            {%- for grandchild_link in child_link.links -%}
            <li class="{% if grandchild_link.current %}active{% endif %}">
               
              <a href="{{ grandchild_link.url }}" {% if grandchild_link.current %}aria-current="page"{% endif %}>
                <span>
                  {{ grandchild_link.title }}
                </span>
                {% assign mapTag = grandchild_link.handle  %}

                  {% if sale_tag contains mapTag %}
                  <em class="tag sale">{{ sale_label }}</em>
                  {% endif %}

                  {% if new_tag contains mapTag %}
                  <em class="tag new">{{ new_label }}</em>
                  {% endif %}

                  {% if hot_tag contains mapTag %}
                  <em class="tag hot">{{ hot_label }}</em>

                  {% endif %}
              </a>
            </li>
            {%- endfor -%}
          </ul>
          {%- endif -%}
        </li>
        {%- endfor -%}
        {% endif %}
        {% endunless %}

        {% if current_blockType == "promo_image" %}
        {{ current_megaPromo }}
        {% endif %}

        {% if current_blockType == "product" %}
        {{ current_megaProduct }}
        {% endif %}

        {% if current_blockType == "tab" %}
        {{ current_megaTabs }}
        {% endif %}

        {% if current_blockType == "promo_banner"  %}
        {{ current_megaBanner2 }}
        {% endif %}
      </ul>
       {% endunless %}
      {% if current_blockType == "brands"  %} {{current_shopBrands }} {% endif %}
      {% if current_blockType == "image_gallery" %} {{ current_megaImageGallery }} {% endif %}
    </div>
    </div>
  </div>
  {% else %}

  {%- if link.links != blank -%}
  <div class="sub-menu-block" style="display:none"> 
    <div class="submenu_inner">
    <div class="dt-sc-dropdown-menu{% if primary or secondary %} dt-sc_main-menu--has-links{% endif %}">
      <ul class="sub-menu-lists">
        {%- for child_link in link.links -%}
        <li class="{% if primary or secondary %}{%- if child_link.links != blank -%}menu-item-has-children {% endif %}{% endif %}{% if child_link.current %}active{% endif %}">
          <a href="{{ child_link.url }}" {% if child_link.current %}aria-current="page"{% endif %}>
            {{ child_link.title }}
          </a>
          {%- if child_link.links != blank -%}
          <div class="sub-menu-block is-hidden">
              <div class="submenu_inner">
            <div class="dt-sc_main-menu--has-links">
              <ul class="sub-menu-lists">
                {%- for grandchild_link in child_link.links -%}
                <li class="{% if grandchild_link.current %}active{% endif %}">
                  <a href="{{ grandchild_link.url }}" {% if grandchild_link.current %}aria-current="page"{% endif %}>
                    {{ grandchild_link.title }}
                  </a>                  
                </li>
                {%- endfor -%}
              </ul>
            </div>
              </div>
          </div>
          {%- endif -%}
        </li>
        {%- endfor -%}
      </ul>
    </div>
    </div>
  </div>
  {% endif %}
  {%- endif -%}
  </div>
</li>
