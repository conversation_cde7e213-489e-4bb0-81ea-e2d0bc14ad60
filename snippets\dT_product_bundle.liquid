{{ 'dt_bundle.css' | asset_url | stylesheet_tag }}
{% if product.metafields.custom.dT_bundle_handles != blank %}
{%- assign handle_metafields =  product.metafields.custom.dT_bundle_handles | split: ',' -%}
{%- assign handle_size =  handle_metafields | size -%}
{%- assign bundle_discount =  product.metafields.custom.dT_bundle_discount -%}
<div class="container bundle_discount={{ bundle_discount }}  handle_metafields- {{ handle_metafields }}" id="dT_bundleSelector">
  <div class="row">
  <div class="dT_bundleSelector">

    {% form 'product', product, id: "bundleAddToCartFrmId", class: "bundleAddToCartFrmCls" %}
    <div class="dt-sc-product_bundle-items">

      <div class="section-title"> <h3>{{ 'upsell.general.heading' | t}}</h3> </div>

      <div class="frequently-buy-togeather-products">
        <div class="products-grouped-info">

          <div class="dt-sc-thumb-image">
            <div>
              <div class="" data-bundle-product-id="{{ product.id }}" data-bundle-product-handle="{{ product.handle }}">
                <a href="{{ product.url | within: collection }}" class="item-image">                
                    {{ product.featured_image.src | image_url: width: 300 | image_tag }}                          
                </a>
              </div>

              <div class="main-product" id="dT_bundle-product-{{ product.id }}" data-bundle-product-id="{{ product.id }}" data-bundle-product-handle="{{ product.handle  }}">
                {% render 'dT_bundle-product-list', product:product %}
              </div>
            </div>

            {% for handle in handle_metafields limit:10 %}
            {% assign a = handle | strip %}
            {%- assign pro = all_products[a] -%}
            <div>
              <div class="" data-bundle-product-id="{{ pro.id  }}" data-bundle-product-handle="{{ pro.id  }}">
                <a href="{{ pro.url | within: collection }}" class="item-image">                 
                    {{ pro.featured_image.src | image_url: width: 300 | image_tag }}                          
                </a>
              </div>

              <div class="group-product" id="dT_bundle-product-{{ pro.id }}" data-bundle-product-id="{{ pro.id }}" data-bundle-product-handle="{{ pro.handle  }}">
                {% render 'dT_bundle-product-list', product:pro %}
              </div> 
            </div>  
            {% endfor %}
          </div>
          
          <div class="bundle-product-additional-offer">
          
            <div class="bundle-product-cart-total">
              <span>{{ 'upsell.general.total' | t}}</span>
              <span class="dT_totalBundleSalePrice">$4,095.00 USD</span> 
              <span class="dT_totalBundleOriginalPrice">$4,550.00 USD</span>
            </div>

            <div class="bundle-product-add-to-cart">
              <button type="submit" class="button btn-product-add-to-cart">{{ 'upsell.general.add_all_to_cart' | t}}</button>
            </div>
            <div class="bundle-product-offer-note">
              {{ 'upsell.general.get_discount_label1' | t}} {{ bundle_discount }}% {{ 'upsell.general.get_discount_label2' | t}}
            </div>
          
          </div>
          
        </div>
      </div>
    </div>
    {% endform %}
  </div>
  </div>
</div>

<script type="text/javascript">

const DT_DISCOUNT = '{{ bundle_discount }}';
const BUNDLE_MAIN_PRODUCT_ID = "{{ product.id }}";
var bundleProductIds = ["{{ product.id }}"];
{% for handle in handle_metafields limit:10 %}
{% assign a = handle | strip %}
{%- assign pro = all_products[a] -%}
bundleProductIds.push( "{{ pro.id }}" );
{% endfor %}
</script>

{% endif %}