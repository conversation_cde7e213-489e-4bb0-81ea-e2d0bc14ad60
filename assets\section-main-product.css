.main-product-template .facets-vertical {
  display: flex;
  justify-content: space-between;
}

.main-product-template .breadcrumb-main-template {
    display: flex;
    column-gap: 5px;
}
.main-product-template .breadcrumb-main-template a, .main-product-template .breadcrumb-main-template span {
display: inline-block;
    margin-top: 0;
    font-size: 1.8rem;
    font-weight: 400;
    padding: 0 0.4rem;
    color: var(--color-icon);
}
.main-product-template .breadcrumb-main-template a:hover {color:rgb(var(--color-base-outline-button-labels))}
.main-product-template .breadcrumb-main-template svg {
    width: 1rem;
    height: 1rem;
    fill: rgba(var(--color-base-accent-1),0.6);
}
.main-product-template .facets-vertical.sidebar-right {
  flex-direction: row-reverse;
}
.main-product-template .breadcrumb-main-template {
    margin-top: 0;
}
.main-product-template modal-opener {
  overflow: hidden;
}
.main-product_info {
  width: calc(100% - var(--sidebar-width) - calc(var(--grid-desktop-horizontal-spacing) * 2));
}
.product {
  margin: 0;
}

.product.grid {
  gap: 0;
}

.product--no-media {
  max-width: 57rem;
  margin: 0 auto;
}

.product__media-wrapper {
  padding-left: 0;
  z-index: 1;
}

.product__info-wrapper {
  padding-left: 0;
  padding-bottom: 0;
}

@media screen and (min-width: 990px) {
  .product__info-container--sticky {
    display: block;
    position: sticky;
    top: 3rem;
    z-index: 2;
    margin-top:0;
  }

  .product--thumbnail .thumbnail-list {
    padding-right: var(--media-shadow-horizontal-offset);
  }

  .product__info-wrapper {
    padding-left: 5rem;
  }

  .product__info-wrapper--extra-padding {
    padding-left: 6rem;
  }

  .product__media-container .slider-buttons {
    display: none;
  }
}
@media screen and (max-width: 1199px) {
  /*   .product--large:not(.product--no-media) .product__media-wrapper {
    max-width: 100%;
    width: calc(100% - var(--grid-desktop-horizontal-spacing) / 2);
  }
   .product--large:not(.product--no-media) .product__info-wrapper {
     padding-left: 4rem;
    max-width: 100%;
    width: calc(100% - var(--grid-desktop-horizontal-spacing) / 2);
  }
 .grid--2-col-tablet .grid__item{width:100%;} */
  .product--thumbnail .product__info-wrapper.grid__item,
  .product--stacked .product__info-wrapper.grid__item,
  .product--thumbnail_slider .product__info-wrapper.grid__item{
    padding-left: 0;
    padding-top: 5rem;
  }
  .product--thumbnail .slider-buttons {
    display: none;
  }
  .product--thumbnail_slider .product__info-wrapper.grid__item {
    padding-left: 0;
  }
}
@media screen and (max-width: 576px) {
  .product--thumbnail .product__info-wrapper.grid__item,
  .product--stacked .product__info-wrapper.grid__item {
    padding-left: 0;
    padding-top: 3rem;
  }
}
/* Dynamic checkout */

.shopify-payment-button__button {
  font-family: inherit;
  min-height: 4.6rem;
}

.shopify-payment-button__button [role="button"].focused,
.no-js .shopify-payment-button__button [role="button"]:focus {
  outline: 0.2rem solid rgba(var(--color-foreground), 0.5) !important;
  outline-offset: 0.3rem;
  box-shadow: 0 0 0 0.1rem rgba(var(--color-button), var(--alpha-button-border)),
    0 0 0 0.3rem rgb(var(--color-background)),
    0 0 0.5rem 0.4rem rgba(var(--color-foreground), 0.3) !important;
}

.shopify-payment-button__button [role="button"]:focus:not(:focus-visible) {
  outline: 0;
  box-shadow: none !important;
}

.shopify-payment-button__button [role="button"]:focus-visible {
  outline: 0.2rem solid rgba(var(--color-foreground), 0.5) !important;
  box-shadow: 0 0 0 0.1rem rgba(var(--color-button), var(--alpha-button-border)),
    0 0 0 0.3rem rgb(var(--color-background)),
    0 0 0.5rem 0.4rem rgba(var(--color-foreground), 0.3) !important;
}

.shopify-payment-button__button--unbranded {
  background-color: rgba(var(--color-button), var(--alpha-button-background));
  color: rgb(var(--color-button-text));
  font-size: 1.4rem;
  line-height: calc(1 + 0.2 / var(--font-body-scale));
  letter-spacing: 0.07rem;
}

.shopify-payment-button__button--unbranded::selection {
  background-color: rgba(var(--color-button-text), 0.3);
}

.shopify-payment-button__button--unbranded:hover,
.shopify-payment-button__button--unbranded:hover:not([disabled]) {
  background-color: rgba(var(--color-base-outline-button-labels));
  color: var(--gradient-base-accent-1);
}

.shopify-payment-button__more-options {
  margin: 1.6rem 0 1rem;
  font-size: 1.2rem;
  line-height: calc(1 + 0.5 / var(--font-body-scale));
  letter-spacing: 0.05rem;
  text-decoration: underline;
  text-underline-offset: 0.3rem;
}

.shopify-payment-button__button + .shopify-payment-button__button--hidden {
  display: none;
}

/* Product form */

.product-form {
  display: block;
}

.product-form__error-message-wrapper:not([hidden]) {
  display: flex;
  align-items: flex-start;
  font-size: 1.4rem;
  margin-bottom: 1.5rem;
}

.product-form__error-message-wrapper svg {
  flex-shrink: 0;
  width: 1.2rem;
  height: 1.2rem;
  margin-right: 0.7rem;
  margin-top: 0.5rem;
}

/* Form Elements */
.product-form__input {
  /*   flex: 0 0 100%; */
  padding: 0;
  /*   margin: 0 0 1.2rem 0; */
  /*   max-width: 37rem; */
  min-width: fit-content;
  border: none;
}

variant-radios,
variant-selects {
  display: block;
}

.product-form__input--dropdown {
  margin-bottom: 1.6rem;
}

.product-form__input .form__label {
  padding-left: 0;
}
.row.straight {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 30px;
}
.suport h6 {
    margin: 0;
    display: inline;
}
p.descrip {
    max-width: 350px;
}
.row.straight .image {
    width: 100%;
}
.row.straight img {
    height: auto;
    width: 100%;
    max-width: 100%;
    border: var(--media-border-width) solid rgba(var(--color-foreground),var(--media-border-opacity));
    border-radius: var(--media-radius);
    box-shadow: var(--media-shadow-horizontal-offset) var(--media-shadow-vertical-offset) var(--media-shadow-blur-radius) rgba(var(--color-shadow),var(--media-shadow-opacity));
    margin-bottom: var(--media-shadow-vertical-offset);
}
.product-description1 h6,
.product-heading + h6,
.product-description2 h6{
    margin: 5px 0;
}
fieldset.product-form__input .form__label,
.product-attributes .product-label,
.advance-product-style .advanced-title {
  /*   margin-bottom: 0.5rem;
  font-size: 2.2rem;
  color: var(--gradient-base-accent-1);
  font-weight: 700;
  margin:0; */
  margin: 0;
  min-width: 140px;
  display: inline-block;
  font-size: 100%;
}
.product-attributes a {
  color: rgba(var(--color-foreground), 1);
  transition: all var(--duration-default) linear;
}
.product-attributes a:hover {
  color: rgba(var(--color-base-background-2));
}
variant-selects .product-form__input .form__label {
  font-size: 2.2rem;
  color: var(--gradient-base-accent-1);
  font-weight: 700;
}
.product-form__input input[type="radio"] {
  clip: rect(0, 0, 0, 0);
  overflow: hidden;
  position: absolute;
  height: 1px;
  width: 1px;
}

.swatch-group label.swatch-variant-color {
  border: var(--variant-pills-border-width) solid
    rgba(var(--color-foreground), var(--variant-pills-border-opacity));
  color: rgba(var(--color-foreground));
  border-radius: var(--variant-pills-radius);
  color: rgb(var(--color-foreground));
  display: inline-block;
  margin: 0;
  padding: 1rem 2rem;
  font-size: 1.4rem;
  letter-spacing: 0.1rem;
  line-height: 1;
  text-align: center;
  transition: all var(--duration-default) linear;
  cursor: pointer;
  position: relative;
}

.product-form__input input[type="radio"] + label:before {
  content: "";
  position: absolute;
  top: calc(var(--variant-pills-border-width) * -1);
  right: calc(var(--variant-pills-border-width) * -1);
  bottom: calc(var(--variant-pills-border-width) * -1);
  left: calc(var(--variant-pills-border-width) * -1);
  z-index: -1;
  border-radius: var(--variant-pills-radius);
  box-shadow: var(--variant-pills-shadow-horizontal-offset)
    var(--variant-pills-shadow-vertical-offset)
    var(--variant-pills-shadow-blur-radius)
    rgba(var(--color-shadow), var(--variant-pills-shadow-opacity));
}

/* .product-form__input input[type="radio"] + label:hover {
  border-color: rgb(var(--color-foreground));
} */

.product-form__input
  input[type="radio"]:checked
  + label:not(.swatch-variant-color),
.product-form__input
  input[type="radio"]
  + label:not(.swatch-variant-color):hover {
  background-color: rgb(var(--color-foreground));
  color: rgb(var(--color-background));
}
.prev-next-product-navigation {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  justify-content: flex-end;
  flex-direction: row;
}
.prev-next-product-navigation .product-main {
  margin-left: 10px;
  margin-right: 10px;
  display: flex;
  opacity: 0.5;
}
.prev-next-product-navigation a {
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--color-link);
  font-weight: 600;
}

.prev-next-product-navigation a:hover {
  color: var(--gradient-base-accent-1);
}

.prev-next-product-navigation a img {
  max-width: 35px;
  height: 35px;
  opacity: 0;
  margin-left: 0;
  border: 1px solid var(--gradient-base-accent-3);
  border-radius: 50%;
  object-fit: cover;
  top: -35px;
  position: absolute;
  display: flex;
  transition: all 0.3s linear;
}
.prev-next-product-navigation a:hover img {
  opacity: 1;
}
.prev-next-product-navigation a:hover img {
  opacity: 1;
  -webkit-transition: opacity 1s ease-in-out;
  -moz-transition: opacity 1s ease-in-out;
  -o-transition: opacity 1s ease-in-out;
  transition: opacity 1s ease-in-out;
}
@media screen and (forced-colors: active) {
  .product-form__input input[type="radio"]:checked + label {
    text-decoration: underline;
  }
}

.product-form__input input[type="radio"]:checked + label::selection {
  background-color: rgba(var(--color-background), 0.3);
}

.product-form__input input[type="radio"]:disabled + label {
  border-color: rgba(var(--color-foreground), 0.1);
  color: rgba(var(--color-foreground), 0.4);
  text-decoration: line-through;
}
.product-form__input input[type="radio"]:focus-visible + label {
  box-shadow: 0 0 0 0.3rem rgb(var(--color-background)),
    0 0 0 0.5rem rgba(var(--color-foreground), 0.55);
}

/* Fallback */
.product-form__input input[type="radio"].focused + label,
.no-js .shopify-payment-button__button [role="button"]:focus + label {
  box-shadow: 0 0 0 0.3rem rgb(var(--color-background)),
    0 0 0 0.5rem rgba(var(--color-foreground), 0.55);
}

/* No outline when focus-visible is available in the browser */
.no-js
  .product-form__input
  input[type="radio"]:focus:not(:focus-visible)
  + label {
  box-shadow: none;
}

.product-form__input .select {
  max-width: 25rem;
}

.product-form__submit {
  margin-bottom: 1rem;
}

.no-js .product-form__submit.button--secondary {
  --color-button: var(--color-base-accent-1);
  --color-button-text: var(--color-base-solid-button-labels);
  --alpha-button-background: 1;
}

.product-form__submit[aria-disabled="true"]
  + .shopify-payment-button
  .shopify-payment-button__button[disabled],
.product-form__submit[disabled]
  + .shopify-payment-button
  .shopify-payment-button__button[disabled] {
  cursor: not-allowed;
  opacity: 0.5;
}

@media screen and (forced-colors: active) {
  .product-form__submit[aria-disabled="true"] {
    color: Window;
  }
}

/* Overrides */
.shopify-payment-button__more-options {
  color: rgb(var(--color-foreground));
}

.shopify-payment-button__button {
  font-size: 1.5rem;
  letter-spacing: 0.1rem;
  font-family: var(--font-heading-family);
  font-weight: 500;
}

/* Product info */

.product__info-container > * + *:not(.sub-total){
  margin: 2rem 0;
}

.product__info-container .product__description {
  margin: 2.5rem 0;
}
.product__info-container .product-form {
  margin: 0 0 2.5rem;
}

.product__text {
  margin-bottom: 0;
}

a.product__text {
  display: block;
  text-decoration: none;
  color: rgba(var(--color-foreground), 0.75);
}

.product__text.caption-with-letter-spacing {
  text-transform: uppercase;
}

.product__title {
  word-break: break-word;
  margin-bottom: 1.5rem;
  margin-top:0;
}

.product__title > * {
  margin: 0;
}

.product__title > a {
  display: none;
}

.product__title + .product__text.caption-with-letter-spacing {
  margin-top: -1.5rem;
}

.product__text.caption-with-letter-spacing + .product__title {
  margin-top: 0;
}

.product__accordion .accordion__content {
  padding: 0 1rem;
}

.product .price .badge {
  margin-bottom: 0;
}

.product .price__container {
  margin-bottom: 0;
}

.product .price dl {
  margin-top: 0;
  margin-bottom: 0;
}

.product .price--sold-out .price__badge-sold-out {
  border-radius: 3px;
  /*   background: var(--gradient-base-background-2); */
  border: none;
  padding: 8px 9px;
  top: 0px;
  left: 12px;
  font-size: 1.2rem;
  font-family: var(--font-additional-family);
  font-weight: 600;
  transition: all 0.3s linear;
  position: relative;
}
.product .price .badge {
  border-radius: 3px;
  border: none;
  padding: 4px 8px;
  position: relative;
  top: 0;
  left: 12px;
  font-size: 1.2rem;
  font-family: var(--font-body-family);
  font-weight: 400;
  transition: all 0.3s linear;
  vertical-align: bottom;
  line-height:normal;
  background: var(--gradient-base-accent-2);
  color: var(--gradient-base-background-1);
}
}
.product .price--sold-out .badge {
  display: none;
}

@media screen and (min-width: 750px) {
  /*   .product__info-container {
    max-width: 60rem;
  } */

  .product__info-container .price--on-sale .price-item--regular {
    font-size: 1.6rem;
  }

  .product__info-container > *:first-child {
    margin-top: 0;
  }
}

.product__description-title {
  font-weight: 600;
}

.product--no-media .product__title,
.product--no-media .product__text,
.product--no-media noscript .product-form__input,
.product--no-media .product__tax,
.product--no-media shopify-payment-terms {
  text-align: center;
}

.product--no-media .product__media-wrapper {
  padding: 0;
}

.product__tax {
  margin-top: -1.4rem;
}

.product--no-media noscript .product-form__input,
.product--no-media .share-button {
  max-width: 100%;
}

.product--no-media fieldset.product-form__input,
.product--no-media .product-form__quantity,
.product--no-media .product-form__input--dropdown,
.product--no-media .share-button,
.product--no-media .product__view-details,
.product--no-media .product__pickup-availabilities,
.product--no-media .product-form {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.product--no-media .product-form {
  flex-direction: column;
}

.product--no-media .product-form > .form {
  max-width: 30rem;
  width: 100%;
}

.product--no-media .product-form__quantity,
.product--no-media .product-form__input--dropdown {
  flex-direction: column;
  max-width: 100%;
}

/* .product-form__quantity .form__label {
  margin-bottom: 1.6rem;
  font-size: 2.2rem;
  font-weight: 700;
}
 */
.product-form__quantity-top .form__label {
  margin-bottom: 1.2rem;
}

.product--no-media fieldset.product-form__input {
  flex-wrap: wrap;
  margin: 0 auto 1.2rem auto;
}

.product-form__buttons {
  max-width: 44rem;
}

.product--no-media .product__info-container > modal-opener {
  display: block;
  text-align: center;
}

.product--no-media .product-popup-modal__button {
  padding-right: 0;
}

.product--no-media .price {
  text-align: center;
}

.product--no-media .product__info-wrapper {
  padding-left: 0;
}

/* Product media */
.product__media-list video {
  border-radius: calc(var(--media-radius) - var(--media-border-width));
}

@media screen and (max-width: 749px) {
  .product__media-list {
    margin-left: -2.5rem;
    padding-bottom: 0rem;
    margin-bottom: 3rem;
    width: calc(100% + 4rem);
  }

  /*   .product__media-wrapper slider-component:not(.thumbnail-slider--no-slide) {
    margin-left: -1.5rem;
    margin-right: -1.5rem;
  } */

  .slider.product__media-list::-webkit-scrollbar {
    height: 0.2rem;
    width: 0.2rem;
  }

  .product__media-list::-webkit-scrollbar-thumb {
    background-color: rgb(var(--color-foreground));
  }

  .product__media-list::-webkit-scrollbar-track {
    background-color: rgba(var(--color-foreground), 0.2);
  }

  .product__media-list .product__media-item {
    width: 100%;max-width:100%;
  }
}

@media screen and (min-width: 750px) {
  .product--thumbnail .product__media-list,
  .product--thumbnail_slider .product__media-list {
    padding-bottom: var(--media-shadow-vertical-offset);
  }

  .product__media-list {
    padding-right: var(--media-shadow-horizontal-offset);
  }

  .product--thumbnail .product__media-item:not(.is-active),
  .product--thumbnail_slider .product__media-item:not(.is-active) {
    display: none;
  }

  .product-media-modal__content
    > .product__media-item--variant.product__media-item--variant {
    display: none;
  }

  .product-media-modal__content > .product__media-item--variant:first-child {
    display: block;
  }
}

.product__media-item.product__media-item--variant {
  display: none;
}

.product__media-item--variant:first-child {
  display: block;
}

@media screen and (max-width: 749px) {
  .product__media-item--variant:first-child {
    padding-right: 1.5rem;
  }
}

@media screen and (min-width: 750px) and (max-width: 989px) {
  .product__media-list .product__media-item:first-child {
    padding-left: 0;
  }

  .product--thumbnail_slider .product__media-list {
    margin-left: 0;
  }

  .product__media-list .product__media-item {
    width: 100%;
  }
}

.product__media-icon .icon {
  width: 1.2rem;
  height: 1.4rem;
}

.product__media-icon,
.thumbnail__badge {
  background-color: rgb(var(--color-background));
  border-radius: 50%;
  border: 0.1rem solid rgba(var(--color-foreground), 0.1);
  color: rgb(var(--color-foreground));
  display: flex;
  align-items: center;
  justify-content: center;
  height: 3rem;
  width: 3rem;
  position: absolute;
  left: calc(0.4rem + var(--media-border-width));
  top: calc(0.4rem + var(--media-border-width));
  z-index: 1;
  transition: color var(--duration-short) ease,
    opacity var(--duration-short) ease;
}

.product__media-video .product__media-icon {
  opacity: 1;
}

.product__modal-opener--image .product__media-toggle:hover {
  cursor: zoom-in;
}

.product__modal-opener:hover .product__media-icon {
  border: 0.1rem solid rgba(var(--color-foreground), 0.1);
}

@media screen and (min-width: 750px) {
  .grid__item.product__media-item--full {
    width: 100%;
  }
}

@media screen and (min-width: 750px) {
  .product--stacked .product__media-item {
    max-width: calc(50% - var(--grid-desktop-horizontal-spacing) / 2);
  }

  .product__media-list .product__media-item:first-child,
  .product__media-list .product__media-item--full {
    width: 100%;
    max-width: 100%;
  }

  .product__modal-opener .product__media-icon {
    opacity: 0;
  }

  .product__modal-opener:hover .product__media-icon,
  .product__modal-opener:focus .product__media-icon {
    opacity: 1;
  }
}
.product--stacked .product__media-list.one-column .product__media-item {
  width: 100%;
  max-width: 100%;
}

.product__media-item > * {
  display: block;
  position: relative;
}

.product__media-toggle {
  display: flex;
  border: none;
  background-color: transparent;
  color: currentColor;
  padding: 0;
}

.product__media-toggle::after {
  content: "";
  cursor: pointer;
  display: block;
  margin: 0;
  padding: 0;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  height: 100%;
  width: 100%;
}

.product__media-toggle:focus-visible {
  outline: 0;
  box-shadow: none;
}

.product__media-toggle.focused {
  outline: 0;
  box-shadow: none;
}

.product__media-toggle:focus-visible:after {
  box-shadow: 0 0 0 0.3rem rgb(var(--color-background)),
    0 0 0rem 0.5rem rgba(var(--color-foreground), 0.5);
  border-radius: var(--media-radius) - var(--media-border-width);
}

.product__media-toggle.focused:after {
  box-shadow: 0 0 0 0.3rem rgb(var(--color-background)),
    0 0 0rem 0.5rem rgba(var(--color-foreground), 0.5);
  border-radius: var(--media-radius);
}

.product__media-toggle:focus-visible:after {
  border-radius: var(--media-radius);
}

.product-media-modal {
  background-color: rgb(var(--color-background));
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  visibility: hidden;
  opacity: 0;
  z-index: -1;
}

.product-media-modal[open] {
  visibility: visible;
  opacity: 1;
  z-index: 101;
}

.product-media-modal__dialog {
  display: flex;
  align-items: center;
  height: 100vh;
}

.product-media-modal__content {
  max-height: 100vh;
  width: 100%;
  overflow: auto;
}

.product-media-modal__content > *:not(.active),
.product__media-list .deferred-media {
  display: none;
}

@media screen and (min-width: 750px) {
  .product-media-modal__content {
    padding-bottom: 2rem;
  }

  .product-media-modal__content > *:not(.active) {
    display: block;
  }

  .product__modal-opener:not(.product__modal-opener--image) {
    display: none;
  }

  .product__media-list .deferred-media {
    display: block;
  }
}

@media screen and (max-width: 749px) {
  .product--thumbnail
    .is-active
    > .product__modal-opener:not(.product__modal-opener--image),
  .product--thumbnail_slider
    .is-active
    > .product__modal-opener:not(.product__modal-opener--image) {
    display: none;
  }

  .product--thumbnail .is-active .deferred-media,
  .product--thumbnail_slider .is-active .deferred-media {
    display: block;
    width: 100%;
  }
}

.product-media-modal__content > * {
  display: block;
  height: auto;
  margin: auto;
}

.product-media-modal__content .media {
  background: none;
}

.product-media-modal__model {
  width: 100%;
}

.product-media-modal__toggle {
  background-color: rgb(var(--color-background));
  border: 0.1rem solid rgba(var(--color-foreground), 0.1);
  border-radius: 50%;
  color: rgba(var(--color-foreground), 0.55);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  right: 2rem;
  padding: 1.2rem;
  position: fixed;
  z-index: 2;
  top: 2rem;
  width: 4rem;
}

.product-media-modal__content .deferred-media {
  width: 100%;
}

@media screen and (min-width: 750px) {
  .product-media-modal__content {
    padding: 2rem 11rem;
  }

  .product-media-modal__content > * {
    width: 100%;
  }

  .product-media-modal__content > * + * {
    margin-top: 2rem;
  }

  .product-media-modal__toggle {
    right: 5rem;
    top: 2.2rem;
  }
}

@media screen and (min-width: 990px) {
  .product-media-modal__content {
    padding: 2rem 11rem;
  }

  .product-media-modal__content > * + * {
    margin-top: 1.5rem;
  }

  .product-media-modal__content {
    padding-bottom: 1.5rem;
  }

  .product-media-modal__toggle {
    right: 5rem;
  }
}

.product-media-modal__toggle:hover {
  color: rgba(var(--color-foreground), 0.75);
}

.product-media-modal__toggle .icon {
  height: auto;
  margin: 0;
  width: 2.2rem;
}

/* Product popup */

.product-popup-modal {
  box-sizing: border-box;
  opacity: 0;
  position: fixed;
  visibility: hidden;
  z-index: -1;
  margin: 0 auto;
  top: 0;
  left: 0;
  overflow: auto;
  width: 100%;
  background: rgba(var(--color-foreground), 0.2);
  height: 100%;
}

.product-popup-modal[open] {
  opacity: 1;
  visibility: visible;
  z-index: 101;
}

.product-popup-modal__content {
  border-radius: var(--popup-corner-radius);
  background-color: rgb(var(--color-background));
  overflow: auto;
  height: auto;
  margin: auto;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  /*   margin-top: 5rem; */
  width: 90%;
  position: absolute;

  padding: 2rem;
  border-color: rgba(var(--color-foreground), var(--popup-border-opacity));
  border-style: solid;
  border-width: var(--popup-border-width);
  box-shadow: var(--popup-shadow-horizontal-offset)
    var(--popup-shadow-vertical-offset) var(--popup-shadow-blur-radius)
    rgba(var(--color-shadow), var(--popup-shadow-opacity));
  overflow: hidden;
}

.product-popup-modal__content.focused {
  box-shadow: 0 0 0 0.3rem rgb(var(--color-background)),
    0 0 0.5rem 0.4rem rgba(var(--color-foreground), 0.3),
    var(--popup-shadow-horizontal-offset) var(--popup-shadow-vertical-offset)
      var(--popup-shadow-blur-radius)
      rgba(var(--color-shadow), var(--popup-shadow-opacity));
}

.product-popup-modal__content:focus-visible {
  box-shadow: 0 0 0 0.3rem rgb(var(--color-background)),
    0 0 0.5rem 0.4rem rgba(var(--color-foreground), 0.3),
    var(--popup-shadow-horizontal-offset) var(--popup-shadow-vertical-offset)
      var(--popup-shadow-blur-radius)
      rgba(var(--color-shadow), var(--popup-shadow-opacity));
}
@media screen  (max-width: 1199px) and (min-width: 991px)  {
.product-popup-modal__content {
   width: 80%;
}
}
@media screen and (min-width: 1200px) {
  .product-popup-modal__content {
    padding-right: 1.5rem;
    width: 50%;
    padding: 5rem;
  }

  .product-media-modal__dialog .global-media-settings--no-shadow {
    overflow: visible !important;
  }
}

.product-popup-modal__content img {
  max-width: 100%;
}

@media screen and (max-width: 749px) {
  .product-popup-modal__content table {
    display: block;
    max-width: fit-content;
    overflow-x: auto;
    white-space: nowrap;
    margin: 0;
  }

  .product-media-modal__dialog .global-media-settings,
  .product-media-modal__dialog .global-media-settings video,
  .product-media-modal__dialog .global-media-settings model-viewer,
  .product-media-modal__dialog .global-media-settings iframe,
  .product-media-modal__dialog .global-media-settings img {
    border: none;
    border-radius: 0;
  }
}

.product-popup-modal__opener {
  display: inline-block;
}

.product-popup-modal__button {
  font-size: 1.6rem;
  padding-right: 1.3rem;
  padding-left: 0;
  height: 5.4rem;
  text-underline-offset: 0.3rem;
  text-decoration-thickness: 0.1rem;
  transition: text-decoration-thickness var(--duration-short) ease;
  font-weight: 500;
}
.product__info-container .varient-model-wrapper svg.popup-svg {
  width: 1.6rem;
  height: 2.7rem;
  /*     margin-top: 15px; */
  margin-right: 10px;
}
.product-popup-modal__button:hover {
  text-decoration-thickness: 0.2rem;
}

/* .product-popup-modal__content-info {
  padding-right: 4.4rem;
} */
.product-popup-modal__content-info h1 {
  margin-bottom: 2rem;
}
.product-popup-modal__content-info > * {
  height: auto;
  margin: 0 auto;
  max-width: 100%;
  width: 100%;
}

@media screen and (max-width: 749px) {
  .product-popup-modal__content-info > * {
    max-height: 100%;
  }
  .product-popup-modal__content-info h1{font-size:20px;}
}

.product-popup-modal__toggle {
  background-color:transparent;
  border: none;
  border-radius: var(--buttons-radius);
  color: rgba(var(--color-foreground));
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: absolute;
  top: 2rem;
  right: 2rem;
  padding: 0;
  z-index: 2;
  margin: 0 0 0 auto;
  transition:all 0.3s linear;
}

.product-popup-modal__toggle:hover {
  color:rgba(var(--color-base-outline-button-labels));

}

.product-popup-modal__toggle .icon {
  height: auto;
  margin: 0;
  width: 2.2rem;
}

.product__media-list .media > * {
  overflow: hidden;
}

.thumbnail-list {
  flex-wrap: wrap;
  grid-gap: 1rem;
}

@media screen and (min-width: 750px) {
  .product--stacked .thumbnail-list {
    display: none;
  }

  .thumbnail-list {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
  }

  .product--thumbnail .thumbnail-list{
    display:unset;
  } 
   .product--thumbnail .thumbnail-slider .thumbnail-list.slider {
    display: flex;
    padding: 0.5rem;
    flex: 1;
    scroll-padding-left: 0.5rem;
}
  .product--thumbnail .thumbnail-slider {
    display: flex;
    align-items: center;
} 
 .product--thumbnail .slider.slider--mobile {
    position: relative;
    flex-wrap: inherit;
    overflow-x: auto;
    scroll-snap-type: x mandatory;
    scroll-behavior: smooth;
    scroll-padding-left: 0rem;
    -webkit-overflow-scrolling: touch;
    margin-bottom: 0rem;
} 
  .product--thumbnail .thumbnail-list__item.slider__slide {
    width: calc(33% - 0.6rem);
}
  .product--thumbnail  .product__media-wrapper .slider-mobile-gutter .slider-button{display:unset;}
}

.thumbnail-list_item--variant:not(:first-child) {
  display: none;
}

@media screen and (min-width: 990px) {
  .thumbnail-list {
    grid-template-columns: repeat(4, 1fr);
  }

  .product--medium .thumbnail-list {
    grid-template-columns: repeat(5, 1fr);
  }

  .product--large .thumbnail-list {
    grid-template-columns: repeat(6, 1fr);
  }
}

@media screen and (max-width: 749px) {
  .product__media-item {
    display: flex;
    align-items: center;
  }

  .product__modal-opener {
    width: 100%;
  }

  .thumbnail-slider {
    display: flex;
    align-items: center;
  }

  .thumbnail-slider .thumbnail-list.slider {
    display: flex;
    padding: 0.5rem;
    flex: 1;
    scroll-padding-left: 0.5rem;
  }

  .thumbnail-list__item.slider__slide {
    width: calc(33% - 0.6rem);
  }
}

@media screen and (min-width: 750px) {
  .product--thumbnail_slider .thumbnail-slider {
    display: flex;
    align-items: center;
  }

  .thumbnail-slider .thumbnail-list.slider--tablet-up {
    display: flex;
    padding: 0.5rem;
    flex: 1;
    /* scroll-padding-left: 0.5rem; */
  }

  .product__media-wrapper .slider-mobile-gutter .slider-button {
    display: none;
  }

  .thumbnail-list.slider--tablet-up .thumbnail-list__item.slider__slide {
    width: calc(25% - 0.8rem);
  }

  .product--thumbnail_slider .slider-mobile-gutter .slider-button {
    display: none;
  }
}

@media screen and (min-width: 900px) {
  .product--small
    .thumbnail-list.slider--tablet-up
    .thumbnail-list__item.slider__slide {
    width: calc(25% - 0.8rem);
  }

  .thumbnail-list.slider--tablet-up .thumbnail-list__item.slider__slide {
    width: calc(20% - 0.8rem);
  }
}

.thumbnail {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  height: 100%;
  width: 100%;
  padding: 0;
  color: rgb(var(--color-base-text));
  cursor: pointer;
  background-color: transparent;
  transition: all 0.3s linear;
  opacity:1;
}

.thumbnail:hover {
  opacity: 0.7;
}

.thumbnail.global-media-settings img {
   border-radius: 0;
    padding: 0;
    object-fit: cover;
    transition: all 0.3s linear;
    clip-path: inset(0);
      object-position: top;
}
.thumbnail[aria-current] img {clip-path: inset(0px);}
.thumbnail[aria-current] {
  /* box-shadow: 0 0 0rem 0.1rem rgb(var(--color-foreground)); */
  border-color: rgb(var(--color-foreground));
  z-index: 1;
  opacity:1;
  border-width:0px;
  box-shadow:none;
}

.thumbnail[aria-current]:focus-visible {
  /* box-shadow: 0 0 0 0.3rem rgb(var(--color-background)),
    0 0 0rem 0.5rem rgba(var(--color-foreground), 0.5); */
  box-shadow:none;
}

.thumbnail[aria-current]:focus,
.thumbnail.focused {
  outline: 0;
  /* box-shadow: 0 0 0 0.3rem rgb(var(--color-background)),
    0 0 0rem 0.5rem rgba(var(--color-foreground), 0.5); */
  box-shadow:none;
}


.thumbnail[aria-current]:focus:not(:focus-visible) {
  outline: 0;
  /* box-shadow: 0 0 0 0.1rem rgb(var(--color-foreground)); */
  box-shadow:none;
}


.thumbnail img {
  pointer-events: none;
}

/* .thumbnail--narrow img {
  height: 100%;
  width: auto;
  max-width: 100%;
}
 */
.thumbnail--narrow img {
  max-width: 100%;
  object-fit: contain;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.thumbnail--wide img {
  height: auto;
  width: 100%;
}

.thumbnail__badge .icon {
  width: 1rem;
  height: 1rem;
}

.thumbnail__badge .icon-3d-model {
  width: 1.2rem;
  height: 1.2rem;
}

.thumbnail__badge {
  color: rgb(var(--color-foreground), 0.6);
  height: 2rem;
  width: 2rem;
  /* left: auto;
  right: calc(0.4rem + var(--media-border-width));
  top: calc(0.4rem + var(--media-border-width)); */
  left:0;
  right:0;
  bottom:0;
  top:0;
  margin:auto;
}

@media screen and (min-width: 750px) {
  .product:not(.product--small) .thumbnail__badge {
    height: 3rem;
    width: 3rem;
  }

  .product:not(.product--small) .thumbnail__badge .icon {
    width: 1.2rem;
    height: 1.2rem;
  }

  .product:not(.product--small) .thumbnail__badge .icon-3d-model {
    width: 1.4rem;
    height: 1.4rem;
  }
}

.thumbnail-list__item {
  position: relative;
}

.thumbnail-list__item::before {
  content: "";
  display: block;
  padding-bottom: 100%;
}

.product:not(.featured-product) .product__view-details {
  display: none;
}

.product__view-details {
  display: block;
  text-decoration: none;
}

.product__view-details:hover {
  text-decoration: underline;
  text-underline-offset: 0.3rem;
}

.product__view-details .icon {
  width: 1.2rem;
  margin-left: 1.2rem;
  flex-shrink: 0;
}
/*main product*/
button.product-form__submit.button.button--full-width.button--secondary {
  margin-left: 20px;
  margin-bottom: 20px;
  width: calc(100% - 160px);
  min-width: calc(100% - 160px);
}

.product-form__buttons {
  font-weight: 400;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin: 0 0 15px;
  min-height: 45px;
  line-height: 30px;
  clear: both;
}
.product__info-container .product-form form {
  margin-bottom: 0;
  flex-direction: inherit;
  max-width: 400px;
}
.product-form__buttons .shopify-payment-button {
  width: 100% ;
}
a.dt-sc-btn.add-wishlist {
  margin-top: 0;
  width: 100%;
  padding-left: 0;
  padding-right: 0;
}
.product__info-as-bottom-tabs .dt-sc-tabs-content h4 {
  font-size: 1.8rem;
  font-family: var(--font-body-family);
}

p.product__text.subtitle {
  font-size: 1.6rem;
  margin: 0;
}
/* .price__regular span.price-item.price-item--regular {
    font-size: 2.4rem;
    font-weight: 600;
} */
.product__info-bottom.tabs .summary__title h6.accordion__title{position:relative;transition:all 0.3s linear;line-height:normal;padding: 0 30px;}
.product__info-bottom.tabs .summary__title h6.accordion__title:after {
  content:'';
  width:0%;
  height:2px;
  border-radius:10px;
  position:absolute;
  background:currentcolor;
  bottom:-17px;left:0;right:0;margin:auto;
  transition:all 0.3s linear;opacity:0;
}
.product__info-bottom.tabs .summary__title.active-tab h6.accordion__title:after {width:100%;opacity:1;}
a.button.add-wishlist.button--secondary {
  /*background:transparent;*/
  white-space: nowrap;
  z-index: 0;
}
/* Product Tab */
.product__info-as-bottom-tabs {
  margin-top: 10rem;
}

.product__info-bottom.tabs {  
  flex-wrap: wrap; padding-bottom: 5px;
  border-bottom: 1px solid rgba(var(--color-foreground),.08);
}
.product__info-bottom.tabs .accordion__title {
  color: currentcolor;
  max-width: 100%;
  font-size: 2.5rem;
  font-weight: 400;
  line-height: 36px;
}
.product__info-as-bottom-tabs .dt-sc-tabs-content p:first-child{ margin:0;font-size: 1.6rem;font-weight: 400;}
.product__info-bottom.tabs .summary__title {
  cursor: pointer;
  transition: linear var(--duration-default);
  margin-bottom: 0px;
  color: currentColor;
  font-size: 2.4rem;
  font-weight: 400;
  padding: 0 ;
  display: flex;
  align-items: center;
  justify-content: center;
  padding:1rem 0;
/*   margin-right: 60px; */
}
.suport h6 {margin: 0;}
.product__info-bottom.tabs .summary__title:not(:last-child) { margin-right: 50px; }
.product__info-bottom.tabs a.summary__title.tablinks:last-child {
  padding: 10px 30px;
}
/* .product__info-bottom.tabs .summary__title:hover,
.product__info-bottom.tabs .summary__title.active {
  background-color: var(--color-button-background);
  color: var(--color-button-text);
} */
.product__info-as-bottom-tabs .dt-sc-tabs-content {
  border: 1px solid rgba(var(--color-foreground),.08);
  padding: 30px;
}

@media only screen and (min-width: 750px) {
  .product__info-as-bottom-tabs .dt-sc-tabs-content {  display: none;}
  .product__info-as-bottom-tabs .dt-sc-tabs-content.tab-active-content {  display: block;}
  .product__info-as-bottom-tabs h6.accordion__title.acc__title {display:none;}
  .product__info-bottom.tabs{display:flex;}
}
/* @media only screen and (max-width: 749px) {
  .product__info-as-bottom-tabs .dt-sc-tabs-content.tab-active-content
}
@media only screen and (min-width: 750px) {

.product__info-bottom.tabs   {display: flex;}
h6.accordion__title.acc__title {display:none;}
} */
@media only screen and (max-width: 749px) {
.product__info-as-bottom-tabs .product__info-bottom.tabs   {display: none;}
.product__info-as-bottom-tabs h6.accordion__title.acc__title {display:flex; position: relative; background:var(--gradient-base-accent-2);color: var(--gradient-base-background-1); margin-bottom: 10px; padding: 10px 20px; max-width: 100%;  cursor: pointer; justify-content: flex-start; align-items: center;}
.product__info-as-bottom-tabs h6.accordion__title.active + .dt-sc-tabs-content {display:block; transition: all .5s ease-in-out; }
.product__info-as-bottom-tabs h6.accordion__title + .dt-sc-tabs-content {display:none;}  
.product__info-as-bottom-tabs h6.accordion__title:before {  content: ""; height: 16px;  width: 2px;  position: absolute; right: 26px; background:var(--gradient-base-background-1); margin: auto;}
.product__info-as-bottom-tabs h6.accordion__title:after { content: "";  background: var(--gradient-base-background-1); width: 15px; height: 2px; right: 20px; margin: auto; position: absolute; top: auto;}
.product__info-as-bottom-tabs h6.accordion__title.active:before{display:none;}
}




.product__info-as-bottom-tabs #shipping-information{margin-top:10px;}
label.swatch-variant-image {
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  height: 40px;
  width: 40px;
  background-color: transparent !important;
}

/*Review*/
#shopify-product-reviews .spr-form-input-text,
#shopify-product-reviews .spr-form-input-email,
#shopify-product-reviews .spr-form-input-textarea {
  font-size: 1.6rem;
  line-height: calc(1 + 0.5 / var(--font-body-scale));
  letter-spacing: 0.04rem;
  font-family: inherit;
  -webkit-appearance: none;
  appearance: none;
  background-color: transparent;
  border: 0.1rem solid;
  color: var(--color-icon);
  width: 100%;
  box-shadow: none;
  height: 4.5rem;
  box-sizing: border-box;
  transition: all var(--duration-default) linear;
  flex-grow: 1;
  text-align: start;
  padding: 1.5rem;
  border-radius: 0;
   border: var(--inputs-border-width) solid rgb(var(--color-foreground),0.2);
}
#shopify-product-reviews .spr-form-input-textarea {
    height: 10rem;
}
.spr-form-label {
  font-size: 18px !important;
}
.spr-form-title {
  font-size: 20px !important;
}
@media only screen and (max-width: 480px) {
  .spr-summary {
    font-size: 1.8rem;
  }
}
#shopify-product-reviews .spr-form-input-text:focus-visible,
#shopify-product-reviews .spr-form-input-email:focus-visible,
#shopify-product-reviews .spr-form-input-textarea:focus-visible {
  outline: none;
  box-shadow: none;
   border: var(--inputs-border-width) solid rgb(var(--color-foreground));
}
#shopify-product-reviews .spr-icon.spr-icon-star-empty {
  color: var(--gradient-base-accent-1); line-height: normal;
}
#shopify-product-reviews .spr-button-primary {
  min-width: calc(12rem + var(--buttons-border-width) * 2);
  min-height: calc(4.5rem + var(--buttons-border-width) * 2);
}
.spr-pagination span a {
  color: var(--color-icon);
}

/* .product-form__buttons button,  .dT_VProdWishList a.button.add-wishlist.button--secondary  {
    background: var(--gradient-base-accent-1);
    color: var(--gradient-base-accent-2);
} */

.product-form__buttons
  button.product-form__submit.button.button--full-width.button--secondary,
.product-form__buttons a.button.add-wishlist.button--secondary {
  background: transparent;
  color: var(--gradient-base-accent-1); border: 2px solid var(--gradient-base-accent-1);
}
.product-form__buttons
  button.product-form__submit.button.button--full-width.button--secondary:hover,
.product-form__buttons a.button.add-wishlist.button--secondary:hover {
  background:rgba(var(--color-base-outline-button-labels));
  color:var(--gradient-base-accent-1);
  border: 2px solid var(--gradient-base-accent-1);
}

.product--thumbnail_slider_left {
  position: relative;
}
/*   .product--thumbnail_slider_left media-gallery {
    display: flex !important;
    flex-direction: row-reverse;
  }
  .product--thumbnail_slider_left .slider-mobile-gutter:not(.thumbnail-slider) {
    width: calc(100% - 200px);
  } */
.product--thumbnail_slider media-gallery {
  display: flex !important;
  justify-content: space-between;
  align-items: center;
}

.product--thumbnail_slider_left media-gallery {
  flex-direction: column;
}
.product--thumbnail_slider_right media-gallery{
   flex-direction: column; 
}
.product--thumbnail_slider .slider-mobile-gutter:not(.thumbnail-slider) {width:100%;}
.product--thumbnail_slider .thumbnail-slider {  display: flex;
  align-items: center;
  margin-top: 0;
  height: 530px;
  padding: 0px 0;
  width: 100%;
}
@media only screen and (min-width: 750px) {
.product--thumbnail_slider .slider-mobile-gutter:not(.thumbnail-slider) {
  width: calc(100% - 90px);
  margin: 0;
}
.product--thumbnail_slider .thumbnail-slider {
  display: flex;
  align-items: center;
  margin-top: 0;
  height: 530px;
  padding: 40px 0;
  width: 80px;
}
.product--thumbnail_slider_left
  .thumbnail-slider
  .thumbnail-list.slider--tablet-up
  .thumbnail-list__item.slider__slide {
  width: 100%;height:120px;    margin: 5px 0;
}  
.product--thumbnail_slider_left media-gallery {
  flex-direction: row-reverse;
}  
.product--thumbnail_slider_right media-gallery{
  flex-direction: row;
}  
.product--thumbnail_slider_left
  .thumbnail-slider
  .thumbnail-list.slider--tablet-up {
  padding: 0;
  display: block;
}  
.product--thumbnail_slider_right
  .thumbnail-slider
  .thumbnail-list.slider--tablet-up {
  padding: 0;
  display: block;
}  
 .product--thumbnail_slider_right
  .thumbnail-slider
  .thumbnail-list.slider--tablet-up
  .thumbnail-list__item.slider__slide {
  width: 100%;;height:110px;margin: 10px 0;
} 
}
@media only screen and (max-width: 749px) {
.product--thumbnail_slider .slider-mobile-gutter:not(.thumbnail-slider) .slider-buttons {
    display: none;
}
}
/*   .product--thumbnail_slider_left .thumbnail-slider {
    width: 100px;
    padding-right: 10px;
  } */

.product--thumbnail_slider_left .thumbnail-slider .thumbnail,
.product--thumbnail_slider_left .thumbnail-slider .thumbnail[aria-current] {
  /* border: none;
  box-shadow: none;
  margin: 5px 0;*/
}

/* .product--thumbnail_slider_left .slider-button {
  display: none !important;
} */


.product--thumbnail_slider_right {
  position: relative;
}
.product--thumbnail_slider_right media-gallery {
      display: flex;
  /* flex-direction: unset; */
}

/*   .product--thumbnail_slider_right
    .slider-mobile-gutter:not(.thumbnail-slider) {
    width: calc(100% - 200px);
  } */
/*   .product--thumbnail_slider_right .thumbnail-slider {
    width: 100px;
    padding-right: 10px;
  } */
.product--thumbnail_slider_right .thumbnail-slider .thumbnail,
.product--thumbnail_slider_right .thumbnail-slider .thumbnail[aria-current] {
  border: none;
  box-shadow: none;
  padding:  0;
}

/* .product--thumbnail_slider_right .slider-button {
  display: none !important;
}
 */

/* } */

.product--thumbnail_slider_bottom {
  position: relative;
}
.product--thumbnail_slider_bottom media-gallery {
  flex-direction: unset;
}

.product--thumbnail_slider_bottom .thumbnail-slider .thumbnail,
.product--thumbnail_slider_bottom .thumbnail-slider .thumbnail[aria-current] {
  border: none;
  box-shadow: none;
  padding: 5px 0;
}

.product--thumbnail_slider_bottom .slider-button {
  display: none !important;
}
.product--thumbnail_slider_bottom
  .thumbnail-slider
  .thumbnail-list.slider--tablet-up {
  padding: 0;
  display: block;
}
.product--thumbnail_slider_bottom
  .thumbnail-slider
  .thumbnail-list.slider--tablet-up
  .thumbnail-list__item.slider__slide {
  width: 100%;
}
.main-product-template .facets-vertical.no-sidebar .main-product_info {
  width: 100%;
}
.thumbnail-list .thumbnail-list__item.slick-current .thumbnail{outline: 0;/*box-shadow: 0 0 0 0.1rem rgb(var(--color-foreground));*/ box-shadow:none;}
.thumbnail-list .thumbnail-list__item.slick-current .thumbnail img{clip-path: inset(0px);}

@media (max-width: 989px) {
  button.toggleFilter svg {
    width: 2rem;
    height: 2rem;
    fill:currentcolor;
    margin-right:5px;
  }
  button.toggleFilter {
    background: transparent;
    border: none;
    position: relative;
    display: flex;
    align-items: center;
    color: var(--gradient-base-accent-1);
    font-size: 1.6rem;
    font-weight:500;
    margin-bottom:20px;
    font-family: var(--font-body-family);
    cursor:pointer;
    transition: all 0.3s linear;
    padding:0;
    /*     margin-left: auto; */
  }
  button.toggleFilter:hover{color:rgba(var(--color-base-outline-button-labels));}
  .main-product-template .facets-vertical {
    display: block;
  }

  .main-product_info {
    width: 100%;
  }
}
@media (max-width: 749px) {
  .product--thumbnail_slider_left
  .thumbnail-slider
  .thumbnail-list.slider--tablet-up
  .thumbnail-list__item.slider__slide { margin: 6px 0;}
  .product--thumbnail_slider .thumbnail-slider{height: unset;}
}
.main-product_info.product.product--medium.product--thumbnail_slider {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.facets-vertical.no-sidebar
  .main-product_info.product.product--medium.product--thumbnail_slider {
  width: 100%;
}
@media (min-width: 767px) and (max-width: 1199px) {
  .main-product_info.product.product--medium.product--thumbnail_slider {
    flex-direction: column;
  }
  .sidebar-left .product__media-wrapper,
  .sidebar-right .product__info-wrapper {
    max-width: 100%;
    width: calc(100% - var(--grid-desktop-horizontal-spacing) / 2);
  }
}
/*color -  detail*/

.product-form__input input[type="radio"] + .swatch-variant-image {
  border: 2px solid var(--gradient-background);
  padding: 1rem;
}
.product-form__input input[type="radio"]:checked + .swatch-variant-image {
  border: 2px solid var(--gradient-base-background-2);
}
.swatch-group label.swatch-variant-color {
  border: none;
  width:30px;
  height:30px;
  border-radius: 50%;padding:0;
}

.product-form__input input[type=radio]:checked+.swatch-element{ border:1px solid var(--gradient-base-accent-1);}
/* .product-form__input input[type="radio"]:checked + .swatch-element label {
  border: 3px double transparent;
} */

/*deal-banner*/
.product__info-container .product-deal-count {
  display: flex;
  flex-direction: column;
  align-items: self-start;
  min-height: inherit;
  margin: 0;
}
.product__info-container label.deal-lable.product-label {
  min-width: initial;
  margin: 0;
  display: inline-block;
  font-size: 2.2rem;
  font-weight: 700;
}
.product__info-container .deal-clock {
  min-width: 250px;
  max-width: 500px;
  position: relative;
  display: inline-block;
  text-align: center;
  z-index: 1;
  transition: all var(--duration-default) linear;
  width: auto;
  margin: 0;
}
.product__info-container .deal-clock ul {
  padding: 5px;
  list-style: none;
  text-align: center;
  width: 100%;
  margin: 0;
  display: flex;
  justify-content: flex-start;
  margin: 0;
  padding: 0;
  margin-top: 0.5rem;
}
.product__info-container .deal-clock ul li {
  padding: 0;
  background: none;
  color: #000;
  display: inline-block;
  font-size: 2rem;
  align-items: center;
  font-family: var(--font-heading-family);
  margin-right: 1rem;
}
.product__info-container .deal-clock ul li span {
  margin-left: 0.3rem;
  font-size: 1.4rem;
  transform: translateY(-5px);
  -webkit-transform: translateY(-5px);
  color: var(--gradient-base-background-2);
}

/*available and sub and enquiry form */
/* .inventory-form__label {
  margin-top: 3rem; /*display: flex; flex-direction: column;
} 
.total-price__container{  display: flex; flex-direction: column;}
.inventory-form__label label.form__label, .sub-total p.product-label {  margin: 0;  min-width: 140px;  display: inline-block;  font-size: 2.2rem;  font-weight: 700;} */
.inventory-form__label p {
  margin: 0;
}
.sub-total span,
.inventoryNote.form__label span,
.inventory-form__label p,
.product__info-container .product-attributes a {
  font-size: 1.8rem;
  margin: 0;
}
.dt-sc-enquiry-form {
  font-size: 2.2rem;
  font-weight: 700;
  text-decoration: underline;
}
.dt-sc-enquiry-form a {
  cursor: pointer;
  text-decoration: none;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 2.6px;
  font-weight: 500;
  position:relative;
}
.dt-sc-enquiry-form a:after{content:'';width:100%;height:1px;background:currentcolor;left:0;position:absolute;bottom:4px;transition:all 0.3s linear}

.main-product-template .facets-vertical .mobile-facets {
  opacity: 0;
}
.main-product-template .facets-vertical.open .mobile-facets {
  opacity: 1;
}
.dt-sc-enquiry-form {
  margin-top: 20px;
}

/* color swatches */
.swatch-group {
  display: flex;
  flex-wrap: wrap;
}
.swatch-group .swatch-element .tooltip {
  border-radius: var(--DTRadius);
  position: absolute;
  pointer-events: none;
  opacity: 0;
  padding: 5px 15px;
  left: 15px;
  /* left: 50%; */
  transition: all var(--duration-default) linear;
  transform: translateX(-50%);
  bottom: 100%;
  word-break: normal;
  white-space: nowrap;
  visibility: hidden;
  z-index: 10000;
  background-color: var(--gradient-base-background-2);
  color: var(--gradient-base-accent-1);
  font-size: 12px;
  line-height: normal;
  box-shadow: #63636333 0 2px 8px;
}

.swatch-group label.swatch-variant-image {
  min-width: 30px;
  width: 30px;
  height: 30px;
  padding: 0;
  margin:0;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%; 
}
.swatch-group .swatch-element > * {
  margin: 7px 5px 2px 0;
}
.swatch-group .swatch-element {
  position: relative;display:flex;align-items:center;justify-content:center; margin: 10px 5px 2px 0;
  width:40px;height:40px;border-radius:50%;transition:all 0.3s linear;border:1px solid transparent;
}
.swatch-group .swatch-element:hover .tooltip {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%);
}
.swatch-group .tooltip:before {
  content: "";
  position: absolute;
  bottom: -6px;
  left: 10px;
  border-top: 6px solid var(--gradient-base-background-2);
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  transform: translateX(-50%);
  left: 50%;
}
.facets-vertical.sidebar-left.open:after {
  content: "";
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 16;
  background-color: rgba(0, 0, 0, 0.7);
}

.secure-badges {
  border: 1px solid rgba(var(--color-foreground),.08);
  padding: 20px;
  display: inline-block;
  position: relative;
  margin: 30px 0 0px;
}
.secure-badges .badge-label {
  background: var(--gradient-background);
  padding: 0 10px;
  position: absolute;
  top: -35px;
  left: 15px;
}
.secure-badges img{display:flex;}
/*custom*/

.product__info-container .product__title h1 {
  font-weight: 400;
  line-height: 50px;
}
.dT_VProdWishList,
.dT_VProdCompareList {
  margin-bottom: 0;
  width: 100%;
}
.share-button__button {
  font-size: 1.6rem;
  font-weight: 400;
  font-family: var(--font-heading-family);
}
.product__info-container p.product__text {
  max-width: 490px;
  font-size: 1.6rem;
  line-height: 26px;
  margin-top: 18px;
}
.product__info-container .price__regular .price-item--regular,
.product__info-container .price__sale .price-item.price-item--sale.price-item--last {
  font-size: 16px;
  font-weight: 500;
  line-height: normal;letter-spacing: 0;
}
.product__info-container  .price .badge{left:0;white-space: nowrap;}
.product__info-container .price--on-sale .price__sale{align-items:center;justify-content: flex-start;}
.product__info-container variant-radios {
  flex-direction: column;
  display: flex;
}
.product__info-container fieldset.product-form__input .form__label,
.product__info-container .sub-total p.product-label,
.product__info-container .inventory-form__label .form__label,
.advance-product-style .advanced-title,
.product__info-container .product-attributes .product-label {
  font-weight: 600;
  font-size: 2.4rem;
  line-height: normal;
  margin-bottom: 5px;
  margin-top: 0;
}
.product-form__input input[type="radio"] + .swatch-variant-color {
  padding: 1.4rem;
  margin: 0.7rem 1rem 0.2rem 0;
}
.product-form__input input[type="radio"] + label {
  margin: 1rem 1rem 0.2rem 0;
  padding: 0.6rem 1.7rem;
  text-align: center;
  cursor: pointer;
  position: relative;
  background: var(--gradient-base-background-2);
  transition: all 0.3s linear;
  border-radius: var(--variant-pills-radius);
  overflow: hidden;
}
.product__info-container .icon-with-text {
  display: flex;
  padding: 0rem 0 2.5rem;
  max-width: 100%;
  align-items:center;
}
.product__info-container .product-form__input.product-form__quantity {
  margin: 0 0 20px;
}
.product__info-container
  button.product-form__submit.button.button--full-width.button--primary {
  margin-right: 0;
  margin-bottom: 12px;
  margin-left: 12px;
  width: calc(68% + 0px);
  min-height: calc(4rem + var(--buttons-border-width) * 2);
  border: 2px solid var(--gradient-base-accent-1);
}
.product__info-container .product-form form {
  max-width: 100%;
}
.product__info-container .product-form__buttons {
  max-width: 100%;
  border-bottom: 2px solid rgba(var(--color-foreground),.08);
  padding-bottom: 25px;
}
.product__info-container .html-sections-product span {
  font-size: 1.6rem;
  font-weight: 400;
  padding-left: 5px;
}
.product__info-container .product__payment{position:relative; margin-top: 3.5rem;}
.product__info-container .product__payment .list-payment {
    justify-content: center;
    margin: 0;
    flex-wrap: wrap;
    border: 1px solid rgba(var(--color-foreground),.08);
    padding: 30px 20px 40px;
    position: relative;
}
.product__info-container .html-sections-product p.delivery {
  margin-top: 40px;
  margin-bottom: 0;
}
.product__info-container .html-sections-product p.shipping {
  margin-top: 20px;
  margin-bottom: 20px;
}

.product__info-container
  modal-opener.product-popup-modal__opener
  .product-popup-modal__button {
  font-size: 1.6rem;
  line-height: 26px;
  text-decoration: none;
  font-weight: 500;
  text-decoration: underline;
  height: auto;transition: all 0.3s linear;
}
/* .product__info-container modal-opener.product-popup-modal__opener.no-js-hidden.quick-add-hidden {     position: absolute; top: 26%; right: 20%; margin-bottom: 0; margin-top: 11px;} */
.product__info-container .varient-class {
  margin-right: 50px;
  display: flex;
}
.product__info-container .varient-model-wrapper {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.product__info-container .product__payment .list-payment .list-payment__item {
  padding: 0;
  flex-wrap: nowrap;
}
.product__info-container .product__payment .list-payment .list-payment__item:not(:last-child) {margin-right:30px;}
.product__info-container svg.icon.icon--full-color {
  width: 5.4rem;
  height: 4.4rem;
}
.product__info-container svg.icon.icon-size-chart {
  width: 1.5rem;
  height: 2.7rem;
  margin-bottom: -7px;
}
.dt-sc-enquiry-form {
  max-width: 100%;
  text-align: left;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.6rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0 20px 48px;
  border: 1px solid rgba(var(--color-foreground),.08);
  margin-top:45px;
}
.dt-sc-enquiry-form  .enquiry-image{display: flex;align-items: center;}
.dt-sc-enquiry-form .enquiry_heading{font-weight: 400;font-size: 2rem;margin:0 0 5px;}

.product__info-wrapper a.button--secondary,
.product__info-wrapper .share-button__button {
  transition: all var(--duration-default) linear;
  font-family: var(--font-body-family);
}
.product__info-wrapper a.button--secondary:hover,
.product__info-wrapper .share-button__button:hover {
  color: rgb(var(--color-base-outline-button-labels)) !important;
}
.product__info-container
  button.product-form__submit.button.button--full-width.button--primary {
  width: calc(100% - 152px);
}
/* .product__info-wrapper .icon-with-text a,
.product__info-wrapper .icon-with-text button {
  width: 100%;
} */
.product__info-container .varient-model-wrapper .size-chart {
  display: flex;
  align-items: center;
}

@media (max-width: 1540px) {
  .product__info-container .varient-model-wrapper {
    align-items: flex-start;
    justify-content: flex-start;
    flex-direction: column;
  }
  .product__info-container .varient-model-wrapper .size-chart {
    margin-top: 20px;
  }
}

@media (max-width: 1439px) and (min-width: 1199px) {
  .dT_VProdWishList,
  .dT_VProdCompareList {
    width: 100%;
    display: block;
  }
  /*  .sidebar-left  .product__info-container .icon-with-text{width:100%} */
  .sidebar-left .product--thumbnail .product__info-wrapper.grid__item,
  .sidebar-left .product--stacked .product__info-wrapper.grid__item {
    padding-left: 30px;
  }
}
@media (max-width: 1200px) {
  .dt-sc-enquiry-form {
    text-align: left;
  }
  .dT_VProdWishList,
  .dT_VProdCompareList {
    width: 100%;
    display: block;
  }
  .product__info-container .product__payment .list-payment .list-payment__item:not(:last-child) {
    margin-right: 20px;
  }
  .product__info-container .varient-class {
    margin-right: 0;
    flex-wrap: wrap;
  }
}
@media (max-width: 990px) {
  .product__info-container .product-form form,
  .product__info-container .product-form__buttons,
  .product__info-container p.product__text {
    max-width: 100%;
  }
  .quick-add-modal__content-info .product__info-container .icon-with-text {
    width: 100%;
  }
}
@media (max-width: 576px) {
  product-recommendations .product-grid .grid__item {
    width: 100%;
    max-width: 100%;
  }
  .product__info-bottom.tabs .summary__title {
    margin-right: 0px;
  }
}
@media (max-width: 480px) {
  .product__info-container .product__payment .list-payment .list-payment__item:not(:last-child) {
    margin-right: 10px;
  }
  .product__info-container .icon-with-text {
    width: 100%;
  }
  .product__info-container svg.icon.icon--full-color {
    width: 3.8rem;
    height: 3.8rem;
  }
}
@media (max-width: 400px) {
  .product__info-container .icon-with-text {
    flex-direction: column;align-items:flex-start;
  }
  .product__info-container .icon-with-text > *:not(:last-child) {
    margin-bottom: 15px;
  }
  .product__info-bottom.tabs .accordion__title {
    font-size: 2rem;
  }
  .quick-add-modal__content-info
    .product__info-container
    button.product-form__submit.button.button--full-width.button--secondary {
    width: 100%;
    margin-left: 0;
  }
  .product__info-container svg.icon.icon--full-color {
    width: 3rem;
    height: 3rem;
  }
}

.main-product_info.product.product--large.product--thumbnail_slider.media-slick-slider.grid
  .product__media-list {
  column-gap: 0; margin-bottom:0;
}
.main-product_info .product__info-as-bottom-tabs.tab_right .accordion__title {
  margin-right: 0;
}
.main-product_info .product-deal-count .deal-clock ul li {
  background: rgba(var(--color-base-accent-1),0.05);
}
.main-product_info .product-deal-count .deal-clock {
  position: relative;
  margin: 0;
}
.main-product_info .product__info-container .product-deal-count {
  display: flex;
}

.product__info-as-bottom-tabs.tab_right {
  margin-top: 3.5rem;
}

.iconwithtext .item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

/* Recipient form */
.recipient-form {
  /* (2.88[line-height] - 1.6rem) / 2 */
  --recipient-checkbox-margin-top: 0.64rem;

  display: block;
  position: relative;
  max-width: 44rem;
  margin-bottom: 2.5rem;
  padding: 20px;
  background: var(--gradient-base-background-2);
}

.recipient-form-field-label {
  margin: 0.6rem 0;
}

.recipient-form-field-label--space-between {
  display: flex;
  justify-content: space-between;
}

.recipient-checkbox {
  flex-grow: 1;
  font-size: 1.6rem;
  display: flex;
  word-break: break-word;
  align-items: flex-start;
  max-width: inherit;
  position: relative;
  cursor: pointer;
}

.no-js .recipient-checkbox {
  display: none;
}

.recipient-form > input[type="checkbox"] {
  position: absolute;
  width: 1.6rem;
  height: 1.6rem;
  margin: var(--recipient-checkbox-margin-top) 0;
  top: 0;
  left: 0;
  z-index: -1;
  appearance: none;
  -webkit-appearance: none;
}

.recipient-fields__field:not(:last-child) {
  margin: 0 0 2rem 0;
}

.recipient-fields .field__label {
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: calc(100% - 3.5rem);
  overflow: hidden;
}

.recipient-checkbox > svg {
  margin-top: var(--recipient-checkbox-margin-top);
  margin-right: 1.2rem;
  flex-shrink: 0;
}

.recipient-form .icon-checkmark {
  visibility: hidden;
  position: absolute;
  left: 0.28rem;
  z-index: 5;
  top: 0.4rem;
}

.recipient-form > input[type="checkbox"]:checked + label .icon-checkmark {
  visibility: visible;
}

.js .recipient-fields {
  display: none;
}

.recipient-fields hr {
  margin: 1.6rem auto;
}

.recipient-form > input[type="checkbox"]:checked ~ .recipient-fields {
  display: block;
  animation: animateMenuOpen var(--duration-default) ease;
  margin-top: 20px;
}
.recipient-form
  > input[type="checkbox"]:not(:checked, :disabled)
  ~ .recipient-fields,
.recipient-email-label {
  display: none;
}

.js .recipient-email-label.required,
.no-js .recipient-email-label.optional {
  display: inline;
}

.recipient-form ul {
  line-height: calc(1 + 0.6 / var(--font-body-scale));
  padding-left: 4.4rem;
  text-align: left;
}

.recipient-form ul a {
  display: inline;
}

.recipient-form .error-message::first-letter {
  text-transform: capitalize;
}
.product__info-container
  fieldset.product-form__input
  .form__label
  span.small--text {
  font-size: 14px;
  color: var(--gradient-base-accent-1);
  padding: 0 5px;
  font-weight: 500;
}
@media screen and (forced-colors: active) {
  .recipient-fields > hr {
    border-top: 0.1rem solid rgb(var(--color-background));
  }

  .recipient-checkbox > svg {
    background-color: inherit;
    border: 0.1rem solid rgb(var(--color-background));
  }

  .recipient-form > input[type="checkbox"]:checked + label .icon-checkmark {
    border: none;
  }
}
/*stock-availablity*/
.product__inventory  span.stock-availablity{    position: relative; padding: 0 10px;}
.product__inventory span.stock-availablity.low-stock:before { background: #f4af29;}
.product__inventory span.stock-availablity.in-stock:before { background: #54c63a;}
.product__inventory span.stock-availablity.out_of_stock:before { background: #c8c8c8;}

.product__inventory  span.stock-availablity:before { content: ""; position: absolute; width: 10px; height: 10px; left: 0; top: 0; bottom: 0; margin: auto; border-radius: 50%; background: transparent;
  transition: all 0.3s linear; }
.product__inventory span.stock-availablity.low-stock:before{-webkit-animation: ripple1 1s linear infinite;  animation: ripple1 1s linear infinite; }
.product__inventory span.stock-availablity.in-stock:before{-webkit-animation: ripple2 1s linear infinite;  animation: ripple2 1s linear infinite; }
.product__inventory span.stock-availablity.out_of_stock:before{-webkit-animation: ripple3 1s linear infinite;  animation: ripple3 1s linear infinite; }
@keyframes ripple1{
  0% {
     -webkit-box-shadow: 0 0 0 0 rgba(244, 175, 41, 0), 0 0 0 5px rgba(244, 175, 41, 0.4), 0 0 0 10px rgba(244, 175, 41, 0);
            box-shadow: 0 0 0 0 rgba(244, 175, 41, 0), 0 0 0 5px rgba(244, 175, 41, 0.4),  0 0 0 10px rgba(244, 175, 41, 0);
  
}
100% {
      -webkit-box-shadow: 0 0 0 10px rgba(244, 175, 41, 0), 0 0 0 5px rgba(244, 175, 41, 0.2), 0 0 0 10px rgba(244, 175, 41, 0);
            box-shadow: 0 0 0 10px rgba(244, 175, 41, 0), 0 0 0 5px rgba(244, 175, 41, 0.2),  0 0 0 10px rgba(244, 175, 41, 0);
   
}
}

@keyframes ripple2{
  0% {
     -webkit-box-shadow: 0 0 0 0 rgba(82, 180, 58, 0), 0 0 0 5px rgba(82, 180, 58, 0.4), 0 0 0 10px rgba(82, 188, 58, 0);
            box-shadow: 0 0 0 0 rgba(19, 180, 58, 0), 0 0 0 5px rgba(19, 198, 58, 0.4),  0 0 0 10px rgba(19, 180, 58, 0);
  
}
100% {
      -webkit-box-shadow: 0 0 0 10px rgba(82, 180, 58, 0), 0 0 0 5px rgba(82, 180, 58, 0.2), 0 0 0 10px rgba(82, 180, 58, 0);
            box-shadow: 0 0 0 10px rgba(82, 180, 58, 0), 0 0 0 5px rgba(82, 180, 58, 0.2),  0 0 0 10px rgba(82, 180, 58, 0);
   
}
}
@keyframes ripple3{
  0% {
     -webkit-box-shadow: 0 0 0 0 rgba(200, 200, 200, 0), 0 0 0 5px rgba(200, 200, 200, 0.4), 0 0 0 10px rgba(200, 200, 200, 0);
            box-shadow: 0 0 0 0 rgba(200, 200, 200, 0), 0 0 0 5px rgba(200, 198, 200, 0.4),  0 0 0 10px rgba(200, 200, 200, 0);
  
}
100% {
      -webkit-box-shadow: 0 0 0 10px rgba(200, 200, 200, 0), 0 0 0 5px rgba(200, 200, 200, 0.2), 0 0 0 10px rgba(200, 200, 200, 0);
            box-shadow: 0 0 0 10px rgba(200, 200, 200, 0), 0 0 0 5px rgba(200, 200, 200, 0.2),  0 0 0 10px rgba(200, 200, 200, 0);
   
}
}
.main-product-template .product__payment p.product_payment_text {
    text-align: center;
    font-size: 16px;
    font-weight: 500;
    position: absolute;
    bottom: -14px;
    margin: 0 auto;
    left: 0;
    background: var(--gradient-base-background-1);
    right: 0;
    width: max-content;
    padding: 0 15px;
}
.main-product-template .rating{margin-right: 50px;}
.main-product-template .rating-fake-sales{    display: flex;align-items: center;line-height: 20px;}

.swatch-label-text {
    display: flex;
    align-items: center;
}

.product__info-container .sub-total p.product-label:after, 
    .product__info-container .inventory-form__label .form__label:after, 
    .advance-product-style .advanced-title:after,
    .main-product-template .product__info-container .product-label:after{content:':';margin:0 5px 0 2px; }
.main-product-template .product__info-container .product__sku .product-attributes-value,
.main-product-template .product__info-container .product_vendor .product-attributes-value,
.main-product-template .product__info-container .product_type .product-attributes-value,
.main-product-template .product__info-container .product_vendor .product-attributes-value a{font-size:16px;}
.main-product-template .product__info-container .deal-lable.product-label{display:none;}

.main-product_info .product-deal-count .deal-clock ul li{font-size:34px;font-weight:600;}
.main-product_info .product-deal-count .deal-clock ul{padding:0;margin:0;}
.main-product_info .product-deal-count .deal-clock ul li span{font-size:10px;font-weight:500;text-transform:uppercase;line-height:10px;margin:0;}

/* sidebar */
.product-sidebar-iconwithtext .iconwithtext .item__content {margin-left: 20px;}
.product-sidebar-iconwithtext .iconwithtext .item__content h6 {
    margin: 0;
    font-size: 2rem;
    font-weight: 400;
    line-height: normal;
}
.product-sidebar-iconwithtext .iconwithtext .item__content p {
    margin: 0;
    font-size: 16px;
    color: var(--gradient-base-accent-2);
}
.product-sidebar-iconwithtext .iconwithtext .item:not(:last-child){border-bottom: 1px solid rgba(var(--color-foreground),.08);margin-bottom:20px;padding:0 0 20px;}
.main-product-template .optional-sidebar .rte .thumbnail-list{padding:0;grid-template-columns: repeat(3,1fr);grid-gap: 3px;display: grid;}
.main-product-template .optional-sidebar .rte .thumbnail-list .thumbnail.global-media-settings img {
    border-radius: 0;
    padding: 0;
    margin: 0;
    width: 100%;
    height: 100%;object-fit: cover;clip-path: none;
}
.main-product-template .optional-sidebar .rte .thumbnail-list .thumbnail.global-media-settings{box-shadow:none;}
.main-product-template .optional-sidebar .product-sidebar-type-carousel  .card--card.card--media>.card__content .card__information{text-align:center;}
.main-product-template .optional-sidebar .product-sidebar-type-carousel .product-icons,
.main-product-template .optional-sidebar .product-sidebar-type-carousel .quick-add.button-quick-add,
.main-product-template .optional-sidebar .product-sidebar-type-carousel  .card--card.card--media>.card__content > *:not(.card__information) {display:none;}
.product__info-container .inventory-form__label p span{    
    background: var(--gradient-base-accent-1);
    color: var(--gradient-base-background-1);
    padding: 6px 20px;
    font-size: 14px;
    letter-spacing: 1px;}
.product__info-container .price--on-sale .price-item--regular{font-weight:500;}
.product__info-container .price {display: flex;align-items: center; column-gap:2rem;}
.main-product-template  .accordion.product__accordion  summary{padding: 1.8rem 35px;background: rgba(var(--color-base-accent-1),0.05);}
.main-product-template  .accordion.product__accordion {border:none;}
.main-product-template  .accordion.product__accordion  .icon-accordion{display:none;}
.main-product-template  .accordion.product__accordion:not(:last-child){margin-bottom:30px;}
.main-product-template  .product__accordion .accordion__content{padding: 20px 0.5rem 10px;}
.main-product-template .optional-sidebar .accordion.product__accordion{margin-top:40px;}
.main-product-template  .accordion.product__accordion+.accordion{margin-top:0;}
.main-product-template .accordion .summary__title+.icon-caret{height:15px;transform: rotate(0deg);-webkit-transform: rotate(0deg);}
.main-product-template .accordion details[open]>summary .icon-caret{transform: rotate(180deg);-webkit-transform: rotate(180deg);}
.main-product-template  .accordion.product__accordion{margin-top:100px;}
.main-product-template  .product__info-wrapper .accordion.product__accordion{margin-top:30px;}
.main-product-template .product__info-wrapper .product__info-bottom.tabs .accordion__title{font-size:2rem;}
.main-product-template .product__info-wrapper .product__info-bottom.tabs .summary__title:not(:last-child) {margin-right: 50px;}
.main-product-template .complementary-products .accordion.product__accordion{ margin: 3rem 0 1rem;}
.main-product-template .complementary-products .complementary-slide{padding-bottom:0;}
.product__info-container .product-form__input.product-form__input--dropdown .select__select{    
    height: 37px;
    background: transparent;
    box-shadow: none;line-height: 18px;padding: 1rem 18px;}
.product-attributes {
    line-height: normal;
}
.product__info-container .product-form__input.product-form__input--dropdown .select:after{box-shadow:none;}


.dt-sc-enquiry-form{
  padding: 15px 30px 12px;
}
@media screen and (max-width: 1540px){
.main-product-template .product__info-wrapper .product__info-bottom.tabs .summary__title:not(:last-child) {margin-right: 20px;}
.product__info-container .product__payment .list-payment .list-payment__item:not(:last-child){margin-right:16px;}  
}
@media screen and (max-width: 1440px) and (min-width: 1199px){
  .product__info-container svg.icon.icon--full-color {width: 4rem;height: 3.4rem;}
.product__info-container .product__payment .list-payment .list-payment__item:not(:last-child) {margin-right: 8px;}

.dt-sc-enquiry-form .enquiry_heading{font-size:1.6rem;}  
.main-product-template .product__info-wrapper  .product__info-bottom.tabs .summary__title h6.accordion__title{padding: 0 20px;}
.main-product-template .product__info-wrapper .product__info-bottom.tabs .summary__title:not(:last-child) {margin-right: 0px;}
.product__info-wrapper a.button--secondary, .product__info-wrapper .share-button__button{font-size:1.4rem;}  
}
@media screen and  (max-width: 1199px){
.product__info-as-bottom-tabs,
.main-product-template .accordion.product__accordion{margin-top: 6rem;}
   .product__info-bottom.tabs .summary__title h6.accordion__title{ font-size: 2rem; }

}
@media screen and  (max-width: 1199px) and (min-width:990px){
  .facets-vertical.no-sidebar .product__info-wrapper{ padding-left: 5rem;padding-top:0;} 
  .main-product-template .product__info-wrapper  .product__info-bottom.tabs .summary__title h6.accordion__title{padding: 0 8px;}
.main-product-template .product__info-wrapper .product__info-bottom.tabs .summary__title:not(:last-child) {margin-right: 0px;}  
}
@media screen and  (max-width: 1023px){
.product__info-bottom.tabs .summary__title:not(:last-child) {
    margin-right: 40px;
}
 .product__info-bottom.tabs .summary__title h6.accordion__title{ padding: 0 20px;} 
}
@media screen and  (max-width: 990px) and (min-width: 750px){
  .product__info-container svg.icon.icon--full-color {width: 4.4rem;height: 3.4rem;}
  .product__info-container .product__payment .list-payment .list-payment__item:not(:last-child) {margin-right: 8px;}
 .dt-sc-enquiry-form{ padding: 15px 30px 12px;} 
 .dt-sc-enquiry-form .enquiry_heading{font-size:1.8rem;}  
 .product__info-wrapper a.button--secondary, .product__info-wrapper .share-button__button{font-size:16px;} 
}
.product-heading h6 {
    margin: 0;
}
@media screen and  (max-width: 989px){
  .product__info-bottom.tabs .accordion__title{
    font-size: 2rem;
  }
}
@media screen and  (max-width: 749px){
.product__info-bottom.tabs .summary__title h6.accordion__title{padding: 0 20px;font-size: 2rem;}
.main-product-template .optional-sidebar .rte .thumbnail-list__item.slider__slide {width:100%;}
.main-product-template  .accordion.product__accordion{margin-top:80px;}  
}
@media screen and  (max-width: 576px){
  .dt-sc-enquiry-form{ padding: 15px 30px 12px;} 
  .dt-sc-enquiry-form .enquiry_heading{font-size:1.8rem;}
  .product__info-bottom.tabs .summary__title h6.accordion__title{padding: 0;}
}
@media screen and  (max-width: 480px){
.main-product_info .product-deal-count .deal-clock ul li{font-size:26px;}
.main-product-template .rating {margin-right: 15px;} 
.main-product-template  .accordion.product__accordion summary {padding: 1.8rem 20px;}  
.main-product-template  .accordion.product__accordion:not(:last-child){margin-bottom:25px;}  
.product__info-container .product__payment .list-payment .list-payment__item:not(:last-child){margin-right:10px;}    
}
@media screen and  (max-width: 479px){
  .product__info-bottom.tabs .summary__title:not(:last-child) {margin-bottom:0px;}
  .product__info-bottom.tabs{flex-direction: column; text-align: center;}
}
      
/popup -sizechart/

 .image-sizeguid table {
    width: 100%;
}