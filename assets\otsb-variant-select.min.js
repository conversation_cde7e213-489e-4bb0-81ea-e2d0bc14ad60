(()=>{var A=(o,i,u,f,y,g,b,S,m,V,M,E,L,$)=>({variants:null,currentVariant:{},options:[],currentAvailableOptions:[],cachedResults:[],quickViewSectionId:"quick-view",handleSectionId:i,paramVariant:!1,mediaGallerySource:[],isChange:!1,optionConnects:[],mediaOption:"",handleSticky:"",initfirstMedia:!1,initialized:!1,initVariant(){this.variants=JSON.parse(this.$el.querySelector('[type="application/json"]').textContent),document.addEventListener(`eurus:product-variant-get:${i}`,t=>{t.detail.callback(this.variants)}),S&&(this.handleSectionId="choose-option"),m&&(this.handleSectionId=V),m||document.addEventListener("eurus:cart:items-changed",()=>{this.cachedResults=[],Alpine.store("xUpdateVariantQuanity").updateQuantity(i,y,this.currentVariant?.id)}),this.$watch("options",()=>{setTimeout(()=>{this._updateVariantSelector(L)},0)})},initMedia(t){t&&(this.initfirstMedia=!0),this._updateMasterId(),this._updateMedia(),this.initfirstMedia=!1},_updateVariantSelector(t=""){if(this._updateMasterId(),this._updateVariantStatuses(),this._updateOptionImage(),!this.currentVariant){this._dispatchUpdateVariant(),this._setUnavailable();return}M!=this.currentVariant.id&&(this.paramVariant=!0),u&&this.paramVariant&&window.history.replaceState({},"",`?variant=${this.currentVariant.id}`),S==""&&!u&&this._updateImageVariant(t),$&&this._updateImageVariant(t),this._updateVariantInput(),this._updateProductForms(),this._setAvailable(),Alpine.store("xPickupAvailable").updatePickUp(i,this.currentVariant.id);let a=i+"-"+this.currentVariant.id;if(this.cachedResults[a]){let e=this.cachedResults[a];this._updateQuickAdd(e),this._renderPriceProduct(e,i),this._renderProductAvailability(e),this._renderSkuProduct(e),this._renderProductBadges(e),this._renderInventoryStatus(e),this._renderInventoryQuantity(e),this._renderQuantityPricing(e),this._updateMedia(e),this._renderBuyButtons(e),this._setMessagePreOrder(e),this._setEstimateDelivery(e),this._setMetafieldInfo(e,".properties_re_render"),this._setMetafieldInfo(e,".table_info_details"),this._setMetafieldInfo(e,".block-text"),this._setMetafieldInfo(e,".text-icon"),this._setMetafieldInfo(e,".collapsible-content"),this._setMetafieldInfo(e,".nutrition-bar-content"),this._setMetafieldInfo(e,".horizontab"),this._setMetafieldInfo(e,".featured-icon"),this._setCartEstimateDelivery(e),this._setPreorderProperties(e),this._setBackInStockAlert(e),this._setPickupPreOrder(e),this.currentVariant.featured_media!=null,this._dispatchUpdateVariant(),this._dispatchVariantSelected(e),m||Alpine.store("xUpdateVariantQuanity").render(e,i)}else{let e=this.currentVariant.id,r=S?`${y}?variant=${e}&section_id=${this.handleSectionId}&page=${E||$}`:`${y}?variant=${e}&section_id=${this.handleSectionId}`;fetch(r).then(s=>s.text()).then(s=>{let n=new DOMParser().parseFromString(s,"text/html");this._updateQuickAdd(n),this.currentVariant&&e==this.currentVariant.id&&n.getElementById(`x-product-template-${g}-${i}`)?(this._renderPriceProduct(n,i),this._renderSkuProduct(n),this._renderProductBadges(n),this._renderInventoryStatus(n),this._renderInventoryQuantity(n),this._renderQuantityPricing(n),b?this._updateMedia(n):this.isChange&&this._updateMedia(n),this._renderBuyButtons(n),this._setMessagePreOrder(n),this._setEstimateDelivery(n),this._setMetafieldInfo(n,".properties_re_render"),this._setMetafieldInfo(n,".table_info_details"),this._setMetafieldInfo(n,".block-text"),this._setMetafieldInfo(n,".text-icon"),this._setMetafieldInfo(n,".collapsible-content"),this._setMetafieldInfo(n,".nutrition-bar-content"),this._setMetafieldInfo(n,".horizontab"),this._setMetafieldInfo(n,".featured-icon"),this._setPickupPreOrder(n),this._setCartEstimateDelivery(n),this._setPreorderProperties(n),this._setBackInStockAlert(n),this.currentVariant.featured_media!=null,m||Alpine.store("xUpdateVariantQuanity").render(n,i),this.cachedResults[a]=n,this._dispatchUpdateVariant(n),this._dispatchVariantSelected(n)):this.currentVariant&&e==this.currentVariant.id&&(this._renderPriceProduct(n,i),this._dispatchUpdateVariant(n))})}},_dispatchVariantSelected(t){document.dispatchEvent(new CustomEvent(`eurus:product-page-variant-select:updated:${i}`,{detail:{currentVariantStatus:this.currentVariant?.available,currentAvailableOptions:this.currentAvailableOptions,options:this.options,html:t}}))},_updateVariantStatuses(){let t=this.variants.filter(a=>this.options[0]===this._decodeOptionValue(a.option1));this.options.forEach((a,e)=>{if(this.currentAvailableOptions[e]=[],e===0)return;let r=this.options[e-1];t.forEach(s=>{s.available&&this._decodeOptionValue(s[`option${e}`])===r&&this.currentAvailableOptions[e].push(this._decodeOptionValue(s[`option${e+1}`]))})})},_decodeOptionValue(t){if(t)return t.replaceAll("\\/","/")},_renderInventoryStatus(t){let a=document.getElementById("block-inventory-"+i),e=t.getElementById("block-inventory-"+i);e&&a&&(a.innerHTML=e.innerHTML)},_renderInventoryQuantity(t){let a=document.getElementById("block-available-quantity-"+i),e=t.getElementById("block-available-quantity-"+i);e&&a&&(a.innerHTML=e.innerHTML)},_renderQuantityPricing(t){let a=document.getElementById("quantity-selector-"+i),e=document.getElementById("volume-"+i),r=t.getElementById("quantity-selector-"+i),s=t.getElementById("volume-"+i);a&&r&&(a.innerHTML=r.innerHTML),e&&s&&(e.innerHTML=s.innerHTML)},_updateQuickAdd(t){let a=document.querySelectorAll(`#product-form-choose-option${g}${$}`),e=t.querySelector(`#product-form-choose-option${g}${$}`);a.length>0&&e&&a.forEach(r=>{r.innerHTML=e.innerHTML;let s=r.closest(".card-product").querySelector(".main-product-price"),n=t.querySelector(".main-product-price");s&&n&&(s.innerHTML=n.innerHTML)})},_updateMedia(t){let a=document.getElementById("product-media-"+i)&&document.getElementById("product-media-"+i).dataset.mediaWithVariantSelected;if(!S&&!m&&!a){let e=document.getElementById("x-product-"+i),r="",s="",n="";if(this.currentVariant&&this.currentVariant.featured_media!=null?(r=document.getElementsByClassName(this.currentVariant.featured_media.id+"-"+i),s=parseInt(r[0]?.getAttribute("index")),n=document.getElementById("postion-image-"+i+"-"+this.currentVariant.featured_media.id)):(r=e.querySelector(".featured-image"),s=parseInt(r?.getAttribute("index")),n=document.querySelector(`#stacked-${i} .featured-image`)),e&&(e.splide&&r?e.splide.go(s):document.addEventListener(`eurus:media-gallery-ready:${i}`,()=>{e.splide&&e.splide.go(s)})),!n)return;if(t&&!a){let d=t.getElementById(`stacked-${i}`),p=document.getElementById(`stacked-${i}`);if(p&&d){let h=p.querySelectorAll("div[data-media-id]")[0];if(d.querySelectorAll("div[data-media-id]")[0].dataset.mediaId!=h.dataset.mediaId&&h.dataset.index!=1){let l=parseInt(h.dataset.index),_=p.querySelector(`div[data-media-id]:nth-of-type(${l+1})`);p.insertBefore(h,_)}p.prepend(n)}}}a&&this.updateMultiMediaWithVariant()},_updateColorSwatch(t){let a=document.querySelector(`#variant-update-${i}`).dataset.showSwatchWithVariantImage,e=document.querySelector(`#variant-update-${i} [data-swatch="true"]`);if(e&&a){let r=t.querySelector(`#variant-update-${i} [data-swatch="true"]`);r&&(e.innerHTML=r.innerHTML)}},_validateOption(){let t=document.querySelector(`#shopify-section-${i} [data-media-option]`);t&&(this.mediaOption=t.dataset.mediaOption.split("_"))},updateMultiMediaWithVariant(){if(this._validateOption(),!this.currentVariant){if(this.initfirstMedia){let a=document.querySelectorAll(`#ProductModal-${i} [data-media-option], #shopify-section-${i} [data-media-option]`);Array.from(a).reverse().forEach(function(e,r){e.classList.add("media_active"),e.classList.contains("media-slide")&&e.classList.add("splide__slide")})}return}let t=this.mediaOption.map(a=>document.querySelector(`#shopify-section-${i} [data-option-name="${a}"]`)).filter(a=>a!==null);if(t.length===0){let a="";this.currentVariant.featured_media?.id?a=document.querySelectorAll(`#ProductModal-${i} [data-media-option="${i}-${this.currentVariant.featured_media.id}"], #shopify-section-${i} [data-media-option="${i}-${this.currentVariant.featured_media.id}"]`):a=document.querySelectorAll(`#ProductModal-${i} [data-media-option].featured-image, #shopify-section-${i} [data-media-option].featured-image`);let e=document.querySelectorAll(`#ProductModal-${i} [data-media-option=""], #shopify-section-${i} [data-media-option=""]`),r=document.querySelectorAll(`#ProductModal-${i} [data-media-option], #shopify-section-${i} [data-media-option]`),s=Array.prototype.concat.call(...e,...a);this._setActiveMedia(r,s,a);let n=document.getElementById(`x-product-${i}`);n&&n.splide&&(n.splide.refresh(),n.splide.go(0));let d=document.getElementById(`media-gallery-${i}`);d&&d.splide&&d.splide.refresh()}else{let a=[];t.forEach(l=>{let _=l&&l.dataset.optionIndex,v=this._handleText(this.currentVariant.options[_]);this.mediaOption.includes(l.dataset.optionName)&&a.push(l.dataset.optionName+"-"+v),this.optionIndex=_});let e=document.querySelectorAll(`#ProductModal-${i} [data-media-type=""], #shopify-section-${i} [data-media-type=""]`),r=[];document.querySelectorAll(`#ProductModal-${i} [data-media-type]:not([data-media-type=""]), #shopify-section-${i} [data-media-type]:not([data-media-type=""])`).forEach(l=>{let _=l.getAttribute("data-media-type"),v=new Set(_.split("_"));a.filter(k=>v.has(k)).length===v.size&&r.push(l)});let n=!1;if(r.length||(this.currentVariant.featured_media?.id?r=document.querySelectorAll(`#ProductModal-${i} [data-media-id="${i}-${this.currentVariant.featured_media.id}"], #shopify-section-${i} [data-media-id="${i}-${this.currentVariant.featured_media.id}"]`):(r=document.querySelectorAll(`#ProductModal-${i} [data-media-option].featured-image, #shopify-section-${i} [data-media-option].featured-image`),n=!0)),!r.length){document.querySelectorAll(`#ProductModal-${i} [data-media-type], #shopify-section-${i} [data-media-type]`).forEach(function(v){v.classList.add("media_active"),v.classList.add("splide__slide")});let l=document.getElementById(`x-product-${i}`);l.splide&&(l.splide.refresh(),l.splide.go(0));let _=document.getElementById(`media-gallery-${i}`);_.splide&&_.splide.refresh();return}let d=Array.prototype.concat.call(...r,...e),p=document.querySelectorAll(`#shopify-section-${i} [data-media-type], #ProductModal-${i} [data-media-type]`);this._setActiveMedia(p,d),this.optionConnect!=a&&(this.optionConnect=a);let h=document.getElementById(`x-product-${i}`);h.splide&&(h.splide.refresh(),h.splide.go(0));let c=document.getElementById(`media-gallery-${i}`);c&&c.splide&&c.splide.refresh(),n&&this._goToFirstSlide()}},_setActiveMedia(t,a,e){if(t.forEach(function(r){r.classList.remove("media_active"),r.classList.remove("splide__slide"),r.classList.remove("x-thumbnail")}),Array.from(a).reverse().forEach(function(r,s){r.classList.add("media_active"),r.classList.contains("media-thumbnail")&&r.classList.add("x-thumbnail"),r.classList.contains("media-slide")&&r.classList.add("splide__slide");let n=r.parentElement;e?n.firstChild!=r&&Array.from(e).includes(r)&&n.prepend(r):n.firstChild!=r&&n.prepend(r)}),e){let r=e.parentElement;r&&r.prepend(e)}},_handleText(t){if(t)return t.toString().replace("\u0131","i").replace("\xDF","ss").normalize("NFC").replace("-"," ").toLowerCase().trim().replace(/[^\p{L}\p{N}\s-]/gu,"").replace(/\s+/g,"-")},_goToFirstSlide(){if(this.currentVariant&&!this.currentVariant.featured_image){let t=document.getElementById("x-product-"+i);t&&t.splide&&this.currentVariant&&this.currentVariant.featured_image!=null&&t.splide.go(0);let a=document.querySelector(`#stacked-${i} .featured-image`),e=document.getElementById("stacked-"+i);e&&a&&e.prepend(a)}},onChange(t,a,e=!1){this.isChange||(this.isChange=this.$el.parentNode.dataset.optionName);let r=this.variants.reduce((s,n)=>(n.featured_image&&(s[n.id]=n.featured_image.src),s),{});if(!e){let s=t.closest(".options-container"),n=s.querySelectorAll("label.color-watches"),d=s.querySelectorAll("input:checked"),p=[];d.forEach(c=>{[...n].some(l=>l.dataset.optionvalue===c.value)||p.push(c.value)});let h=this.variants.filter(c=>p.every(l=>c.options.includes(l))).map(c=>`url(${r[c.id]?r[c.id]:a})`);n.forEach((c,l)=>{c.style.setProperty("--bg-image",h[l])})}},_updateMasterId(){this.currentVariant=this.variants.find(t=>!t.options.map((a,e)=>this.options[e]===a.replaceAll("\\/","/")).includes(!1))},_updateVariantInput(){document.querySelectorAll(`#product-form-${i}, #product-form-installment-${i}, #product-form-sticky-${i}`).forEach(a=>{let e=a.querySelector('input[name="id"]');e&&(e.value=this.currentVariant.id,e.dispatchEvent(new Event("change",{bubbles:!0})))})},_updateProductForms(){document.querySelectorAll(`#product-form-${i}, #product-form-installment-${i}, #product-form-sticky-${i}`).forEach(a=>{let e=a.querySelector('input[name="id"]');e&&(e.value=this.currentVariant.id,e.dispatchEvent(new Event("change",{bubbles:!0})))})},_renderPriceProduct(t,a){let e=document.getElementById("price-"+a),r=t.getElementById("price-"+a);r||(r=t.querySelector(".price")),r&&e?e.innerHTML=r.innerHTML:console.warn("Cannot find source or destination element to update price")},_renderProductAvailability(t){let a=document.getElementById("x-availability-notice-"+i),e=t.getElementById("x-availability-notice-"+i);e&&a&&(a.innerHTML=e.innerHTML)},_renderSkuProduct(t){let a=document.getElementById("sku-"+i),e=t.getElementById("sku-"+i);e&&a&&(a.innerHTML=e.innerHTML)},_renderProductBadges(t){let a=document.getElementById("x-badges-"+i),e=t.getElementById("x-badges-"+i);e&&a&&(a.innerHTML+=e.innerHTML)},_renderBuyButtons(t){document.querySelectorAll(`#product-form-${i}, #product-form-installment-${i}, #product-form-sticky-${i}`).forEach(s=>{let n=t.querySelector(`#${s.getAttribute("id")} .add_to_cart_button`),d=s.querySelector(".add_to_cart_button");d&&(n&&d&&(d.innerHTML=n.innerHTML),this.currentVariant?.available?(d.dataset.available="true",t.getElementById("form-gift-card-"+i)&&document.getElementById("Recipient-checkbox-"+i).checked&&document.getElementById("recipient-form-"+i).dataset.disabled=="true"?d.setAttribute("disabled","disabled"):d.removeAttribute("disabled")):(d.dataset.available="false",d.setAttribute("disabled","disabled")))});let e=document.getElementById("x-payment-button-"+i),r=t.getElementById("x-payment-button-"+i);r&&e&&(r.classList.contains("hidden")?e.classList.add("hidden"):e.classList.remove("hidden"))},_setMessagePreOrder(t){let a=document.querySelector(`.pre-order-${i}`);if(!a)return;a.classList.add("hidden");let e=t.getElementById(`pre-order-${i}`);e&&(a.classList.remove("hidden"),a.innerHTML=e.innerHTML)},_setEstimateDelivery(t){let a=document.getElementById(`x-estimate-delivery-${i}`);if(!a)return;let e=t.getElementById(`x-estimate-delivery-${i}`);e.classList.contains("disable-estimate")?a.classList.add("hidden"):(a.classList.remove("hidden"),a.innerHTML=e.innerHTML);let r=document.querySelectorAll(`.cart-edt-${i}`),s=t.querySelectorAll(`.cart-edt-${i}`);r.length>0&&s.length>0&&r.forEach((n,d)=>{s[d]!=null&&s[d].innerHTML!=null&&(n.innerHTML=s[d].innerHTML)})},_setMetafieldInfo(t,a){let e=document.querySelectorAll(`${a}-${i}`),r=t.querySelectorAll(`${a}-${i}`);e.length>0&&r.length>0&&e.forEach((s,n)=>{s.innerHTML=r[n].innerHTML})},_setPreorderProperties(t){let a=document.getElementById(`preorder-${i}`),e=t.getElementById(`preorder-${i}`);a&&e&&(a.innerHTML=e.innerHTML)},_setCartEstimateDelivery(t){let a=document.getElementById(`cart-edt-${i}`),e=t.getElementById(`cart-edt-${i}`);a&&e&&(a.innerHTML=e.innerHTML)},_setBackInStockAlert(t){if(!this.initialized){this.initialized=!0;return}let a=document.getElementById(`back_in_stock_alert-${i}`),e=t.getElementById(`back_in_stock_alert-${i}`);e&&a&&(a.innerHTML=e.innerHTML)},_setPickupPreOrder(t){let a=document.getElementById(`pickup-pre-order-${i}`);if(!a)return;t.getElementById(`pickup-pre-order-${i}`).classList.contains("disable-pickup")?a.classList.add("hidden"):a.classList.remove("hidden")},_setUnavailable(){let t=document.getElementById("price-"+i);t&&t.classList.add("hidden");let a=document.getElementById(`price-sticky-${i}`);a&&a.classList.add("hidden");let e=document.getElementById("block-inventory-"+i);e&&e.classList.add("hidden");let r=document.getElementById("x-badges-"+i);r&&r.classList.add("hidden");let s=document.getElementById("pickup-"+i);s&&s.classList.add("hidden");let n=document.getElementById("x-quantity-"+i);n&&n.classList.add("unavailable");let d=document.querySelector(`.pre-order-${i}`);d&&d.classList.add("hidden");let p=document.getElementById("sku-"+i);p&&p.classList.add("hidden");let h=document.getElementById(`back_in_stock_alert-${i}`);h&&h.classList.add("hidden"),this._setBuyButtonUnavailable()},_setAvailable(){let t=document.getElementById("price-"+i);t&&t.classList.remove("hidden");let a=document.getElementById("block-inventory-"+i);a&&a.classList.remove("hidden");let e=document.getElementById("x-badges-"+i);e&&e.classList.remove("hidden");let r=document.getElementById("pickup-"+i);r&&r.classList.remove("hidden");let s=document.getElementById("x-quantity-"+i);s&&s.classList.remove("unavailable");let n=document.getElementById("sku-"+i);n&&n.classList.remove("hidden");let d=document.getElementById(`back_in_stock_alert-${i}`);d&&d.classList.remove("hidden")},_setBuyButtonUnavailable(){document.querySelectorAll(`#product-form-${i},  #product-form-sticky-${i}`).forEach(a=>{let e=a.querySelector(".add_to_cart_button");if(!e)return;e.setAttribute("disabled","disabled");let r=e.querySelector(".x-atc-text");r&&(r.textContent=f)})},_dispatchUpdateVariant(t=""){document.dispatchEvent(new CustomEvent(`eurus:product-card-variant-select:updated:${i}`,{detail:{currentVariant:this.currentVariant,currentAvailableOptions:this.currentAvailableOptions,options:this.options,html:t}}))},_updateImageVariant(t=""){if(this.currentVariant!=null){let a=t;this.currentVariant.featured_image!=null&&(a=this.currentVariant.featured_image.src),Alpine.store("xPreviewColorSwatch").updateImage(this.$el,y,a,this.currentVariant.id,i)}},_updateOptionImage(){if(o.closest(".card-product")&&this.currentVariant&&this.currentVariant.featured_image){let t=this.currentVariant.featured_image.src,a=this.options.map(e=>o.querySelector(`label.color-watches[data-name*="${e.replace(/["\\]/g,"\\$&")}"]`)).find(e=>e!==null);a&&a.style.setProperty("--bg-image",`url(${t})`)}},initEventSticky(){document.addEventListener(`eurus:product-page-variant-select-sticky:updated:${i}`,t=>{this.handleSticky=t.detail.variantElSticky,this.updateVariantSelector(t.detail.inputId,t.detail.targetUrl)})},changeSelectOption(t){Array.from(t.target.options).find(s=>s.getAttribute("selected")).removeAttribute("selected"),t.target.selectedOptions[0].setAttribute("selected","selected");let a=t.target.selectedOptions[0],e=a.id,r=a.dataset.productUrl;this.updateVariantSelector(e,r)},updateVariantSelector(t,a){S&&(this.handleSectionId="choose-option"),m&&(this.handleSectionId=V),this.currentVariant=this._getVariantData(t);let e=!1,r=()=>{},s=a||o.dataset.url;o.dataset.url!==s?(this._updateURL(s),this._setAvailable(),u&&(e=!0),r=n=>{this._handleSwapProduct(i,n,e),this._handleSwapQuickAdd(n),this._renderCardBundle(n),this._renderCardFBT(n),this._dispatchUpdateVariant(n)}):this.currentVariant?(this._updateURL(s),this._updateVariantInput(),this._setAvailable(),r=n=>{this._handleUpdateProductInfo(n),this._updateOptionValues(n),this._updateMedia(n),this._handleAvailable(n)}):(this._setUnavailable(),r=n=>{this._updateOptionValues(n),this._dispatchVariantSelected(n),this._dispatchUpdateVariant(n)}),this._renderProductInfo(s,r,e)},_renderProductInfo(t,a,e){let r="",s=`option_values=${this._getSelectedOptionValues().join(",")}`;if((S||m)&&(s=`option_values=${this._getSelectedOptionValues().join(",")}&page=${E}`),r=e?`${t}?${s}`:`${t}?section_id=${this.handleSectionId}&${s}`,this.cachedResults[r]){let n=this.cachedResults[r];a(n)}else fetch(r).then(n=>n.text()).then(n=>{let d=new DOMParser().parseFromString(n,"text/html");a(d),this.cachedResults[r]=d});this.handleSticky=""},_handleUpdateProductInfo(t){this._renderCardBundle(t),this._renderCardFBT(t),this._renderPriceProduct(t,i),this._renderProductBadges(t),this._renderInventoryStatus(t),this._renderInventoryQuantity(t),this._renderQuantityPricing(t),this._renderSkuProduct(t),this._renderBuyButtons(t),this._setMessagePreOrder(t),this._setEstimateDelivery(t),this._setMetafieldInfo(t,".properties_re_render"),this._setMetafieldInfo(t,".table_info_details"),this._setMetafieldInfo(t,".block-text"),this._setMetafieldInfo(t,".text-icon"),this._setMetafieldInfo(t,".collapsible-content"),this._setMetafieldInfo(t,".nutrition-bar-content"),this._setMetafieldInfo(t,".horizontab"),this._setMetafieldInfo(t,".featured-icon"),this._setPickupPreOrder(t),this._setCartEstimateDelivery(t),this._setPreorderProperties(t),this._setBackInStockAlert(t),m||Alpine.store("xUpdateVariantQuanity").render(t,this.handleSectionId),this._dispatchUpdateVariant(t),this._dispatchVariantSelected(t),this._updateOptionValues(t),Alpine.store("xPickupAvailable").updatePickUp(i,this.currentVariant.id)},initFirstAvailableVariant(t){this.currentVariant=JSON.parse(t.querySelector('script[type="application/json"][data-selected-variant]').textContent),m||document.addEventListener("eurus:cart:items-changed",()=>{this.cachedResults=[],Alpine.store("xUpdateVariantQuanity").updateQuantity(i,y,this.currentVariant?.id)})},_handleAvailable(t){t.querySelector(".variant-selects [data-selected-variant]")?.innerHTML=="null"&&this._setUnavailable()},_updateOptionValues(t){if(!m){let a=t.querySelector(".variant-selects");a&&(o.innerHTML=a.innerHTML)}},_getVariantData(t){return JSON.parse(this._getVariantDataElement(t).textContent)},_getVariantDataElement(t){return o.querySelector(`script[type="application/json"][data-resource="${t}"]`)},_updateURL(t){u&&window.history.replaceState({},"",`${t}${this.currentVariant?.id?`?variant=${this.currentVariant.id}`:""}`)},_getSelectedOptionValues(){return this.handleSticky==""?Array.from(o.querySelectorAll("select option[selected], fieldset input:checked")).map(t=>t.dataset.optionValueId):Array.from(this.handleSticky.querySelectorAll("select option[selected]")).map(t=>t.dataset.optionValueId)},_renderCardBundle(t){let a=o.closest(".x-product-bundle-data"),e=t.getElementById("card-product-bundle-"+this.handleSectionId);if(e){let r=e.querySelector(".x-product-bundle-data");r&&a&&(a.innerHTML=r.innerHTML)}},_renderCardFBT(t){let a=o.closest(".card-product-fbt"),e=t.querySelector(".card-product-fbt-clone .card-product-fbt");e&&a&&(a.innerHTML=e.innerHTML)},_handleSwapProduct(t,a,e){if(e){document.querySelector("head title").innerHTML=a.querySelector("head title").innerHTML;let r=document.querySelector("main"),s=a.querySelector("main");s&&r&&(r.innerHTML=s.innerHTML)}else{let r=document.querySelector(".x-product-"+t),s=a.querySelector(".x-product-"+t);s&&r&&(r.innerHTML=s.innerHTML)}},_handleSwapQuickAdd(t){let a=o.closest(".choose-options-content"),e=t.querySelector(".choose-options-content");e&&a&&(a.innerHTML=e.innerHTML)}});window.otsb.loadedScript.includes("otsb-variant-select")||(window.otsb.loadedScript.push("otsb-variant-select"),document.addEventListener("alpine:init",()=>{Alpine.data("xVariantSelect",A),Alpine.store("xPickupAvailable",{updatePickUp(o,i){let u=document.getElementsByClassName("pick-up-"+o)[0];u&&fetch(window.Shopify.routes.root+`variants/${i}/?section_id=pickup-availability`).then(f=>f.text()).then(f=>{let y=new DOMParser().parseFromString(f,"text/html").querySelector(".shopify-section");u.innerHTML=y.innerHTML}).catch(f=>{console.error(f)})}}),Alpine.store("xUpdateVariantQuanity",{updateQuantity(o,i,u){if(!document.getElementById("x-quantity-"+o))return;let y=u?`${i}?variant=${u}&section_id=${o}`:`${i}?section_id=${o}`;fetch(y).then(g=>g.text()).then(g=>{let b=new DOMParser().parseFromString(g,"text/html");this.render(b,o)})},render(o,i){let u=document.getElementById("x-quantity-"+i),f=o.getElementById("x-quantity-"+i);f&&u&&(u.innerHTML=f.innerHTML)}})}));})();
