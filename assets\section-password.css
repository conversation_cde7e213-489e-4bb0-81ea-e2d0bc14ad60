/* Base */
*,
*::before,
*::after {
  box-sizing: inherit;
}
html {
  box-sizing: border-box;
  font-size: calc(var(--font-body-scale) * 62.5%);
}
body {
  background-color: rgb(var(--color-background));
  color: rgb(var(--color-foreground));
  font-size: 1.5rem;
  letter-spacing: 0.07rem;
  line-height: calc(1 + 0.8 / var(--font-body-scale));
  margin: 0;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: var(--font-body-family);
  font-style: var(--font-body-style);
  font-weight: var(--font-body-weight);
}
@media screen and (min-width: 750px) {
  body {
    font-size: 1.6rem;
    line-height: calc(1 + 0.8 / var(--font-body-scale));
  }
}
.password-modal__content-heading {
  font-size: 1.8rem;
  font-weight: 400;
  line-height: calc(1 + 0.6 / var(--font-body-scale));
}
@media only screen and (min-width: 750px) {
  .password-modal__content-heading {
    font-size: 1.8rem;
  }
}
/* Password Section */
.full-height {
  height: 100%;
}
.password {
  background-color: rgb(var(--color-background));
  height: 100%;
}
.password-link {
  align-items: center;
  font-size: 1.4rem;
  font-weight: 400;
  white-space: nowrap;
}
.password-link svg {
  width: 1.8rem;
  height: 1.8rem;
  margin-right: 1rem;
}
.password-modal__content {
  padding: 4.5rem 3.2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}
.password-modal__content-heading {
  font-size: 1.8rem;
  font-weight: 400;
  line-height: calc(1 + 0.6 / var(--font-body-scale));
}
@media only screen and (min-width: 750px) {
  .password-modal__content-heading {
    font-size: 1.8rem;
  }
}
.password-modal .password-form {
  max-width: 50rem;
}
.password-form {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-top: 4rem;
  margin-bottom: 2rem;
  width: 100%;
}
.password-field.field {
  flex: 1 20rem;
}
.password-field .form__message {
  margin-top: 1.5rem;
}
.password-button {
  margin-top: 3rem;
  width: 100%;
}
@media only screen and (max-width: 749px) {
  .password-field--error + .password-button {
    margin-top: 1.5rem;
  }
}
@media only screen and (min-width: 750px) {
  .password-button {
    margin-top: 0;
    margin-left: 2rem;
    width: auto;
    align-self: start;
  }
}
.password-logo {
  width: 100%;
  margin-bottom: 1.5rem;
}
@media only screen and (min-width: 750px) {
  .password-logo {
    margin-bottom: 0;
  }
}
.password-heading {
  margin-top: 5rem;
  font-weight: 400;
}
.password-main {
  flex-grow: 1;
}
.password-main > section:only-child {
  height: 100%;
}
.password-main > section:only-child > .newsletter {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  margin-top: 0;
}
.password-main > section:only-child .newsletter__wrapper:not(.email-signup-banner__box) {
  width: 100%;
}
.password-main > section:only-child > :not(.newsletter--narrow) > .newsletter__wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.password__footer-text a {
  padding: 0;
  font-size: 1.3rem;
  font-weight: 400;
  line-height: calc(1 + 0.5 / var(--font-body-scale));
}
.password__footer-login {
  margin-top: 1.2rem;
  padding-bottom: 4rem;
}
.password-modal .icon-close {
  color: rgb(var(--color-foreground));
}
.password__footer {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 4rem;
  background-color: rgb(var(--color-background));
  color: rgb(var(--color-foreground));
}
hr {
  margin: 0 !important;
}
.list-social:not(:empty) + .password__footer-caption {
  margin-top: 2rem;
}
.password__footer-caption a {
  padding: 0;
  color: rgb(var(--color-link));
}
.modal__toggle,
.modal__close-button {
  list-style-type: none;
}
details[open] .modal__toggle,
.modal__close-button {
  position: absolute;
  top: 2.2rem;
  right: 2.2rem;
  padding: 0.8rem;
  color: rgb(var(--color-foreground));
  background-color: transparent;
}
.no-js .modal__close-button {
  display: none;
}
.no-js .modal__toggle {
  z-index: 2;
}
.modal__toggle::-webkit-details-marker {
  display: none;
}
details.modal .modal__toggle-close {
  display: none;
}
details[open].modal .modal__toggle-close {
  background: rgb(var(--color-background));
  cursor: pointer;
  display: flex;
  padding: 0.8rem;
  z-index: 1;
}
details[open].modal .modal__toggle-close svg,
.modal__close-button svg {
  height: 1.7rem;
  width: 1.7rem;
}
details[open].modal .modal__toggle-close:hover {
  opacity: 0.75;
}
.js details[open].modal .modal__toggle-close {
  display: none;
}
details.modal .modal__toggle-open {
  display: flex;
}
.no-js details[open].modal .modal__toggle-open {
  display: none;
}
.password-header {
  padding: 2rem 1.5rem 2.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: column;
  color: rgb(var(--color-foreground));
  max-width: var(--page-width);
  margin: 0 auto;
  text-align: center;
}
@media only screen and (min-width: 750px) {
  .password-header {
    display: grid;
    gap: 3rem;
    grid-template-columns: 1fr 1.5fr 1fr;
    padding: 2rem 5rem 2.5rem;
    text-align: left;
  }
}
.password-header details-modal {
  flex-shrink: 0;
}
.password-content {
  text-align: center;
}
@media only screen and (max-width: 749px) {
  .password-content {
    margin-bottom: 1.8rem;
    margin-top: 1rem;
  }
}
.shopify-name {
  overflow: hidden;
  position: absolute;
  height: 1px;
  width: 1px;
}
.icon-shopify {
  width: 7rem;
  height: 2rem;
  vertical-align: top;
  color: rgb(var(--color-foreground));
}
password-modal {
  justify-self: flex-end;
  grid-column: 3;
}
.password-main .email-signup-banner__box .newsletter-form__field-wrapper .field__input,
.password-main .email-signup-banner__box .newsletter-form__button{height:5rem;border-radius: 30px;}
.modal__toggle-open.password-link.link.underlined-link:hover {
    color: rgb(var(--color-base-solid-button-labels));
}
.password__footer-text a:hover{color:rgb(var(--color-base-solid-button-labels));}
.password__footer li.list-social__item {
    padding: 10px;
}
details.password-modal.modal {
    padding: 10px 18px;
    display: inline-block;
    background: rgb(var(--color-base-solid-button-labels));
    color: #fff;
  border: 1px solid rgb(var(--color-base-solid-button-labels));
  border-radius: 30px;
  transition: all 0.3s linear;
}
details.password-modal.modal .modal__toggle-open{ color:#fff; text-decoration: none; line-height: normal;}
details.password-modal.modal:hover {
    background: var(--gradient-base-background-1);
    border: 1px solid rgb(var(--color-base-solid-button-labels));
}
details.password-modal.modal:hover .modal__toggle-open{ color:rgb(var(--color-base-solid-button-labels));}
.email-signup-banner .newsletter-form__button{    background: rgb(var(--color-base-solid-button-labels)); color:var(--gradient-background);}
.email-signup-banner .banner__content{height:unset;}
.email-signup-banner  .banner--desktop-transparent .email-signup-banner__box .field__input{background:var(--gradient-background);}
small.password__footer-text{color: #fff;}
.password__footer .list-social li.list-social__item, .password__footer .list-social li.list-social__item a { padding: 0;}
.password__footer .list-social { gap: 2rem; margin-top: 0;}
.password-field.field.password-field--error{display:block;}