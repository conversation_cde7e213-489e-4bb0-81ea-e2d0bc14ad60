{{ 'section-main-product.css' | asset_url | stylesheet_tag }}
{{ 'section-featured-product.css' | asset_url | stylesheet_tag }}
{{ 'component-accordion.css' | asset_url | stylesheet_tag }}
{{ 'component-price.css' | asset_url | stylesheet_tag }}
{{ 'component-rte.css' | asset_url | stylesheet_tag }}
{{ 'component-loading-overlay.css' | asset_url | stylesheet_tag }}
{{ 'slick.min.css' | asset_url | stylesheet_tag }}
{{ 'component-slider.css' | asset_url | stylesheet_tag }}
<script src="{{ 'slick.min.js' | asset_url }}" async></script>
{%- style -%}
    .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.50 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.50 | round: 0 }}px;
  }
   @media screen and (min-width: 576px) and (max-width: 749px){
   .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }
   }
  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }

  .product__info-wrapper variant-selects .product-form__input .form__label{font-size:16px;font-weight:400;margin-right:6px;margin-bottom:0;} 
    .product__info-wrapper variant-selects .product-form__input .form__label:after{content:':';margin:0 5px;}  
    .product__info-wrapper .product__info-container .product-form__input.product-form__input--dropdown{display:flex;margin-bottom: 2rem;}
    /* .product__info-wrapper .product-form__input.product-form__input--dropdown:not(:first-child){margin-bottom:3rem;}     */
     .product__info-container .product__sku .product-label,
     .product__info-container .product_vendor .product-label,
     .product__info-container .product_type .product-label{font-size:14px;min-width:auto;margin:0;font-weight:400;position:relative;color: var(--gradient-base-accent-2);}
     .product__info-container .product__sku .product-attributes-value, 
    .product__info-container .product_vendor .product-attributes-value, 
    .product__info-container .product_type .product-attributes-value, 
    .product__info-container .product_vendor .product-attributes-value a {
    font-size: 14px;color: var(--gradient-base-accent-2);}
    .product__info-container .sub-total p.product-label:after, .product__info-container .inventory-form__label .form__label:after, .advance-product-style .advanced-title:after,  .product__info-container .product-label:after {
    content: ":";
    margin: 0 5px;
}
  .product__info-container fieldset.product-form__input .form__label {margin-bottom: 3px;min-width: max-content;}
  .product__info-container fieldset.product-form__input .swatch-label-text .form__label{font-size: 16px;font-weight: 500;color: var(--gradient-base-accent-2);}
 .product__info-container fieldset.product-form__input .form__label span { color: var(--gradient-base-accent-1);}  
  .product__info-container .product-form__input {margin: 0 0 20px;}
  .product-form__input input[type=radio]:disabled+label, .product-form__input input[type=radio].disabled+label {
    border-color: rgba(var(--color-foreground),.1);
    color: rgba(var(--color-foreground),.6);
    text-decoration: line-through;
}

 .product__info-wrapper .dT_VProdWishList a.add-wishlist:after { content:"{{ 'products.wishlist.addToWishList' | t }}"; position:relative; left: 25px; }
        .product__info-wrapper .dT_VProdWishList a.add-wishlist.adding:after { content:"{{ 'products.wishlist.addingWihlist' | t }}"; }
        .product__info-wrapper .dT_VProdWishList a.add-wishlist.added:after { content:"{{ 'products.wishlist.viewMyWishList' | t }}"; }
        .product__info-wrapper .dT_VProdWishList  a.dt-sc-btn.add-wishlist {    display: block;    }

        .product__info-wrapper .dT_VProdCompareList a.add-compare:after {content:"{{ 'products.compare.add_to_compare' | t }}";  position:relative; left: 25px; }
        .product__info-wrapper .dT_VProdCompareList a.add-compare.adding:after { content:"{{ 'products.compare.add_to_compare' | t }}"; }
        .product__info-wrapper .dT_VProdCompareList a.add-compare.added:after { content:"{{ 'products.compare.view_compare' | t }}"; }
        .product__info-wrapper .dT_VProdCompareList  a.dt-sc-btn.add-compare {    display: block;    }
         button.slick-prev.slick-arrow, button.slick-next.slick-arrow {
          width: 78px;
          height: 30px;
          background: var(--gradient-base-accent-2);
          border: none;
          transition: all 0.3s linear;
          cursor: pointer;
          margin-left: 0;
       }
        a.slick-prev.pull-left:before{ 
          content: "";
          color: currentColor;
          position: absolute;
          width: 13px;
          height: 13px;
          border-right: 2px solid currentColor;
          border-bottom: 2px solid currentColor;
          transform: rotate(225deg);
          left: 0;
          right: 0;
          margin: auto;
          top: 17px;  
          cursor: pointer;
        }
        a.slick-next.pull-right:before{
           content: "";
          color: currentColor;
          position: absolute;
          width: 13px;
          height: 13px;
          border-right: 2px solid currentColor;
          border-bottom: 2px solid currentColor;
          transform: rotate(45deg);
          bottom: 11px;
          left: 0;
          right: 0;
          margin: auto;
        }
        button.slick-next.slick-arrow a.slick-next.pull-right, button.slick-prev.slick-arrow a.slick-next.pull-right { cursor: pointer;}
        .product--thumbnail_slider_left .thumbnail-slider .thumbnail-list.slider--tablet-up:not(:hover) .slick-arrow {
        opacity: 0;
        transition: all 0.3s linear;
}
        .main-product_info .thumbnail-slider .thumbnail-list.slider--tablet-up:hover button.slick-prev.slick-arrow:hover,
        .main-product_info .thumbnail-slider .thumbnail-list.slider--tablet-up:hover button.slick-next.slick-arrow:hover{ background: var(--gradient-base-background-2); color: var(--gradient-base-accent-2);}
  
  

  
  {%- endstyle -%}

<link rel="stylesheet" href="{{ 'component-deferred-media.css' | asset_url }}" media="print" onload="this.media='all'">

{%- assign product = section.settings.product -%}
{%- if section.settings.gallery_layout == 'thumbnail_slider' %}
        {%- assign thumb_carousel_layout = true -%}
      {%- endif -%}
      {%- if section.settings.thumb_carousel_layout == 'thumbnail_slider_left' or section.settings.thumb_carousel_layout == 'thumbnail_slider_right' %}
        {%- assign thumb_slick_layout = true -%}
      {%- endif -%}


{%- assign first_3d_model = product.media | where: "media_type", "model" | first -%}
{%- if first_3d_model -%}
  {{ 'component-product-model.css' | asset_url | stylesheet_tag }}
  <link id="ModelViewerStyle" rel="stylesheet" href="https://cdn.shopify.com/shopifycloud/model-viewer-ui/assets/v1.0/model-viewer-ui.css" media="print" onload="this.media='all'">
  <link id="ModelViewerOverride" rel="stylesheet" href="{{ 'component-model-viewer-ui.css' | asset_url }}" media="print" onload="this.media='all'">
{%- endif -%}

<section class="color-{{ section.settings.color_scheme }} {% if section.settings.secondary_background %}background-secondary{% else %}gradient{% endif %}">
<div class="{% if section.settings.page_full_width %} page-full-width {% else %} page-width {% endif %} {% if section.settings.page_full_width_spacing %} page-full-width_spacing {% endif %}section-{{ section.id }}-padding isolate">
  <div class="row">
  <div class="{% if section.settings.secondary_background %} isolate{% endif %}">
    <div class="featured-product product  product--{{ section.settings.gallery_layout }}  {%  if thumb_carousel_layout %}{% if thumb_slick_layout %} media-slick-slider{% endif %} product--{{ section.settings.thumb_carousel_layout }}{% endif %} grid grid--1-col gradient color-{{ section.settings.color_scheme }}{% if section.settings.secondary_background == false %} isolate{% endif %} {% if product.media.size > 0 %}grid--2-col-tablet{% else %}product--no-media{% endif %}">
      <div class="grid__item product__media-wrapper">
         <media-gallery
              id="MediaGallery-{{ section.id }}"
              role="region"              
              class="product__media-gallery product__info-container--sticky"              
              aria-label="{{ 'products.product.media.gallery_viewer' | t }}"
              data-desktop-layout="thumbnail_slider"
            >
              <div id="GalleryStatus-{{ section.id }}" class="visually-hidden" role="status"></div>
              <slider-component id="GalleryViewer-{{ section.id }}" class="slider-mobile-gutter">
                <a
                  class="skip-to-content-link button visually-hidden quick-add-hidden"
                  href="#ProductInfo-{{ section.id }}"
                >
                  {{ 'accessibility.skip_to_product_info' | t }}
                </a>
          {% if product.metafields.custom.custom_tag != blank %}
                  <div class="ribbon">{{ product.metafields.custom.custom_tag }}</div>
                  {% endif %}
                <ul
                  id="Slider-Gallery-{{ section.id }}"
                  class="product__media-list contains-media grid grid--peek list-unstyled slider slider--mobile {{ section.settings.stacked_column_alignment }}-column"
                  role="list"
                >
                  {%- liquid
                    assign variant_images = product.images | where: 'attached_to_variant?', true | map: 'src'
                    assign media_count = product.media.size
                    if section.settings.hide_variants
                      assign media_count = media_count | minus: variant_images.size | plus: 1
                    endif

                    if section.settings.media_size == 'large'
                      assign media_width = 0.65
                    elsif section.settings.media_size == 'medium'
                      assign media_width = 0.55
                    elsif section.settings.media_size == 'small'
                      assign media_width = 0.45
                    endif
                  -%}
                  {%- if product.selected_or_first_available_variant.featured_media != null -%}
                    {%- assign featured_media = product.selected_or_first_available_variant.featured_media -%}
                    <li
                      id="Slide-{{ section.id }}-{{ featured_media.id }}"
                      class="product__media-item grid__item slider__slide is-active{% if featured_media.media_type != 'image' %} product__media-item--full{% endif %}{% if section.settings.hide_variants and variant_images contains featured_media.src %} product__media-item--variant{% endif %}"
                      data-media-id="{{ section.id }}-{{ featured_media.id }}"
                    >
                      {%- assign media_position = 1 -%}
                      {% render 'product-thumbnail',
                        media: featured_media,
                        position: media_position,
                        loop: section.settings.enable_video_looping,
                        modal_id: section.id,
                        xr_button: true,
                        media_width: media_width,
                        lazy_load: false
                      %}
                    </li>
                  {%- endif -%}
                  {%- for media in product.media -%}
                    {%- unless media.id == product.selected_or_first_available_variant.featured_media.id -%}
                      <li
                        id="Slide-{{ section.id }}-{{ media.id }}"
                        class="product__media-item grid__item slider__slide{% if product.selected_or_first_available_variant.featured_media == null and forloop.index == 1 %} is-active{% endif %}{% if media.media_type != 'image' %} product__media-item--full{% endif %}{% if section.settings.hide_variants and variant_images contains media.src %} product__media-item--variant{% endif %}"
                        data-media-id="{{ section.id }}-{{ media.id }}"
                      >
                        {%- assign media_position = media_position | default: 0 | plus: 1 -%}
                        {% render 'product-thumbnail',
                          media: media,
                          position: media_position,
                          loop: section.settings.enable_video_looping,
                          modal_id: section.id,
                          xr_button: true,
                          media_width: media_width,
                          lazy_load: true
                        %}
                      </li>
                    {%- endunless -%}
                  {%- endfor -%}
                </ul>
                <div class="slider-buttons no-js-hidden quick-add-hidden{% if media_count < 2 or section.settings.mobile_thumbnails == 'show' %} small-hide{% endif %} small-hide">
                  <button
                    type="button"
                    class="slider-button slider-button--prev"
                    name="previous"
                    aria-label="{{ 'general.slider.previous_slide' | t }}"
                  >
                    {% render 'icon-caret' %}
                  </button>
                  <div class="slider-counter caption">
                    <span class="slider-counter--current">1</span>
                    <span aria-hidden="true"> / </span>
                    <span class="visually-hidden">{{ 'general.slider.of' | t }}</span>
                    <span class="slider-counter--total">{{ media_count }}</span>
                  </div>
                  <button
                    type="button"
                    class="slider-button slider-button--next"
                    name="next"
                    aria-label="{{ 'general.slider.next_slide' | t }}"
                  >
                    {% render 'icon-caret' %}
                  </button>
                </div>
              </slider-component>
              {%- if first_3d_model -%}
                <button
                  class="button button--full-width product__xr-button"
                  type="button"
                  aria-label="{{ 'products.product.xr_button_label' | t }}"
                  data-shopify-xr
                  data-shopify-model3d-id="{{ first_3d_model.id }}"
                  data-shopify-title="{{ product.title | escape }}"
                  data-shopify-xr-hidden
                >
                  {% render 'icon-3d-model' %}
                  {{ 'products.product.xr_button' | t }}
                </button>
              {%- endif -%}
              {%- if media_count > 1
                and section.settings.gallery_layout != 'stacked'
                or section.settings.mobile_thumbnails == 'show'
              -%}
                <slider-component
                  id="GalleryThumbnails-{{ section.id }}"
                  class="slider-vertical thumbnail-slider slider-mobile-gutter quick-add-hidden{% if section.settings.mobile_thumbnails == 'hide' %} small-hide{% endif %}{% if media_count <= 3 %} thumbnail-slider--no-slide{% endif %}"
                >
                  <button
                    type="button"
                    class="slider-button slider-button--prev{% if media_count <= 3 %} small-hide{% endif %}{% if media_count <= 4 %} medium-hide large-up-hide{% endif %}"
                    name="previous"
                    aria-label="{{ 'general.slider.previous_slide' | t }}"
                    aria-controls="GalleryThumbnails-{{ section.id }}"
                    {% unless section.settings.slider_position == 'bottom' %}
                      data-step="1"
                    {% else %}
                      data-step="3"
                    {% endunless %}
                  >
                    {% render 'icon-caret' %}
                  </button>
                  <ul
                    id="Slider-Thumbnails-{{ section.id }} slider-vert-height"
                    class="thumbnail-list list-unstyled slider slider--mobile{% if section.settings.gallery_layout == 'thumbnail_slider' %} slider--tablet-up{% endif %}"
                  >
                    {%- if featured_media != null -%}
                      {%- liquid
                        capture media_index
                          if featured_media.media_type == 'model'
                            increment model_index
                          elsif featured_media.media_type == 'video' or featured_media.media_type == 'external_video'
                            increment video_index
                          elsif featured_media.media_type == 'image'
                            increment image_index
                          endif
                        endcapture
                        assign media_index = media_index | plus: 1
                      -%}
                      <li
                        id="Slide-Thumbnails-{{ section.id }}-0"
                        class="thumbnail-list__item slider__slide{% if section.settings.hide_variants and variant_images contains featured_media.src %} thumbnail-list_item--variant{% endif %}"
                        data-target="{{ section.id }}-{{ featured_media.id }}"
                        data-media-position="{{ media_index }}"
                      >
                        <button
                          class="thumbnail global-media-settings global-media-settings--no-shadow {% if featured_media.preview_image.aspect_ratio > 1 %}thumbnail--wide{% else %}thumbnail--narrow{% endif %}"
                          aria-label="{%- if featured_media.media_type == 'image' -%}{{ 'products.product.media.load_image' | t: index: media_index }}{%- elsif featured_media.media_type == 'model' -%}{{ 'products.product.media.load_model' | t: index: media_index }}{%- elsif featured_media.media_type == 'video' or featured_media.media_type == 'external_video' -%}{{ 'products.product.media.load_video' | t: index: media_index }}{%- endif -%}"
                          aria-current="true"
                          aria-controls="GalleryViewer-{{ section.id }}"
                          aria-describedby="Thumbnail-{{ section.id }}-0"
                        >
                          <img
                            id="Thumbnail-{{ section.id }}-0"
                            srcset="
                              {% if featured_media.preview_image.width >= 54 %}{{ featured_media.preview_image | image_url: width: 54 }} 54w,{% endif %}
                              {% if featured_media.preview_image.width >= 74 %}{{ featured_media.preview_image | image_url: width: 74 }} 74w,{% endif %}
                              {% if featured_media.preview_image.width >= 104 %}{{ featured_media.preview_image | image_url: width: 104 }} 104w,{% endif %}
                              {% if featured_media.preview_image.width >= 162 %}{{ featured_media.preview_image | image_url: width: 162 }} 162w,{% endif %}
                              {% if featured_media.preview_image.width >= 208 %}{{ featured_media.preview_image | image_url: width: 208 }} 208w,{% endif %}
                              {% if featured_media.preview_image.width >= 324 %}{{ featured_media.preview_image | image_url: width: 324 }} 324w,{% endif %}
                              {% if featured_media.preview_image.width >= 416 %}{{ featured_media.preview_image | image_url: width: 416 }} 416w,{% endif %},
                              {{ featured_media.preview_image | image_url }} {{ media.preview_image.width }}w
                            "
                            src="{{ featured_media | image_url: width: 416 }}"
                            sizes="(min-width: {{ settings.page_width }}px) calc(({{ settings.page_width | minus: 100 | times: media_width | round }} - 4rem) / 4), (min-width: 990px) calc(({{ media_width | times: 100 }}vw - 4rem) / 4), (min-width: 750px) calc((100vw - 15rem) / 1), calc((100vw - 14rem) / 1)"
                            alt="{{ featured_media.alt | escape }}"
                            height="208"
                            width="208"
                            loading="lazy"
                          >
                        </button>
                      </li>
                    {%- endif -%}
                    {%- for media in product.media -%}
                      {%- unless media.id == product.selected_or_first_available_variant.featured_media.id -%}
                        {%- liquid
                          capture media_index
                            if media.media_type == 'model'
                              increment model_index
                            elsif media.media_type == 'video' or media.media_type == 'external_video'
                              increment video_index
                            elsif media.media_type == 'image'
                              increment image_index
                            endif
                          endcapture
                          assign media_index = media_index | plus: 1
                        -%}
                        <li
                          id="Slide-Thumbnails-{{ section.id }}-{{ forloop.index }}"
                          class="thumbnail-list__item slider__slide{% if section.settings.hide_variants and variant_images contains media.src %} thumbnail-list_item--variant{% endif %}"
                          data-target="{{ section.id }}-{{ media.id }}"
                          data-media-position="{{ media_index }}"
                        >
                          {%- if media.media_type == 'model' -%}
                            <span class="thumbnail__badge" aria-hidden="true">
                              {%- render 'icon-3d-model' -%}
                            </span>
                          {%- elsif media.media_type == 'video' or media.media_type == 'external_video' -%}
                            <span class="thumbnail__badge" aria-hidden="true">
                              {%- render 'icon-play' -%}
                            </span>
                          {%- endif -%}
                          <button
                            class="thumbnail global-media-settings global-media-settings--no-shadow {% if media.preview_image.aspect_ratio > 1 %}thumbnail--wide{% else %}thumbnail--narrow{% endif %}"
                            aria-label="{%- if media.media_type == 'image' -%}{{ 'products.product.media.load_image' | t: index: media_index }}{%- elsif media.media_type == 'model' -%}{{ 'products.product.media.load_model' | t: index: media_index }}{%- elsif media.media_type == 'video' or media.media_type == 'external_video' -%}{{ 'products.product.media.load_video' | t: index: media_index }}{%- endif -%}"
                            {% if media == product.selected_or_first_available_variant.featured_media
                              or product.selected_or_first_available_variant.featured_media == null
                              and forloop.index == 1
                            %}
                              aria-current="true"
                            {% endif %}
                            aria-controls="GalleryViewer-{{ section.id }}"
                            aria-describedby="Thumbnail-{{ section.id }}-{{ forloop.index }}"
                          >
                            <img
                              id="Thumbnail-{{ section.id }}-{{ forloop.index }}"
                              srcset="
                                {% if media.preview_image.width >= 59 %}{{ media.preview_image | image_url: width: 59 }} 59x,{% endif %}
                                {% if media.preview_image.width >= 118 %}{{ media.preview_image | image_url: width: 118 }} 118w,{% endif %}
                                {% if media.preview_image.width >= 84 %}{{ media.preview_image | image_url: width: 84 }} 84w,{% endif %}
                                {% if media.preview_image.width >= 168 %}{{ media.preview_image | image_url: width: 168 }} 168w,{% endif %}
                                {% if media.preview_image.width >= 130 %}{{ media.preview_image | image_url: width: 130 }} 130w,{% endif %}
                                {% if media.preview_image.width >= 260 %}{{ media.preview_image | image_url: width: 260 }} 260w{% endif %}
                              "
                              src="{{ media | image_url: width: 84, height: 84 }}"
                              sizes="(min-width: 1200px) calc((1200px - 19.5rem) / 4), (min-width: 750px) calc((100vw - 16.5rem) / 3), calc((100vw - 8rem) / 4)"
                              alt="{{ media.alt | escape }}"
                              height="200"
                              width="200"
                              loading="lazy"
                            >
                          </button>
                        </li>
                      {%- endunless -%}
                    {%- endfor -%}
                  </ul>
                
                  <button
                    type="button"
                    class="slider-button slider-button--next{% if media_count <= 3 %} small-hide{% endif %}{% if media_count <= 4 %} medium-hide large-up-hide{% endif %}"
                    name="next"
                    aria-label="{{ 'general.slider.next_slide' | t }}"
                    aria-controls="GalleryThumbnails-{{ section.id }}"
                    {% unless section.settings.slider_position == 'bottom' %}
                      data-step="1"
                    {% else %}
                      data-step="3"
                    {% endunless %}
                  >
                    {% render 'icon-caret' %}
                  </button>
                </slider-component>
              {%- endif -%}
            </media-gallery>             
      </div>
      <div class="product__info-wrapper grid__item">
        <div id="ProductInfo-{{ section.id }}" class="product__info-container product__info-container--sticky">
          {%- assign product_form_id = 'product-form-' | append: section.id -%}

          {%- for block in section.blocks -%}
            {%- case block.type -%}
            {%- when '@app' -%}
              {% render block %}
            {%- when 'text' -%}
                    <p
                      class="product__text{% if block.settings.text_style == 'uppercase' %} caption-with-letter-spacing{% elsif block.settings.text_style == 'subtitle' %} subtitle{% endif %}"
                      {{ block.shopify_attributes }}
                    >
                      {{- block.settings.text -}}
                    </p>
            {%- when 'title' -%}
                    <div class="product__title" {{ block.shopify_attributes }}>
                      <h1>{{ product.title | escape }}</h1>
                      <a href="{{ product.url }}" class="product__title">
                        <h2 class="h1">
                          {{ product.title | escape }}
                        </h2>
                      </a>
                    </div>
            {%- when 'price' -%}
              <div class="no-js-hidden" id="price-{{ section.id }}" role="status" {{ block.shopify_attributes }}>
                {%- render 'price', product: product, use_variant: true, show_badges: true, price_class: 'price--large' -%}
              </div>
              {%- if shop.taxes_included or shop.shipping_policy.body != blank -%}
                <div class="product__tax caption rte">
                  {%- if shop.taxes_included -%}
                    {{ 'products.product.include_taxes' | t }}
                  {%- endif -%}
                  {%- if shop.shipping_policy.body != blank -%}
                    {{ 'products.product.shipping_policy_html' | t: link: shop.shipping_policy.url }}
                  {%- endif -%}
                </div>
              {%- endif -%}
              {%- if product != blank -%}
                <div {{ block.shopify_attributes }}>
                  {%- form 'product', product -%}
                    <input type="hidden" name="id" value="{{ product.selected_or_first_available_variant.id }}">
                    {{ form | payment_terms }}
                  {%- endform -%}
                </div>
              {%- endif -%}
                {%- when 'vendor' -%}
                <div class="product-attributes product_vendor">
                  <p class="product-label">{{ 'products.product.vendor' | t }}</p>
                  <span class="product-attributes-value">{{ product.vendor | link_to_vendor }}</span>
                </div>
                {%- when 'sku' -%}              
               <div class="product__sku product-attributes no-js-hidden{% if product.selected_or_first_available_variant.sku.size == 0 %} visibility-hidden{% endif %}" id="Sku-{{ section.id }}" role="status" {{ block.shopify_attributes }}>
                <p class="product-label">{{ 'products.product.sku' | t }}</p>
                 <span class="product-attributes-value"> {{- product.selected_or_first_available_variant.sku -}}</span>
              </div>
                {%- when 'type' -%}
                <div class="product-attributes product_type">
                  <p class="product-label">{{ 'products.product.type' | t }}</p> <span class="product-attributes-value">{{ product.type }}</span>
                </div>
                   {%- when 'deal' -%}
                    {% render 'deal-snippet', product: product %}
             <div class="advance-product-style">
              {%- assign choose_style_metafields =  product.metafields.my_fields.choose_style.value -%}
              {% if choose_style_metafields != blank %}
              {% if section.settings.show_adv_product %}
                {% if section.settings.choose_label != blank %}
                <p class="advanced-title">{{ section.settings.choose_label }}</p>
                {% endif %}
                <div data-section-type="advanced-product-link-section">
                  <div class="dT_vDynamicPWrap-{{ section.id }}-block-{{ block.id}} dT_vProdWrap">
                    <ul class="adv-product-list">
                      {% for pro in choose_style_metafields limit: 3 %}
                      <li class="{% if pro.handle == product.handle %}active {% endif %}">
                        <a href="{{ pro.url }}">
                          {{ pro.featured_image | image_url: width: 400 | image_tag: loading: 'lazy' }}
                          <span>{{ pro.title}}</span>
                        </a>
                      </li>
                      {% endfor %}
                    </ul>
                  </div>
                </div>
                {% endif %}
                {% endif %}
                </div>           
            {%- when 'share' -%}
              <script src="{{ 'share.js' | asset_url }}" defer="defer"></script>
              <share-button id="Share-{{ section.id }}" class="share-button" {{ block.shopify_attributes }}>
                <button class="share-button__button hidden">
                  {% render 'icon-share' %}
                  {{ block.settings.share_label | escape }}
                </button>
                <details id="Details-{{ block.id }}-{{ section.id }}">
                  <summary class="share-button__button">
                    {% render 'icon-share' %}
                    {{ block.settings.share_label | escape }}
                  </summary>
                  <div id="Product-share-{{ section.id }}" class="share-button__fallback motion-reduce">
                    <div class="field">
                      <span id="ShareMessage-{{ section.id }}" class="share-button__message hidden" role="status">
                      </span>
                      <input type="text"
                            class="field__input"
                            id="url"
                            value="{{ product.selected_variant.url | default: product.url | prepend: request.origin }}"
                            placeholder="{{ 'general.share.share_url' | t }}"
                            onclick="this.select();"
                            readonly
                      >
                      <label class="field__label" for="url">{{ 'general.share.share_url' | t }}</label>
                    </div>
                    <button class="share-button__close hidden no-js-hidden">
                      {% render 'icon-close' %}
                      <span class="visually-hidden">{{ 'general.share.close' | t }}</span>
                    </button>
                    <button class="share-button__copy no-js-hidden">
                      {% render 'icon-clipboard' %}
                      <span class="visually-hidden">{{ 'general.share.copy_to_clipboard' | t }}</span>
                    </button>
                  </div>
                </details>
              </share-button>
               {%- when 'variant_picker' -%}
                                      {%- liquid
                      assign variants_available_arr = product.variants | map: 'available'
                      assign variants_option1_arr = product.variants | map: 'option1'
                      assign variants_option2_arr = product.variants | map: 'option2'
                      assign variants_option3_arr = product.variants | map: 'option3'
                    
                      assign product_form_id = 'product-form-' | append: section.id
                    -%}

  
                    {%- unless product.has_only_default_variant -%}
                      {%- if block.settings.picker_type == 'button' -%}
                        <variant-radios
                        id="variant-radios-{{ section.id }}"
                        class="no-js-hidden"
                        data-section="{{ section.id }}"
                        data-url="{{ product.url }}"
                        {% if update_url == false %}
                          data-update-url="false"
                        {% endif %}
                        {{ block.shopify_attributes }}
                        >
                        
                          {%- for option in product.options_with_values -%}
                             {%- liquid
                                    assign option_disabled = true
                                
                                    for option1_name in variants_option1_arr
                                      case option.position
                                        when 1
                                          if variants_option1_arr[forloop.index0] == value and variants_available_arr[forloop.index0]
                                            assign option_disabled = false
                                          endif
                                        when 2
                                          if option1_name == product.selected_or_first_available_variant.option1 and variants_option2_arr[forloop.index0] == value and variants_available_arr[forloop.index0]
                                            assign option_disabled = false
                                          endif
                                        when 3
                                          if option1_name == product.selected_or_first_available_variant.option1 and variants_option2_arr[forloop.index0] == product.selected_or_first_available_variant.option2 and variants_option3_arr[forloop.index0] == value and variants_available_arr[forloop.index0]
                                            assign option_disabled = false
                                          endif
                                      endcase
                                    endfor
                                  -%}
                            {% assign option_index = forloop.index0 %}
                            <fieldset class="js product-form__input option-name-{{ option.name | downcase }}">   
                              <div class="swatch-label-text">
                                <legend class="form__label">{{ option.name }} : <span class="append-options-values">{{ option.selected_value }}</span>
                                </legend>
                            </div>
                               {% if block.settings.picker_method == 'variant_image' or block.settings.picker_method == 'custom_color' %}<div class="swatch-group">{% endif %}                            
                            {% if  option.name  == 'Size' %}                             
                                <div class="varient-model-wrapper">                              
                                <div class="varient-class">
                                {%- for value in option.values -%}                                
                                <input
                                type="radio"
                                id="{{ section.id }}-{{ option.position }}-{{ forloop.index0 }}"
                                name="{{ option.name }}"
                                value="{{ value | escape }}"
                                form="{{ product_form_id }}"                                
                                class="{% if variant.available %} available {% else %} unavailable {% endif %}" {% if option.selected_value == value %}checked{% endif %}
                                 {% if option_disabled %}
                                 class="disabled"
                                 {% endif %}>                                
                                {% if option.name == 'Color' %}                                
                                {%- if block.settings.picker_method == 'custom_color' -%}
                                  {%- liquid
                                  assign colorName = value | handle            
                                  assign colorFile = value | handle | append: '.' | append: file_extension
                                  assign colorFileURL = colorFile | file_img_url: '50x50' | prepend: 'https:' | split: '?' | first                
                                   -%}
                                <div data-value="{{ value | escape }}" class="swatch-element">  
                                <div class="tooltip x">
                                <label for="{{ section.id }}-{{ option.position }}-{{ forloop.index0 }}" class="swatch-variant-text">{{ value | handle }}</label>
                                </div>
                                 <label
                                for="{{ section.id }}-{{ option.position }}-{{ forloop.index0 }}"
                                class="swatch-variant-color bg-color-{{ value | handle }}"                                
                                style="background-size: cover;background-color: {{- colorName -}}; ">
                                </label>
                                </div> 
                                {%- else block.settings.picker_method == 'variant_image' -%} 
                                
                                <div data-value="{{ value | escape }}" class="swatch-element">
                                <div class="tooltip">
                                <label
                                for="{{ section.id }}-{{ option.position }}-{{ forloop.index0 }}"
                                class="swatch-variant-text"
                                >{{ value | escape }}</label>
                                </div>
                                <label
                                for="{{ section.id }}-{{ option.position }}-{{ forloop.index0 }}"
                                class="swatch-variant-image "
                                style="background-image: url('{{ product.variants[forloop.index0].image.src  | product_img_url: '460x' }}');"
                                ></label>
                                </div> 
                                {%- endif -%}
                                {% else %}
                                <label for="{{ section.id }}-{{ option.position }}-{{ forloop.index0 }}">
                                {{ value }}
                                </label>
                                
                                {% endif %}
                                
                                {%- endfor -%}
                                </div>
                               {%- for block in section.blocks -%}
                                {%- case block.type -%}
                                {%- when 'size-popup' -%}
                                 <div class="size-chart"> 
                                <svg xmlns="http://www.w3.org/2000/svg" class="popup-svg" width="16.432" height="28.2" viewBox="0 0 16.432 28.2">
                                <g id="Component_46_1" data-name="Component 46 – 1" transform="translate(0.6 0.6)">
                                <rect id="Rectangle_8800" data-name="Rectangle 8800" width="8" height="27" rx="1" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.2"/>
                                <line id="Line_302" data-name="Line 302" x2="3" transform="translate(5 3.5)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.2"/>
                                <line id="Line_303" data-name="Line 303" x2="2" transform="translate(6 5.5)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.2"/>
                                <line id="Line_304" data-name="Line 304" x2="3" transform="translate(5 7.5)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.2"/>
                                <line id="Line_305" data-name="Line 305" x2="2" transform="translate(6 9.5)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.2"/>
                                <line id="Line_306" data-name="Line 306" x2="3" transform="translate(5 11.5)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.2"/>
                                <line id="Line_307" data-name="Line 307" x2="2" transform="translate(6 13.5)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.2"/>
                                <line id="Line_308" data-name="Line 308" x2="3" transform="translate(5 15.5)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.2"/>
                                <line id="Line_309" data-name="Line 309" x2="2" transform="translate(6 17.5)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.2"/>
                                <line id="Line_310" data-name="Line 310" x2="3" transform="translate(5 19.5)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.2"/>
                                <line id="Line_311" data-name="Line 311" x2="2" transform="translate(6 21.5)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.2"/>
                                <line id="Line_312" data-name="Line 312" x2="3" transform="translate(5 23.5)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.2"/>
                                <path id="Path_54441" data-name="Path 54441" d="M11.5,3.5l2-3,2,3" transform="translate(-0.5 -0.5)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.2"/>
                                <path id="Path_54442" data-name="Path 54442" d="M11.5,24.5l2,3,2-3" transform="translate(-0.5 -0.5)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.2"/>
                                <line id="Line_313" data-name="Line 313" y2="24" transform="translate(13 2)" fill="none" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.2"/>
                                </g>
                                </svg> 
                                <modal-opener
                                class="product-popup-modal__opener no-js-hidden quick-add-hidden"
                                data-modal="#PopupModal-{{ block.id }}"
                                {{ block.shopify_attributes }}
                                >
                                <button
                                id="ProductPopup-{{ block.id }}"
                                class="product-popup-modal__button link"
                                type="button"
                                aria-haspopup="dialog"
                                >
                                {{ block.settings.text | default: block.settings.page.title }}
                                </button>
                                </modal-opener>
                                <a href="{{ block.settings.page.url }}" class="product-popup-modal__button link no-js">
                                {{- block.settings.text -}}
                                </a>
                                {% assign popups = section.blocks | where: 'type', 'size-popup' %}
                                {%- for block in popups -%}
                                <modal-dialog
                                id="PopupModal-{{ block.id }}"
                                class="product-popup-modal"
                                {{ block.shopify_attributes }}
                                >
                                <div
                                role="dialog"
                                aria-label="{{ block.settings.text }}"
                                aria-modal="true"
                                class="product-popup-modal__content"
                                tabindex="-1"
                                >
                                <button
                                id="ModalClose-{{ block.id }}"
                                type="button"
                                class="product-popup-modal__toggle"
                                aria-label="{{ 'accessibility.close' | t }}"
                                >
                                {% render 'icon-close' %}
                                </button>
                                <div class="product-popup-modal__content-info">
                                <h1 class="h2">{{ block.settings.page.title }}</h1>
                                {{ block.settings.page.content }}
                                </div>
                                </div>
                                </modal-dialog>
                                  </div> 
                                {%- endfor -%}
                                {%- endcase -%}
                                {%- endfor -%}
                                </div>  
                                {% else %}
                                
                                {%- for value in option.values -%}
                                <input
                                type="radio"
                                id="{{ section.id }}-{{ option.position }}-{{ forloop.index0 }}"
                                name="{{ option.name }}"
                                value="{{ value | escape }}"
                                form="{{ product_form_id }}"
                                class="{% if variant.available %} available {% else %} unavailable {% endif %}" {% if option.selected_value == value %}checked{% endif %}>
                                
                                {% if option.name == 'Color' %}
                                {%- liquid
                                  assign colorName = value | handle            
                                  assign colorFile = value | handle | append: '.' | append: file_extension
                                  assign colorFileURL = colorFile | file_img_url: '50x50' | prepend: 'https:' | split: '?' | first                
                                   -%}
                                {%- if block.settings.picker_method == 'custom_color' -%}
                                 <div data-value="{{ value | escape }}" class="swatch-element">  
                                <div class="tooltip">
                                <label for="{{ section.id }}-{{ option.position }}-{{ forloop.index0 }}" class="swatch-variant-text">{{ value | escape }}</label>
                                </div>
                                  
                                 <label
                                for="{{ section.id }}-{{ option.position }}-{{ forloop.index0 }}"
                                class="swatch-variant-color bg-color-{{ value | handle }}"                                
                                 style="background-size: cover;background-color: {{- colorName -}}; "> 
                                </label>
                                </div> 
                                {%- else block.settings.picker_method == 'variant_image' -%} 
                                
                                <div data-value="{{ value | escape }}" class="swatch-element">
                                <div class="tooltip">
                                <label
                                for="{{ section.id }}-{{ option.position }}-{{ forloop.index0 }}"
                                class="swatch-variant-text"
                                >{{ value | escape }}</label>
                                </div>
                                <label
                                for="{{ section.id }}-{{ option.position }}-{{ forloop.index0 }}"
                                class="swatch-variant-image "
                                style="background-image: url('{{ product.variants[forloop.index0].image.src  | product_img_url: '460x' }}');"
                                ></label>
                                </div> 
                                {%- endif -%}
                                {% else %}
                                <label for="{{ section.id }}-{{ option.position }}-{{ forloop.index0 }}">
                                {{ value }}
                                </label>                                
                                {% endif %}                                
                                {%- endfor -%}                       
                            {% endif %}                        
                             {%- if block.settings.picker_method == 'variant_image' or block.settings.picker_method == 'custom_color' -%}</div>{% endif %}
                           </fieldset>
                          {%- endfor -%}
                          <script type="application/json">
                            {{ product.variants | json }}
                          </script>
                        </variant-radios>
                      {%- else -%}
                        <variant-selects
                          class="no-js-hidden"
                          data-section="{{ section.id }}"
                          data-url="{{ product.url }}"
                          {{ block.shopify_attributes }}
                        >
                          {%- for option in product.options_with_values -%}
                            <div class="product-form__input product-form__input--dropdown">
                              <label class="form__label" for="Option-{{ section.id }}-{{ forloop.index0 }}">
                                {{ option.name }}
                              </label>
                              <div class="select">
                                <select
                                  id="Option-{{ section.id }}-{{ forloop.index0 }}"
                                  class="select__select"
                                  name="options[{{ option.name | escape }}]"
                                  form="{{ product_form_id }}"
                                >
                                  {%- for value in option.values -%}
                                    <option
                                      value="{{ value | escape }}"
                                      {% if option.selected_value == value %}
                                        selected="selected"
                                      {% endif %}
                                    >
                                      {{ value }}
                                    </option>
                                  {%- endfor -%}
                                </select>
                                {% render 'icon-caret' %}
                              </div>
                            </div>
                          {%- endfor -%}

                          <script type="application/json">
                            {{ product.variants | json }}
                          </script>
                        </variant-selects>
                      {%- endif -%}
                    {%- endunless -%}

                    <noscript class="product-form__noscript-wrapper-{{ section.id }}">
                      <div class="product-form__input{% if product.has_only_default_variant %} hidden{% endif %}">
                        <label class="form__label" for="Variants-{{ section.id }}">
                          {{- 'products.product.product_variants' | t -}}
                        </label>
                        <div class="select">
                          <select
                            name="id"
                            id="Variants-{{ section.id }}"
                            class="select__select"
                            form="{{ product_form_id }}"
                          >
                            {%- for variant in product.variants -%}
                              <option
                                {% if variant == product.selected_or_first_available_variant %}
                                  selected="selected"
                                {% endif %}
                                {% if variant.available == false %}
                                  disabled
                                {% endif %}
                                value="{{ variant.id }}"
                              >
                                {{ variant.title }}
                                {%- if variant.available == false %} - {{ 'products.product.sold_out' | t }}{% endif %}
                                - {{ variant.price | money | strip_html }}
                              </option>
                            {%- endfor -%}
                          </select>
                          {% render 'icon-caret' %}
                        </div>
                      </div>
                    </noscript>
            {%- when 'buy_buttons' -%}
              <div {{ block.shopify_attributes }}>
                {%- if product != blank -%}
                  <product-form class="product-form">
                    <div class="product-form__error-message-wrapper" role="alert" hidden>
                      <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-error" viewBox="0 0 13 13">
                        <circle cx="6.5" cy="6.50049" r="5.5" stroke="white" stroke-width="2"/>
                        <circle cx="6.5" cy="6.5" r="5.5" fill="#EB001B" stroke="#EB001B" stroke-width="0.7"/>
                        <path d="M5.87413 3.52832L5.97439 7.57216H7.02713L7.12739 3.52832H5.87413ZM6.50076 9.66091C6.88091 9.66091 7.18169 9.37267 7.18169 9.00504C7.18169 8.63742 6.88091 8.34917 6.50076 8.34917C6.12061 8.34917 5.81982 8.63742 5.81982 9.00504C5.81982 9.37267 6.12061 9.66091 6.50076 9.66091Z" fill="white"/>
                        <path d="M5.87413 3.17832H5.51535L5.52424 3.537L5.6245 7.58083L5.63296 7.92216H5.97439H7.02713H7.36856L7.37702 7.58083L7.47728 3.537L7.48617 3.17832H7.12739H5.87413ZM6.50076 10.0109C7.06121 10.0109 7.5317 9.57872 7.5317 9.00504C7.5317 8.43137 7.06121 7.99918 6.50076 7.99918C5.94031 7.99918 5.46982 8.43137 5.46982 9.00504C5.46982 9.57872 5.94031 10.0109 6.50076 10.0109Z" fill="white" stroke="#EB001B" stroke-width="0.7">
                      </svg>
                      <span class="product-form__error-message"></span>
                    </div>

                    {%- form 'product', product, id: product_form_id, class: 'form', novalidate: 'novalidate', data-type: 'add-to-cart-form' -%}
                      <input type="hidden" name="id" value="{{ product.selected_or_first_available_variant.id }}" disabled>
                      <div class="product-form__buttons">
                           {%- for block in section.blocks -%}
                          {%- case block.type -%}     
                          {%- when 'quantity_selector' -%}
                          <div
                          class="product-form__input product-form__quantity{% if settings.inputs_shadow_vertical_offset != 0 and settings.inputs_shadow_vertical_offset < 0 %} product-form__quantity-top{% endif %}"
                          {{ block.shopify_attributes }}
                          >
                          <quantity-input class="quantity">
                          <button class="quantity__button no-js-hidden" name="minus" type="button">
                          <span class="visually-hidden">
                          {{- 'products.product.quantity.decrease' | t: product: product.title | escape -}}
                          </span>
                          {% render 'icon-minus' %}
                          </button>
                          <input
                          class="quantity__input"
                          type="number"
                          name="quantity"
                          id="Quantity-{{ section.id }}"
                          min="1"
                          value="1"
                          form="{{ product_form_id }}"
                          >
                          <button class="quantity__button no-js-hidden" name="plus" type="button">
                          <span class="visually-hidden">
                          {{- 'products.product.quantity.increase' | t: product: product.title | escape -}}
                          </span>
                          {% render 'icon-plus' %}
                          </button>
                          </quantity-input>
                          </div>
                          {%- endcase -%}
                          {%- endfor -%}   
                        <button
                          type="submit"
                          name="add"
                          class="product-form__submit button button--full-width {% if block.settings.show_dynamic_checkout and product.selling_plan_groups == empty %}button--secondary{% else %}button--primary{% endif %}"
                        {% if product.selected_or_first_available_variant.available == false %}disabled{% endif %}
                        >
                            <span>
                              {%- if product.selected_or_first_available_variant.available -%}
                                {{ 'products.product.add_to_cart' | t }}
                              {%- else -%}
                                {{ 'products.product.sold_out' | t }}
                              {%- endif -%}
                            </span>
                            <div class="loading-overlay__spinner hidden">
                              <svg aria-hidden="true" focusable="false" role="presentation" class="spinner" viewBox="0 0 66 66" xmlns="http://www.w3.org/2000/svg">
                                <circle class="path" fill="none" stroke-width="6" cx="33" cy="33" r="30"></circle>
                              </svg>
                            </div>
                        </button>
                        {%- if block.settings.show_dynamic_checkout -%}
                          {{ form | payment_button }}
                        {%- endif -%}
                      </div>
                    {%- endform -%}
                  </product-form>
                {%- else -%}
                  <div class="product-form">
                    <div class="product-form__buttons form">
                      <button
                        type="submit"
                        name="add"
                        class="product-form__submit button button--full-width button--primary"
                        disabled
                      >
                        {{ 'products.product.sold_out' | t }}
                      </button>
                    </div>
                  </div>
                {%- endif -%}
              </div>
            {%- when 'custom_liquid' -%}
              {{ block.settings.custom_liquid }}
            {%- when 'rating' -%}
              {%- if product.metafields.reviews.rating.value != blank -%}
                {% liquid
                  assign rating_decimal = 0
                  assign decimal = product.metafields.reviews.rating.value.rating | modulo: 1
                  if decimal >= 0.3 and decimal <= 0.7
                  assign rating_decimal = 0.5
                elsif decimal > 0.7
                  assign rating_decimal = 1
                  endif
                %}
                <div class="rating" role="img" aria-label="{{ 'accessibility.star_reviews_info' | t: rating_value: product.metafields.reviews.rating.value, rating_max: product.metafields.reviews.rating.value.scale_max }}">
                  <span aria-hidden="true" class="rating-star color-icon-{{ settings.accent_icons }}" style="--rating: {{ product.metafields.reviews.rating.value.rating | floor }}; --rating-max: {{ product.metafields.reviews.rating.value.scale_max }}; --rating-decimal: {{ rating_decimal }};"></span>
                </div>
                <p class="rating-text caption">
                  <span aria-hidden="true">{{ product.metafields.reviews.rating.value }} / {{ product.metafields.reviews.rating.value.scale_max }}</span>
                </p>
                <p class="rating-count caption">
                  <span aria-hidden="true">({{ product.metafields.reviews.rating_count }})</span>
                  <span class="visually-hidden">{{ product.metafields.reviews.rating_count }} {{ "accessibility.total_reviews" | t }}</span>
                </p>
              {%- endif -%}
            {%- endcase -%}
          {%- endfor -%}
        <a{% if product == blank %} role="link" aria-disabled="true"{% else %} href="{{ product.url }}"{% endif %} class="link product__view-details animate-arrow">
          {{ 'products.product.view_full_details' | t }}
          {% render 'icon-arrow' %}
        </a>
        </div>
      </div>
    </div>
    <product-modal id="ProductModal-{{ section.id }}" class="product-media-modal media-modal">
      <div class="product-media-modal__dialog" role="dialog" aria-label="{{ 'products.modal.label' | t }}" aria-modal="true" tabindex="-1">
        <button id="ModalClose-{{ section.id }}" type="button" class="product-media-modal__toggle" aria-label="{{ 'accessibility.close' | t }}">{% render 'icon-close' %}</button>

        <div class="product-media-modal__content color-background-1 gradient" role="document" aria-label="{{ 'products.modal.label' | t }}" tabindex="0">
          {%- liquid
            if product.selected_or_first_available_variant.featured_media != null
              assign media = product.selected_or_first_available_variant.featured_media
              render 'product-media', media: media, loop: section.settings.enable_video_looping, variant_image: section.settings.hide_variants
            endif
          -%}

          {%- for media in product.media -%}
            {%- liquid
              if section.settings.hide_variants and media_to_render contains media.id
                assign variant_image = true
              else
                assign variant_image = false
              endif

              unless media.id == product.selected_or_first_available_variant.featured_media.id
                render 'product-media', media: media, loop: section.settings.enable_video_looping, variant_image: variant_image
              endunless
            -%}
          {%- endfor -%}
        </div>
      </div>
    </product-modal>
  </div>
  </div>      
 </div>                         
</section>

<script src="{{ 'product-form.js' | asset_url }}" defer="defer"></script>
{%- if first_3d_model -%}
  <script type="application/json" id="ProductJSON-{{ product.id }}">
    {{ product.media | where: 'media_type', 'model' | json }}
  </script>
  <script src="{{ 'product-model.js' | asset_url }}" defer></script>
{%- endif -%}

{%- liquid
  if product.selected_or_first_available_variant.featured_media
    assign seo_media = product.selected_or_first_available_variant.featured_media
  else
    assign seo_media = product.featured_media
  endif
-%}

<script type="application/ld+json">
  {
    "@context": "http://schema.org/",
    "@type": "Product",
    "name": {{ product.title | json }},
    "url": {{ request.origin | append: product.url | json }},
    {% if seo_media -%}
      "image": [
        {{ seo_media | image_url: width: seo_media.preview_image.width | prepend: "https:" | json }}
      ],
    {%- endif %}
    "description": {{ product.description | strip_html | json }},
    {% if product.selected_or_first_available_variant.sku != blank -%}
      "sku": {{ product.selected_or_first_available_variant.sku | json }},
    {%- endif %}
    "brand": {
      "@type": "Thing",
      "name": {{ product.vendor | json }}
    },
    "offers": [
      {%- for variant in product.variants -%}
        {
          "@type" : "Offer",
          {%- if variant.sku != blank -%}
            "sku": {{ variant.sku | json }},
          {%- endif -%}
          "availability" : "http://schema.org/{% if variant.available %}InStock{% else %}OutOfStock{% endif %}",
          "price" : {{ variant.price | divided_by: 100.00 | json }},
          "priceCurrency" : {{ cart.currency.iso_code | json }},
          "url" : {{ request.origin | append: variant.url | json }}
        }{% unless forloop.last %},{% endunless %}
      {%- endfor -%}
    ]
  }
</script>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    function isIE() {
      const ua = window.navigator.userAgent;
      const msie = ua.indexOf('MSIE ');
      const trident = ua.indexOf('Trident/');

      return (msie > 0 || trident > 0);
    }

    if (!isIE()) return;
    const hiddenInput = document.querySelector('#{{ product_form_id }} input[name="id"]');
    const noScriptInputWrapper = document.createElement('div');
    const variantSwitcher = document.querySelector('variant-radios[data-section="{{ section.id }}"]') || document.querySelector('variant-selects[data-section="{{ section.id }}"]');
    noScriptInputWrapper.innerHTML = document.querySelector('.product-form__noscript-wrapper-{{ section.id }}').textContent;
    variantSwitcher.outerHTML = noScriptInputWrapper.outerHTML;

    document.querySelector('#Variants-{{ section.id }}').addEventListener('change', function(event) {
      hiddenInput.value = event.currentTarget.value;
    });
  });
</script>

{% if product.media.size > 0 %}
  <script src="{{ 'product-modal.js' | asset_url }}" defer="defer"></script>
  <script src="{{ 'media-gallery.js' | asset_url }}" defer="defer"></script>
{% endif %}

{% schema %}
{
  "name": "t:sections.featured-product.name",
  "tag": "section",
  "class": "section section-featured-product",
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "text",
      "name": "t:sections.featured-product.blocks.text.name",
      "settings": [
        {
          "type": "text",
          "id": "text",
          "default": "Text block",
          "label": "t:sections.featured-product.blocks.text.settings.text.label"
        },
        {
          "type": "select",
          "id": "text_style",
          "options": [
            {
              "value": "body",
              "label": "t:sections.featured-product.blocks.text.settings.text_style.options__1.label"
            },
            {
              "value": "subtitle",
              "label": "t:sections.featured-product.blocks.text.settings.text_style.options__2.label"
            },
            {
              "value": "uppercase",
              "label": "t:sections.featured-product.blocks.text.settings.text_style.options__3.label"
            }
          ],
          "default": "body",
          "label": "t:sections.featured-product.blocks.text.settings.text_style.label"
        }
      ]
    },
    {
      "type": "title",
      "name": "t:sections.featured-product.blocks.title.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "heading_size",
          "options": [
            {
              "value": "h2",
              "label": "t:sections.all.heading_size.options__1.label"
            },
            {
              "value": "h1",
              "label": "t:sections.all.heading_size.options__2.label"
            },
            {
              "value": "h0",
              "label": "t:sections.all.heading_size.options__3.label"
            }
          ],
          "default": "h1",
          "label": "t:sections.all.heading_size.label"
        }
      ]
    },
    {
      "type": "price",
      "name": "t:sections.featured-product.blocks.price.name",
      "limit": 1
    },
          {
    "type": "vendor",
    "name": "t:sections.main-product.blocks.vendor.name",
    "limit": 1
    },
    {
    "type": "type",
    "name": "t:sections.main-product.blocks.type.name",
    "limit": 1
    },
    {
    "type": "sku",
    "name": "t:sections.main-product.blocks.sku.name",
    "limit": 1
    },
    {
      "type": "quantity_selector",
      "name": "t:sections.featured-product.blocks.quantity_selector.name",
      "limit": 1
    },
    {
      "type": "variant_picker",
      "name": "t:sections.featured-product.blocks.variant_picker.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "picker_type",
          "options": [
            {
              "value": "dropdown",
              "label": "t:sections.featured-product.blocks.variant_picker.settings.picker_type.options__1.label"
            },
            {
              "value": "button",
              "label": "t:sections.featured-product.blocks.variant_picker.settings.picker_type.options__2.label"
            }
          ],
          "default": "button",
          "label": "t:sections.featured-product.blocks.variant_picker.settings.picker_type.label"
        },
        {
        "type": "select",
        "id": "picker_method",
        "options": [
        {
        "value": "custom_color",
        "label": "t:sections.featured-product.blocks.variant_picker.settings.picker_method.options__1.label"
        },
        {
        "value": "variant_image",
        "label": "t:sections.featured-product.blocks.variant_picker.settings.picker_method.options__2.label"
        }
        ],
        "default": "custom_color",
        "label": "t:sections.featured-product.blocks.variant_picker.settings.picker_method.label"
        }
      ]
    },
    {
      "type": "buy_buttons",
      "name": "t:sections.featured-product.blocks.buy_buttons.name",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "show_dynamic_checkout",
          "default": true,
          "label": "t:sections.featured-product.blocks.buy_buttons.settings.show_dynamic_checkout.label",
          "info": "t:sections.featured-product.blocks.buy_buttons.settings.show_dynamic_checkout.info"
        }
      ]
    },
    {
      "type": "deal",
      "name": "t:sections.featured-product.blocks.deal.name",
      "limit": 1
    },
    {
      "type": "share",
      "name": "t:sections.featured-product.blocks.share.name",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "share_label",
          "label": "t:sections.featured-product.blocks.share.settings.text.label",
          "default": "Share"
        },
        {
          "type": "paragraph",
          "content": "t:sections.featured-product.blocks.share.settings.featured_image_info.content"
        },
        {
          "type": "paragraph",
          "content": "t:sections.featured-product.blocks.share.settings.title_info.content"
        }
      ]
    },
    {
      "type": "custom_liquid",
      "name": "t:sections.featured-product.blocks.custom_liquid.name",
      "settings": [
        {
          "type": "liquid",
          "id": "custom_liquid",
          "label": "t:sections.featured-product.blocks.custom_liquid.settings.custom_liquid.label"
        }
      ]
    },
    {
      "type": "rating",
      "name": "t:sections.featured-product.blocks.rating.name",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "t:sections.featured-product.blocks.rating.settings.paragraph.content"
        }
      ]
    },
    {
    "type": "size-popup",
    "name": "t:sections.main-product.blocks.size-popup.name",
    "settings": [
    {
    "type": "text",
    "id": "text",
    "default": "Size Guide",
    "label": "t:sections.main-product.blocks.size-popup.settings.link_label.label"
    },
    {
    "id": "page",
    "type": "page",
    "label": "t:sections.main-product.blocks.size-popup.settings.page.label"
    }
    ]
    } 
  ],
  "settings": [
    {
      "type":"checkbox",
      "id":"page_full_width",
       "default": false,
      "label": "t:sections.all.page_full_width.label"
    },
    {
      "type":"checkbox",
      "id":"page_full_width_spacing",
       "default": false,
      "label": "t:sections.all.page_full_width_spacing.label"
    },
    {
      "type": "product",
      "id": "product",
      "label": "t:sections.featured-product.settings.product.label"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "t:sections.all.colors.accent_1.label"
        },
        {
          "value": "accent-2",
          "label": "t:sections.all.colors.accent_2.label"
        },
        {
          "value": "background-1",
          "label": "t:sections.all.colors.background_1.label"
        },
        {
          "value": "background-2",
          "label": "t:sections.all.colors.background_2.label"
        },
        {
          "value": "inverse",
          "label": "t:sections.all.colors.inverse.label"
        }
      ],
      "default": "background-1",
      "label": "t:sections.all.colors.label"
    },
    {
      "type": "checkbox",
      "id": "secondary_background",
      "default": false,
      "label": "t:sections.featured-product.settings.secondary_background.label"
    },
    {
  "type": "select",
  "id": "gallery_layout",
  "options": [
  {
  "value": "stacked",
  "label": "t:sections.main-product.settings.gallery_layout.options__1.label"
  },
  {
  "value": "thumbnail",
  "label": "t:sections.main-product.settings.gallery_layout.options__2.label"
  },
  {
  "value": "thumbnail_slider",
  "label": "t:sections.main-product.settings.gallery_layout.options__3.label"
  }
  ],
  "default": "stacked",
  "label": "t:sections.main-product.settings.gallery_layout.label"
  },
       {
  "type": "select",
  "id": "thumb_carousel_layout",
  "options": [
  {
  "value": "thumbnail_slider_left",
  "label": "t:sections.main-product.settings.thumb_carousel_layout.options__1.label"
  },
  {
  "value": "thumbnail_slider_right",
  "label": "t:sections.main-product.settings.thumb_carousel_layout.options__2.label"
  }
  ],
  "default": "thumbnail_slider_left",
  "label": "t:sections.main-product.settings.thumb_carousel_layout.label"
  },
    {
      "type": "header",
      "content": "t:sections.featured-product.settings.header.content",
      "info": "t:sections.featured-product.settings.header.info"
    },
    {
      "type": "checkbox",
      "id": "hide_variants",
      "default": false,
      "label": "t:sections.main-product.settings.hide_variants.label"
    },
    {
      "type": "checkbox",
      "id": "enable_video_looping",
      "default": false,
      "label": "t:sections.featured-product.settings.enable_video_looping.label"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    }
  ],
  "presets": [
    {
      "name": "t:sections.featured-product.presets.name",
      "blocks": [
        {
          "type": "text",
          "settings": {
            "text": "{{ section.settings.product.vendor }}",
            "text_style": "uppercase"
          }
        },
        {
          "type": "title"
        },
        {
          "type": "text",
          "settings": {
            "text": "{{ section.settings.product.metafields.descriptors.subtitle.value }}",
            "text_style": "subtitle"
          }
        },
        {
          "type": "price"
        },
        {
          "type": "variant_picker"
        },
        {
          "type": "quantity_selector"
        },
        {
          "type": "buy_buttons"
        },
        {
          "type": "deal"
        },
        {
          "type": "share"
        }
      ]
    }
  ]
}
{% endschema %}
