.collection-list-type-2 .slider__slide .slideshow__text-wrapper.page-width {max-width:100%;}
slider-component {--desktop-margin-left-first-item:max(5rem, calc((100vw - var(--page-width) + 10rem - var(--grid-desktop-horizontal-spacing)) / 2)); position:relative; display:block;}
slider-component.slider-component-full-width {--desktop-margin-left-first-item:1.5rem;}

@media screen and (max-width: 749px) {
slider-component.page-width {padding:0 1.5rem;}
}

@media screen and (min-width: 749px) and (max-width:990px) {
slider-component.page-width {padding:0 5rem;}
}

@media screen and (max-width: 989px) {
.collection-list-type-2 .no-js slider-component .slider {padding-bottom:3rem;}
}

.collection-list-type-2 .slider__slide {--focus-outline-padding:0.5rem; --shadow-padding-top:calc(var(--shadow-vertical-offset) * -1 + var(--shadow-blur-radius)); --shadow-padding-bottom:calc(var(--shadow-vertical-offset) + var(--shadow-blur-radius)); scroll-snap-align:start; flex-shrink:0; padding-bottom:0;}

@media screen and (max-width: 749px) {
.collection-list-type-2 .slider.slider--mobile {position:relative; flex-wrap:inherit; overflow-x:auto; scroll-snap-type:x mandatory; scroll-behavior:smooth; scroll-padding-left:0rem; -webkit-overflow-scrolling:touch; margin-bottom:0rem;}
.collection-list-type-2 .slider.slider--mobile .slider__slide {margin-bottom:0; padding-top:max(var(--focus-outline-padding), var(--shadow-padding-top)); padding-bottom:max(var(--focus-outline-padding), var(--shadow-padding-bottom));}
.collection-list-type-2 .slider.slider--mobile.contains-card--standard .slider__slide:not(.collection-list__item--no-media) {padding-bottom:var(--focus-outline-padding);}
.collection-list-type-2 .slider.slider--mobile.contains-content-container .slider__slide {--focus-outline-padding:0rem;}
}

@media screen and (min-width: 750px) {
.collection-list-type-2 .slider.slider--tablet-up {position:relative; flex-wrap:inherit; overflow-x:auto; scroll-snap-type:x mandatory; scroll-behavior:smooth; -webkit-overflow-scrolling:touch;}
.collection-list-type-2 .slider.slider--tablet-up .slider__slide {margin-bottom:0;}
}

@media screen and (max-width: 989px) {
.collection-list-type-2 .slider.slider--tablet {position:relative; flex-wrap:inherit; overflow-x:auto; scroll-snap-type:x mandatory; scroll-behavior:smooth; -webkit-overflow-scrolling:touch; margin-bottom:1rem;}
.collection-list-type-2 .slider.slider--tablet .slider__slide {margin-bottom:0; padding-top:max(var(--focus-outline-padding), var(--shadow-padding-top));}
.collection-list-type-2 .slider.slider--tablet.contains-card--standard .slider__slide:not(.collection-list__item--no-media) {padding-bottom:var(--focus-outline-padding);}
.collection-list-type-2 .slider.slider--tablet.contains-content-container .slider__slide {--focus-outline-padding:0rem;}
}

.collection-list-type-2 .slider--everywhere {position:relative; flex-wrap:inherit; overflow-x:auto; scroll-snap-type:x mandatory; scroll-behavior:smooth; -webkit-overflow-scrolling:touch; margin-bottom:1rem;}
.collection-list-type-2 .slider.slider--everywhere .slider__slide {margin-bottom:0; scroll-snap-align:center;}

@media screen and (min-width: 990px) {
.collection-list-type-2 .slider-component-desktop.page-width {max-width:none;}
.collection-list-type-2 .slider--desktop {position:relative; flex-wrap:inherit; overflow-x:auto; scroll-snap-type:x mandatory; scroll-behavior:smooth; -webkit-overflow-scrolling:touch; margin-bottom:1rem;}
.collection-list-type-2 .slider.slider--desktop .slider__slide {margin-bottom:0; padding-top:max(var(--focus-outline-padding), var(--shadow-padding-top)); padding-bottom:max(var(--focus-outline-padding), var(--shadow-padding-bottom));}
.collection-list-type-2 .slider--desktop .slider__slide:first-child {margin-left:var(--desktop-margin-left-first-item); scroll-margin-left:var(--desktop-margin-left-first-item);}
.collection-list-type-2 .slider.slider--desktop .slider__slide:last-child {margin-right:5rem;}
.collection-list-type-2 .slider-component-full-width .slider--desktop .slider__slide:first-child {margin-left:1.5rem; scroll-margin-left:1.5rem;}
.collection-list-type-2 .slider-component-full-width .slider--desktop .slider__slide:last-child {margin-right:1.5rem;}
.collection-list-type-2 .slider--desktop.grid--5-col-desktop .grid__item {width:calc( (100% - var(--desktop-margin-left-first-item)) / 5 - var(--grid-desktop-horizontal-spacing) * 2);}
.collection-list-type-2 .slider--desktop.grid--4-col-desktop .grid__item {width:calc( (100% - var(--desktop-margin-left-first-item)) / 4 - var(--grid-desktop-horizontal-spacing) * 3);}
.collection-list-type-2 .slider--desktop.grid--3-col-desktop .grid__item {width:calc( (100% - var(--desktop-margin-left-first-item)) / 3 - var(--grid-desktop-horizontal-spacing) * 4);}
.collection-list-type-2 .slider--desktop.grid--2-col-desktop .grid__item {width:calc( (100% - var(--desktop-margin-left-first-item)) / 2 - var(--grid-desktop-horizontal-spacing) * 5);}
.collection-list-type-2 .slider--desktop.grid--1-col-desktop .grid__item {width:calc( (100% - var(--desktop-margin-left-first-item)) - var(--grid-desktop-horizontal-spacing) * 9);}
.collection-list-type-2 .slider.slider--desktop.contains-card--standard .slider__slide:not(.collection-list__item--no-media) {padding-bottom:var(--focus-outline-padding);}
.collection-list-type-2 .slider.slider--desktop.contains-content-container .slider__slide {--focus-outline-padding:0rem;}
}

@media (prefers-reduced-motion) {
.collection-list-type-2 .slider {scroll-behavior:auto;}
}

.collection-list-type-2 .slider {scrollbar-color:rgb(var(--color-foreground)) rgba(var(--color-foreground), 0.04); -ms-overflow-style:none; scrollbar-width:none;}
.collection-list-type-2 .slider::-webkit-scrollbar {height:0.4rem; width:0.4rem; display:none;}
.collection-list-type-2 .no-js .slider {-ms-overflow-style:auto; scrollbar-width:auto;}
.collection-list-type-2 .no-js .slider::-webkit-scrollbar {display:initial;}
.collection-list-type-2 .slider::-webkit-scrollbar-thumb {background-color:rgb(var(--color-foreground)); border-radius:0.4rem; border:0;}
.collection-list-type-2 .slider::-webkit-scrollbar-track {background:rgba(var(--color-foreground), 0.04); border-radius:0.4rem;}
.collection-list-type-2 .slider-counter {display:flex; justify-content:center; min-width:2rem;}
.collection-list-type-2 .slider-counter span {display:none;}

@media screen and (min-width: 750px) {
.collection-list-type-2 .slider-counter--dots {margin:0 0rem;}
}

.collection-list-type-2 .slider-counter__link {padding:1rem;}

@media screen and (max-width: 749px) {
.collection-list-type-2 .slider-counter__link {padding:0.7rem;}
}

.collection-list-type-2 .slider-counter__link--dots .dot {width:1rem; height:1rem; border-radius:50%; border:0.1rem solid rgba(var(--color-foreground), 0.5); padding:0; display:block;}
.collection-list-type-2 .slider-counter__link--active.slider-counter__link--dots .dot {background-color:rgb(var(--color-foreground));}

@media screen and (forced-colors: active) {
.collection-list-type-2 .slider-counter__link--active.slider-counter__link--dots .dot {background-color:CanvasText;}
}

.collection-list-type-2 .slider-counter__link--dots:not(.slider-counter__link--active):hover .dot {border-color:rgb(var(--color-foreground));}
.collection-list-type-2 .slider-counter__link--dots .dot, .slider-counter__link--numbers {transition:transform 0.2s ease-in-out;}
.collection-list-type-2 .slider-counter__link--active.slider-counter__link--numbers, .slider-counter__link--dots:not(.slider-counter__link--active):hover .dot, .slider-counter__link--numbers:hover {transform:scale(1.1);}
.collection-list-type-2 .slider-counter__link--numbers {color:rgba(var(--color-foreground), 0.5); text-decoration:none;}
.collection-list-type-2 .slider-counter__link--numbers:hover {color:rgb(var(--color-foreground));}
.collection-list-type-2 .slider-counter__link--active.slider-counter__link--numbers {text-decoration:underline; color:rgb(var(--color-foreground));}
.collection-list-type-2 .slider-buttons {display:flex; align-items:center; justify-content:center;}
.collection-list-type-2 .slider-counter--numbers .slider-buttons {position:absolute; top:50%; width:100%; height:var(--swiper-navigation-size); margin-top:calc(0px - (var(--swiper-navigation-size)/ 2)); z-index:2; cursor:pointer; display:flex; align-items:center; justify-content:center; color:red; transform:translateY(-50%);}
.collection-list-type-2 .slider-counter--numbers button.slider-button.slider-button--prev {left:10px; right:auto; position:absolute;}
.collection-list-type-2 .slider-counter--numbers button.slider-button.slider-button--next {right:10px; left:auto; position:absolute;}
.collection-list-type-2 .slider-counter--dots .slideshow__controls.slider-buttons {position:absolute; bottom:40px; margin:auto; left:0; right:0; z-index:4;}
.collection-list-type-2 .slider-counter--counter .slideshow__controls.slider-buttons {position:absolute; bottom:40px; margin:auto; left:0; right:0; z-index:2;}

@media screen and (min-width: 990px) {
.collection-list-type-2 .slider:not(.slider--everywhere):not(.slider--desktop) + .slider-buttons {display:none;}
}

@media screen and (max-width: 989px) {
.collection-list-type-2 .slider--desktop:not(.slider--tablet) + .slider-buttons {display:none;}
}

@media screen and (min-width: 750px) {
.collection-list-type-2 .slider--mobile + .slider-buttons {display:none;}
}

.collection-list-type-2 .slider-button {color:rgb(var(--color-button-text)); background:rgba(var(--color-button), var(--alpha-button-background)); border:none; cursor:pointer; width:35px; height:35px; display:flex; align-items:center; justify-content:center; transition:all 0.3s linear; z-index:3; border-radius:50%;}
.collection-list-type-2 .slider-button:not([disabled]):hover {background-color:rgba( var(--color-hover-button), var(--alpha-button-background) ); color:rgba(var(--color-button-hover-text));}
.collection-list-type-2 .slider-button .icon {height:0.6rem;}
.collection-list-type-2 .slider-button[disabled] .icon {color:rgba(var(--color-button-text), 0.5); cursor:not-allowed;}
.collection-list-type-2 .slider-button--next .icon {transform:rotate(270deg); width:15px; height:15px;}
.collection-list-type-2 .slider-button--prev .icon {transform:rotate(90deg); width:15px; height:15px;}

.collection-list-type-2 .slider-buttons {margin-top:3rem;}