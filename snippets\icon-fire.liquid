<svg id="Group_25106" data-name="Group 25106" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="11.677" height="15" viewBox="0 0 11.677 15">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_111" data-name="Rectangle 111" width="11.677" height="15" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-2">
      <path id="Path_28592" data-name="Path 28592" d="M428.633,970.8a.159.159,0,0,1,0,.017l.008-.018h0" transform="translate(-428.629 -970.8)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient" x1="-354.048" y1="12.459" x2="-353.222" y2="12.459" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffca4c"/>
      <stop offset="1" stop-color="#ff9838"/>
    </linearGradient>
    <clipPath id="clip-path-4">
      <path id="Path_28594" data-name="Path 28594" d="M100.749,184.721a5.138,5.138,0,0,0-1.65,3.552c-.084,1.651-1.007,1.818-1.007,1.818a1.135,1.135,0,0,1-1.342-.755,4.677,4.677,0,0,0,.835,3.344c.037.051.076.1.117.152s.08.1.122.144l.032.036c.043.048.088.1.133.143l.045.045c.032.032.065.064.1.1l.035.033.079.071c.23.2.46.4.685.612.425.393.706.628.887.767a.755.755,0,0,1,.143.159.615.615,0,0,0,.095.122h0a.419.419,0,0,0,.048.026l0,0,.01,0,.129.01v0c0-.008,0-.016,0-.024s0-.03-.007-.053,0-.038-.006-.06a1.116,1.116,0,0,0-.022-.142,3.3,3.3,0,0,0-.981-1.445,3.049,3.049,0,0,1-.635-.854,3.872,3.872,0,0,1-.406-1.5c.5.269,1.108-.269,1.276-1.041a6.623,6.623,0,0,1,2.283-4.13c-.6,3.391,1.545,4.1,1.545,4.1a1.347,1.347,0,0,0,.638,1.175,3.374,3.374,0,0,1-.521,1.691,2.531,2.531,0,0,1-.453.524,5.012,5.012,0,0,0-1.047,1.342c-.034.062-.063.117-.086.164a1.422,1.422,0,0,0-.022.147h0l.065-.145.024-.017.34-.237c.339-.237.837-.586,1.146-.809l.073-.047.071-.048c.047-.031.093-.064.137-.1s.088-.065.13-.1l.062-.049.06-.049.044-.037c.057-.048.111-.1.163-.145l.035-.034.051-.05c.046-.045.09-.09.132-.135a3.477,3.477,0,0,0,.942-1.787.571.571,0,0,1-.363.165l-.034,0h-.005l-.028,0h0l-.027-.006-.041-.015-.022-.011-.006,0-.024-.014-.021-.014h0l-.015-.012-.015-.012a.454.454,0,0,1-.041-.039l-.017-.019a.5.5,0,0,1-.089-.159.2.2,0,0,1-.008-.024.2.2,0,0,1-.007-.024s0,0,0,0a.147.147,0,0,1-.005-.024.537.537,0,0,1,.013-.258c0-.009.005-.017.009-.025s.006-.017.01-.025l.019-.048.018-.047,0-.005c.012-.031.023-.061.034-.091a3.6,3.6,0,0,0,.141-.492c.01-.049.019-.1.026-.143,0-.023.007-.046.01-.068.008-.062.013-.12.016-.176a1.329,1.329,0,0,0-.114-.721c-1.039.627-1.45.189-1.614-.276a1.54,1.54,0,0,1-.026-.923,4.692,4.692,0,0,0-1.591-4.774,2.526,2.526,0,0,1-.713,1.8" transform="translate(-96.707 -182.917)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-2" x1="0.053" y1="1.147" x2="0.054" y2="1.147" xlink:href="#linear-gradient"/>
    <clipPath id="clip-path-6">
      <path id="Path_28596" data-name="Path 28596" d="M194.509,378.683c-.168.772-.772,1.31-1.276,1.041a3.863,3.863,0,0,0,.406,1.5,3.047,3.047,0,0,0,.635.854,3.305,3.305,0,0,1,.981,1.445,1.039,1.039,0,0,1,.022.142c0,.021,0,.041.006.059s.005.043.007.053l0,.025a.007.007,0,0,0,0,0l.136.007h0c0-.011,0-.023-.006-.036-.005-.029-.012-.063-.021-.1a2.692,2.692,0,0,0-.824-1.31,3.042,3.042,0,0,1-.867-1.958c.42.224.923-.224,1.063-.867a5.52,5.52,0,0,1,1.9-3.441c-.5,2.825,1.287,3.413,1.287,3.413a1.123,1.123,0,0,0,.531.979,2.652,2.652,0,0,1-.811,1.846,3.953,3.953,0,0,0-.89,1.231c-.029.061-.053.114-.071.156l.047-.009.023,0,.052-.01a1.421,1.421,0,0,1,.022-.147c.023-.047.052-.1.086-.164A5.008,5.008,0,0,1,198,382.04a2.53,2.53,0,0,0,.453-.525,3.372,3.372,0,0,0,.521-1.691,1.348,1.348,0,0,1-.638-1.175s-2.149-.705-1.545-4.1a6.625,6.625,0,0,0-2.283,4.13" transform="translate(-193.233 -374.553)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-3" x1="-0.113" y1="1.25" x2="-0.111" y2="1.25" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-4" x1="-0.093" y1="1.187" x2="-0.091" y2="1.187" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffde11"/>
      <stop offset="1" stop-color="#ffb443"/>
    </linearGradient>
    <clipPath id="clip-path-9">
      <path id="Path_28599" data-name="Path 28599" d="M225.54,478.372c-.14.643-.643,1.091-1.063.867a3.042,3.042,0,0,0,.867,1.958,2.691,2.691,0,0,1,.824,1.31c.009.039.016.073.021.1,0,.013,0,.025.006.036h0l.108,0h.122a6,6,0,0,0,1.006-.086l.057-.01c.018-.042.042-.1.071-.156a3.952,3.952,0,0,1,.89-1.231,2.652,2.652,0,0,0,.811-1.846,1.123,1.123,0,0,1-.532-.979s-1.79-.587-1.287-3.413a5.52,5.52,0,0,0-1.9,3.441" transform="translate(-224.477 -474.931)" fill="none"/>
    </clipPath>
    <linearGradient id="linear-gradient-5" x1="-0.235" y1="1.25" x2="-0.233" y2="1.25" xlink:href="#linear-gradient"/>
    <linearGradient id="linear-gradient-6" x1="-0.211" y1="1.187" x2="-0.209" y2="1.187" xlink:href="#linear-gradient-4"/>
    <linearGradient id="linear-gradient-7" x1="-0.211" y1="1.187" x2="-0.209" y2="1.187" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fefde4"/>
      <stop offset="1" stop-color="#ffd449"/>
    </linearGradient>
  </defs>
  <g id="Group_24671" data-name="Group 24671">
    <g id="Group_24670" data-name="Group 24670" clip-path="url(#clip-path)">
      <path id="Path_28582" data-name="Path 28582" d="M19.405,22.078a2.861,2.861,0,0,1-.6.431c-.07.041-.148.085-.233.13l0-.045-.014-.146-.011-.106v-.014a.2.2,0,0,0-.107-.151.1.1,0,0,1-.038-.032.16.16,0,0,0-.1-.074l-.039-.008-.283-.056c.046-.045.09-.09.132-.135a3.477,3.477,0,0,0,.942-1.787.571.571,0,0,1-.363.165l-.034,0h-.005l-.028,0h0l-.027-.006-.041-.015-.022-.011-.006,0-.024-.014-.021-.014h0l-.015-.012-.015-.012a.449.449,0,0,1-.041-.039l-.017-.019a.5.5,0,0,1-.089-.159l-.008-.024-.007-.024s0,0,0,0l-.005-.024a.561.561,0,0,1,.013-.258c0-.009.005-.017.009-.025s.006-.017.01-.025l.019-.048.018-.047,0-.005c.012-.031.023-.061.034-.091a3.6,3.6,0,0,0,.141-.492c.01-.049.019-.1.026-.143,0-.023.007-.046.01-.068.008-.062.013-.12.016-.176a1.329,1.329,0,0,0-.114-.721c-1.039.627-1.45.189-1.614-.276a1.54,1.54,0,0,1-.026-.923,4.692,4.692,0,0,0-1.591-4.774,2.526,2.526,0,0,1-.713,1.8,5.138,5.138,0,0,0-1.65,3.552c-.084,1.651-1.007,1.818-1.007,1.818a1.135,1.135,0,0,1-1.342-.755,4.677,4.677,0,0,0,.835,3.344c.037.051.076.1.117.152l-.29.046a.333.333,0,0,0-.252.182.239.239,0,0,1-.014.027.314.314,0,0,1-.017.027h0a.361.361,0,0,0-.069.274l0,.019.016.065h0l.016.067.052.209h0l.026.1c-.09-.062-.175-.125-.256-.187a6.032,6.032,0,0,1-1-.953,3.237,3.237,0,0,0,.681.268h0a.161.161,0,0,0,.175-.086l.021-.039,0-.006a.147.147,0,0,0,0-.143h0l0,0c-.13-.217-.37-.6-.748-1.134a3.631,3.631,0,0,1-.656-1.7,4.019,4.019,0,0,1,1.306-3.309c-.214,2.434,1.62,2.007,1.623,1.132a5.922,5.922,0,0,1,1.11-4.014c1.815.491,1.22-3.423,1.22-3.423s1.185.956,2.44,2.053a4.994,4.994,0,0,1,.374.362,4.106,4.106,0,0,1,1.07,3.19,2.744,2.744,0,0,0,.151,1.416.578.578,0,0,0,.393.3c.654.13.774-1.067.774-1.067,2.322,4.115-.466,6.05-.9,6.458a.3.3,0,0,0-.1.136c-.016.07.05.106.15.124a2.661,2.661,0,0,0,.642-.019" transform="translate(-8.991 -8.986)" fill="#ff4610"/>
      <path id="Path_28583" data-name="Path 28583" d="M119.925,867.5h0l-.016-.065Z" transform="translate(-118.071 -854.137)" fill="#ff4610"/>
      <path id="Path_28584" data-name="Path 28584" d="M9.816,12.953l-.022.021a2.318,2.318,0,0,0,.6-.02.14.14,0,0,1,.123.234,3.083,3.083,0,0,1-.688.489,8.59,8.59,0,0,1-.92.449c.276-.141.5-.269.681-.372l-.01-.1c.085-.045.163-.089.233-.13a2.861,2.861,0,0,0,.6-.431,2.66,2.66,0,0,1-.642.019c-.1-.018-.166-.055-.15-.124a.3.3,0,0,1,.1-.136c.437-.407,3.225-2.342.9-6.458,0,0-.12,1.2-.774,1.067a.578.578,0,0,1-.393-.3A2.744,2.744,0,0,1,9.3,5.745a4.106,4.106,0,0,0-1.07-3.19,4.991,4.991,0,0,0-.374-.362C6.6,1.1,5.42.14,5.42.14s.6,3.915-1.22,3.423a5.922,5.922,0,0,0-1.11,4.014c0,.875-1.836,1.3-1.623-1.132A4.019,4.019,0,0,0,.162,9.754a3.631,3.631,0,0,0,.656,1.7c.379.534.618.917.748,1.134l0,0h0a.147.147,0,0,1,0,.143l0,.006-.021.039a.161.161,0,0,1-.175.086h0A3.237,3.237,0,0,1,.688,12.6a6.031,6.031,0,0,0,1,.953c.081.062.166.125.256.187l.024.1c.135.1.283.2.442.3a5.8,5.8,0,0,0,2.47.84l.01,0a6.142,6.142,0,0,1-2.68-.9c-.169-.1-.326-.21-.47-.315A6.18,6.18,0,0,1,.577,12.685a.14.14,0,0,1,.172-.211,3.2,3.2,0,0,0,.65.257.044.044,0,0,0,.047-.017.043.043,0,0,0,0-.049c-.134-.224-.371-.6-.744-1.128A3.783,3.783,0,0,1,.022,9.769,3.944,3.944,0,0,1,.613,7.264a4.107,4.107,0,0,1,.766-.926.14.14,0,0,1,.229.119c-.094,1.069.221,1.445.416,1.573a.6.6,0,0,0,.619.019.551.551,0,0,0,.308-.471A6.012,6.012,0,0,1,4.1,3.469a.139.139,0,0,1,.14-.041.676.676,0,0,0,.67-.114,2.554,2.554,0,0,0,.466-1.8A9.791,9.791,0,0,0,5.282.161a.14.14,0,0,1,.226-.13C5.52.04,6.709,1,7.952,2.087c.133.117.262.241.385.372a4.249,4.249,0,0,1,1.107,3.3,2.56,2.56,0,0,0,.135,1.337.438.438,0,0,0,.3.228.293.293,0,0,0,.245-.053,1.5,1.5,0,0,0,.363-.891.14.14,0,0,1,.261-.055,5.316,5.316,0,0,1,.683,4.514,4.888,4.888,0,0,1-1.5,2.013c-.047.041-.089.076-.116.1" transform="translate(0 0)" fill="#ee0301"/>
      <path id="Path_28585" data-name="Path 28585" d="M541.592,879.529c-.045.032-.09.064-.137.1.047-.032.093-.063.137-.1" transform="translate(-533.156 -866.048)" fill="#f79500"/>
      <path id="Path_28586" data-name="Path 28586" d="M550.545,873.16c-.042.033-.086.065-.13.1.045-.032.088-.065.13-.1" transform="translate(-541.978 -859.776)" fill="#f79500"/>
      <path id="Path_28587" data-name="Path 28587" d="M119.925,867.5h0l-.016-.065Z" transform="translate(-118.071 -854.137)" fill="#ff4610"/>
      <path id="Path_28588" data-name="Path 28588" d="M607.836,686.256l-.019.047a.026.026,0,0,1,0-.006l.017-.042" transform="translate(-598.5 -675.737)" fill="#ff4610"/>
      <path id="Path_28589" data-name="Path 28589" d="M122.353,832.1a5.8,5.8,0,0,1-2.47-.84c-.159-.1-.307-.2-.442-.3l-.024-.1-.026-.1h0l-.052-.209-.016-.067-.016-.065,0-.019a.361.361,0,0,1,.069-.274h0c.006-.009.012-.018.017-.027a.241.241,0,0,0,.014-.027.333.333,0,0,1,.252-.182l.29-.046c.039.048.08.1.122.144l.032.036c.043.048.088.1.133.142l.045.045c.032.032.065.064.1.1l.035.033.079.071c.23.2.46.4.685.612.425.393.706.628.887.767a.758.758,0,0,1,.143.159.615.615,0,0,0,.095.122h0a.42.42,0,0,0,.048.026Z" transform="translate(-117.468 -817.12)" fill="#f79500"/>
      <path id="Path_28590" data-name="Path 28590" d="M119.925,867.5h0l-.016-.065Z" transform="translate(-118.071 -854.137)" fill="#f79500"/>
      <path id="Path_28591" data-name="Path 28591" d="M432.186,850.167c-.181.1-.4.232-.681.372a16.225,16.225,0,0,1-2.217.731h-.006l-.107.022.065-.145.024-.017.34-.237c.339-.237.837-.586,1.146-.809l.073-.047.072-.048c.047-.031.093-.064.137-.1s.088-.065.13-.1l.062-.049.06-.049.044-.037c.057-.048.111-.1.163-.145l.035-.034.051-.05.283.056.039.008a.16.16,0,0,1,.1.074.1.1,0,0,0,.038.032.2.2,0,0,1,.107.151v.014l.011.106.014.146,0,.045Z" transform="translate(-422.597 -836.414)" fill="#f79500"/>
    </g>
  </g>
  <g id="Group_24673" data-name="Group 24673" transform="translate(6.57 14.88)">
    <g id="Group_24672" data-name="Group 24672" clip-path="url(#clip-path-2)">
      <rect id="Rectangle_112" data-name="Rectangle 112" width="0.019" height="0.009" transform="translate(-0.001 0.018) rotate(-87.274)" fill="url(#linear-gradient)"/>
    </g>
  </g>
  <g id="Group_24675" data-name="Group 24675">
    <g id="Group_24674" data-name="Group 24674" clip-path="url(#clip-path)">
      <path id="Path_28593" data-name="Path 28593" d="M105.281,191.211a3.477,3.477,0,0,1-.942,1.787c-.042.045-.086.09-.132.135l-.051.05-.036.034c-.052.048-.106.1-.163.145l-.044.037-.06.049-.062.049c-.042.033-.085.065-.13.1s-.09.065-.137.1l-.071.048-.073.047c-.309.223-.807.573-1.146.809l-.34.237-.024.017-.065.145h0a1.42,1.42,0,0,1,.022-.147c.023-.047.052-.1.086-.164a5.012,5.012,0,0,1,1.047-1.341,2.531,2.531,0,0,0,.453-.524,3.374,3.374,0,0,0,.521-1.691,1.347,1.347,0,0,1-.638-1.175s-2.149-.705-1.545-4.1a6.623,6.623,0,0,0-2.283,4.13c-.168.772-.772,1.309-1.276,1.041a3.872,3.872,0,0,0,.406,1.5,3.049,3.049,0,0,0,.635.854,3.3,3.3,0,0,1,.981,1.445,1.109,1.109,0,0,1,.022.142c0,.021,0,.041.006.06s.005.043.007.053,0,.017,0,.024v0l-.129-.01-.01,0,0,0a.426.426,0,0,1-.048-.026h0a.616.616,0,0,1-.095-.122.755.755,0,0,0-.143-.159c-.18-.139-.462-.374-.887-.767-.225-.208-.455-.41-.685-.612l-.079-.071-.035-.033c-.034-.032-.067-.063-.1-.1l-.045-.045c-.046-.047-.09-.095-.133-.142l-.032-.036c-.042-.048-.083-.1-.122-.144s-.08-.1-.117-.152a4.677,4.677,0,0,1-.835-3.344,1.135,1.135,0,0,0,1.342.755s.923-.168,1.007-1.818a5.138,5.138,0,0,1,1.65-3.552,2.526,2.526,0,0,0,.713-1.8,4.692,4.692,0,0,1,1.591,4.774,1.54,1.54,0,0,0,.026.923c.163.465.575.9,1.614.276a1.329,1.329,0,0,1,.114.721c0,.055-.009.114-.016.176,0,.022-.006.045-.01.068-.007.046-.016.094-.026.143a3.6,3.6,0,0,1-.141.492c-.011.03-.022.06-.034.091l0,.005-.018.047-.019.048-.01.025c0,.008-.006.017-.009.025a.536.536,0,0,0-.013.258.149.149,0,0,0,.005.024s0,0,0,0a.206.206,0,0,0,.007.024.2.2,0,0,0,.008.024.5.5,0,0,0,.089.159l.017.019a.449.449,0,0,0,.041.039l.015.012.015.012h0l.021.014.024.014.006,0,.022.011.041.015.027.006h0l.028,0h.005l.034,0a.571.571,0,0,0,.363-.165" transform="translate(-95.224 -180.113)" fill="#f79500"/>
    </g>
  </g>
  <g id="Group_24677" data-name="Group 24677" transform="translate(1.482 2.804)">
    <g id="Group_24676" data-name="Group 24676" clip-path="url(#clip-path-4)">
      <rect id="Rectangle_114" data-name="Rectangle 114" width="12.592" height="9.428" transform="translate(-0.863 12.157) rotate(-87.274)" fill="url(#linear-gradient-2)"/>
    </g>
  </g>
  <g id="Group_24679" data-name="Group 24679">
    <g id="Group_24678" data-name="Group 24678" clip-path="url(#clip-path)">
      <path id="Path_28595" data-name="Path 28595" d="M196.953,383.382A5.008,5.008,0,0,1,198,382.04a2.53,2.53,0,0,0,.453-.525,3.372,3.372,0,0,0,.521-1.691,1.348,1.348,0,0,1-.638-1.175s-2.149-.705-1.545-4.1a6.625,6.625,0,0,0-2.283,4.13c-.168.772-.772,1.31-1.276,1.041a3.863,3.863,0,0,0,.406,1.5,3.048,3.048,0,0,0,.635.854,3.305,3.305,0,0,1,.981,1.445,1.037,1.037,0,0,1,.022.142c0,.021,0,.041.006.059s.005.043.007.053l0,.025a.006.006,0,0,0,0,0l.136.007h0c0-.011,0-.023-.006-.036-.005-.029-.012-.063-.021-.1a2.692,2.692,0,0,0-.824-1.31,3.042,3.042,0,0,1-.867-1.958c.42.224.923-.224,1.063-.867a5.52,5.52,0,0,1,1.9-3.441c-.5,2.825,1.287,3.413,1.287,3.413a1.123,1.123,0,0,0,.531.979,2.652,2.652,0,0,1-.811,1.846,3.953,3.953,0,0,0-.89,1.231c-.029.061-.053.114-.071.156l.047-.009.023,0,.052-.01a1.421,1.421,0,0,1,.022-.147c.023-.047.052-.1.086-.164" transform="translate(-190.271 -368.812)" fill="#f79500"/>
    </g>
  </g>
  <g id="Group_24681" data-name="Group 24681" transform="translate(2.962 5.741)">
    <g id="Group_24680" data-name="Group 24680" clip-path="url(#clip-path-6)">
      <rect id="Rectangle_116" data-name="Rectangle 116" width="9.517" height="6.175" transform="translate(-0.44 9.233) rotate(-87.274)" fill="url(#linear-gradient-3)"/>
    </g>
  </g>
  <g id="Group_24683" data-name="Group 24683" transform="translate(2.962 5.741)">
    <g id="Group_24682" data-name="Group 24682" clip-path="url(#clip-path-6)">
      <rect id="Rectangle_117" data-name="Rectangle 117" width="9.838" height="6.74" transform="matrix(0.112, -0.994, 0.994, 0.112, -1.028, 9.138)" fill="url(#linear-gradient-4)"/>
    </g>
  </g>
  <g id="Group_24685" data-name="Group 24685">
    <g id="Group_24684" data-name="Group 24684" clip-path="url(#clip-path)">
      <path id="Path_28598" data-name="Path 28598" d="M228.45,481.17a2.652,2.652,0,0,0,.811-1.846,1.123,1.123,0,0,1-.531-.979s-1.79-.587-1.287-3.413a5.52,5.52,0,0,0-1.9,3.441c-.14.643-.643,1.091-1.063.867a3.042,3.042,0,0,0,.867,1.958,2.691,2.691,0,0,1,.824,1.31c.009.039.016.073.021.1,0,.013,0,.025.006.036h0l.108,0h.122a6,6,0,0,0,1.006-.086l.057-.01c.018-.042.042-.1.071-.156a3.952,3.952,0,0,1,.89-1.231" transform="translate(-221.037 -467.652)" fill="#f79500"/>
    </g>
  </g>
  <g id="Group_24687" data-name="Group 24687" transform="translate(3.441 7.28)">
    <g id="Group_24686" data-name="Group 24686" clip-path="url(#clip-path-9)">
      <rect id="Rectangle_119" data-name="Rectangle 119" width="7.939" height="5.145" transform="translate(-0.367 7.703) rotate(-87.274)" fill="url(#linear-gradient-5)"/>
    </g>
  </g>
  <g id="Group_24689" data-name="Group 24689" transform="translate(3.441 7.28)">
    <g id="Group_24688" data-name="Group 24688" clip-path="url(#clip-path-9)">
      <rect id="Rectangle_120" data-name="Rectangle 120" width="8.207" height="5.617" transform="translate(-0.858 7.624) rotate(-83.581)" fill="url(#linear-gradient-6)"/>
    </g>
  </g>
  <g id="Group_24691" data-name="Group 24691" transform="translate(3.441 7.28)">
    <g id="Group_24690" data-name="Group 24690" clip-path="url(#clip-path-9)">
      <rect id="Rectangle_121" data-name="Rectangle 121" width="8.207" height="5.617" transform="translate(-0.858 7.624) rotate(-83.581)" fill="url(#linear-gradient-7)"/>
    </g>
  </g>
  <g id="Group_24693" data-name="Group 24693">
    <g id="Group_24692" data-name="Group 24692" clip-path="url(#clip-path)">
      <path id="Path_28602" data-name="Path 28602" d="M57.174,250.621a2.318,2.318,0,0,1-.313.384,2.556,2.556,0,0,0-.562.79,1.451,1.451,0,0,0-.024.984,2.3,2.3,0,0,0,.9-1.518,2.352,2.352,0,0,0,0-.64" transform="translate(-55.339 -246.78)" fill="#ff4610"/>
      <path id="Path_28603" data-name="Path 28603" d="M50.978,234.85a.048.048,0,0,0-.094.013c0,.139-.161.312-.347.513a2.63,2.63,0,0,0-.582.821,1.487,1.487,0,0,0,0,1.116.048.048,0,0,0,.032.026H50a.048.048,0,0,0,.029-.01,2.4,2.4,0,0,0,.949-2.48m-.957,2.365a1.451,1.451,0,0,1,.024-.985,2.557,2.557,0,0,1,.562-.79,2.318,2.318,0,0,0,.313-.384,2.352,2.352,0,0,1,0,.64,2.3,2.3,0,0,1-.9,1.518" transform="translate(-49.086 -231.216)" fill="#ee0301"/>
    </g>
  </g>
</svg>
