{{ 'section-brand-logos.css' | asset_url | stylesheet_tag }}
<link rel="stylesheet" href="{{ 'component-brand-slider.css' | asset_url }}" media="print" onload="this.media='all'">
<noscript>{{ 'component-brand-slider.css' | asset_url | stylesheet_tag }}</noscript>

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.50 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.50 | round: 0 }}px;
  }
   @media screen and (min-width: 576px) and (max-width: 749px){
   .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }
   }
  @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  }
{%- endstyle -%}

{%- liquid
  assign columns_mobile_int = section.settings.columns_mobile | plus: 0
  assign show_mobile_slider = false
  if section.settings.swipe_on_mobile 
    assign show_mobile_slider = true
  endif

  assign show_desktop_slider = false
  if section.settings.enable_desktop_slider and section.block.size > section.settings.columns_desktop
    assign show_desktop_slider = true
  endif

  if section.settings.swiper_enable
  assign enable_slider = true  
  endif
  
-%}

<div class="brand-logos color-{{ section.settings.color_scheme }} gradient{% unless section.settings.background_style == 'none' and settings.text_boxes_border_thickness > 0 or settings.text_boxes_shadow_opacity > 0 %} background-{{ section.settings.background_style }}{% endunless %}{% if section.settings.title == blank %} no-heading{% endif %}  {{ section.settings.custom_class_name }}">
  <div class="{% if section.settings.page_full_width %} page-full-width {% else %} page-width {% endif %} {% if section.settings.page_full_width_spacing %} page-full-width_spacing {% endif %} section-{{ section.id }}-padding isolate">
    <div class="row">
    {%- unless section.settings.title == blank -%}
      <div class="title-wrapper-with-link title-wrapper--self-padded-mobile title-wrapper--no-top-margin content-align--{{ section.settings.column_alignment }}">
         {%- if section.settings.sub_heading != blank -%}  
         <h6 class="sub-heading">{{ section.settings.sub_heading | escape }}</h6>
         {%- endif -%} 
         {%- if section.settings.title != blank -%}  
          <h2 class="title {{ section.settings.heading_size }}">
            {{ section.settings.title | escape }}
          </h2>
          {%- endif -%} 
          {%- if section.settings.description != blank -%}  
          <p class="description">{{ section.settings.description }}</p>
          {%- endif -%}   
          {%- if section.settings.button_label != blank -%}
            <a {% if section.settings.button_link == blank %} role="link" aria-disabled="true"{% else %} href="{{ section.settings.button_link }}"{% endif %} class="button{% if section.settings.button_style_secondary %} button--secondary{% else %} button--primary{% endif %}">{{ section.settings.button_label | escape }}</a>
          {%- endif -%}
          </div>
    {%- endunless -%}
    {% unless enable_slider %}
    <slider-component class="slider-mobile-gutter">
      {% else %}
    <swiper-slider>
      <div data-slider-options='{"loop": "{%- if section.settings.centered_slide -%}1{%- else -%}2{%- endif -%}","desktop": "{{ section.settings.desktop_column }}", "tablet": "{{ section.settings.tablet_column }}","mobile": "{{ section.settings.mobile_column }}","auto_play": "{{ section.settings.auto_play }}"}'>
      <div class="swiper" data-swiper-slider>
      {%- endunless -%}
      <ul class="brand-logos-list slider contains-content-container {% if enable_slider %} swiper-wrapper{% else %} grid grid--{{ section.settings.columns_mobile }}-col-tablet-down grid--{{ section.settings.columns_desktop }}-col-desktop{% endif %}{% if show_mobile_slider %} slider slider--mobile grid--peek{% endif %}"
        id="Slider-{{ section.id }}"
        role="list"
      >
        
        {%- for block in section.blocks -%}
          {%- assign empty_column = '' -%}
          {%- if block.settings.image == blank and block.settings.title == blank and block.settings.text == blank and block.settings.link_label == blank -%}
            {%- assign empty_column = ' brand-logos-list__item--empty' -%}
          {%- endif -%}

          <li id="Slide-{{ section.id }}-{{ forloop.index }}" class=" color-{{ section.settings.block_color_scheme }} gradient {% unless enable_slider %} brand-logos-list__item grid__item {% if section.settings.swipe_on_desktop %} slider__slide{% endif %} {% else %} swiper-slide{% endunless %} center {{ empty_column }}" {{ block.shopify_attributes }}>
            <a class="link animate-arrow" {% if block.settings.link == blank %}role="link" aria-disabled="true"{% else %}href="{{ block.settings.link }}"{% endif %}>
           
                <div class="brand-logos-card__image-wrapper  brand-logos-card__image-wrapper--{{ section.settings.image_width }}-width">
             
            {%- if block.settings.image != blank -%}        
                 <img
                      class="brand-logos-card__image"
                      srcset="{%- if block.settings.image.width >= 375 -%}{{ block.settings.image | image_url: width: 375 }} 375w,{%- endif -%}
                      {%- if block.settings.image.width >= 550 -%}{{ block.settings.image | image_url: width: 550 }} 550w,{%- endif -%}
                      {%- if block.settings.image.width >= 750 -%}{{ block.settings.image | image_url: width: 750 }} 750w,{%- endif -%}
                      {%- if block.settings.image.width >= 1100 -%}{{ block.settings.image | image_url: width: 1100 }} 1100w,{%- endif -%}
                      {%- if block.settings.image.width >= 1500 -%}{{ block.settings.image | image_url: width: 1500 }} 1500w,{%- endif -%}
                      {%- if block.settings.image.width >= 1780 -%}{{ block.settings.image | image_url: width: 1780 }} 1780w,{%- endif -%}
                      {%- if block.settings.image.width >= 2000 -%}{{ block.settings.image | image_url: width: 2000 }} 2000w,{%- endif -%}
                      {%- if block.settings.image.width >= 3000 -%}{{ block.settings.image | image_url: width: 3000 }} 3000w,{%- endif -%}
                      {%- if block.settings.image.width >= 3840 -%}{{ block.settings.image | image_url: width: 3840 }} 3840w,{%- endif -%}
                      {{ block.settings.image | image_url: width: 1500 }} {{ block.settings.image.width }}w"
                      sizes="100vw"
                      src="{{ block.settings.image | image_url: width: 1500 }}"
                      loading="lazy"              
                      alt="{{ block.settings.image.alt | escape }}"
                      width="{{ block.settings.image.width }}"
                      height="{{ block.settings.image.width | divided_by: block.settings.image.aspect_ratio | round }}"
                    >
                 {%- else -%}
                    {{ 'logo' | placeholder_svg_tag: 'placeholder_svg' }}
                  {%- endif -%}
                 
                </div>              
            </a> 
          </li>
        {%- endfor -%}
      </ul>

    {% unless enable_slider %}
      {%- if show_mobile_slider -%}
        <div class="brand-swiper-buttons no-js-hidden large-up-hide medium-hide">
          <button type="button" class="slider-button slider-button--prev" name="previous" aria-label="{{ 'general.slider.previous_slide' | t }}">{% render 'icon-caret' %}</button>
         <div class="slider-counter caption">
            <span class="slider-counter--current">1</span>
            <span aria-hidden="true"> / </span>
            <span class="visually-hidden">{{ 'general.slider.of' | t }}</span>
            <span class="slider-counter--total">{{ section.blocks.size }}</span>
          </div> 
          <button type="button" class="slider-button slider-button--next" name="next" aria-label="{{ 'general.slider.next_slide' | t }}">{% render 'icon-caret' %}</button>
        </div>
      {%- endif -%}
    </slider-component>
    {% else %}
        {% if section.settings.swiper_navigation != blank %}
        <div class="swiper-button-next"><span></span></div>
        <div class="swiper-button-prev"><span></span></div>
        {% endif %}
    </div>
    {% if section.settings.swiper_pagination != blank %}
    <div class="swiper-pagination"></div>
    {% endif %}
    </div>
    </swiper-slider>
    {%- endunless -%}
   </div>
  </div>
</div>

{% schema %}
{
  "name": "t:sections.brand-logos.name",
  "class": "section brand-logos",
  "tag": "section",
  "settings": [
     {
      "type":"checkbox",
      "id":"page_full_width",
       "default": false,
      "label": "t:sections.all.page_full_width.label"
    },
    {
      "type":"checkbox",
      "id":"page_full_width_spacing",
       "default": false,
      "label": "t:sections.all.page_full_width_spacing.label"
    },
     {
      "type": "text",
      "id": "title",
      "default": "Brand Logo",
      "label": "t:sections.all.title.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "h2",
          "label": "t:sections.all.heading_size.options__1.label"
        },
        {
          "value": "h1",
          "label": "t:sections.all.heading_size.options__2.label"
        },
        {
          "value": "h0",
          "label": "t:sections.all.heading_size.options__3.label"
        }
      ],
      "default": "h1",
      "label": "t:sections.all.heading_size.label"
    },
    {
      "type": "text",
      "id": "sub_heading",
      "default": "Sub Heading", 
      "label": "t:sections.all.sub_heading.label"
    },
     {
      "type": "text",
      "id": "description",
      "default": "Use This Text To Share The Information Which You Like!.",   
      "label": "t:sections.all.description.label"
    },
    {
      "type": "text",
      "id": "button_label",
      "default": "Button label",
      "label": "t:sections.all.button_label.label"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "t:sections.all.button_link.label"
    },
    {
      "type": "checkbox",
      "id": "button_style_secondary",
      "default": false,
      "label": "t:sections.all.button_style_secondary.label"
      },
     {
      "type": "select",
      "id": "column_alignment",
      "label": "t:sections.brand-logos.settings.column_alignment.label",
      "options": [
        {
          "value": "left",
          "label": "t:sections.brand-logos.settings.column_alignment.options__1.label"
        },
        {
          "value": "center",
          "label": "t:sections.brand-logos.settings.column_alignment.options__2.label"
        }
      ],
      "default": "center"
    },
    {
      "type": "range",
      "id": "columns_desktop",
      "min": 1,
      "max": 6,
      "step": 1,
      "default": 5,
      "label": "t:sections.brand-logos.settings.columns_desktop.label"
    },
    {
      "type": "select",
      "id": "background_style",
      "options": [
        {
          "value": "none",
          "label": "t:sections.brand-logos.settings.background_style.options__1.label"
        },
        {
          "value": "primary",
          "label": "t:sections.brand-logos.settings.background_style.options__2.label"
        }
      ],
      "default": "primary",
      "label": "t:sections.brand-logos.settings.background_style.label"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "t:sections.all.colors.accent_1.label"
        },
        {
          "value": "accent-2",
          "label": "t:sections.all.colors.accent_2.label"
        },
        {
          "value": "background-1",
          "label": "t:sections.all.colors.background_1.label"
        },
        {
          "value": "background-2",
          "label": "t:sections.all.colors.background_2.label"
        },
        {
          "value": "inverse",
          "label": "t:sections.all.colors.inverse.label"
        }
      ],
      "default": "background-1",
      "label": "t:sections.all.colors.label"
    },
     {
      "type": "header",
      "content": "t:sections.brand-logos.settings.block_color_setting.content"
    },
    {
      "type": "select",
      "id": "block_color_scheme",
      "options": [
        {
          "value": "accent-1",
          "label": "t:sections.all.colors.accent_1.label"
        },
        {
          "value": "accent-2",
          "label": "t:sections.all.colors.accent_2.label"
        },
        {
          "value": "background-1",
          "label": "t:sections.all.colors.background_1.label"
        },
        {
          "value": "background-2",
          "label": "t:sections.all.colors.background_2.label"
        },
        {
          "value": "inverse",
          "label": "t:sections.all.colors.inverse.label"
        }
      ],
      "default": "background-1",
      "label": "t:sections.all.colors.label"
    },
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 120,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 120,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    },
     {	
    "type": "header",	
    "content": "t:sections.all.custom_class_heading.content"	
    },	
    {	
    "type": "text",	
    "id": "custom_class_name",	
    "label": "t:sections.all.custom_class_name.label"	
    },
    {
      "type": "header",
      "content": "t:sections.all.swiper.swiper_slider_title"
    },
    {
      "type": "checkbox",
      "id": "swiper_enable",
      "default": false,
      "label": "t:sections.all.swiper.swiper_slider_enable"
    },
    {
      "type": "range",
      "id": "desktop_column",
      "min": 1,
      "max": 10,
      "step": 1,
      "label": "t:sections.all.swiper.desktop_column",
      "default": 4
    },
    {
      "type": "range",
      "id": "tablet_column",
      "min": 1,
      "max": 5,
      "step": 1,
      "label": "t:sections.all.swiper.tablet_column",
      "default": 3
    },
    {
      "type": "range",
      "id": "mobile_column",
      "min": 1,
      "max": 3,
      "step": 1,
      "label": "t:sections.all.swiper.mobile_column",
      "default": 1
    },
    {
      "type": "checkbox",
      "id": "swiper_pagination",
      "default": false,
      "label": "t:sections.all.swiper.swiper_pagination"
    },
    {
      "type": "checkbox",
      "id": "swiper_navigation",
      "default": false,
      "label": "t:sections.all.swiper.swiper_navigation"
    },
    {
      "type": "checkbox",
      "id": "centered_slide",
      "default": false,
      "label": "t:sections.all.swiper.centered_slide"
    }, 
    {
      "type": "range",
      "id": "auto_play",
      "min": 0,
      "max": 5,
      "step": 1,
      "label": "t:sections.all.swiper.auto_play",
      "default": 0
    },
    {
      "type": "checkbox",
      "id": "swipe_on_mobile",
      "default": false,
      "label": "t:sections.brand-logos.settings.swipe_on_mobile.label"
    },
     {
      "type": "checkbox",
      "id": "arrow_on_mobile",
      "default": false,
      "label": "t:sections.multicolumn.settings.swipe_on_mobile.controls"
    },
     {
      "type": "select",
      "id": "columns_mobile",
      "options": [
        {
          "value": "1",
          "label": "t:sections.all.swiper.options__1"
        },
        {
          "value": "2",
          "label": "t:sections.all.swiper.options__2"
        }
      ],
      "default": "1",
      "label": "t:sections.all.swiper.columns_mobile"
    }
  ],
  "blocks": [
    {
      "type": "column",
      "name": "t:sections.brand-logos.blocks.column.name",
       "limit": 8,
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.brand-logos.blocks.column.settings.image.label"
        },
        {
          "type": "url",
          "id": "link",
          "label": "t:sections.brand-logos.blocks.column.settings.link.label"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.brand-logos.presets.name",
      "blocks": [
        {
          "type": "column"
        },
        {
          "type": "column"
        },
        {
          "type": "column"
        },
        {
          "type": "column"
        },
        {
          "type": "column"
        }
      ]
    }
  ]
}
{% endschema %}
