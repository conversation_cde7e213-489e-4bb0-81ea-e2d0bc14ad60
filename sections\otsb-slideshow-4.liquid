{% render 'otsb-slideshow-base' %}

{% schema %}
{
  "name": "OT: Slideshow #4",
  "tag": "section",
  "class": "section section-slideshow",
  "disabled_on": {
    "groups": [
      "header",
      "footer",
      "aside"
    ]
  },
  "settings": [
    {
      "type": "checkbox",
      "id": "show_hero",
      "label": "Used as Hero",
      "info": "Enable if this section is used as hero, disable if not."
    },
    {
      "type": "header",
      "content": "Carousel"
    },
    {
      "type": "checkbox",
      "id": "auto_play",
      "label": "Enable auto-play",
      "default": true
    },
    {
      "type": "range",
      "id": "change_slides_speed",
      "min": 5,
      "max": 10,
      "step": 1,
      "unit": "s",
      "label": "Change slides every",
      "default": 5
    },
    {
      "type": "checkbox",
      "id": "show_arrow",
      "label": "Next/Previous arrows",
      "default": false,
      "info": "Show navigation arrows"
    },
    {
      "type": "color",
      "id": "slider_button_color",
      "default": "#525151",
      "label": "Slider button color"
    },
    {
      "type": "color",
      "id": "slider_button_text_color",
      "default": "#525151",
      "label": "Slider button text color"
    },
    {
      "type": "color",
      "id": "slider_button_hover_color",
      "default": "#ff6600",
      "label": "Slider button hover color"
    },
    {
      "type": "color",
      "id": "slider_button_hover_text_color",
      "default": "#000",
      "label": "Slider button hover text color"
    },
    {
      "type": "select",
      "id": "transition_style",
      "options": [
        {
          "value": "default",
          "label": "Slide"
        },
        {
          "value": "fade",
          "label": "Fade"
        }
      ],
      "default": "fade",
      "label": "Transition style"
    },
    {
      "type": "select",
      "id": "slider_visual",
      "options": [
        {
          "value": "none",
          "label": "None"
        },
        {
          "value": "dots",
          "label": "Dots"
        },
        {
          "value": "bars",
          "label": "Bars"
        }
      ],
      "default": "dots",
      "label": "Pagination"
    },
    {
      "type": "header",
      "content": "Desktop layout"
    },
    {
      "type": "checkbox",
      "id": "full_width",
      "default": true,
      "label": "Make section full width"
    },
    {
      "type": "checkbox",
      "id": "padding_full_width",
      "default": false,
      "label": "Enable side padding",
      "info": "Add left and right padding when section is full-width."
    },
    {
      "type": "checkbox",
      "id": "rounded_corner_image",
      "default": false,
      "label": "Enable rounded corner images"
    },
    {
      "type": "select",
      "id": "desktop_height",
      "options": [
        {
          "value": "450",
          "label": "450px"
        },
        {
          "value": "550",
          "label": "550px"
        },
        {
          "value": "650",
          "label": "650px"
        },
        {
          "value": "750",
          "label": "750px"
        },
        {
          "value": "natural",
          "label": "Adapt to first image"
        },
        {
          "value": "fullscreen",
          "label": "Fullscreen"
        }
      ],
      "default": "550",
      "label": "Desktop height"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "unit": "px",
      "step": 4,
      "default": 0,
      "label": "Top padding"
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "unit": "px",
      "step": 4,
      "default": 28,
      "label": "Bottom padding"
    },
    {
      "type": "header",
      "content": "Mobile layout"
    },
    {
      "type": "checkbox",
      "id": "full_width_mobile",
      "default": true,
      "label": "Make section full width"
    },
    {
      "type": "checkbox",
      "id": "rounded_corner_image_mobile",
      "default": false,
      "label": "Enable rounded corner images"
    },
    {
      "type": "select",
      "id": "mobile_height",
      "options": [
        {
          "value": "250",
          "label": "250px"
        },
        {
          "value": "300",
          "label": "300px"
        },
        {
          "value": "400",
          "label": "400px"
        },
        {
          "value": "500",
          "label": "500px"
        },
        {
          "value": "natural",
          "label": "Adapt to first image"
        },
        {
          "value": "fullscreen",
          "label": "Fullscreen"
        }
      ],
      "default": "500",
      "label": "Mobile height"
    },
    {
      "type": "range",
      "id": "padding_top_mobile",
      "min": 0,
      "max": 100,
      "unit": "px",
      "step": 4,
      "default": 0,
      "label": "Top padding"
    },
    {
      "type": "range",
      "id": "padding_bottom_mobile",
      "min": 0,
      "max": 100,
      "unit": "px",
      "step": 4,
      "default": 20,
      "label": "Bottom padding"
    }
  ],
  "blocks": [
    {
      "type": "slide",
      "name": "Slide",
      "limit": 5,
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "image_picker",
          "id": "image_mobile",
          "label": "Mobile image"
        },
        {
          "type": "range",
          "id": "overlay_opacity",
          "min": 0,
          "max": 100,
          "step": 10,
          "unit": "%",
          "label": "Overlay opacity",
          "default": 0
        },
        {
          "type": "color",
          "id": "image_overlay_color",
          "default": "#202020",
          "label": "Overlay color"
        },
        {
          "type": "url",
          "id": "slide_link",
          "label": "Slide link"
        },
        {
          "type": "checkbox",
          "id": "open_new_window_slide",
          "default": false,
          "label": "Open this link in a new window"
        },
        {
          "type": "header",
          "content": "Shopify-hosted video"
        },
        {
          "type": "video",
          "id": "video",
          "label": "Video"
        },
        {
          "type": "checkbox",
          "id": "show_sound_control",
          "label": "Show sound control",
          "info": "Applies to auto play videos only.",
          "default": false
        },
        {
          "type": "header",
          "content": "Or embed video from URL"
        },
        {
          "type": "paragraph",
          "content": "Shows when no Shopify-hosted video is selected."
        },
        {
          "type": "video_url",
          "id": "video_url",
          "accept": [
            "youtube",
            "vimeo"
          ],
          "label": "URL",
          "info": "Supports YouTube and Vimeo. When pagination style is Image and a Vimeo video is used, please select desktop and mobile image above to show as image pagination."
        },
        {
          "type": "checkbox",
          "id": "enable_video_autoplay",
          "default": false,
          "label": "Enable video autoplay",
          "info": "Video will be muted when autoplay is on."
        },
        {
          "type": "text",
          "id": "video_alt_text",
          "label": "Video alt text"
        },
        {
          "type": "header",
          "content": "Content"
        },
        {
          "type": "checkbox",
          "id": "enable_bakground",
          "default": false,
          "label": "Enable background for content"
        },
        {
          "type": "text",
          "id": "subheading",
          "label": "Subheading"
        },
        {
          "type": "text",
          "id": "heading",
          "default": "Special Offer 70% Off",
          "label": "Heading",
          "info": "Wrap your text between [] to add heading highlights. E.g: Adding [marker] will underline [highlight] text."
        },
        {
          "type": "select",
          "id": "highlight_type",
          "default": "underline",
          "label": "Marker",
          "options": [
            {
              "value": "underline",
              "label": "Underline"
            },
            {
              "value": "font_highlight",
              "label": "Font highlight"
            }
          ]
        },
        {
          "type": "range",
          "id": "heading_size",
          "min": 50,
          "max": 200,
          "unit": "%",
          "step": 10,
          "default": 170,
          "label": "Heading size"
        },
        {
          "type": "select",
          "id": "heading_tag",
          "default": "h2",
          "label": "Heading tag",
          "options": [
            {
              "value": "h1",
              "label": "H1"
            },
            {
              "value": "h2",
              "label": "H2"
            },
            {
              "value": "h3",
              "label": "H3"
            },
            {
              "value": "h4",
              "label": "H4"
            },
            {
              "value": "h5",
              "label": "H5"
            },
            {
              "value": "h6",
              "label": "H6"
            },
            {
              "value": "p",
              "label": "p"
            }
          ]
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p>Tell your brand's story through image</p>"
        },
        {
          "type": "header",
          "content": "Button Design"
        },
        {
          "type": "select",
          "id": "button_type",
          "options": [
            {
              "value": "square",
              "label": "Square"
            },
            {
              "value": "rounded",
              "label": "Rounded"
            },
            {
              "value": "rounded_corners",
              "label": "Rounded Corners"
            },
            {
              "value": "mixed",
              "label": "Mixed"
            }
          ],
          "label": "Button style",
          "default": "rounded"
        },
        {
          "type": "select",
          "id": "button_color_mobile",
          "options": [
            {
              "value": "hover",
              "label": "Use button hover color"
            },
            {
              "value": "color",
              "label": "Use button color"
            }
          ],
          "label": "Mobile primary button style",
          "default": "color"
        },
        {
          "type": "select",
          "id": "button_animation",
          "options": [
            {
              "value": "slide",
              "label": "Slide"
            },
            {
              "value": "fill_up",
              "label": "Fill up"
            },
            {
              "value": "sliced",
              "label": "Sliced with icon"
            },
            {
              "value": "underline",
              "label": "Underline"
            }
          ],
          "label": "Primary button hover animation",
          "default": "sliced"
        },
        {
          "type": "html",
          "id": "custom_icon_button",
          "label": "Custom icon (SVG code)",
          "info": "Applies to 'Sliced with icon' animation type. For assistance with custom SVG code or fixing issues that arise from custom SVG code, please [contact our support](https://omnithemes.com/contact/)."
        },
        {
          "type": "text",
          "id": "button_label_1",
          "default": "Button Label",
          "label": "First button label"
        },
        {
          "type": "url",
          "id": "button_link_1",
          "label": "First button link"
        },
        {
          "type": "checkbox",
          "id": "open_new_window_button",
          "default": false,
          "label": "Open this link in a new window"
        },
        {
          "type": "checkbox",
          "id": "button_primary_1",
          "default": true,
          "label": "Show as primary button"
        },
        {
          "type": "text",
          "id": "button_label_2",
          "label": "Second button label"
        },
        {
          "type": "url",
          "id": "button_link_2",
          "label": "Second button link"
        },
        {
          "type": "checkbox",
          "id": "open_new_window_button_2",
          "default": false,
          "label": "Open this link in a new window"
        },
        {
          "type": "checkbox",
          "id": "button_primary_2",
          "default": false,
          "label": "Show as primary button"
        },
        {
          "type": "header",
          "content": "Desktop"
        },
        {
          "type": "select",
          "id": "content_alignment",
          "options": [
            {
              "value": "top-left",
              "label": "Top left"
            },
            {
              "value": "top-center",
              "label": "Top center"
            },
            {
              "value": "top-right",
              "label": "Top right"
            },
            {
              "value": "middle-left",
              "label": "Center left"
            },
            {
              "value": "middle-center",
              "label": "Center"
            },
            {
              "value": "middle-right",
              "label": "Center right"
            },
            {
              "value": "bottom-left",
              "label": "Bottom left"
            },
            {
              "value": "bottom-center",
              "label": "Bottom center"
            },
            {
              "value": "bottom-right",
              "label": "Bottom right"
            },
            {
              "value": "custom",
              "label": "Custom"
            }
          ],
          "default": "custom",
          "label": "Position"
        },
        {
          "type": "range",
          "id": "custom_horizontal",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "%",
          "default": 85,
          "label": "Custom horizontal position",
          "info": "Used with Custom position only."
        },
        {
          "type": "range",
          "id": "custom_vertical",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "%",
          "default": 80,
          "label": "Custom vertical position",
          "info": "Used with Custom position only."
        },
        {
          "type": "select",
          "id": "alignment",
          "default": "right",
          "label": "Content alignment",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ]
        },
        {
          "type": "header",
          "content": "Mobile"
        },
        {
          "type": "checkbox",
          "id": "mobile_custom_position",
          "default": false,
          "label": "Use custom position"
        },
        {
          "type": "range",
          "id": "custom_horizontal_mobile",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "%",
          "default": 50,
          "label": "Custom horizontal position",
          "info": "Used with Custom position only."
        },
        {
          "type": "range",
          "id": "custom_vertical_mobile",
          "min": 0,
          "max": 100,
          "step": 1,
          "unit": "%",
          "default": 50,
          "label": "Custom vertical position",
          "info": "Used with Custom position only."
        },
        {
          "type": "select",
          "id": "alignment_mobile",
          "default": "center",
          "label": "Content alignment",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ]
        },
        {
          "type": "header",
          "content": "Colors"
        },
        {
          "type": "color",
          "id": "heading_color",
          "label": "Heading",
          "default": "#FFFFFF"
        },
        {
          "type": "color",
          "id": "color_heading_highlight",
          "label": "Heading highlight color",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "color_text",
          "label": "Text",
          "default": "#FFFFFF"
        },
        {
          "type": "color",
          "id": "color_text_link",
          "label": "Text link",
          "default": "#FFFFFF"
        },
        {
          "type": "color",
          "id": "color_button",
          "label": "Button",
          "default": "#FFFFFF"
        },
        {
          "type": "color",
          "id": "color_text_button",
          "label": "Button text",
          "default": "#2DB29B"
        },
        {
          "type": "color",
          "id": "color_button_hover",
          "label": "Button hover",
          "default": "#2DB29B"
        },
        {
          "type": "color",
          "id": "color_text_button_hover",
          "label": "Button text hover",
          "default": "#FFFFFF"
        },
        {
          "type": "color",
          "id": "color_button_secondary",
          "label": "Secondary button"
        },
        {
          "type": "color",
          "id": "secondary_button_text",
          "label": "Secondary button text"
        }
      ]
    },
    {
      "type": "slide_text",
      "name": "Media with text",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image",
          "info": "For recommended image sizes, check our [user guide](https://support.omnithemes.com/blogs/faqs/can-you-recommend-image-sizes-for-the-different-image-layouts-of-different-section)"
        },
        {
          "type": "image_picker",
          "id": "image_mobile",
          "label": "Mobile image",
          "info": "For recommended image sizes, check our [user guide](https://support.omnithemes.com/blogs/faqs/can-you-recommend-image-sizes-for-the-different-image-layouts-of-different-section)"
        },
        {
          "type": "url",
          "id": "image_link",
          "label": "Image link"
        },
        {
          "type": "checkbox",
          "id": "open_new_window_image",
          "default": false,
          "label": "Open this link in a new window"
        },
        {
          "type": "text",
          "id": "subheading",
          "label": "Subheading"
        },
        {
          "type": "text",
          "id": "heading",
          "default": "Slide title",
          "label": "Heading",
          "info": "Wrap your text between [] to add heading highlights. E.g: Adding [marker] will underline [highlight] text."
        },
        {
          "type": "select",
          "id": "highlight_type",
          "default": "underline",
          "label": "Marker",
          "options": [
            {
              "value": "underline",
              "label": "Underline"
            },
            {
              "value": "font_highlight",
              "label": "Font highlight"
            }
          ]
        },        
        {
          "type": "range",
          "id": "heading_size",
          "min": 50,
          "max": 200,
          "unit": "%",
          "step": 10,
          "default": 170,
          "label": "Heading size"
        },
        {
          "type": "select",
          "id": "heading_tag",
          "default": "h2",
          "label": "Heading tag",
          "options": [
            {
              "value": "h1",
              "label": "H1"
            },
            {
              "value": "h2",
              "label": "H2"
            },
            {
              "value": "h3",
              "label": "H3"
            },
            {
              "value": "h4",
              "label": "H4"
            },
            {
              "value": "h5",
              "label": "H5"
            },
            {
              "value": "h6",
              "label": "H6"
            },
            {
              "value": "p",
              "label": "p"
            }
          ]
        },
        {
          "type": "richtext",
          "id": "text",
          "default": "<p>Tell your brand's story through images</p>",
          "label": "Text"
        },
        {
          "type": "header",
          "content": "Button Design"
        },
        {
          "type": "select",
          "id": "button_type",
          "options": [
            {
              "value": "square",
              "label": "Square"
            },
            {
              "value": "rounded",
              "label": "Rounded"
            },
            {
              "value": "rounded_corners",
              "label": "Rounded Corners"
            },
            {
              "value": "mixed",
              "label": "Mixed"
            }
          ],
          "label": "Button style",
          "default": "square"
        },
        {
          "type": "select",
          "id": "button_color_mobile",
          "options": [
            {
              "value": "hover",
              "label": "Use button hover color"
            },
            {
              "value": "color",
              "label": "Use button color"
            }
          ],
          "label": "Mobile primary button style",
          "default": "color"
        },
        {
          "type": "select",
          "id": "button_animation",
          "options": [
            {
              "value": "slide",
              "label": "Slide"
            },
            {
              "value": "fill_up",
              "label": "Fill up"
            },
            {
              "value": "sliced",
              "label": "Sliced with icon"
            },
            {
              "value": "underline",
              "label": "Underline"
            }
          ],
          "label": "Primary button hover animation",
          "default": "fill_up"
        },
        {
          "type": "html",
          "id": "custom_icon_button",
          "label": "Custom icon (SVG code)",
          "info": "Applies to 'Sliced with icon' animation type. For assistance with custom SVG code or fixing issues that arise from custom SVG code, please [contact our support](https://omnithemes.com/contact/)."
        },
        {
          "type": "text",
          "id": "button_label_1",
          "default": "Button label",
          "label": "First button label"
        },
        {
          "type": "url",
          "id": "button_link_1",
          "label": "First button link"
        },
        {
          "type": "checkbox",
          "id": "open_new_window_button",
          "default": false,
          "label": "Open this link in a new window"
        },
        {
          "type": "select",
          "id": "show_button_style_1",
          "options": [
            {
              "value": "primary",
              "label": "Primary"
            },
            {
              "value": "secondary",
              "label": "Secondary"
            },
            {
              "value": "text-link",
              "label": "Text link"
            }
          ],
          "label": "Button style",
          "default": "primary"
        },
        {
          "type": "text",
          "id": "button_label_2",
          "default": "Button label",
          "label": "Second button label"
        },
        {
          "type": "url",
          "id": "button_link_2",
          "label": "Second button link"
        },
        {
          "type": "checkbox",
          "id": "open_new_window_button_2",
          "default": false,
          "label": "Open this link in a new window"
        },
        {
          "type": "select",
          "id": "show_button_style_2",
          "options": [
            {
              "value": "primary",
              "label": "Primary"
            },
            {
              "value": "secondary",
              "label": "Secondary"
            },
            {
              "value": "text-link",
              "label": "Text link"
            }
          ],
          "label": "Button style",
          "default": "primary"
        },
        {
          "type": "select",
          "id": "content_alignment",
          "options": [
            {
              "value": "top-center",
              "label": "Top center"
            },
            {
              "value": "middle-center",
              "label": "Center"
            },
            {
              "value": "bottom-center",
              "label": "Bottom center"
            }
          ],
          "default": "middle-center",
          "label": "Position"
        },
        {
          "type": "select",
          "id": "alignment",
          "default": "center",
          "label": "Content alignment",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ]
        },
        {
          "type": "header",
          "content": "Layout"
        },
        {
          "type": "select",
          "id": "desktop_layout",
          "default": "left",
          "label": "Image desktop position",
          "options": [
            {
              "value": "left",
              "label": "Left"
            },
            {
              "value": "right",
              "label": "Right"
            }
          ]
        },
        {
          "type": "select",
          "id": "mobile_layout",
          "default": "top",
          "label": "Image mobile position",
          "options": [
            {
              "value": "top",
              "label": "Top"
            },
            {
              "value": "bottom",
              "label": "Bottom"
            }
          ]
        },
        {
          "type": "header",
          "content": "Styles"
        },
        {
          "type": "color",
          "id": "content_background_color",
          "label": "Content background color",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "color_text",
          "label": "Text color",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "color_button",
          "label": "Button color",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "color_text_button",
          "label": "Button text color",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "color_button_hover",
          "label": "Button hover color",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "color_text_button_hover",
          "label": "Button text hover color",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "color_button_secondary",
          "label": "Secondary button color",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "secondary_button_text",
          "default": "rgba(0,0,0,0)",
          "label": "Secondary button text color"
        },
        {
          "type": "color",
          "id": "colors_text_link",
          "default": "rgba(0,0,0,0)",
          "label": "Text link"
        },
        {
          "type": "color",
          "id": "color_heading_highlight",
          "label": "Heading highlight color",
          "default": "rgba(0,0,0,0)"
        }
      ]
    },
  ],
  "presets": [
    {
      "name": "OT: Slideshow #4",
      "blocks": [
        {
          "type": "slide"
        },
        {
          "type": "slide"
        },
        {
          "type": "slide"
        }
      ]
    }
  ]
}
{% endschema %}
