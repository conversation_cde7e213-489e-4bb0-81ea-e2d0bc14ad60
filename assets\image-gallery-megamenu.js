/**
 * Image Gallery Mega Menu JavaScript
 * Enhances the mega menu with smooth interactions
 */

class ImageGalleryMegaMenu {
  constructor() {
    this.init();
  }

  init() {
    // this.setupMegaMenuTriggers(); // Let CSS handle mega menu show/hide
    this.setupImageGalleryInteractions();
    this.setupDynamicPositioning();
  }

  setupDynamicPositioning() {
    // Calculate and set header height for proper mega menu positioning
    const updateHeaderHeight = () => {
      const header = document.querySelector('header, .shopify-section-header, [data-section-type="header"]');
      if (header) {
        const headerHeight = header.offsetHeight;
        document.documentElement.style.setProperty('--header-height', `${headerHeight}px`);
      }
    };

    // Update on load and resize
    updateHeaderHeight();
    window.addEventListener('resize', updateHeaderHeight);
    
    // Update when header changes (sticky behavior, etc.)
    const observer = new ResizeObserver(updateHeaderHeight);
    const header = document.querySelector('header, .shopify-section-header, [data-section-type="header"]');
    if (header) {
      observer.observe(header);
    }
  }

  setupMegaMenuTriggers() {
    const menuItems = document.querySelectorAll('.image-gallery-menu.has-mega-menu');
    
    menuItems.forEach(item => {
      const trigger = item.querySelector('a.dt-sc-nav-link');
      const megaMenu = item.querySelector('.sub-menu-block');
      
      if (trigger && megaMenu) {
        let showTimeout, hideTimeout;
        
        const showMegaMenu = () => {
          clearTimeout(hideTimeout);
          showTimeout = setTimeout(() => {
            megaMenu.style.display = 'block';
            megaMenu.style.opacity = '1';
            megaMenu.style.visibility = 'visible';
          }, 150);
        };
        
        const hideMegaMenu = () => {
          clearTimeout(showTimeout);
          hideTimeout = setTimeout(() => {
            megaMenu.style.opacity = '0';
            megaMenu.style.visibility = 'hidden';
            setTimeout(() => {
              if (megaMenu.style.opacity === '0') {
                megaMenu.style.display = 'none';
              }
            }, 300);
          }, 150);
        };
        
        item.addEventListener('mouseenter', showMegaMenu);
        item.addEventListener('mouseleave', hideMegaMenu);
        
        // Handle keyboard navigation
        trigger.addEventListener('focus', showMegaMenu);
        trigger.addEventListener('blur', (e) => {
          if (!item.contains(e.relatedTarget)) {
            hideMegaMenu();
          }
        });
      }
    });
  }

  setupImageGalleryInteractions() {
    const galleryItems = document.querySelectorAll('.image-gallery-item');
    
    galleryItems.forEach(item => {
      const img = item.querySelector('img');
      
      if (img) {
        // Add loading state
        img.addEventListener('load', () => {
          item.classList.add('loaded');
        });
        
        // Add hover effect for better accessibility
        item.addEventListener('mouseenter', () => {
          item.style.transform = 'translateY(-5px)';
        });
        
        item.addEventListener('mouseleave', () => {
          item.style.transform = 'translateY(0)';
        });
        
        // Add keyboard support
        item.addEventListener('keydown', (e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            item.click();
          }
        });
      }
    });
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new ImageGalleryMegaMenu();
});

// Also initialize on theme editor events
document.addEventListener('shopify:section:load', () => {
  new ImageGalleryMegaMenu();
});
