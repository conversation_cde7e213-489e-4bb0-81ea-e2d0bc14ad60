.newsletter-form {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  position: relative;
}
.footer-block--newsletter .newsletter-form{justify-content:unset; align-items:unset;flex-direction: unset;}
@media screen and (min-width: 750px) {
  .newsletter-form {
    align-items: flex-start;
    max-width: 45rem;
    position: relative;
    padding-left: 0px;
  }

}
.footer-block__details-content-newsletter{margin-bottom:2rem;}
.newsletter-form__field-wrapper {
  width: 100%;
}

.newsletter-form__field-wrapper .field__input {
  padding: 0 2.6rem 0 2.6rem;
  border-radius: 30px;
}

@media screen and (max-width: 480px) {
.newsletter-form__field-wrapper .field__input{  padding: 0 1.4rem 0 1.4rem;}
}
.newsletter-form__field-wrapper .field {
  z-index: 0;
}

.newsletter-form__message {
  justify-content: flex-start;
  margin-bottom: 0;
  outline: none;
  box-shadow: none;
  outline-offset: unset;
}

.newsletter-form__message--success {
  margin-top: 2rem; margin-bottom: .2rem;
}

@media screen and (min-width: 750px) {
  .newsletter-form__message {
    justify-content: flex-start;
    width:100%;
  }
  .newsletter-form__button {
  width: 11rem;
  }
}

.newsletter-form__button {
     width: 11rem;
    top: 0;
    bottom:0;
    margin:0 0 0 10px ;
    z-index: 2;
    font-size: 1.4rem;
    position: absolute;
    background:transparent;

}

.newsletter-form__button:focus-visible {
  box-shadow: 0 0 0 .3rem rgb(var(--color-background)),0 0 0 .4rem rgba(var(--color-foreground));
  background-color: rgb(var(--color-background));
  transition:all 0.3s linear;
}

.newsletter-form__button:focus, .newsletter-form__button:hover {
  color:rgba(var(--color-base-outline-button-labels));
}


.newsletter-form__button .icon {
  width: 1.5rem;
}
.field:after{display:none;}
 .newsletter__wrapper-right{margin-top:30px;}

.newletter_wrapper-block .field__input{ height: 5rem;background: var(--gradient-base-background-2);}
.newletter_wrapper-block .field:hover.field:after{box-shadow:unset;}
.newletter_wrapper-block .field__button{ font-family: var(--font-body-family);height:auto;}
.newletter_wrapper-block .field__button svg{width:16px; height:14px; transition: all .3s linear; fill: var(--gradient-background);}
.newletter_wrapper-block .field__button:hover { color: var(--gradient-base-background-1);}
.newsletter__wrapper-left h2{ margin:0 0 1rem 0;} 
@media screen and (max-width: 749px) {
.newsletter__wrapper .newsletter-form__field-wrapper{ max-width: 50rem;}
.newsletter__wrapper-left{    margin-bottom: 20px;}
}
.section-email-signup .newsletter-form__button{font-family:var(--font-heading-family);}



