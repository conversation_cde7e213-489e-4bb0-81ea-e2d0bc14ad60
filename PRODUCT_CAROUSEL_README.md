# Product Carousel Section

A comprehensive, accessible, and responsive product carousel section for Shopify themes.

## Features

### 🎠 Carousel Functionality
- **Horizontal scrolling/sliding** with smooth transitions
- **Navigation arrows** with hover effects and accessibility support
- **Dot indicators** with dynamic bullets and keyboard navigation
- **Touch/swipe support** for mobile devices with gesture recognition
- **Auto-play option** with configurable timing and pause on interaction
- **Infinite loop** capability for continuous browsing

### 📱 Responsive Design
- **Desktop**: 4-5 products per view (configurable)
- **Laptop**: 3 products per view (configurable)
- **Tablet**: 2-3 products per view (configurable)
- **Mobile**: 1-2 products per view (configurable)
- **Adaptive spacing** that adjusts based on screen size
- **Touch-optimized** interface for mobile devices

### 🎨 Product Card Design
- **Modern, clean aesthetic** with sophisticated hover effects
- **High-quality product images** with lazy loading and hover zoom
- **Multiple card styles**: Standard, Button with icons, Card with icons, Card with buttons, Card with overlay
- **Consistent card sizing** with equal height alignment
- **Product information display**:
  - Product title with hover effects
  - Price (regular and sale price)
  - Product rating/reviews (if available)
  - "Add to Cart" or "Quick View" buttons
  - Sale/discount badges
  - Inventory status indicators
  - Product vendor information

### ♿ Accessibility Features
- **ARIA labels** and roles for screen readers
- **Keyboard navigation** (Arrow keys, Home, End, Enter, Space)
- **Focus management** with proper tab order
- **Screen reader announcements** for slide changes
- **Skip links** for keyboard users
- **Reduced motion support** for users with motion sensitivity
- **High contrast** navigation elements
- **Semantic HTML** structure

### ⚡ Performance Optimizations
- **Lazy loading** for images with intersection observer
- **Preload optimization** for next/previous slides
- **Efficient CSS** with minimal reflows
- **JavaScript optimization** with event delegation
- **Performance monitoring** with timing marks

## Installation

1. **Add the section file**: Copy `sections/product-carousel.liquid` to your theme's sections directory
2. **Add the CSS file**: Copy `assets/section-product-carousel.css` to your theme's assets directory
3. **Dependencies**: Ensure your theme includes:
   - Swiper.js (included in the section)
   - Existing product card snippets
   - Theme's base CSS variables

## Configuration Options

### Basic Settings
- **Title**: Section heading text
- **Description**: Optional description text
- **Collection**: Select which collection to display
- **Products to show**: Maximum number of products (2-25)

### Layout Options
- **Card style**: Choose from 5 different product card designs
- **Image ratio**: Adapt to image, Portrait, or Square
- **Columns**: Configure for Desktop (1-6), Laptop (1-5), Tablet (1-4), Mobile (1-3)
- **Full width**: Make section span full viewport width

### Carousel Settings
- **Force carousel**: Always show as carousel regardless of product count
- **Enable loop**: Infinite scrolling capability
- **Auto-play speed**: 0-10 seconds (0 disables auto-play)
- **Space between slides**: 0-50px spacing

### Navigation Options
- **Show navigation arrows**: Enable/disable arrow buttons
- **Hide arrows on mobile**: Mobile-specific arrow visibility
- **Show pagination dots**: Enable/disable dot indicators
- **Hide pagination on desktop**: Desktop-specific dot visibility

### Product Information
- **Show secondary image**: Display second image on hover
- **Show vendor**: Display product brand/vendor
- **Show rating**: Display product reviews/ratings
- **Show 'New' tag**: Display new product indicators
- **Show 'View all' button**: Link to full collection

### Styling
- **Color scheme**: Choose from theme color schemes
- **Section padding**: Top and bottom spacing (0-120px)

## Usage Examples

### Basic Implementation
```liquid
{% section 'product-carousel' %}
```

### With Custom Settings
```json
{
  "type": "product-carousel",
  "settings": {
    "title": "Featured Products",
    "collection": "featured-products",
    "products_to_show": 12,
    "columns_desktop": 4,
    "columns_mobile": 1,
    "auto_play": 5,
    "show_navigation": true,
    "show_pagination": true
  }
}
```

## Browser Support

- **Modern browsers**: Chrome 60+, Firefox 55+, Safari 12+, Edge 79+
- **Mobile browsers**: iOS Safari 12+, Chrome Mobile 60+
- **Accessibility**: WCAG 2.1 AA compliant
- **Performance**: Optimized for Core Web Vitals

## Customization

### CSS Variables
The carousel uses theme CSS variables for consistent styling:
- `--gradient-base-accent-1`: Primary accent color
- `--color-background`: Background color
- `--color-foreground`: Text color
- `--color-border`: Border color

### JavaScript Events
The carousel emits custom events for integration:
- `carousel:init`: When carousel initializes
- `carousel:slideChange`: When slide changes
- `carousel:autoplayStart`: When autoplay starts
- `carousel:autoplayStop`: When autoplay stops

## Troubleshooting

### Common Issues
1. **Images not loading**: Check lazy loading implementation
2. **Navigation not working**: Verify Swiper.js is loaded
3. **Accessibility issues**: Test with screen readers
4. **Performance problems**: Check image optimization

### Debug Mode
Add `?debug=true` to URL for console logging and performance metrics.

## Contributing

When modifying the carousel:
1. Test on all breakpoints
2. Verify accessibility with screen readers
3. Check performance impact
4. Validate HTML and CSS
5. Test keyboard navigation

## License

This component follows the theme's licensing terms.
